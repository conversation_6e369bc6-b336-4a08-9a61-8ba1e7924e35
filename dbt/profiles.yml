config:
  send_anonymous_usage_stats: False

presentation:
  outputs:
    dev:
      type: snowflake
      threads: 1
      account: "{{ env_var('SNOWFLAKE_HOST') }}"
      user: "{{ env_var('T_REX_SNOWFLAKE_LOGIN') }}"
      password: "{{ env_var('T_REX_SNOWFLAKE_PASSWORD') }}"
      role: "{{ env_var('T_REX_SNOWFLAKE_ROLE') }}"
      database: "{{ env_var('T_REX_SNOWFLAKE_DATABASE') }}"
      warehouse: "{{ env_var('T_REX_SNOWFLAKE_DEFAULT_WAREHOUSE') }}"
      schema: TRANSIENT
      client_session_keep_alive: False
    stage:
      type: snowflake
      threads: 1
      account: "{{ env_var('SNOWFLAKE_HOST') }}"
      user: "{{ env_var('T_REX_SNOWFLAKE_LOGIN') }}"
      password: "{{ env_var('T_REX_SNOWFLAKE_PASSWORD') }}"
      role: "{{ env_var('T_REX_SNOWFLAKE_ROLE') }}"
      database: "{{ env_var('T_REX_SNOWFLAKE_DATABASE') }}"
      warehouse: "{{ env_var('T_REX_SNOWFLAKE_DEFAULT_WAREHOUSE') }}"
      schema: TRANSIENT
      client_session_keep_alive: False
    prod:
      type: snowflake
      threads: 1
      account: "{{ env_var('SNOWFLAKE_HOST') }}"
      user: "{{ env_var('T_REX_SNOWFLAKE_LOGIN') }}"
      password: "{{ env_var('T_REX_SNOWFLAKE_PASSWORD') }}"
      role: "{{ env_var('T_REX_SNOWFLAKE_ROLE') }}"
      database: "{{ env_var('T_REX_SNOWFLAKE_DATABASE') }}"
      warehouse: "{{ env_var('T_REX_SNOWFLAKE_DEFAULT_WAREHOUSE') }}"
      schema: TRANSIENT
      client_session_keep_alive: False
    local:
      type: snowflake
      threads: 4
      account: si44367.eu-central-1
      authenticator: externalbrowser
      user: "{{ env_var('SNOWFLAKE_USER_EMAIL') }}"
      role: "{{ env_var('T_REX_SNOWFLAKE_ROLE') }}"
      database: "{{ env_var('T_REX_SNOWFLAKE_DATABASE') }}"
      warehouse: "{{ env_var('T_REX_SNOWFLAKE_DEFAULT_WAREHOUSE') }}"
      schema: TRANSIENT
      client_session_keep_alive: False
    sqlfluff:
      type: snowflake
      account: si44367.eu-central-1
      user: "{{ env_var('SQLFLUFF_SNOWFLAKE_USER') }}"
      password: "{{ env_var('SQLFLUFF_SNOWFLAKE_PASSWORD') }}"
      role: "{{ env_var('SQLFLUFF_SNOWFLAKE_ROLE') }}"
      database: "{{ env_var('T_REX_SNOWFLAKE_DATABASE') }}"
      schema: PUBLIC
      query_tag: sqlfluff
      client_session_keep_alive: False
  target: "{{ env_var('AIRFLOW_VAR_ENV') }}"
