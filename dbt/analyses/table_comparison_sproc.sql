-- Docs: https://github.com/getndazn/dazn-dp20-presentation/blob/main/docs/TREX_TABLE_COMPARISON.md

CREATE OR REPLACE PROCEDURE TREX_TABLE_COMPARISON(
    TABLE1 VARCHAR, TABLE1_PRIMARY_KEY VARCHAR, TABLE1_WHERE_CLAUSE VARCHAR,
    TABLE2 VARCHAR, TABLE2_PRIMARY_KEY VARCHAR, TABLE2_WHERE_CLAUSE VARCHAR
)
    RETURNS TABLE(TEST_NAME VARCHAR, PASS BOOLEAN)
    LANGUAGE SQL
    AS
    $$
    BEGIN
        LET count_table1_statement VARCHAR := 'SELECT COUNT(*) FROM ' || :TABLE1 || ' ' || :TABLE1_WHERE_CLAUSE;
        LET count_table2_statement VARCHAR := 'SELECT COUNT(*) FROM ' || :TABLE2 || ' ' || :TABLE2_WHERE_CLAUSE;
        LET check_row_counts_equal_statement VARCHAR := 'SELECT \'check_row_counts_equal\', COUNT(*) = 1 FROM (' || count_table1_statement || ' UNION ' || count_table2_statement || ')';

        LET distinct_primary_key_rows_table1_statement VARCHAR := 'SELECT \'check_distinct_primary_key_rows_table_1\', "check" FROM (SELECT COUNT(DISTINCT ' || :TABLE1_PRIMARY_KEY || ') AS "distinct_primary_key_rows", COUNT(*) AS "total_rows", "distinct_primary_key_rows" = "total_rows" AS "check" FROM ' || :TABLE1 || ' ' || :TABLE1_WHERE_CLAUSE  || ')';
        LET distinct_primary_key_rows_table2_statement VARCHAR := 'SELECT \'check_distinct_primary_key_rows_table_2\', "check" FROM (SELECT COUNT(DISTINCT ' || :TABLE2_PRIMARY_KEY || ') AS "distinct_primary_key_rows", COUNT(*) AS "total_rows", "distinct_primary_key_rows" = "total_rows" AS "check" FROM ' || :TABLE2 || ' ' || :TABLE2_WHERE_CLAUSE  || ')';

        LET primary_keys_in_table1_statement VARCHAR := 'SELECT ' || :TABLE1_PRIMARY_KEY || ' AS "primary_key" FROM ' || :TABLE1 || ' ' || :TABLE1_WHERE_CLAUSE;
        LET primary_keys_in_table2_statement VARCHAR := 'SELECT ' || :TABLE2_PRIMARY_KEY || ' AS "primary_key" FROM ' || :TABLE2 || ' ' || :TABLE2_WHERE_CLAUSE;
        LET primary_keys_in_table1_except_those_in_table2 VARCHAR := 'SELECT \'table1_primary_keys_are_in_table2\', COUNT(*) = 0 FROM (' || primary_keys_in_table1_statement || ' EXCEPT ' || primary_keys_in_table2_statement || ')';
        LET primary_keys_in_table2_except_those_in_table1 VARCHAR := 'SELECT \'table2_primary_keys_are_in_table1\', COUNT(*) = 0 FROM (' || primary_keys_in_table2_statement || ' EXCEPT ' || primary_keys_in_table1_statement || ')';

        LET final_statement VARCHAR :=  check_row_counts_equal_statement || ' UNION ' ||
                                        distinct_primary_key_rows_table1_statement || ' UNION ' || distinct_primary_key_rows_table2_statement || ' UNION ' ||
                                        primary_keys_in_table1_except_those_in_table2 || ' UNION ' || primary_keys_in_table2_except_those_in_table1;

        LET result_set RESULTSET := (execute immediate final_statement);
        return table(result_set);
    END;
    $$;
