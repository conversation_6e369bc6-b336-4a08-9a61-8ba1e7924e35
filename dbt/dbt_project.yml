# dbt Project for Presentation Layer of Data Warehouse
name: presentation
version: '1.0.0'
config-version: 2

profile: presentation

# These configurations specify where dbt should look for different types of files.
# The `source-paths` config, for example, states that models in this project can be
# found in the "models/" directory. You probably won't need to change these!
model-paths: ["models"]
analysis-paths: ["analyses"]
test-paths: ["tests"]
seed-paths: ["seeds"]
macro-paths: ["macros"]
snapshot-paths: ["snapshots"]

target-path: "target"  # directory which will store compiled SQL files
clean-targets:         # directories to be removed by `dbt clean`
  - "target"
  - "dbt_packages"


# Snowflake Config
quoting:
  database: false
  schema: false
  identifier: false
query-comment:
  append: true


# Models Config
# Full documentation: https://docs.getdbt.com/docs/configuring-models
models:
  presentation:
    +persist_docs:
      relation: true
      columns: true
    +materialized: view
    +transient: false
    # Tactical solution to backport code to PRD
    tactical:
      prd:
        +database: "{{ env_var('PRD_SNOWFLAKE_DATABASE') }}"
        analytics:
          +schema: 'ANALYTICS'
    staging:
      +schema: 'STAGING'
      fanatics:
        +tags: ['staging-fanatics']
      amazon_personalize:
        +tags: ['staging-amazon-personalize']
      hyper_personalisation:
        +tags: ['staging-hyper-personalisation']
      picks:
        +tags: ['staging-picks']
      presentation_staging:
        +tags: ['presentation-staging']
tests:
  +severity: warn

# Variable config
vars:
  # The `batch_date` variable default to be used only for compilation
  batch_date: '2025-05-18'
  build_mode: 'daily'
  rebuild_days: 1
  'dbt_date:time_zone': 'UTC'

# configuring seeds
seeds:
  +quote_columns: true
  schema: PRESENTATION
