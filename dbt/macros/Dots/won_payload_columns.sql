-- All data coming from dots/WON will have these fields in the table.
-- We macro this so we don't have to rewrite them every time{% macro won_payload_columns()  -%}
        "DBT_INSERT_DTTS" AS "DBT_INSERT_DTTS"
        ,"EDM_INSERT_DTTS" AS "EDM_INSERT_DTTS"
        ,"lastUpdatedTime" AS "last_updated_timestamp"
        ,"isTest" AS "is_test"
        ,"envelopeSchemaVersion" AS "envelope_schema_version"
        ,"payloadId" AS "payload_id"
        ,"payloadType" AS "payload_type"
        ,"payloadState" AS "payload_state"
        ,"payload" AS "payload"
        ,"changeTime__timestamp" AS "change_timestamp"
        ,"changeOwner" AS "change_owner"
        ,"changeSource" AS "change_source"
{%- endmacro %}


