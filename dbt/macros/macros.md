# DBT Macros

## Source_ref Macro

### Use case

We often want to run our models in DEV or UAT but with source data coming from PROD. Source doesn't necessarily mean the
curated layer but also any other model already in PRESENTATION that we're using for what we're currently developing.

In order to achieve this, at the moment, we have to hardcode a table_name instead of the ref() function.
We then revert the change back when we want to push the changes to the remote.

Over a development cycle this can happen several times adding considerable overhead and a risk of pushing hardcoded table names through the environments.

### Solution

A macro called ref_env() and source_env() that can be used (in specific cases) instead of ref() and source().

``` sql

ref_env(model_name)

source_env(source_name, model_name)

```

The macro will look up an environment variable called 'SOURCE_REF_ENV' (can change the name if we want) and use it to point model references to a different environment.

_You can set this env variable in the .env file and change it depending on what env you want to point at._

The database name will be scanned and if either `_DEV`, `_UAT` or `_PROD` is found at the end of the string it will be replaced with
whatever environment is set in the SOURCE_REF_ENV variable

### Usage

Imagine a dbt model composed of one intermediate step and one 'final' step:

``` sql
#dbt/models/new_model/intermediate_step.sql
SELECT * FROM {{ ref_env()('MART__PLAYBACK_STREAM__FACT') }}
```

``` sql
#dbt/models/new_model/final.sql
SELECT * FROM {{ ref('intermediate_step') }}
```

The first step is referencing a model independent of what we're working on. We want the MART__PLAYBACK_STREAM__FACT data to come from **prod**.

Using the ref_env() function we can re-point the model to read from PROD. This is the compiled output:

``` sql
#dbt/target/..../new_model/intermediate_step.sql
SELECT * FROM PLAYBACK_STREAM__B2C__MART__PROD.FACT.PLAYBACK_STREAM__FACT
```

The final step on the other hand depends on the intermediate one. The intermediate one is being build in DEV so we need our ref to pick from dev.
In this case we use the ref() function that will look up the model in the same environment you're running dbt in:

``` sql
#dbt/target/..../new_model/final.sql
SELECT * FROM TRANSFORMATION_DEV.TRANSIENT.intermediate_step
```

The source_env() macro works in the same way, the only difference is the number of arguments which are two (like with the source())

``` sql
SELECT * FROM {{ source_env('DEVICE','device_dimension_test')}}
```

### Effects on airflow instances

The macro will default to the current environment (dev, uat, prod) if a SOURCE_REF_ENV env variable is not set.
This means we don't have to add or change anything in the airflow instances in order to make this macro work.
This also ensures that the ref_env() (and source_env()) macro in any of the airflow instances **will be equivalent to the ref() function** so we don't have to worry about potential impact in any of our deployed instances.

### Consideration on complexity

Having two types of refs() adds complexity to our models. Developers must be aware that this tool is available and that it might be implemented in any of our models.

In order to reduce the complexity it is important to agree on good practises for the use of ref_env().

There is no perfect solution to this. The one that seems to make most sense is to consider "model boundaries".
What this means is that you can **only** use ref_env() when you're referencing a completely different model in presentation (in almost all cases in a different model folder than the one you're working on).
We could implement a github action to ensure this rule is respected when pushing through the enviroments.

In short, **ref_env() should only be used to reference a resource with a different tag**

For example, in the SkyITRevenueShare model we have several intermediate steps and we're referencing other models in the presentation layer (subscription_domain, and fct_invoice). We would use source_ref() when referencing the sub domain and invoices, but we would keep using the ref() to reference all the models within the SkyITRevenueShare model.

There are some cases where this assumption breaks down, the most apparent is the boundary between the InvoiceFact and the  InvoiceItems models. They are very much dependent on each other and usually a change in one will have to be tested downstream.

### Consideration on downstream development

There might be issues when we multiple depths of dependencies between models (model folders).
Then we might have to go back hardcoding sources if the change happens in some intermediate step, but so far this hasn't happened yet.

### Checks

* The macro works for any Database where the naming convention has the [ENV] part at the end.
* The macro should maintain the lineage in DBT (this was taken running dbt docs generate/serve locally)
* If we implement this macro across our repo and then decide we no longer need it or it doesn't fit our requirements anymore we can easily scan the repo for all ref_env() functions and replace them all quite quickly.

## Templates

### Removing NULL values from a JINJA list

```text
{% macro remove_nulls_from_list(list_object)  -%}

    -- create an empty list to contain our not-null values
    {% set return_list = [] %}
    -- iterate through the original list and only append the not-null values
    {% for v in list_object %}
        {% if v is not none %}
            {% do return_list.append(v) %}
        {% endif %}
    {% endfor %}
    -- return our output list containing no null values.
    {{ return(return_list) }}
{%- endmacro %}
```

usage:

```text
{% set some_list = another_macro_returning_lists() %}
-- macro contains nulls
{{ some_list_without_nulls = remove_nulls_from_list(some_list) }}
```
