{% macro timestamp_build_trigger(build_mode, rebuild_days, timestamp_field)  -%}
('{{ build_mode }}' = 'daily'
    -- filtering for the past 3 days
    AND "{{ timestamp_field }}" >= DATEADD(day, -{{ rebuild_days }}, CURRENT_DATE)
    AND "{{ timestamp_field }}" < DATE_TRUNC(day, CURRENT_DATE)
    )
    OR
('{{ build_mode }}' = 'rebuild'
    AND 1=1
    )
    OR
('{{ build_mode }}' = 'test'
    -- filtering for the events on the 2021-11-01
    AND "{{ timestamp_field }}" >= '2021-12-01'
    AND "{{ timestamp_field }}" < '2022-01-31'
    )
    OR
('{{ build_mode }}' = 'debug'
    AND 1=0
    )
{%- endmacro %}
