-- the inclause macro accepts a list object and returns the elements in the string quoted in single quotes and
-- separated by commas. This is used in statements like ```where column IN ('a','b','c'...)
-- Example: select * from table where "column_a" in ({{ in_clause_from_list(list_of_values) }})
{% macro in_clause_from_list(list_object) %}
    {% set new_list = []%}
    {% for element in list_object%}
        {{ new_list.append( "'{}'".format(element)) }}
    {% endfor %}
    {{ return(','.join(new_list)) }}
{% endmacro %}
