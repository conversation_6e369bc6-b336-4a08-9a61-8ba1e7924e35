{% macro build_mode_trigger(build_mode, source_timestamp_field, target_timestamp_field)  -%}

    ('{{ build_mode }}' = 'daily'
    -- filtering for any rows that have been updated since last ingest
    AND {{ source_timestamp_field }} > (SELECT MAX({{ target_timestamp_field }}) FROM {{ this }})
    )

    OR
    ('{{ build_mode }}' = 'output_test'
    -- filtering for a specific range of test dates
    AND {{ source_timestamp_field }} >= '2021-12-01'
    AND {{ source_timestamp_field }} < '2022-01-31'
    )

    OR
    ('{{ build_mode }}' = 'compile_test'
    -- Filter for no data so that just the SQL syntax is tested for typos ect.
    AND 1=0
    )

{%- endmacro %}
