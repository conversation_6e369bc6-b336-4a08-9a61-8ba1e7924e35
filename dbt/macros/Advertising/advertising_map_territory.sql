-- This macro standardises the territory fields in all our models. It's used for grass valley, cloud tx and m2a.
{% macro advertising_map_territory(territory_column)  -%}

CASE
    WHEN CONTAINS("{{ territory_column }}", 'Off Platform') THEN INITCAP(REPLACE("{{ territory_column }}", ' Off Platform'))
    WHEN UPPER("{{ territory_column }}") IN ('SPAIN', 'SPAIN DAZN') THEN 'Spain'
    WHEN UPPER("{{ territory_column }}") IN ('Dach', 'dach', 'DACH') THEN 'DACH'
    WHEN UPPER("{{ territory_column }}") IN ('USA', 'UNITED STATES') THEN 'United States'
    WHEN UPPER("{{ territory_column }}") IN ('SHARED', 'SHD', 'SHD_GROUP') THEN 'Shared'
    ELSE INITCAP("{{ territory_column }}")
END

{%- endmacro %}
