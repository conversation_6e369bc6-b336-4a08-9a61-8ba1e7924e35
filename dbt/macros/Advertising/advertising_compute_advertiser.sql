-- This logic is used in the advertising_stack_source.sql model. It will derive an "advertiser" value for each record
-- regardless of whether it's a m2a, gv or cloud tx log.
{% macro advertising_compute_advertiser()  -%}

CASE
    -- The advertiser is nested in the line item name for mediaset logs.
    WHEN lower(logs."line_item_name") LIKE '%mediaset%' THEN SPLIT_PART(logs."line_item_name",'-',2)
    -- The advertiser is nested in the same way for mediaset Lbanner and 2box logs. The position in the string however is different.
    WHEN logs."break_type" IN ('lbanner', '2box') AND LOWER(logs."creative") LIKE '%mediaset%' THEN SPLIT_PART(logs."creative",'-',4)
    -- If the ad_type is one of these values then cast to DAZN.
    WHEN logs."ad_type" in ('House Ad', 'Legal Sting', 'Promo') THEN 'DAZN'
    -- Otherwise pick the advertiser field directly. Note this is only ever populated for M2A logs.
    ELSE pl_mapping."advertiser"
END
{%- endmacro %}
