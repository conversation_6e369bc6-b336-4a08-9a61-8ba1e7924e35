-- This macro contains the logic to derive the AD TYPE from the logs.
-- It looks at the creative to determine if it's a commercial, promo, sponsor etc..
-- This is then used downstream by other macros to compute PL numbers or advertisers.
{% macro advertising_compute_ad_type()  -%}

REGEXP_SUBSTR("creative", '[A-Za-z]{2}[0-9]{7}') AS "promo_substring"
,CASE
    WHEN SPLIT_PART("creative", '-', 2) = 'AD' AND "creative" ILIKE 'DAZN' THEN 'Promo'
    WHEN SPLIT_PART("creative", '-', 2) = 'AD' THEN 'Commercial'
    WHEN SPLIT_PART("creative", '-', 2) = 'SP' THEN 'Sponsorship'
    WHEN LEFT("promo_substring", 2 ) IN ('PR', 'pr', 'AL', 'ww', 'qa', 'nl', 'aj', 'or') THEN 'Promo'
    WHEN LEFT("creative", 2 ) IN ('Pr') THEN 'Commercial'
    WHEN LEFT("promo_substring", 2 ) IN ('SP','sp') THEN 'Sponsorship'
    WHEN LEFT("promo_substring", 2 ) IN ('UE', 'TT', 'TO', 'ST', 'SR' ,'SL','SB','ME', 'LS','LI', 'LB','ID' ,'CD', 'BP', 'ue', 'li','ja', 'it','vo') THEN 'Bumper'
    WHEN LEFT("promo_substring", 2 ) IN ('LS','ls') THEN 'Legal Sting'
    WHEN LEFT("creative", 2 ) IN ('GA') THEN 'Gap Media'
    WHEN "creative" IS NULL THEN 'Bumper'
    WHEN SPLIT_PART("creative", '-', 3) = 'AD' THEN 'Commercial'
    ELSE 'Unknown Ad Type'
END

{%- endmacro %}
