
{% macro advertising_compute_campaign()  -%}

CASE
    -- Deals with mediaset logs
    WHEN LOWER(logs."line_item_name") LIKE '%mediaset%' THEN SPLIT_PART(logs."line_item_name",'-',4)
    -- Deals with specific daznfun logs --todo check if this is still happening
    WHEN LOWER(logs."line_item_name") LIKE '%daznfun%' AND logs."break_type" = '2box' THEN 'DAZN2Box'
    -- deals with Lband and 2box (which are not daznfun ones)
    WHEN logs."break_type" IN ('lbanner', '2box') THEN SPLIT_PART(logs."creative",'-',6)
    -- some ad types that are DAZN campaigns
    WHEN logs."ad_type" in ('Sponsorship', 'House Ad', 'Legal Sting', 'Promo') THEN 'DAZN'
    -- otherwise we use the campaign that comes from the advertising_pl_mapping model
    ELSE pl_mapping."campaign_name"
END

{%- endmacro %}
