
{% macro advertising_compute_pl()  -%}

CASE
    -- Deal with cases where pl column exists
    WH<PERSON> logs."pl" LIKE 'PL%' THEN logs."pl"
    -- Deal with cases wehere PL in at the end
    WHEN logs."line_item_name" LIKE '%test%' OR logs."line_item_name" LIKE '%TEST%' OR logs."line_item_name" LIKE '%Test%' THEN 'Test'
    -- Deal with mediaset (External 2box)
    WHEN LOWER(logs."line_item_name") LIKE '%mediaset%' THEN CONCAT('PL_', REPLACE(SPLIT_PART(logs."line_item_name",'-',6), 'PL', ''))
    -- Deal with mediaset (Logs coming from source. extract using creative)
    WHEN logs."break_type" IN ('lbanner', '2box') AND LOWER(logs."creative") LIKE '%mediaset%' THEN CONCAT('PL_', REPLACE(SPLIT_PART(logs."creative",'-',8), 'PL', ''))
    -- House Ads
    WHEN logs."ad_type" = 'House Ad' THEN 'PL_007698'
    -- Promos
    WHEN logs."ad_type" = 'Promos' THEN 'PL_007612'
    -- Stings
    WHEN logs."ad_type" = 'Legal Sting' THEN 'PL_007699'
    ELSE NULL
END

{%- endmacro %}




