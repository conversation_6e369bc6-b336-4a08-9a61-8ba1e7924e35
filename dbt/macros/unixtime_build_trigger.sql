{% macro unixtime_build_trigger(build_mode, rebuild_days, unixtime, rebuild_start_date="'2022-01-01'", rebuild_end_date="'2022-02-01'")  -%}
('{{ build_mode }}' = 'daily'
    -- filtering for the past 3 days
    AND "{{ unixtime }}" >= DATE_PART(EPOCH_SECOND, DATEADD(day, -{{ rebuild_days }}, CURRENT_DATE))
    AND "{{ unixtime }}" < DATE_PART(EPOCH_SECOND, DATE_TRUNC(day, CURRENT_DATE))
    )
    OR
('{{ build_mode }}' = 'rebuild'
    AND 1=1
    )
    OR
('{{ build_mode }}' = 'test'
        -- filtering for the events on the 2021-11-01
    AND "{{ unixtime }}" >= 1635897600
    AND "{{ unixtime }}" < 1635997600
    )
    OR
('{{ build_mode }}' = 'batch'
    AND "{{ unixtime }}" >= DATE_PART(EPOCH_SECOND, TO_DATE({{ rebuild_start_date }}))
    AND "{{ unixtime }}" < DATE_PART(EPOCH_SECOND, TO_DATE({{ rebuild_end_date }}))
    )
    OR
('{{ build_mode }}' = 'debug'
    AND 1=0
    )
{%- endmacro %}
