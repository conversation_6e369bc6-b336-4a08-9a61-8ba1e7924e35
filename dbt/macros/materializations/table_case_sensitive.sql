{#
Custom materialization designed to handle create & replace of tables with lower and mixed case names

Based of dbt-snowflake package's table materialization, but with custom quoting macros added. Requires
quoting dictionary to be added to models config block, which will set the quoting rules for database, schema
and identifier.
#}
{% materialization table_case_sensitive, adapter='snowflake' %}

    {% set original_query_tag = set_query_tag() %}

    {%- set identifier = model['alias'] -%}

    {# Alter quoting rules before checking relation existence #}
    {% set quoting = config.get('quoting') %}
    {% do set_model_quoting(database=quoting['database'], schema=quoting['schema'], identifier=quoting['identifier']) %}

    {%- set old_relation = adapter.get_relation(database=database, schema=schema, identifier=identifier) -%}
    {%- set target_relation = api.Relation.create(identifier=identifier,
                                                    schema=schema,
                                                    database=database, type='table') -%}

    {{ run_hooks(pre_hooks) }}

    {#-- Drop the relation if it was a view to "convert" it in a table. This may lead to
        -- downtime, but it should be a relatively infrequent occurrence  #}
    {% if old_relation is not none and not old_relation.is_table %}
        {{ log("Dropping relation " ~ old_relation ~ " because it is of type " ~ old_relation.type) }}
        {{ drop_relation_if_exists(old_relation) }}
    {% endif %}

    --build model
    {% call statement('main') -%}
        {{ create_table_as(false, target_relation, sql) }}
    {%- endcall %}

    {{ run_hooks(post_hooks) }}

    {% do persist_docs(target_relation, model) %}

    {% do restore_default_quoting() %}
    {% do unset_query_tag(original_query_tag) %}

    {{ return({'relations': [target_relation]}) }}

{% endmaterialization %}
