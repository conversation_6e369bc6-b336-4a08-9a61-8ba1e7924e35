{#
Custom materialization designed to handle incremental loads into tables with lower and mixed case names

Based of dbt-snowflake package's incremental materialization, but with custom quoting macros added. Requires
quoting dictionary to be added to models config block, which will set the quoting rules for database, schema
and identifier.
#}

{% macro dbt_snowflake_validate_get_incremental_strategy(config) %}
  {#-- Find and validate the incremental strategy #}
  {%- set strategy = config.get("incremental_strategy", default="merge") -%}

  {% set invalid_strategy_msg -%}
    Invalid incremental strategy provided: {{ strategy }}
    Expected one of: 'merge', 'delete+insert'
  {%- endset %}
  {% if strategy not in ['merge', 'delete+insert'] %}
    {% do exceptions.raise_compiler_error(invalid_strategy_msg) %}
  {% endif %}

  {% do return(strategy) %}
{% endmacro %}

{% macro dbt_snowflake_get_incremental_sql(strategy, tmp_relation, target_relation, unique_key, dest_columns) %}
  {% if strategy == 'merge' %}
    {% do return(get_merge_sql(target_relation, tmp_relation, unique_key, dest_columns)) %}
  {% elif strategy == 'delete+insert' %}
    {% do return(get_delete_insert_merge_sql(target_relation, tmp_relation, unique_key, dest_columns)) %}
  {% else %}
    {% do exceptions.raise_compiler_error('invalid strategy: ' ~ strategy) %}
  {% endif %}
{% endmacro %}

{% materialization incremental_case_sensitive, adapter='snowflake' -%}

    {% set original_query_tag = set_query_tag() %}

    {%- set unique_key = config.get('unique_key') -%}
    {%- set full_refresh_mode = (should_full_refresh()) -%}

    {# Alter quoting rules before checking relation existence #}
    {% set quoting = config.get('quoting') %}
    {% do set_model_quoting(database=quoting['database'], schema=quoting['schema'], identifier=quoting['identifier']) %}

    {% set target_relation = this %}
    {% set existing_relation = load_relation(this) %}
    {% set tmp_relation = make_temp_relation(this) %}

    {#-- Validate early so we don't run SQL if the strategy is invalid --#}
    {% set strategy = dbt_snowflake_validate_get_incremental_strategy(config) -%}
    {# {% set on_schema_change = incremental_validate_on_schema_change(config.get('on_schema_change'), default='ignore') %} #}

    {{ run_hooks(pre_hooks) }}

    {% if existing_relation is none %}
        {% set build_sql = create_table_as(False, target_relation, sql) %}

    {% elif existing_relation.is_view %}
        {#-- Can't overwrite a view with a table - we must drop --#}
        {{ log("Dropping relation " ~ target_relation ~ " because it is a view and this model is a table.") }}
        {% do adapter.drop_relation(existing_relation) %}
        {% set build_sql = create_table_as(False, target_relation, sql) %}

    {% elif full_refresh_mode %}
        {% set build_sql = create_table_as(False, target_relation, sql) %}

    {% else %}
        {% do run_query(create_table_as(True, tmp_relation, sql)) %}
        {% do adapter.expand_target_column_types(
            from_relation=tmp_relation,
            to_relation=target_relation) %}
        {# {% do process_schema_changes(on_schema_change, tmp_relation, existing_relation) %} #}
        {% set dest_columns = adapter.get_columns_in_relation(existing_relation) %}
        {% set build_sql = dbt_snowflake_get_incremental_sql(strategy, tmp_relation, existing_relation, unique_key, dest_columns) %}

    {% endif %}

    {%- call statement('main') -%}
        {{ build_sql }}
    {%- endcall -%}

    {{ run_hooks(post_hooks) }}

    {% set target_relation = target_relation.incorporate(type='table') %}
    {% do persist_docs(target_relation, model) %}

    {% do restore_default_quoting() %}
    {% do unset_query_tag(original_query_tag) %}

    {{ return({'relations': [target_relation]}) }}

{%- endmaterialization %}
