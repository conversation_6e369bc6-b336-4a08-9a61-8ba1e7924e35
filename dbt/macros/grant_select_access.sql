{% macro grant_select_access(table_name, grant_role)  -%}

    {%- if snowflake_env() == 'DEV' -%}

        {% set qry_grant_select = 'GRANT SELECT ON TABLE TRANSFORMATION_DEV.PRESENTATION.' +  table_name + ' TO TRANSFORMATION_DEV_' +  grant_role %}

    {%- elif snowflake_env() == 'UAT' -%}

        {% set qry_grant_select = 'GRANT SELECT ON TABLE TRANSFORMATION_UAT.PRESENTATION.' +  table_name + ' TO TRANSFORMATION_UAT_' +  grant_role  %}

    {%- elif snowflake_env() == 'PROD' -%}

        {% set qry_grant_select = 'GRANT SELECT ON TABLE TRANSFORMATION_PROD.PRESENTATION.' +  table_name + ' TO TRANSFORMATION_PROD_' +  grant_role  %}
        
    {%- endif -%}

    {% set run_grant = run_query(qry_grant_select) %}

{%- endmacro %}