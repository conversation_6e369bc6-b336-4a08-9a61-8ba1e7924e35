{% macro get_missing_columns(from_schema, from_identifier, to_schema, to_identifier) %}
    {% set src_relation = api.Relation.create(schema=from_schema, identifier=from_identifier) %}
    {% set tar_relation = api.Relation.create(schema=to_schema, identifier=to_identifier) %}

    {% for col in adapter.get_missing_columns(src_relation, tar_relation) %}
        {% do log("col name: " ~ col, info=True) %}
        {% call statement('add_missing_column', fetch_result=True, auto_begin=False) -%}
            alter table "{{ tar_relation.schema }}"."{{ tar_relation.identifier }}" add column "{{col.name}}" {{col.data_type}};
        {%- endcall %}
        {%- set result = load_result('add_missing_column') -%}
        {{ log(result['data'][0][0], info=True)}}
    {% endfor %}

{%- endmacro %}
