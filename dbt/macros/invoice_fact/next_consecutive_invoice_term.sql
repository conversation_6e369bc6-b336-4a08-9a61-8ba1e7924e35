{% macro next_consecutive_invoice_term() -%}

CASE
    -- ignore the proration invoices
    WHEN "invoice_amount" < 0 THEN 'Proration Invoice'
    -- if the current invoice starts in the future
    WHEN "service_start_date" > CURRENT_DATE() THEN 'Term Start In Future'
    -- if the current invoice is still active
    WHEN "service_end_date" > CURRENT_DATE() THEN 'Invoice Term Still Active'
    -- next invoice has a start date in the future
    WHEN LEAD("temp_start_date") IGNORE NULLS OVER (PARTITION BY "dazn_user_id" ORDER BY "invoice_created_timestamp" ASC) > CURRENT_DATE() THEN 'Next Invoice Posted For Future Date'
    -- if there is no next start date (no next invoice)
    WHEN LEAD("temp_start_date") IGNORE NULLS OVER (PARTITION BY "dazn_user_id" ORDER BY "invoice_created_timestamp" ASC) IS NULL THEN 'No Next Invoice Posted'
    -- In normal circumstances the invoice end date will be one day before the next invoice's start date. Occasionally we have re-subs after churning in which case the start date can be more than 1 day before the previous invoice's end date.
    WHEN LEAD("temp_start_date") IGNORE NULLS OVER (PARTITION BY "dazn_user_id" ORDER BY "invoice_created_timestamp" ASC ) <= DATEADD(day, 1, "service_end_date") THEN 'Next Invoice Posted'
    -- We're looking at consecutive invoice terms. At sub level, if a new invoice is generated after the current one's end date then we assume it's for a new sub and we categorise this as no-next-invoice.
    WHEN LEAD("temp_start_date") IGNORE NULLS OVER (PARTITION BY "dazn_user_id" ORDER BY "invoice_created_timestamp" ASC ) != DATEADD(day, 1, "service_end_date") THEN 'No Next Invoice Posted'
    ELSE 'Unknown'
END

{%- endmacro %}
