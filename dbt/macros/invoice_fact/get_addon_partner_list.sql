-- The function itself returns the unique values found in a column of a table using the dbt_utils.get_column_values function.
-- You specify the table, column and a where condition (if you want) and the macro outputs the distinct values.
-- This macro specifically is returning the unique values the Partner ID takes in the invoice_items (effectively a list of addon partners)

{% macro get_addon_partner_list()  -%}

    {{ return(dbt_utils.get_column_values(table=ref('fct_invoice_items'), column='"rateplan_partner_id"', where='"rateplan_product_type" = \'addon\' and "rateplan_partner_id" is not null and "rateplan_partner_id" <>\'0\' ', default='This_is_Default')) }}

{%- endmacro %}
