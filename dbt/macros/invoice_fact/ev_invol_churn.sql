{% macro ev_invol_churn() -%}

-- todo description - need some description what is being checked here and why.
-- todo why do we need a macro for a single-use piece of logic?
-- Logic:
--  -
CASE
        -- For partners/3PP purchases and renewal we won't get payment details and so relying on subscription_type to find those
        WHEN
            "subscription_type" = 'Externally Billed'
        THEN 'No Payment Required'

        -- If invoice has a zero amount we don't need a payment for it. Customer can't churn
        WHEN
            "invoice_amount" = 0
        THEN 'No Payment Required'

        -- Anything that has no fails and at least one successful payment then this is a regular payment (invoice paid as expected)
        WHEN
            "invoice_amount" > 0
            AND "failed_payment_attempt_count" = 0
            AND "successful_payment_attempt_count" > 0
        THEN 'Regular Payment'

        -- If we have a successful and a failed payment we assume that the failed came first. And therefore they're saved from churning.
        WHEN
            "invoice_amount" > 0
            AND "failed_payment_attempt_count" > 0
            AND "successful_payment_attempt_count" > 0
        THEN 'Involuntary Save'

        -- Looking for an invoice that is a proration invoice next so we know the original invoice has been prorated, we also look for balance 0 on invoice so that we can decipher this invoice has been written off.
        -- example: https://performgroup.eu.looker.com/explore/product_beta/involuntary_churn_movements?qid=1nwRYgrRljzPuRUVfqC8bP&toggle=fil
        WHEN "invoice_amount" > 0
            AND "failed_payment_attempt_count" > 0
            AND "successful_payment_attempt_count" = 0
            AND "invoice_churn_type" = 'INVOLUNTARY'
        THEN 'Involuntary Churn'
--
--         -- These are the "next negative amount" invoices that we were looking for in the previous WHEN statement.
--         WHEN
--             "invoice_amount" < 0
--         THEN 'Proration Invoice'

        -- This covers mostly 3PPs. Sometimes we don't have payment information but we can see the invoice having been
        -- paid. When the payment_amount from the invoice source is equal to the invoice amount, we know it's a regular payment
        -- Make sure that this invoice has no adjustments nor refunds.
--         WHEN
--             "first_payment_attempt_timestamp" IS NOT NULL
--             AND ROUND("invoice_payment_amount",2) = ROUND("invoice_amount",2)
--             AND "invoice_adjustment_amount" = 0
--             AND "invoice_refund_amount" = 0
--         THEN 'Regular Payment'

        -- When there's been a failure and no successes, and the invoice isn't in a settled state (balance != 0), the invoice is still open
        WHEN
            "invoice_amount" > 0
            AND "failed_payment_attempt_count" > 0
            AND "successful_payment_attempt_count" = 0
        THEN 'Open Invoice'

--         -- If there's no payment. And the invoice amount is equal to the adjustment amount, then we consider this as written off.
--         -- This is possibly caused by CS intervention.
--         WHEN
--             "invoice_payment_amount" = 0
--             AND "is_written_off"  = 1
--             AND "invoice_amount" > 0
--         THEN 'Write Off'

        -- If any remaining invoices with a non-zero balance and amount >0 then it's still an open invoice.
        ELSE 'Unknown'

        END
{%- endmacro %}
