{% macro zr_invol_churn() -%}

-- todo description - need some description what is being checked here and why.
-- todo why do we need a macro for a single-use piece of logic?
-- Logic:
--  -
CASE
        -- If invoice has a zero amount we don't need a payment for it. Customer can't churn
        WHEN
            "invoice_amount" = 0
        THEN 'No Payment Required'

        -- Anything that has no fails and at least one successful payment then this is a regular payment (invoice paid as expected)
        WHEN
            "failed_payment_attempt_count" = 0
            AND "successful_payment_attempt_count" > 0
        THEN 'Regular Payment'

        -- If we have a successful and a failed payment we assume that the failed came first. And therefore they're saved from churning.
        WHEN
            "failed_payment_attempt_count" > 0
            AND "successful_payment_attempt_count" > 0
        THEN 'Involuntary Save'

        -- Looking for an invoice that is a proration invoice next so we know the original invoice has been prorated, we also look for balance 0 on invoice so that we can decipher this invoice has been written off.
        -- example: https://performgroup.eu.looker.com/explore/product_beta/involuntary_churn_movements?qid=1nwRYgrRljzPuRUVfqC8bP&toggle=fil
        WHEN
            -- When there are failed payments for this invoice...
            ("failed_payment_attempt_count" > 0)
            -- And there are no successful ones...
            AND "successful_payment_attempt_count" = 0
            -- If the balance is also zero, the invoice is in a set state. It can't change again. We no longer expect payments for this invoice.
            AND "invoice_balance" = 0
            -- And the next invoice has the same service end date as this one.
            AND "service_end_date" = LEAD("service_end_date") OVER (PARTITION BY "crm_account_id" ORDER BY "invoice_created_timestamp" ASC NULLS FIRST)
            -- And the next invoice has a negative amount
            AND LEAD("invoice_amount") OVER (PARTITION BY "crm_account_id" ORDER BY "invoice_created_timestamp" ASC NULLS FIRST) < 0
        THEN 'Involuntary Churn'

        -- These are the "next negative amount" invoices that we were looking for in the previous WHEN statement.
        WHEN
            "invoice_amount" < 0
        THEN 'Proration Invoice'

        -- This covers mostly 3PPs. Sometimes we don't have payment information but we can see the invoice having been
        -- paid. When the payment_amount from the invoice source is equal to the invoice amount, we know it's a regular payment
        -- Make sure that this invoice has no adjustments nor refunds.
        WHEN
            "first_payment_attempt_timestamp" IS NOT NULL
            AND ROUND("invoice_payment_amount",2) = ROUND("invoice_amount",2)
            AND "invoice_adjustment_amount" = 0
            AND "invoice_refund_amount" = 0
        THEN 'Regular Payment'

        -- When there's been a failure and no successes, and the invoice isn't in a settled state (balance != 0), the invoice is still open
        WHEN
            ("failed_payment_attempt_count" > 0)
            AND "successful_payment_attempt_count" = 0
            AND "invoice_balance" != 0
        THEN 'Open Invoice'

        -- If there's no payment. And the invoice amount is equal to the adjustment amount, then we consider this as written off.
        -- This is possibly caused by CS intervention.
        WHEN
            "invoice_payment_amount" = 0
            AND "is_written_off"  = 1
            AND "invoice_amount" > 0
        THEN 'Write Off'

        -- If any remaining invoices with a non-zero balance and amount >0 then it's still an open invoice.
        WHEN
            "invoice_balance" != 0
            AND "invoice_amount" >= 0 -- i think this needs to be updated to "invoice_amount" > 0
        THEN 'Open Invoice'

        ELSE 'Unknown'

        END
{%- endmacro %}
