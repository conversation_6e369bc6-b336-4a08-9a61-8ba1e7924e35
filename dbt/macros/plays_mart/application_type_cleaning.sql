-- Clean and standardize the ApplicationType column
{% macro application_type_cleaning(ApplicationType)  -%}
CASE
    WHEN "{{ ApplicationType }}" ILIKE 'tvos' THEN 'tvos'
    WHEN "{{ ApplicationType }}" ILIKE '%[DAZN 2.0] MERCURY BROWSER%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Web'
    WHEN "{{ ApplicationType }}" ILIKE '%skyq%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'SkyQ'
    WHEN "{{ ApplicationType }}" ILIKE '%MDK_SKY_Q' AND LENGTH("{{ ApplicationType }}")<50 THEN 'SkyQ'
    WHEN "{{ ApplicationType }}" ILIKE '%VIDAA%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Vidaa'
    WHEN "{{ ApplicationType }}" ILIKE '%panasonic%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Panasonic TV'
    WHEN "{{ ApplicationType }}" ILIKE '%magenta%' AND "{{ ApplicationType }}" ILIKE '%tv%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Magenta TV'
    WHEN "{{ ApplicationType }}" ILIKE '%virgin%' AND "{{ ApplicationType }}" ILIKE '%tv%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Virgin TV'
    WHEN "{{ ApplicationType }}" ILIKE '%fetch%' AND "{{ ApplicationType }}" ILIKE '%tv%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Fetch TV'
    WHEN "{{ ApplicationType }}" ILIKE '%fire%' AND "{{ ApplicationType }}" ILIKE '%tv%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Fire TV'
    WHEN "{{ ApplicationType }}" ILIKE '%Movistar%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Movistar'
    WHEN "{{ ApplicationType }}" ILIKE '%sagemcom%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Sagemcom'
    WHEN "{{ ApplicationType }}" ILIKE '%orange%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'orange'
    WHEN "{{ ApplicationType }}" ILIKE '%[DAZN 2.0] MERCURY XFINITY%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Xfinity'
    WHEN "{{ ApplicationType }}" ILIKE '%freebox%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Freebox'
    WHEN "{{ ApplicationType }}" = 'sky' THEN 'sky'
    WHEN "{{ ApplicationType }}" = 'philips' THEN 'philips'
    WHEN "{{ ApplicationType }}" = 'foxxum' THEN 'foxxum'
    WHEN "{{ ApplicationType }}" = 'tivo' THEN 'tivo'
    WHEN "{{ ApplicationType }}" = 'sunrise' THEN 'sunrise'
    WHEN "{{ ApplicationType }}" = 'vewd' THEN 'vewd'
    WHEN "{{ ApplicationType }}" = 'fetchtv' THEN 'fetchtv'
    WHEN "{{ ApplicationType }}" = 'firetv' THEN 'firetv'
    WHEN "{{ ApplicationType }}" = 'sfr' THEN 'sfr'
    WHEN "{{ ApplicationType }}" = 'telenet' THEN 'telenet'
    WHEN "{{ ApplicationType }}" = 'whaleos' THEN 'whaleos' 
    -- Above block added as per the mapping from mail "Application type values missing in Playback" -- dated 13 Nov
    WHEN "{{ ApplicationType }}" = 'smartcast' THEN 'Smartcast'
    WHEN "{{ ApplicationType }}" ILIKE '%tizen%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Tizen'
    WHEN "{{ ApplicationType }}" = 'xfinity' THEN 'Xfinity'
    WHEN "{{ ApplicationType }}" ILIKE '%webos%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Web OS'
    WHEN "{{ ApplicationType }}" ILIKE '%android' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Android' --might want to clarify if this is android mobile
    WHEN "{{ ApplicationType }}" ILIKE '%exoplayer%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Android TV'
    WHEN "{{ ApplicationType }}" ILIKE '%abstract%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Abstract Method'
    WHEN "{{ ApplicationType }}" ILIKE 'ios' AND LENGTH("{{ ApplicationType }}")<50 THEN 'IOS'
    WHEN "{{ ApplicationType }}" ILIKE '%skyq%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Sky Q'
    WHEN "{{ ApplicationType }}" ILIKE '%MDK_SKY_Q' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Sky Q'
    WHEN "{{ ApplicationType }}" ILIKE 'roku' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Roku TV'
    WHEN "{{ ApplicationType }}" = '[DAZN 2.0] SHAKA' THEN 'Web' --this is Chrome, Edge Chromium and Firefox browsers
    WHEN "{{ ApplicationType }}" = '[DAZN 2.0] SHAKA_WEDGE' THEN 'Web' --this is Edge legacy
    WHEN "{{ ApplicationType }}" = '[DAZN 2.0] HASPLAYER' THEN 'Web' --this is Internet Explorer
    WHEN "{{ ApplicationType }}" = '[DAZN 2.0] HTML5_FAIRPLAY' THEN 'Web' --this is safari browser
    WHEN "{{ ApplicationType }}" ILIKE 'web' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Web'
    WHEN "{{ ApplicationType }}" ILIKE 'Netcast' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Netcast'
    WHEN "{{ ApplicationType }}" ILIKE 'Orsay' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Orsay'
    WHEN "{{ ApplicationType }}" = 'operatv' THEN 'Opera TV'
    WHEN "{{ ApplicationType }}" ILIKE '%chromecast%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'chromecast'
    WHEN "{{ ApplicationType }}" ILIKE '%MDK_ANDROID_TV%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Android TV'
    WHEN "{{ ApplicationType }}" ILIKE '%Hisense%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Hisense TV'
    WHEN "{{ ApplicationType }}" ILIKE '%xboxone%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Xbox One'
    WHEN "{{ ApplicationType }}" ILIKE '%PS5%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'PlayStation 5'
    WHEN "{{ ApplicationType }}" = 'PlayStation+4' THEN 'PlayStation 4'
    WHEN "{{ ApplicationType }}" ILIKE '%PS4%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'PlayStation 4'
    WHEN "{{ ApplicationType }}" = 'PlayStation+3' THEN 'PlayStation 3'
    WHEN "{{ ApplicationType }}" ILIKE '%AppleTV%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Apple TV'
    WHEN "{{ ApplicationType }}" ILIKE '%Toshiba%' AND "{{ ApplicationType }}" ILIKE '%tv%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Toshiba TV'
    WHEN "{{ ApplicationType }}" ILIKE '%Panasonic%' AND "{{ ApplicationType }}" ILIKE '%tv%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Panasonic TV'
    WHEN "{{ ApplicationType }}" ILIKE '%SONY%' AND "{{ ApplicationType }}" ILIKE '%tv%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'SONY TV'
    WHEN "{{ ApplicationType }}" ILIKE '%android%' AND "{{ ApplicationType }}" ILIKE '%tv%' AND "{{ ApplicationType }}" != '%5BDAZN+2.0%5D+MDK_ANDROID_TV' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Android TV'
    WHEN "{{ ApplicationType }}" = 'hisensejp' THEN 'Hisensejp'
    WHEN "{{ ApplicationType }}" = 'sonyceb' THEN 'Sony CEB'
    WHEN "{{ ApplicationType }}" ILIKE '%MDK_WEBMAF%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'PS4'
    WHEN "{{ ApplicationType }}" = 'funai' THEN 'Funai'
    WHEN "{{ ApplicationType }}" ILIKE '%MAGENTA%' AND LENGTH("{{ ApplicationType }}")<50 THEN 'Magenta'
    ELSE 'Unknown'
END
{%- endmacro %}
