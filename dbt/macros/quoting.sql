{#
Update the adapter's quoting config for an object

Arguments:
    object: One of 'database', 'schema' or 'identifier'
    quote: <PERSON>ol of whether the object should be quoted
#}
{% macro alter_quoting(object, quote) %}
    {%- set quoting = {object: quote} -%}
    {%- do adapter.config.quoting.update(quoting) -%}
{% endmacro %}


{#
Override project quoting config for a model

Set database, schema & identifier quoting configuration at a model level. Designed to be used in
a custom case sensitive materialisation, where it will alter the adapters quoting policy before
the model is run. It must be used in conjunction with the restore_default_quoting macro, otherwise
the quoting policy change will effect quoting of other models.

Arguments:
    database: Bool of whether the database name should be quoted (optional)
    schema: Bool of whether the schema name should be quoted (optional)
    identifier: Bool of whether the schema name should be quoted (optional)

#}
{% macro set_model_quoting(database=none, schema=none, identifier=none) -%}

    {%- if database is not none -%}
        {{ alter_quoting('database', database) }}
        {{ log("Updated Database quoting config to " ~ database, info=false) }}
    {%- endif -%}

    {%- if schema is not none -%}
        {{ alter_quoting('schema', schema) }}
        {{ log("Updated Schema quoting config to " ~ schema, info=false) }}
    {%- endif-%}

    {%- if identifier is not none %}
        {{ alter_quoting('identifier', identifier) }}
        {{ log("Updated Identifier quoting config to " ~ identifier, info=false) }}
    {%- endif -%}

    {{ log("Quoting config for " ~ this ~ " is " ~ adapter.config.quoting, info=false) }}
{%- endmacro %}


{#
Restore quoting config back to project defaults

Fetches the project's default quoting config and updates the adapters quoting config with it.
Designed to be used in conjunction with set_model_quoting.
#}
{% macro restore_default_quoting() -%}

    {% set default_quoting = this.get_default_quote_policy() %}
    {{ alter_quoting('database', default_quoting.database ) }}
    {{ alter_quoting('schema', default_quoting.schema) }}
    {{ alter_quoting('identifier', default_quoting.identifier) }}

    {{ log("After " ~ this ~ " restored quoted config to " ~ adapter.config.quoting, info=false) }}
{%- endmacro %}
