-- Variables:
-- rebuild_days: Used for both the Sub Mart and the Content Attribution incremental build as they need to rebuild at the same rate
-- test_campaign_ids: list of campaign ids that are known tests

{% macro subscription_mart_variables() %}

{% set yml_str %}

test_campaign_ids: [
    'dazn x docomo shop staff trainning',
    'dazn x docomo staff training 2',
    'dazn x docomo sales staff training',
    'dazn x docomo sales staff 2022',
    'dazn x docomo retail staffs training account 2',
    'dazn x docomo_retail_demo',
    'dazn x docomo sports lounge 032018',
    'dazn x docomo staff training',
    'dazn x docomo partner',
    'test x region code us',
    'test x access accounts',
    'test x internal es',
    'test x internalbr',
    'dazn x internal global codes',
    'test x es employees',
    'dazn x employee codes',
    'test x internal it',
    'dazn x internal us',
    'dazn x internal ca',
    'test x employee eu2019',
    'dazn x internal dach',
    'dazn x internal jp',
    'test x load test',
    'test x de load test'
]

rebuild_days: 7

{% endset %}
{{ return(fromyaml(yml_str)) }}
{% endmacro %}
