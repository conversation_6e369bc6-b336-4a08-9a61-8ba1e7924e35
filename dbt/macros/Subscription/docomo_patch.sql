{% macro docomo_patch() %}

CASE
    WHEN "subscription_start_date" < '2020-10-01' THEN 'dazn x docomo legacy grandfathered patched'
    WHEN "subscription_start_date" BETWEEN '2020-10-01' AND '2021-08-23' THEN 'dazn x docomo grandfathered patched'
    WHEN "subscription_start_date" BETWEEN '2021-08-24' AND '2021-10-17' THEN 'dazn x docomo afc offer patched'
    WHEN "subscription_start_date" BETWEEN '2021-10-18' AND '2022-04-18' THEN 'dazn x docomo grandfathered patched'
    ELSE 'dazn x docomo fp patched'
END

{% endmacro %}
