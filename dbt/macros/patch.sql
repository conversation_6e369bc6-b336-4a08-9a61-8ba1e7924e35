{% macro patch(curated_table_name, patch_table_name) %}

    {{ log("This is the start", info=True) }}

    --easier to string bash and get_relation relies on dbt cash to know about the table
    --when the model is first run the object has not been created so dbt does not know about it.
    --{% if execute %}
        --{% set curated_table = adapter.get_relation(database=database, schema=schema, identifier=curated_table_name) %}
        --the table is not found so exit ToDo: warning!
        --{% if curated_table is none %}
        --    {{ log("Curated table not found, exiting", info=True) }}
        --   {{ return("Curated table not found, exiting") }}
        --{% elif patch_table is none %}
        --    {{ log("Patch table not found, exiting", info=True) }}
        --    {{ return("Patch table not found, exiting") }}
        --{% endif %}
    --{% endif %}

    {% set curated_table = '"' + database + '"."' + schema + '"."' + curated_table_name + '"' %}
    {% set patch_table = '"' + database + '"."PATCH"."' + patch_table_name + '"' %}

    --check for any patch rows to be applied, exit if none
    {% set qry_patch_count = 'select count(1) as cnt from ' +  patch_table + ' WHERE META_PATCH_APPLIED_DT is NULL;' %}

    {% set patch_count = run_query(qry_patch_count) %}

    {% if execute %}
        {% if patch_count.columns[0].values()[0] == 0 %}
            {{ log("No Patch rows so exiting", info=True) }}
            {{ return("") }}
        {% endif %}
    {% endif %}

    {% set columns = adapter.get_columns_in_relation(curated_table) %}

    {% set coalesce_list = [] %}  {# Temporary list #}

    {% for column in columns if column.column[:4] != 'EDM_' and column.column[:5] != 'META_' %}
        {% set abc = '"' + column.column + '" = COALESCE(a."' + column.column +'", ' + curated_table + '."' + column.column + '")' %}
        {{ coalesce_list.append(abc) }}
        {% if not loop.last %} {{ coalesce_list.append(',') }} {% endif %}
    {% endfor %}

    {% set qry_update_curated = 'UPDATE ' + curated_table + ' SET ' %}
    --hacking away as concat in loop not liked..
    {% set qry_update_curated = qry_update_curated +  coalesce_list|join %}
    {% set qry_update_curated = qry_update_curated + ' FROM ' + patch_table + ' a WHERE ' + curated_table + ".META_UUID = a.META_UUID and a.META_PATCH_APPLIED_DT is NULL and a.META_PATCH_TYPE = 'UPDATE';"%}

    {% set qry_update_Patch = 'UPDATE ' + patch_table + " SET META_PATCH_APPLIED_DT = CURRENT_TIMESTAMP() where META_PATCH_APPLIED_DT is NULL and META_PATCH_TYPE = 'UPDATE'
                                    AND EXISTS (SELECT 1 FROM " + curated_table + " a WHERE a.META_UUID = " + patch_table + ".META_UUID );" %}

    --first pass to update the curated table.
    {{ run_hooks(pre_hooks, inside_transaction=True) }}

        --apply patch to curated
        {% do run_query(qry_update_curated) %}
        --update patch tsble
        {% do run_query(qry_update_Patch) %}

    {{ adapter.commit() }}
    {{ run_hooks(post_hooks, inside_transaction=False) }}

    --second pass to mark off soft deletes.
    {% set qry_update_curated = 'UPDATE ' + curated_table + " SET META_SOFT_DELETED = 'Yes' where
                                EXISTS (SELECT 1 FROM " + patch_table + " a WHERE a.META_UUID = META_UUID and META_PATCH_TYPE = 'DELETE')" %}

    {% set qry_update_Patch = 'UPDATE ' + patch_table + " SET META_PATCH_APPLIED_DT = CURRENT_TIMESTAMP() where META_PATCH_APPLIED_DT is NULL and META_PATCH_TYPE = 'DELETE'
                                    AND EXISTS (SELECT 1 FROM " + curated_table + " a WHERE a.META_UUID = " + patch_table + ".META_UUID )" %}

    {{ run_hooks(pre_hooks, inside_transaction=True) }}

        --apply patch to curated
        {% do run_query(qry_update_curated) %}
        --update patch tsble
        {% do run_query(qry_update_Patch) %}

    {{ adapter.commit() }}
    {{ run_hooks(post_hooks, inside_transaction=False) }}

    {{ log("This is the end", info=True) }}
    --it seems that what ever the macro was returning was being executed as sql, so return nothing,,
    {{ return("") }}

{% endmacro %}
