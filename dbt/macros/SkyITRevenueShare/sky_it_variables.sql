{% macro sky_it_variables() %}

{% set yml_str %}

tracking_ids: {
    non_soip: [
        'de276c21-b9f9-4572-8aa5-acd422db40a2'
    ]
    ,soip: [
        '5746246d-fa5f-45dd-878a-ca19ec702cb0'
    ]
}

campaign_ids: [
    'dazn x sky november promo'
    ,'dazn x sky december promo'
]

user_agents: {
    non_soip: [
        'Mozilla/5.0 (X11; Linux armv7l) AppleWebKit/537.36 (KHTML, like Gecko) QtWebEngine/5.9.7 Chrome/56.0.2924.122 Safari/537.36 GW-Device/1.0.0 (Sky, ES160, )'
        ,'Mozilla/5.0 (X11; Linux armv7l) AppleWebKit/537.36 (KHTML, like Gecko) QtWebEngine/5.9.7 Chrome/56.0.2924.122 Safari/537.36 GW-Device/1.0.0 (<PERSON>, <PERSON>S340, )'
        ,'Mozilla/5.0 (X11; Linux armv7l) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) QtWebEngine/5.9.7 Chrome/56.0.2924.122 Safari/537.36 GW-Device/1.0.0 (Sky, ES240, )'
        ,'Mozilla/5.0 (X11; Linux armv7l) AppleWebKit/537.36 (KHTML, like Gecko) QtWebEngine/5.9.7 Chrome/56.0.2924.122 Safari/537.36 MR-Device/1.0.0 (Sky, EM150, )'
        ,'Mozilla/5.0 (Linux; x86_64 GNU/Linux) AppleWebKit/601.1 (KHTML, like Gecko) Version/8.0 Safari/601.1 WPE Sky_OTT_RTD1319_2020/1.0.0 (Sky, XiOneIT, Wired)'
    ]
    ,soip: [
        'Mozilla/5.0 (Linux; x86_64 GNU/Linux) AppleWebKit/601.1 (KHTML, like Gecko) Version/8.0 Safari/601.1 WPE Sky_TV_T962X3_2020/1.0.0 (Sky, LlamaIT, Wired)'
        ,'Mozilla/5.0 (Linux; x86_64 GNU/Linux) AppleWebKit/601.1 (KHTML, like Gecko) Version/8.0 Safari/601.1 WPE Sky_OTT_RTD1319_2020/1.0.0 (Sky, AlpacaIT, Wired)'
    ]
}

allowed_target_tiers: [
    'tier_gold_it'
]

sky_attribution_days_delta: 60

sky_deal_start_date: '2022-08-08'

allowed_target_tiers: [
    'tier_gold_it'
]

sky_attribution_days_delta: 60

sky_deal_start_date: '2022-08-08'

{% endset %}
{{ return(fromyaml(yml_str)) }}
{% endmacro %}
