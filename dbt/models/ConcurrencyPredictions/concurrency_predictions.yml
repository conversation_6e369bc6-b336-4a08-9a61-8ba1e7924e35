version: 2

models:
  - name: concurrency_predictions__viewership
    description: "Aggregate viewership counts at fixture, outlet and competition, sport level. Used for the concurrency prediction model"
    columns:
      - name: playback_stream_date
        description: "The date when the playback stream was initiated."
        quote: true

      - name: outlet
        description: "Name of the region to which the content is being delivered."
        quote: true

      - name: fixture_name
        description: "The name or description of the fixture associated with the content item."
        quote: true

      - name: fixture_id
        description: "Foreign key, references primary (surrogate) key values of the Device Info dimension."
        quote: true

      - name: content_category
        description: "Category assigned to this fixture based on sport, competition or fixture name"
        quote: true

      - name: viewership
        description: "Count of distinct viewers"
        quote: true


  - name: concurrency_predictions__fixtures
    description: "Won and MFL data at fixture and territory level."
    columns:
      - &fixture_id
        name: fixture_id
        description: "The fixture ID coming from WON"
        quote: true

      - &fixture_start_timestamp
        name: fixture_start_timestamp
        description: "Timestamp for the start of the fixture"
        quote: true

      - &fixture_start_date
        name: fixture_start_date
        description: "Date of the fixture"
        quote: true

      - &fixture_start_time
        name: fixture_start_time
        description: "Time of the start of the fixture"
        quote: true

      - &sport_name
        name: sport_name
        description: "sport name of the fixture id"
        quote: true

      - &competition_name
        name: competition_name
        description: "competition name of the fixture id"
        quote: true

      - &live_event_workflow_type
        name: live_event_workflow_type
        description: ""
        quote: true

      - &live_event_title
        name: live_event_title
        description: "The fixture name"
        quote: true

      - &live_event_type_name
        name: live_event_type_name
        description: ""
        quote: true

      - &live_event_broadcast_tier
        name: live_event_broadcast_tier
        description: ""
        quote: true

      - &live_event_start_timestamp
        name: live_event_start_timestamp
        description: ""
        quote: true

      - &region_name
        name: region_name
        description: ""
        quote: true

      - &territory
        name: territory
        description: "The region name but InitCapped and processed"
        quote: true

      - &live_product_support_tier
        name: live_product_support_tier
        description: ""
        quote: true

      - &live_product_ruleset_name
        name: live_product_ruleset_name
        description: ""
        quote: true

      - &live_right_allowed_country_codes
        name: live_right_allowed_country_codes
        description: ""
        quote: true

      - &live_right_title
        name: live_right_title
        description: ""
        quote: true


  - name: concurrency_predictions
    description: "View to prepare the data for the concurrency predicitons output - the next step will only output what's needed"
    columns:
      - *fixture_id
      - *fixture_start_timestamp
      - *fixture_start_date
      - *fixture_start_time
      - *sport_name
      - *competition_name
      - *live_event_workflow_type
      - *live_event_title
      - *live_event_type_name
      - *live_event_broadcast_tier
      - *live_event_start_timestamp
      - *region_name
      - *territory
      - *live_product_support_tier
      - *live_product_ruleset_name
      - *live_right_allowed_country_codes
      - *live_right_title
      - name: type
        description: "The type of competition that should match the type found in the SEED file"
        quote: true

      - name: estimate
        description: "An estimate for the viewership derived from average viewership metrics from plays. By sport and competition"
        quote: true

      - name: ds_prediction
        description: "The maximum concurrency prediction value from the data science model"
        quote: true

      - name: ops_prediction
        description: "The maximum concurrency prediction value uploaded by the OPS team"
        quote: true

      - name: prediction
        description: "The ops prediction if available otherwise it defaults to the DS prediction"
        quote: true

      - name: ops_prediction_upload_timestamp
        description: "The timestamp of the upload by OPS for this prediction"
        quote: true

      - name: fixed_overrides
        description: "A ratio to multiply the prediction by based on hardcoded fixture combinations"
        quote: true

      - name: ys_prediction
        description: "The prediction specific to YS fixtures - based off a seed file in the presentation layer."
        quote: true

      - name: ys_tizen_multiplier
        description: "tizen multiplier for the YS fixtures - based off a seed file in the presentation layer."
        quote: true

      - name: ys_web_multiplier
        description: "web multiplier for the YS fixtures - based off a seed file in the presentation layer."
        quote: true
