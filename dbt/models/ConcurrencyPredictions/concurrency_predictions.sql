{{
    config(
        materialized='view',
        schema='PREDICTIONS',
        database='CONCURRENCY_PREDICTIONS__B2C__' + snowflake_env(),
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['presentation-concurrency-predictions']
    )
}}

/*
    This view will use the fixture data from won.as it's base.
    It will then join onto it the playback viewership data, averaging it at sport and competition level.
    It will also join the predictions coming from data science
    Finally it will join the predictions uploaded by OPS.

    This has to be a view as the predictions from OPS could change at any time.
*/

WITH won AS (
    select * from {{ ref('concurrency_predictions__fixtures') }}
)

, sport_viewership AS (
    SELECT * FROM {{ ref('concurrency_predictions__viewership') }}
)

, ds_source AS (
    SELECT * FROM {{ source('DS','fvp_final_predictions') }}
)

, ops_source AS (
    SELECT * FROM {{ source('CONCURRENCY_PREDICTIONS','OPS_PREDICTION_UPLOADS') }}
)

-- get the MCurve ratios from the seed file.
, concurrency_seed_file AS (
    SELECT * FROM {{ ref('seed__concurrency_predictions__sport_scaling') }}
)

, ys_ratios AS (
    SELECT * FROM {{ ref('seed__concurrency_predictions__ys_ratios') }}
)

-- get the predictions provided by data science
, ds_predictions AS (
    SELECT
        "fixture_id",
        "territory",
        CASE
            WHEN "viewership_count_predicted" > 999 THEN ROUND("viewership_count_predicted",-2)::INT
            ELSE ROUND("viewership_count_predicted",-1)::INT
            END AS "rounded_viewership_prediction"
    FROM ds_source
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "fixture_id", "territory" ORDER BY "model_prediction_date" DESC, TO_DATE("training_data_start_date") DESC, TO_DATE("model_training_date") DESC) = 1
)

-- get the predictions provided by ops
, latest_ops_predictions AS (
    SELECT * FROM ops_source
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "fixture_id", "territory" ORDER BY "upload_timestamp" DESC) = 1
)

-- get playback averages calculated with the concurrency_predictions__viewership model.
, playback_averages AS (
    SELECT
        "outlet" AS "territory"
        ,"content_category"
        ,AVG("viewership") AS "average_viewership"
    FROM sport_viewership
    GROUP BY 1,2
)

SELECT
    won.*
    -- this case when helps to identify types of content - it is used to join to the sport_scaling seed when we calculate MCurves.
    ,CASE
        WHEN won."sport_name" = 'Motorsport' AND won."live_event_title" iLIKE '%Conference%' THEN 'Press Conference'
        WHEN won."live_event_title" = 'Before The Bell' THEN 'Before the Bell'
        WHEN won."live_event_title" ILIKE '%Yabecchi Stadium%' THEN 'Yabecchi Stadium'
        WHEN won."live_event_title" = 'Bundesliga Highlights' THEN 'Bundesliga Highlights'
        WHEN won."sport_name" = 'Soccer' AND won."live_event_title" iLIKE '%Highlights Show%' THEN 'Highlights Show'
        WHEN won."sport_name" = 'Soccer' AND won."live_event_title" iLIKE '%Zona Gol Serie A%' THEN 'Zona Gol Serie A'
        WHEN won."competition_name" ILIKE '%MotoGP%' AND won."live_event_title" iLIKE '%Qualifying%' THEN 'MotoGP-Qualifying'
        WHEN won."competition_name" ILIKE '%MotoGP%' AND won."live_event_title" iLIKE '%Races All Classes%' THEN 'MotoGP-Race'
        WHEN won."competition_name" ILIKE '%F1%' AND won."live_event_title" iLIKE '%Race%' THEN 'F1-Race'
        WHEN won."competition_name" ILIKE '%F1%' AND won."live_event_title" iLIKE '%Quali%' THEN 'F1-Qualifying'
        WHEN won."sport_name" = 'American Football' AND won."live_event_title" iLIKE '%redzone%' THEN 'Redzone'
        WHEN won."sport_name" = 'American Football' AND won."live_event_title" iLIKE '%red zone%' THEN 'Redzone'
        WHEN won."competition_name" = 'Ultimate Fighting Championship' THEN 'UFC-Normal'
        WHEN won."fixture_start_timestamp"::time <= '18:30' AND won."live_event_title" ILIKE '%UEFA Champions League German Teams Conference%' THEN 'UEFA-CL-German-Conference-Early'
        WHEN won."fixture_start_timestamp"::time > '18:30' AND won."live_event_title" ILIKE '%UEFA Champions League German Teams Conference%' THEN 'UEFA-CL-German-Conference-Late'
        WHEN won."fixture_start_timestamp"::time <= '18:30' AND won."live_event_title" ILIKE '%UEFA Champions League Conference%' THEN 'UEFA-CL-Conference-Early'
        WHEN won."fixture_start_timestamp"::time > '18:30' AND won."live_event_title" ILIKE '%UEFA Champions League Conference%' THEN 'UEFA-CL-Conference-Late'
        ELSE 'Normal'
     END AS "type"
     -- Estimate field derived from average viewership over time.
    ,COALESCE(avg_fixture."average_viewership", avg_comp."average_viewership", avg_sport."average_viewership") AS "estimate"
    -- data science prediction
    ,CASE
        WHEN won."territory" = 'Canada' AND won."live_event_workflow_type" ILIKE '%Multiple Language%' THEN (ds."rounded_viewership_prediction"::INT * 0.1)::INT
        WHEN won."territory" = 'Brazil' AND ds."rounded_viewership_prediction" <= 0 THEN 400
        ELSE ds."rounded_viewership_prediction"::integer
    END AS "ds_prediction"
    -- ops prediction, uploaded by ops via the OPS_PREDICTION_UPLOADS source.
    ,ops."ops_prediction"

    /*
        TODO
        this below is temporary logic to ensure the ops guys have time to download predictions with the fixture/sport/compettion
        level overrides while we work on a better solution involving seed files.

        See this ticket for details: https://livesport.atlassian.net/browse/EB-1937
    */

    ,CASE
        WHEN won."territory" = 'Italy'
            AND SPLIT_PART(won."live_event_title", ' v ',1) LIKE ANY ('%Milan%','%Juventus%','%Inter%','%Napoli%')
            AND SPLIT_PART(won."live_event_title", ' v ',2) LIKE ANY ('%Milan%','%Juventus%','%Inter%','%Napoli%')
            THEN 0.95
        WHEN won."territory" = 'Italy'
            AND SPLIT_PART(won."live_event_title", ' v ',1) LIKE ANY ('%Milan%','%Juventus%','%Roma%','%Inter%','%Lazio%','%Napoli%')
            AND SPLIT_PART(won."live_event_title", ' v ',2) LIKE ANY ('%Milan%','%Juventus%','%Roma%','%Inter%','%Lazio%','%Napoli%')
            THEN 0.9
        WHEN won."territory" = 'Spain'
            AND won."competition_name" = 'Primera División'
            AND SPLIT_PART(won."live_event_title", ' v ',1) LIKE ANY ('%Barcelona%','%Madrid%')
            AND SPLIT_PART(won."live_event_title", ' v ',2) LIKE ANY ('%Barcelona%','%Madrid%')
            THEN 0.95
        WHEN won."territory" = 'Spain'
            AND won."competition_name" = 'Primera División'
            AND SPLIT_PART(won."live_event_title", ' v ',1) LIKE ANY ('%Barcelona%','%Madrid%','%Villarreal%','%Real Betis%','%Real Sociedad%')
            AND SPLIT_PART(won."live_event_title", ' v ',2) LIKE ANY ('%Barcelona%','%Madrid%','%Villarreal%','%Real Betis%','%Real Sociedad%')
            THEN 0.9
        WHEN won."territory" = 'DACH'
            AND won."competition_name" = 'Bundesliga'
            AND SPLIT_PART(won."live_event_title", ' v ',1) LIKE ANY ('%Bayern M%','%Borussia Dortmund%','%Union Berlin%')
            AND SPLIT_PART(won."live_event_title", ' v ',2) LIKE ANY ('%Bayern M%','%Borussia Dortmund%','%Union Berlin%')
            THEN 0.95
        WHEN won."territory" = 'DACH'
            AND won."competition_name" = 'Bundesliga'
            AND SPLIT_PART(won."live_event_title", ' v ',1) LIKE ANY ('%Bayern M%','%Borussia Dortmund%','%Union Berlin%','%Freiburg%','%Leipzig%','%Bayer Lev%')
            AND SPLIT_PART(won."live_event_title", ' v ',2) LIKE ANY ('%Bayern M%','%Borussia Dortmund%','%Union Berlin%','%Freiburg%','%Leipzig%','%Bayer Lev%')
            THEN 0.8
        WHEN won."territory" = 'Italy'
            AND won."fixture_id" = '5acp8ky1a0w05a6x30uhfoopg'
            THEN 0.95
        WHEN won."territory" = 'Italy'
            AND SPLIT_PART(won."live_event_title", ' v ',1) LIKE ANY ('%Napoli%','%Atalanta%','%Lazio%')
            AND SPLIT_PART(won."live_event_title", ' v ',2) LIKE ANY ('%Napoli%','%Atalanta%','%Lazio%')
            THEN 0.8
        --big team's other games
        WHEN won."territory" = 'Spain' AND won."competition_name" = 'Primera División' AND won."live_event_title" LIKE ANY ('%Barcelona%','%Madrid%') THEN 0.85
        WHEN won."competition_name" = 'Bundesliga' AND won."live_event_title" LIKE ANY ('%Bayern M%','%%Borussia Dortmund%') THEN 0.85
        WHEN won."competition_name" = 'Serie A' AND won."live_event_title" LIKE ANY ('%Milan%','%Juventus%','%Roma%','%Inter%','%Napoli%','%Lazio%') THEN .85
        WHEN won."territory" = 'Italy' AND  won."competition_name" = 'Serie A' AND won."live_event_title" LIKE ANY ('%Atalanta%') THEN 0.75
        --important properties in each territory
        WHEN won."competition_name" IN ('FIM MotoGP World Championship') THEN 0.85
        WHEN won."competition_name" IN ('Bundesliga','NFL','FIA F1 World Championship','NBA','Primera División') THEN 0.75
        WHEN won."competition_name" IN ('UEFA Champions League') THEN 0.95
        WHEN won."sport_name" IN ('Boxing') THEN 0.85
        WHEN won."sport_name" IN ('Mixed Martial Arts') THEN 0.75
        WHEN won."territory" = 'Brazil' AND won."sport_name" = 'Soccer' THEN 0.75
        --other predictions
        WHEN won."live_event_broadcast_tier"::integer < 4 THEN 0.4
        ELSE 0.6
    END AS "fixed_overrides"
    -- we take the ops prediction if there is one, otherwise we take the data science one after scaling it with the fixed_override values.
    ,ops."upload_timestamp" AS "ops_prediction_upload_timestamp"
    -- This piece of logic is used by ops to calculate concurrency specifically for YS events.
    ,CASE WHEN REPLACE(won."live_event_type_name",' ','') ILIKE ANY ('%DAI50%', '%DAI59%', '%DCI50%','%DCI 50%') OR (won."live_event_type_name" = 'TX50 1080p' AND "competition_name"='Serie A')  THEN ys_ratios."tizen_audience"*ys_ratios."tizen_device_ratio" END AS "ys_tizen_multiplier"
    ,CASE WHEN REPLACE(won."live_event_type_name",' ','') ILIKE ANY ('%DAI50%', '%DAI59%', '%DCI50%','%DCI 50%') OR (won."live_event_type_name" = 'TX50 1080p' AND "competition_name"='Serie A') THEN ys_ratios."web_audience"*ys_ratios."web_device_ratio" END AS "ys_web_multiplier"
FROM won
-- add the ds predictions in
LEFT JOIN ds_predictions ds
    ON ds."fixture_id" = won."fixture_id"
    AND ds."territory" = won."territory"
-- add the playback averages, try to match on sport_name.
LEFT JOIN playback_averages avg_sport
    ON  LOWER(avg_sport."territory") = LOWER(won."territory")
    AND avg_sport."content_category" = won."sport_name"
-- add the playback averages, try to match on competition name
LEFT JOIN playback_averages avg_comp
    ON  LOWER(avg_comp."territory") = LOWER(won."territory")
    AND avg_comp."content_category" = won."competition_name"
-- add the playback averages, try to match on fixture name
LEFT JOIN playback_averages avg_fixture
    ON  LOWER(avg_fixture."territory") = LOWER(won."territory")
    AND avg_fixture."content_category" = won."live_event_title"
-- add the uploaded ops predictions
LEFT JOIN latest_ops_predictions ops
    ON ops."fixture_id" = won."fixture_id"
    AND ops."territory" = won."territory"
LEFT JOIN ys_ratios
    ON ys_ratios."territory" = won."territory"
-- filter out anything that does not show up in the concurrency ratio seed file.
WHERE won."sport_name" IN (SELECT DISTINCT "sport_name" FROM concurrency_seed_file)
-- Remove boxing
AND NOT (nvl(won."sport_name",'empty') = 'Boxing' AND nvl(won."live_event_title",'empty') ILIKE ANY ('%Weigh-In%','%Press Conference%'))
ORDER BY won."fixture_id",won."territory",won."sport_name",won."competition_name",won."live_event_title"
