{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"playback_stream_date"',
        schema='PREDICTIONS',
        database='CONCURRENCY_PREDICTIONS__B2C__' + snowflake_env(),
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['presentation-concurrency-predictions']
)
}}

/*
This model is part of the concurrency prediction product. Most of the code sits in the streamlit repo.
Here we calculate incrementally the total viewership since 2022 per fixture. This is the averaged out in a later step at
competition level.
*/

WITH playback_stream_fact AS (
    SELECT * FROM {{ ref_env('MART__PLAYBACK_STREAM__FACT') }}
)

,content AS (
    SELECT * FROM {{ ref_env('MART__CONTENT_ITEM__DIM') }}
)

, concurrency_seed_file AS (
    SELECT * FROM {{ ref('seed__concurrency_predictions__sport_scaling') }}
)

SELECT
    playback_stream_fact."playback_stream_date"
    ,content."outlet"
    ,content."fixture_name"
    ,content."fixture_id"
    ,CASE
        WHEN (content."fixture_name" IN ('Weigh-In','Press Conference','Before The Bell') AND content."sport_name" = 'Boxing') THEN content."fixture_name"
        WHEN content."sport_name" IN ('Golf','Pool','Tennis','Snooker','Darts') THEN content."sport_name"
        ELSE content."competition_name"
        END AS "content_category"
    ,COUNT(DISTINCT playback_stream_fact."viewer_id") AS "viewership"
FROM playback_stream_fact
LEFT JOIN content
    ON content."content_item__skey" = playback_stream_fact."content_item__skey"
WHERE 1=1
    AND content."article_type" = 'Live'
    AND playback_stream_fact."playback_duration_milliseconds" > 0
    -- only select for some competitions
    AND content."sport_name" IN (SELECT DISTINCT "sport_name" FROM concurrency_seed_file)
    {% if is_incremental() %}
    -- Because we have streams crossing midnight, we won't catch these until 2 days after the batch date.
    -- Therefore, on an incremental we're going to look back for two days worth of data.
    -- Note that data from yesterday will not be fully complete until tomorrow.
    AND DATE_TRUNC(DAY, playback_stream_fact."playback_stream_date") BETWEEN DATEADD(DAY, -1, TO_DATE('{{ var('batch_date') }}', 'YYYY-MM-DD')) AND '{{ var('batch_date') }}'
    {% endif %}
    AND DATE_TRUNC(DAY, playback_stream_fact."playback_stream_date") >= '2022-01-01'
GROUP BY 1,2,3,4,5
