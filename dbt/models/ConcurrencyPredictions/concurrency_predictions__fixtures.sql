{{
    config(
        materialized='table',
        schema='PREDICTIONS',
        database='CONCURRENCY_PREDICTIONS__B2C__' + snowflake_env(),
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['presentation-concurrency-predictions']
    )
}}

/*
    The purpose of this table is to create a fixture/territory level dataset with information coming from MFL and WON.

    - Collect all fixtures from WON for the next 3 weeks.
        - filter out any non-live content.
        - filter out any TEST / Backup / Empty content.
    - Merge with MFL data for sport and competition information
    - Merge with other WON dimensions for region and rights information
    - Artificially create a new row for UK & Ireland fixtures out of the Global territories.
*/

-- Grab fields from MFL and WON data
WITH won_enriched AS (
    SELECT
        mfl."fixture_id"
        ,mfl."fixture_start_timestamp"
        ,mfl."fixture_start_timestamp"::DATE AS "fixture_start_date"
        ,TO_CHAR(mfl."fixture_start_timestamp",'HH24:Mi:SS') AS "fixture_start_time"
        ,mfl."sport_name"
        ,mfl."competition_name"
        ,live_event."live_event_workflow_type"
        ,live_event."live_event_title"
        ,live_event."live_event_type_name"
        ,live_event."live_event_start_timestamp"
        ,CASE
            WHEN live_event."region_name" = 'dach' THEN 'DACH'
            WHEN live_event."region_name" = 'usa' THEN 'United States'
            WHEN live_event."region_name" = 'row' THEN 'Global'
            ELSE initcap(live_event."region_name")
            END AS "region_name"
        -- I believe this is replacing broadcasts that begin with live-italy-1 and extracts the 1 so everything is an INT.
        ,CASE
            WHEN live_event."live_event_broadcast_tier" NOT LIKE '%Live%' THEN live_event."live_event_broadcast_tier"
            ELSE SUBSTR(live_event."live_event_broadcast_tier", POSITION("region_name",live_event."live_event_broadcast_tier") + 7 + LEN("region_name"), 1)
        END AS "live_event_broadcast_tier"
        ,live_event."live_product_support_tier"
        ,live_event."live_product_ruleset_name"
        ,live_event."live_right_allowed_country_codes"
        ,live_event."live_right_title"
    FROM {{ ref('won_liveevent__dim') }} live_event
    LEFT JOIN {{ ref('mfl_fixture__dim') }} mfl
        ON mfl."fixture_id" = live_event."fixture_id"
    WHERE live_event."live_event_start_timestamp"::DATE BETWEEN DATEADD('day',-3,DATEADD('week',1,DATE_TRUNC('week',CURRENT_DATE))) AND DATEADD('week',3,DATE_TRUNC('week',CURRENT_DATE))
        AND live_event."live_event_type" = 'LIVE'
        AND nvl(live_event."live_event_status",'empty') != 'Not Selected%'
        AND NOT (nvl(live_event."live_event_workflow_type",'empty') ILIKE ANY ('%TEST%','%Back Up%','B2B Only'))
    QUALIFY ROW_NUMBER() OVER (PARTITION BY mfl."fixture_id", live_event."region_name" ORDER BY live_event."live_event_id" DESC) = 1
)

-- We're manually adding UK as it's own fixture when it's part of a collection of row/global fixtures.
-- From everything else we're removing the boxing extras.
, add_uk_and_ireland AS (
    SELECT
        *
        ,"region_name" AS "territory"
    FROM won_enriched
    WHERE NOT (nvl("sport_name",'empty') = 'Boxing' AND nvl("live_event_title",'empty') ILIKE ANY ('%Weigh-In%','%Press Conference%'))

    UNION

    SELECT
        *
        ,'UK and Ireland' AS "territory"
    FROM won_enriched
    WHERE "region_name" = 'Global'
        AND "live_right_allowed_country_codes" LIKE '%GB%'
)

select
        "fixture_id"
        ,"fixture_start_timestamp"
        ,"fixture_start_date"
        ,"fixture_start_time"
        ,"sport_name"
        ,"competition_name"
        ,"live_event_workflow_type"
        ,"live_event_title"
        ,"live_event_type_name"
        ,"live_event_broadcast_tier"
        ,"live_event_start_timestamp"
        ,"region_name"
        ,"territory"
        ,"live_product_support_tier"
        ,"live_product_ruleset_name"
        ,"live_right_allowed_country_codes"
        ,"live_right_title"
from add_uk_and_ireland
