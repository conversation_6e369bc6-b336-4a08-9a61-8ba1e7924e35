version: 2

models:
  - name: currency__daily_usd_fx_rates
    description: "Dimension used for mapping daily exchange rates to USD for all available currencies"
    columns:
      - name: full_date
        description: "The date of the conversion to USD"
        quote: true

      - name: currency_from
        description: "The currency before the conversion to USD"
        quote: true

      - name: currency_to
        description: "The currency after the conversion"
        quote: true

      - name: exchange_rate
        description: "The exchange rate to USD"
        quote: true

  - name: currency__daily_usd_fx_rate__dim
    description: "Dimension used for mapping daily exchange rates to USD for all available currencies"
    columns:
      - name: date
        description: "The date of the conversion to USD"
        quote: true

      - name: currency
        description: "The currency before the conversion to USD"
        quote: true

      - name: fx_rate
        description: "The exchange rate to USD"
        quote: true

      - name: daily_usd_fx_rate__skey
        description: "Hashed reference to all rows of this table"
        quote: true
        tests:
          - unique:
              config:
                severity: error
