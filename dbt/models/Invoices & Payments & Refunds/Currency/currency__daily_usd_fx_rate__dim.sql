{{
    config(
        materialized='table',
        database='MART_DIMENSIONS__B2C__DOMAIN__' + snowflake_env(),
        schema='GLOBAL',
        tags=['presentation-subscription-domain']
    )
}}

WITH usd_fx_source AS (
    SELECT 
        "full_date" as "date"
        ,"currency_from" AS "currency"
        ,"exchange_rate" AS "fx_rate"
    FROM {{ ref('currency__daily_usd_fx_rates') }}
)

,date_source AS (
    SELECT "date_day" AS "full_date" FROM {{ ref('dim_date') }}
    WHERE "full_date" <= CURRENT_DATE()
)

-- We are handling the conversion USD -> USD to avoid NULLs because currency_from = USD doesn't exist in source.
,usd_handler AS (
    SELECT
        date_source.*
        ,'USD' AS "currency"
        ,1 AS "fx_rate"
    FROM date_source
)

,union_all AS (
    SELECT *
    FROM usd_fx_source
    UNION ALL
    SELECT *
    FROM usd_handler
)

SELECT
    *
    ,HASH(
        "date"
        ,"currency"
    ) AS "daily_usd_fx_rate__skey"
FROM union_all
