{{
    config(
        materialized='table',
        tags=['presentation-invoice-items']
    )
}}

WITH usd_fx_source AS (
    SELECT * FROM {{ ref('staging__fxloader__exchange_rates') }}
    WHERE "currency_to" = 'USD'
)

,date_source AS (
    SELECT "date_day" AS "full_date" FROM {{ ref('dim_date') }}
    WHERE "full_date" <= CURRENT_DATE()
)

-- This gives me a list of currencies to join to date dimension in order to create a table that has one row per currency per day
,currencies AS (
    SELECT
        "currency_from"
        ,"currency_to"
    FROM usd_fx_source
    GROUP BY
        "currency_from"
        ,"currency_to"
)

-- The cross join creates this full table
-- Each row will have date,currency_from,currency_to
,joined_dates AS (
    SELECT
        date_source.*
        ,currencies.*
    FROM date_source
    CROSS JOIN currencies
)

-- Each row will have date,currency_from,currency_to, provider_exchange_rate_date, exchange_rate
-- We now re-join back to the original table in order to fill in the information that we already have without needing to enrich
,enriched_fx_dates AS (
    SELECT
        joined_dates.*
        ,usd_fx_source."exchange_rate"
    FROM joined_dates
    LEFT JOIN usd_fx_source ON
        joined_dates."currency_from" = usd_fx_source."currency_from"
        AND joined_dates."full_date" = usd_fx_source."provider_exchange_rate_date"
)

-- We can then create a partition that allows us to fill in each gap with the value that was previously before it in the chain
-- basically we will have an increment of 1 each time we have a new value in the exchange rate.
-- if we have a sequence of non-nulls each row will have a different value and we're okay.
-- if we have an exchange rate followed by nones, the nones are assigned a number equal to that of the previous non-null value. a new non-null value will have a new value_partition value.
,value_partition AS (
    SELECT
        *
        ,SUM(CASE WHEN "exchange_rate" IS NULL THEN 0 ELSE 1 END) OVER (PARTITION BY "currency_from" ORDER BY "full_date") AS "value_partition"
    FROM enriched_fx_dates
)

-- scan the value_partition and fill in the nulls with the first value found for that group.
SELECT
    "full_date"
    ,"currency_from"
    ,"currency_to"
    ,FIRST_VALUE("exchange_rate") OVER (PARTITION BY "value_partition", "currency_from" ORDER BY "full_date") AS "exchange_rate"
FROM value_partition
