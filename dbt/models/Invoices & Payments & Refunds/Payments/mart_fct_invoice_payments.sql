{{
    config(
        materialized='view',
        database='INVOICE__B2C__MART__' + snowflake_env(),
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        schema='INTERMEDIATE',
        alias='fct_invoice_payments',
        tags=['presentation-invoice-items']
    )
}}

WITH fct_inv_pay AS (
    SELECT * FROM {{ ref('fct_invoice_payments') }}
)

SELECT
    "META__DBT_INSERT_DTTS"
    ,"invoice_payment_id"
    ,"invoice_id"
    ,"payment_id"
    ,"dazn_user_id"
    ,"billing_account_id"
    ,"payment_number"
    ,"payment_reference_id"
    ,"payment_method_id"
    ,"payment_amount"
    ,"payment_currency"
    ,"payment_source"
    ,"payment_source_name"
    ,"payment_type" AS "payment_source_type"
    ,"invoice_source"
    ,CASE
        WHEN "payment_source_of_data" = 'zr' AND "payment_source"= 'API' AND "payment_created_by_id" = '8a1291128180c1a3018181952468394b' THEN 'Paywall'
        WHEN "payment_source_of_data" = 'ev' AND "payment_source"= 'API' AND "invoice_source" = 'BillRun' THEN 'Paywall'
        ELSE 'CPR'
    END AS "payment_type"
    ,"payment_status"
    ,"payment_effective_date"
    ,"payment_created_date"
    ,"payment_created_timestamp"
    ,"payment_updated_timestamp"
    ,"payment_refund_amount"
    ,"payment_cancelled_on"
    ,"payment_gateway"
    ,"payment_gateway_response" AS "payment_gateway_response_code"
    ,"payment_gateway_state"
    ,"payment_amount_usd"
    ,"payment_refund_amount_usd"
    ,TO_VARCHAR(Get(t.Value, 'subscription_name') ) AS "subscription_name"
    ,TO_VARCHAR(Get(t.Value, 'subscription_id')) AS "subscription_id"
    ,TO_VARCHAR(Get(t.Value, 'invoice_item_charge_name') )  AS "invoice_item_charge_name"
FROM fct_inv_pay,
TABLE (flatten("subscription_details")) t
