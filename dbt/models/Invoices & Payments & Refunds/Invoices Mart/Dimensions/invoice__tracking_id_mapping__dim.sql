{{
    config(
        materialized='view',
        database='INVOICE__B2C__MART__' + snowflake_env(),
        schema='DIM',
        alias='INVOICE_TRACKING_ID_MAPPING__DIM',
        tags=['presentation-invoice-fact']
    )
}}
 
SELECT 
        "subscription_tracking_id__skey" AS "invoice_tracking_id__skey"
        ,"tracking_id_type" 
        ,"tracking_id_detailed_type" 
        ,"tracking_id_partner_name" 
        ,"tracking_id_campaign_name" 
FROM {{ ref('subscription_tracking_id_mapping__dim') }}
