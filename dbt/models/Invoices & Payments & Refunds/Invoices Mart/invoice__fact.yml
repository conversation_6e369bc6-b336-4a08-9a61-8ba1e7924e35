version: 2

models:
  - name: invoice__fact
    description: "Shows the latest state of all invoices in zuora, with information on payment attempts, invoice items and adjustments, as well as USD conversions on all invoice values. Used to track involuntary churn, payment performance and revenues."
    columns:
      - name: invoice_id
        description: "Id of the Invoice"
        quote: true

      - name: billing_account_id
        description: "<PERSON><PERSON>ra Billing Account Id for the customer"
        quote: true

      - name: subscription_id
        description: "Subscription Id associated with the invoice"
        quote: true

      - name: subscription_name
        description: "The Name of the Subscription. This often starts with an 'A-******', for example: A-Sa19f446cdf9e63afb1ae7b378f36ca04"
        quote: true

      - name: crm_account_id
        description: "Salesforce Id for the customer"
        quote: true

      - name: dazn_user_id
        description: "DAZN User Id associated with the customer"
        quote: true

      - name: invoice_source_id
        description: "Source Id of the Invoice"
        quote: true

      - name: invoice_number
        description: "Number associated to the invoice"
        quote: true

      - name: invoice_due_date
        description: "Date the invoice is Due"
        quote: true

      - name: invoice_date
        description: "Date of the Invoice"
        quote: true

      - name: invoice_posted_timestamp
        description: "Timestamp the Invoice was Posted"
        quote: true

      - name: invoice_created_timestamp
        description: "Timestamp the invoice was created"
        quote: true

      - name: invoice_updated_timestamp
        description: "Timestamp the invoice was updated"
        quote: true

      - name: first_payment_attempt_timestamp
        description: "Timestamp of the first payment attempt, null when no payment attempt"
        quote: true

      - name: last_payment_attempt_timestamp
        description: "Timestamp of the last payment attempt, null when no payment attempt"
        quote: true

      - name: service_start_date
        description: "Start Date of the Service Period (Invoice)"
        quote: true

      - name: service_end_date
        description: "End Date of the Service Period (Invoice)"
        quote: true

      - name: invoice_is_reversed
        description: "Flag, 1 if the Invoice is Reversed"
        quote: true

      - name: invoice_source
        description: "Source of the invoice"
        quote: true

      - name: invoice_status
        description: "Current status of the Invoice"
        quote: true

      - name: has_applied_item
        description: "Flag, 1 if invoice has applied item"
        quote: true

      - name: is_zero_amount
        description: "Flag, 1 if invoice has 0 amount"
        quote: true

      - name: is_proration
        description: "Flag, 1 if invoice is proration (less than 0 amount)"
        quote: true

      - name: is_written_off
        description: "Flag, 1 if invoice is written off"
        quote: true

      - name: has_refund
        description: "Flag, 1 if invoice has a refund"
        quote: true

      - name: invoice_balance
        description: "Balance of the Invoice"
        quote: true

      - name: invoice_payment_amount
        description: "Payment Amount of the Invoice"
        quote: true

      - name: invoice_tax_amount
        description: "Tax amount of the invoice"
        quote: true

      - name: invoice_tax_exempt_amount
        description: "Tax exempt amount of the invoice"
        quote: true

      - name: invoice_adjustment_amount
        description: "Adjustment amount of the invoice"
        quote: true

      - name: invoice_amount
        description: "Amount of the invoice"
        quote: true

      - name: invoice_credit_balanced_adjustment_amount
        description: "Credit balance adjustment amount of the invoice"
        quote: true

      - name: invoice_amount_without_tax
        description: "Amount of the invoice without tax"
        quote: true

      - name: invoice_refund_amount
        description: "Refund Amount of the invoice, in the currency of the invoice"
        quote: true

      - name: total_recurring_amount
        description: "Total amount related to recurring charges"
        quote: true

      - name: total_one_time_amount
        description: "Total amount related to one time charges"
        quote: true

      - name: charge_subscription_amount
        description: "Total amount related to subscription charges"
        quote: true

      - name: tax_subscription_amount
        description: "Total amount related to subscription tax"
        quote: true

      - name: total_subscription_amount
        description: "Total amount related to subscription product type"
        quote: true

      - name: total_ppv_amount
        description: "Total amount related to ppv product type"
        quote: true

      - name: total_addon_amount
        description: "Total amount related to addon product type"
        quote: true

      - name: invoice_balance_usd
        description: "Balance of the Invoice in US Dollars"
        quote: true

      - name: invoice_payment_amount_usd
        description: "Payment Amount of the Invoice in US Dollars"
        quote: true

      - name: invoice_tax_amount_usd
        description: "Tax amount of the invoice in US Dollars"
        quote: true

      - name: invoice_tax_exempt_amount_usd
        description: "Tax exempt amount of the invoice in US Dollars"
        quote: true

      - name: invoice_adjustment_amount_usd
        description: "Adjustment amount of the invoice in US Dollars"
        quote: true

      - name: invoice_amount_usd
        description: "Amount of the invoice in US Dollars"
        quote: true

      - name: invoice_credit_balanced_adjustment_amount_usd
        description: "Credit balance adjustment amount of the invoice in US Dollars"
        quote: true

      - name: invoice_amount_without_tax_usd
        description: "Amount of the invoice without tax in US Dollars"
        quote: true

      - name: invoice_refund_amount_usd
        description: "Refund Amount of the invoice in US Dollars"
        quote: true

      - name: total_recurring_amount_usd
        description: "Total amount related to recurring charges in US Dollars"
        quote: true

      - name: total_one_time_amount_usd
        description: "Total amount related to one time charges in US Dollars"
        quote: true

      - name: charge_subscription_amount_usd
        description: "Total amount related to subscription charges in US Dollars"
        quote: true

      - name: tax_subscription_amount_usd
        description: "Total amount related to subscription tax in US Dollars"
        quote: true

      - name: total_subscription_amount_usd
        description: "Total amount related to subscription product type in US Dollars"
        quote: true

      - name: total_ppv_amount_usd
        description: "Total amount related to ppv charges in US Dollars"
        quote: true

      - name: total_addon_amount_usd
        description: "Total amount related to addon charges in US Dollars"
        quote: true

      - name: first_payment_method_type_detail
        description: "Type of Payment method associated to the first payment on the invoice"
        quote: true

      - name: last_payment_method_type_detail
        description: "Type of Payment method associated to the last payment on the invoice"
        quote: true

      - name: first_payment_method_type
        description: "Type of Payment method associated to the first payment on the invoice"
        quote: true

      - name: last_payment_method_type
        description: "Type of Payment method associated to the last payment on the invoice"
        quote: true

      - name: first_payment_attempt_is_success
        description: "Payment outcome associated to the first payment on the invoice"
        quote: true

      - name: last_payment_attempt_is_success
        description: "Payment outcome associated to the last payment on the invoice"
        quote: true

      - name: first_payment_attempt_response
        description: "Payment response associated to the first payment on the invoice"
        quote: true

      - name: last_payment_attempt_response
        description: "Payment response associated to the last payment on the invoice"
        quote: true

      - name: collect_group
        description: "Collect Group for the invoice, based on zuora payment comment, only populated for invoices in the zuora collect process"
        quote: true

      - name: max_collect_attempt
        description: "Max number of collect attempts for the invoice"
        quote: true

      - name: distinct_payment_method_count
        description: "Distinct count of payment methods"
        quote: true

      - name: successful_payment_attempt_count
        description: "Count of successful payment attempts"
        quote: true

      - name: failed_payment_attempt_count
        description: "Count of failed payment attempts"
        quote: true

      - name: total_payment_attempt_count
        description: "Total number of payment attemps"
        quote: true

      - name: has_recurring_item
        description: "Flag, 1 if invoice has a recurring item"
        quote: true

      - name: has_one_time_item
        description: "Flag, 1 if invoice has a one time charge item"
        quote: true

      - name: has_ppv_item
        description: "Flag, 1 if invoice has a PPV item"
        quote: true

      - name: has_addon_item
        description: "Flag, 1 if invoice has an addon item"
        quote: true

      - name: invoice_item_count
        description: "Total item count"
        quote: true

      - name: subscription_entitlement_set_id_array
        description: "An array of all entitlement set IDs relating to the subscription under this invoice, can be multiple if the tier has changed"
        quote: true

      - name: ppv_entitlement_set_id_array
        description: "An array of all entitlement set IDs relating to PPVs under this invoice"
        quote: true

      - name: addon_entitlement_set_id_array
        description: "An array of all entitlement set IDs relating to Add Ons under this invoice, can be multiple if there are multiple Add Ons active"
        quote: true

      - name: billing_account__skey
        description: "The surrogate key (skey) used to join on the billing_account dim"
        quote: true

      - name: subscription_info__skey
        description: "The surrogate key (skey) used to join on the subscription_info dim"
        quote: true

      - name: subscription_term__skey
        description: "The surrogate key (skey) used to join on the subscription_term dim"
        quote: true

      - name: subscription_charge__skey
        description: "The surrogate key (skey) used to join on the subscription_charge dim"
        quote: true

      - name: subscription_free_trial__skey
        description: "The surrogate key (skey) used to join on the subscription_free_trial dim"
        quote: true

      - name: subscription_discount__skey
        description: "The surrogate key (skey) used to join on the subscription_discount dim"
        quote: true

      - name: subscription_pause__skey
        description: "The surrogate key (skey) used to join on the subscription_pause dim"
        quote: true

      - name: subscription_add_on__skey
        description: "The surrogate key (skey) used to join on the subscription_add_on dim"
        quote: true

      - name: subscription_source_system_name_derived__skey
        description: "The surrogate key (skey) used to join on the sub_source_system dim"
        quote: true

      - name: subscription_tracking_id__skey
        description: "The surrogate key (skey) used to join on the tracking_id dim"
        quote: true

      - name: subscription_sign_up_campaign_id__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: first_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: last_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: subscription_type
        description: "Type of subscription associated with the invoice"
        quote: true

      - name: subscription_age_months
        description: "The age of the subscription in months at the time the invoice was posted"
        quote: true

      - name: has_30_day_cancel_charge
        description: "This flag is used to identify the 30 day cancellation charge"
        quote: true

      - name: taxation_adjustments
        description: "Number of taxation adjustments"
        quote: true

      - name: invoice_item_adjustments
        description: "Number of invoice item adjustments"
        quote: true

      - name: involuntary_churn_outcome
        description: "Describes the payment outcome for this invoice based on the payment attempts, amount and balance. the ratio of total invoices to involuntary churn where there's been a payment attempt is our involuntary churn rate"
        quote: true

      - name: next_consecutive_invoice_term
        description: "A description of the next invoice for the customer"
        quote: true

      - name: billing_account_greatest_updated_timestamp
        description: "Greatest updated date of the account information for the invoice"
        quote: true

      - name: invoice_item_greatest_updated_timestamp
        description: "Greatest updated date of the invoice item information for the invoice"
        quote: true

      - name: payment_greatest_updated_timestamp
        description: "Greatest updated date of the payment information for the invoice"
        quote: true

      - name: invoice_item_adjustment_greatest_updated_timestamp
        description: "Greatest updated date of the invoice item adjustments information for the invoice"
        quote: true

      - name: charge_addon_amount_partner
        description: "Charge amount for partner specific field names. Note: this is not a real field but a generalised one relating to partner data"
        quote: true

      - name: charge_addon_amount_usd_partner
        description: "Charge amount in USD for partner specific field names. Note: this is not a real field but a generalised one relating to partner data"
        quote: true

      - name: tax_addon_amount_partner
        description: "Tax amount for partner specific field names. Note: this is not a real field but a generalised one relating to partner data"
        quote: true

      - name: tax_addon_amount_usd_partner
        description: "Tax amount in USD for partner specific field names. Note: this is not a real field but a generalised one relating to partner data"
        quote: true

      - name: total_addon_amount_partner
        description: "Total amount for partner specific field names. Note: this is not a real field but a generalised one relating to partner data"
        quote: true

      - name: total_addon_amount_usd_partner
        description: "Total amount in USD for partner specific field names. Note: this is not a real field but a generalised one relating to partner data"
        quote: true

      - name: addon_partner_list
        description: "An array containing all addon partners available in the source data"
        quote: true

      - name: addon_partner_data
        description: "An array of objects containing all the partner data (charge, tax, total). One object per partner"
        quote: true
