{{
    config(
        materialized='view',
        database='INVOICE__B2C__MART__' + snowflake_env(),
        schema='FACT',
        tags=['presentation-invoice-fact']
    )
}}

WITH invoice AS (
    SELECT *
        EXCLUDE (
                "subscription_tier"
                ,"subscription_source_system_name"
                ,"subscription_country"
                ,"subscription_territory"
                ,"subscription_status"
                ,"subscription_end_date"
                ,"billing_account_currency_code"
                )
    FROM {{ ref('fct_invoice') }}
)

,user AS (
    SELECT * FROM {{ ref('user_mapping') }}
    WHERE TRUE
        AND "nonbilled_user" = FALSE OR "nonbilled_user" IS NULL
)

,final AS (
    SELECT
        invoice.*
    FROM invoice
    INNER JOIN user
    USING ("billing_account_id")
)

SELECT * FROM final
