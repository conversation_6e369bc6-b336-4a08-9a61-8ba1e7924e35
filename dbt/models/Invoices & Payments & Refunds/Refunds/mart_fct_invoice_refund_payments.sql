{{
    config(
        materialized='view',
        database='INVOICE__B2C__MART__' + snowflake_env(),
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        schema='INTERMEDIATE',
        alias='fct_invoice_refund_payments',
        tags=['presentation-invoice-items']
    )
}}

WITH fct_inv_pay AS (
    SELECT * FROM {{ ref('fct_invoice_payments') }}
)

SELECT
    "META__DBT_INSERT_DTTS"
    ,"invoice_payment_id"
    ,"invoice_id"
    ,"payment_id"
    ,"dazn_user_id"
    ,"billing_account_id"
    ,"payment_number"
    ,"payment_amount"
    ,"payment_reference_id"
    ,"payment_currency"
    ,TO_VARCHAR(Get(t.Value, 'refund_id') ) AS "refund_id"
    ,TO_DECIMAL(Get(t.Value, 'refund_amount')) AS "refund_amount"
    ,TO_VARCHAR(Get(t.Value, 'refund_gateway_response') )  AS "refund_gateway_response"
    ,TO_VARCHAR(Get(t.Value, 'refund_gateway_response_code') )  AS "refund_gateway_response_code"
    ,TO_VARCHAR(Get(t.Value, 'refund_reason_code') )  AS "refund_reason_code"
    ,TO_VARCHAR(Get(t.Value, 'refund_date') )  AS "refund_date"
    ,TO_VARCHAR(Get(t.Value, 'refund_status') )  AS "refund_status"
    ,TO_VARCHAR(Get(t.Value, 'refund_gateway') ) AS "refund_gateway"
    ,TO_VARCHAR(Get(t.Value, 'refund_method_type') ) AS "refund_method_type"
    ,TO_VARCHAR(Get(t.Value, 'refund_created_by_id') ) AS "refund_created_by_id"
    ,TO_VARCHAR(Get(t.Value, 'refund_source_type') ) AS "refund_source_type"
    ,TO_VARCHAR(Get(t.Value, 'refund_reference_id') ) AS "refund_reference_id"
    ,TO_VARCHAR(Get(t.Value, 'refund_number') ) AS "refund_number"
FROM fct_inv_pay F,
    TABLE(FLATTEN("payment_refund_details")) t
WHERE TRUE
    AND "payment_refund_details" IS NOT NULL

