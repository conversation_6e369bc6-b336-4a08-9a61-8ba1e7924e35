{{
    config(
        materialized='table',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_2XL_WH',
        tags=['presentation-invoice-fact'],
        cluster_by =['"invoice_date"']
    )
}}

{% set partners = get_addon_partner_list() %}

WITH fx_data AS (
    SELECT * FROM {{ ref_env('currency__daily_usd_fx_rates') }}
)

,invoice_data AS (
    SELECT * FROM {{ ref_env('staging__invoice_current')}}
)

,invoice_item_data AS (
    SELECT * FROM {{ ref('fct_invoice_invoice_items_agg') }}
)

,invoice_addon_data AS (
    SELECT * FROM {{ ref('fct_invoice_addons') }}
)

,invoice_item_adjustment_data AS (
    SELECT * FROM {{ ref_env('fct_invoice_invoice_item_adjustment_agg') }}
)

,payments_data AS (
    SELECT * FROM {{ ref('fct_invoice_payments_agg') }}
)

,ev_account_data AS (
    SELECT
        daznid AS "dazn_user_id"
        ,TO_TIMESTAMP_TZ(DATE_TRUNC('second', MAX(eventtimestamp))) AS "billing_account_last_updated_timestamp"
    FROM {{ source_env('DMP_INVOICES', 'CURATED__DMP_BILLING_ACCOUNT') }}
    GROUP BY ALL
)

,zr_account_data AS (
    SELECT
        "dazn_user_id"
        ,"billing_account_last_updated_timestamp"
    FROM {{ ref_env('staging__zuora__account_current') }}
)

,account_data_union AS (
    SELECT * FROM zr_account_data
    UNION ALL
    SELECT * FROM ev_account_data
)

,accounts_data AS (
    SELECT
        "dazn_user_id"
        ,MAX("billing_account_last_updated_timestamp") AS "billing_account_last_updated_timestamp"
    FROM account_data_union
    GROUP BY ALL
)

,subs_data AS (
    SELECT
        "subscription_id"
        ,"subscription_name"
        ,"billing_account__skey"
        ,"subscription_info__skey"
        ,"subscription_term__skey"
        ,"subscription_free_trial__skey"
        ,"subscription_pause__skey"
        ,"subscription_add_on__skey"
        ,"subscription_source_system_name_derived__skey"
        ,"subscription_tracking_id__skey"
        ,"subscription_sign_up_campaign_id__skey"
        ,"first_post_sign_up_giftcode_campaign_name__skey"
        ,"last_post_sign_up_giftcode_campaign_name__skey"
        ,"subscription_type"
        ,"subscription_tier"
        ,"subscription_source_system_name"
        ,"subscription_country"
        ,"subscription_territory"
        ,"subscription_status"
        ,"subscription_end_date"
        ,"subscription_start_date"
        ,"subscription_id_updated_timestamp"
    FROM {{ ref_env('subscription_id__dim') }}
    QUALIFY TRUE
        AND ROW_NUMBER() OVER (PARTITION BY "subscription_id" ORDER BY "subscription_id_updated_timestamp" DESC) = 1
)

,final_data AS (
    SELECT
        invoice_data."invoice_id"
        ,invoice_data."billing_account_id"
        ,invoice_item_data."subscription_id"
        ,subs_data."subscription_name"
        ,invoice_data."crm_account_id"
        ,invoice_data."dazn_user_id"
        ,invoice_data."invoice_source_id"
        ,invoice_data."invoice_number"
        ,invoice_data."invoice_due_date"
        ,invoice_data."invoice_date"
        ,invoice_data."invoice_posted_timestamp"
        ,invoice_data."invoice_created_timestamp"
        ,invoice_data."invoice_updated_timestamp"
        ,payments_data."first_payment_attempt_timestamp"
        ,payments_data."last_payment_attempt_timestamp"
        ,invoice_item_data."service_start_date"
        ,invoice_item_data."service_end_date"
        ,invoice_data."invoice_is_reversed"
        ,invoice_data."invoice_source"
        ,invoice_data."invoice_status"
        ,invoice_data."invoice_balance"
        ,invoice_data."invoice_payment_amount"
        ,invoice_data."invoice_etf_amount"
        ,invoice_data."invoice_tax_amount"
        ,invoice_data."invoice_tax_exempt_amount"
        ,invoice_item_adjustment_data."invoice_adjustment_amount"
        ,invoice_data."invoice_amount"
        ,invoice_data."invoice_credit_balanced_adjustment_amount"
        ,invoice_data."invoice_amount_without_tax"
        ,invoice_data."invoice_refund_amount"
        ,invoice_item_data."total_recurring_amount"
        ,invoice_item_data."total_one_time_amount"
        ,invoice_item_data."charge_subscription_amount"
        ,invoice_item_data."tax_subscription_amount"
        ,invoice_item_data."total_subscription_amount"
        ,invoice_item_data."charge_pass_amount"
        ,invoice_item_data."tax_pass_amount"
        ,invoice_item_data."total_pass_amount"
        ,invoice_item_data."charge_ppv_amount"
        ,invoice_item_data."tax_ppv_amount"
        ,invoice_item_data."total_ppv_amount"
        ,invoice_addon_data."charge_addon_amount"
        ,invoice_addon_data."tax_addon_amount"
        ,invoice_addon_data."total_addon_amount"
        ,payments_data."first_payment_method_type_detail"
        ,payments_data."last_payment_method_type_detail"
        ,payments_data."first_payment_method_type"
        ,payments_data."last_payment_method_type"
        ,payments_data."first_payment_attempt_is_success"
        ,payments_data."last_payment_attempt_is_success"
        ,payments_data."first_payment_attempt_response"
        ,payments_data."last_payment_attempt_response"
        ,payments_data."collect_group"
        ,payments_data."max_collect_attempt"
        ,payments_data."distinct_payment_method_count"
        ,payments_data."successful_payment_attempt_count"
        ,payments_data."failed_payment_attempt_count"
        ,payments_data."total_payment_attempt_count"
        ,payments_data."first_payment_payment_source"
        ,CASE
            WHEN payments_data."first_payment_payment_source" ILIKE 'paypal%' THEN 'PayPal'
            WHEN payments_data."first_payment_payment_source" ILIKE 'adyen%' THEN 'CreditCard'
            WHEN payments_data."first_payment_payment_source" ILIKE 'paynow%' THEN 'Klarna'
            ELSE payments_data."first_payment_payment_source"
        END AS "first_payment_payment_source_simple"
        ,invoice_item_data."has_one_time_item"
        ,invoice_item_data."has_recurring_item"
        ,invoice_item_data."has_ppv_item"
        ,invoice_item_data."has_pass_item"
        ,COALESCE(invoice_addon_data."has_addon_item",0) AS "has_addon_item"
        ,invoice_item_data."invoice_item_count"
        ,invoice_item_data."ppv_count"
        ,invoice_item_data."subscription_count"
        ,invoice_item_data."addon_count"
        ,invoice_item_data."pass_count"
        ,invoice_item_data."subscription_entitlement_set_id_array"
        ,invoice_item_data."ppv_entitlement_set_id_array"
        ,invoice_item_data."addon_entitlement_set_id_array"
        ,invoice_item_data."pass_entitlement_set_id_array"
        ,invoice_data."billing_account_currency_code"
        ,fx_data."exchange_rate"
        ,subs_data."billing_account__skey"
        ,subs_data."subscription_info__skey"
        ,subs_data."subscription_term__skey"
        ,subs_data."subscription_free_trial__skey"
        ,subs_data."subscription_pause__skey"
        ,subs_data."subscription_add_on__skey"
        ,subs_data."subscription_source_system_name_derived__skey"
        ,subs_data."subscription_tracking_id__skey"
        ,subs_data."subscription_sign_up_campaign_id__skey"
        ,subs_data."first_post_sign_up_giftcode_campaign_name__skey"
        ,subs_data."last_post_sign_up_giftcode_campaign_name__skey"
        ,invoice_item_data."invoice_tracking_id__skey"
        ,invoice_item_data."invoice_source_system_name_derived__skey"
        ,subs_data."subscription_type"
        ,subs_data."subscription_tier"
        ,subs_data."subscription_source_system_name"
        ,subs_data."subscription_country"
        ,subs_data."subscription_territory"
        ,subs_data."subscription_status"
        ,subs_data."subscription_end_date"
        ,DATEDIFF('month', subs_data."subscription_start_date", "invoice_posted_timestamp") AS "subscription_age_months"
        ,invoice_item_adjustment_data."taxation_adjustments"
        ,invoice_item_adjustment_data."invoice_item_adjustments"
        ,invoice_item_data."has_applied_item"
        -- columns from joined table has to be uppercase because the partner aren't hardcoded in the yaml and the column names are automatically upper-cased
        {% for partner in partners -%}
        {% set partner_uppercase = "{}".format(partner.upper())  %}
        ,COALESCE(invoice_addon_data."CHARGE_AMOUNT_{{ partner_uppercase }}", 0) AS "charge_addon_amount_{{ partner }}"
        ,COALESCE(invoice_addon_data."TAX_AMOUNT_{{ partner_uppercase }}", 0) AS "tax_addon_amount_{{ partner }}"
        ,COALESCE(invoice_addon_data."TOTAL_AMOUNT_{{ partner_uppercase }}", 0) AS "total_addon_amount_{{ partner }}"
        {%- endfor %}
        ,invoice_addon_data."addon_partner_data"
        ,invoice_addon_data."addon_partner_list"
        -- Used to create the next post status column - We need to set this to null so that the macro's logic can use the IGNORE-NULL functionality.
        ,CASE WHEN invoice_data."invoice_amount" < 0 THEN NULL ELSE invoice_item_data."service_start_date" END AS "temp_start_date"
        ,accounts_data."billing_account_last_updated_timestamp" AS "billing_account_greatest_updated_timestamp"
        ,invoice_item_data."ii_greatest_updated_timestamp" AS "invoice_item_greatest_updated_timestamp"
        ,payments_data."p_greatest_updated_timestamp" AS "payment_greatest_updated_timestamp"
        ,invoice_item_adjustment_data."iia_greatest_updated_timestamp" AS "invoice_item_adjustment_greatest_updated_timestamp"
        ,CASE
           WHEN 'ev' IN (invoice_data."recent_invoice_source_of_data", payments_data."recent_payment_source_of_data") THEN 'ev'
           ELSE 'zr'
        END AS "recent_source_of_data"
       ,invoice_data."invoice_churn_type" AS "invoice_churn_type"
       ,payments_data."recent_payment_source" AS "payment_source"
    FROM invoice_data
    LEFT JOIN invoice_item_data
        ON invoice_data."invoice_id" = invoice_item_data."invoice_id"
    LEFT JOIN payments_data
        ON invoice_data."invoice_id" = payments_data."invoice_id"
    LEFT JOIN accounts_data
        ON invoice_data."dazn_user_id" = accounts_data."dazn_user_id"
    LEFT JOIN fx_data
        ON invoice_data."invoice_date" = fx_data."full_date"
            AND invoice_data."billing_account_currency_code" = fx_data."currency_from"
    LEFT JOIN invoice_item_adjustment_data
        ON invoice_data."invoice_id" = invoice_item_adjustment_data."invoice_id"
    LEFT JOIN subs_data
        ON invoice_item_data."subscription_id" = subs_data."subscription_id"
    LEFT JOIN invoice_addon_data
        ON invoice_addon_data."invoice_id" = invoice_data."invoice_id"
)

SELECT
    "invoice_id"
    ,"dazn_user_id"
    ,"crm_account_id"
    ,"billing_account_id"
    ,"subscription_id"
    ,"subscription_name"
    ,"invoice_source_id"
    ,"invoice_number"
    ,"invoice_date"
    ,"invoice_due_date"
    ,"invoice_created_timestamp"
    ,"invoice_posted_timestamp"
    ,GREATEST_IGNORE_NULLS("invoice_updated_timestamp", "last_payment_attempt_timestamp" ) AS "invoice_updated_timestamp"
    ,"invoice_status"
    ,"service_start_date"
    ,"service_end_date"
    ,CASE
        WHEN "invoice_amount" = 0 THEN 1
        ELSE 0
    END AS "is_zero_amount"
    ,CASE
        WHEN "invoice_amount" < 0 THEN 1
        ELSE 0
    END AS "is_proration"
    ,CASE
        WHEN "recent_source_of_data" = 'zr'
             AND ROUND("invoice_amount", 2) = ROUND("invoice_adjustment_amount", 2)
                 AND "invoice_amount" != 0 AND "invoice_balance" = 0 AND "invoice_payment_amount" = 0 THEN 1
        ELSE 0
    END AS "is_written_off"
    ,CASE
        WHEN "invoice_refund_amount" != 0 THEN 1
        ELSE 0
    END AS "has_refund"
    ,CASE
        WHEN "invoice_etf_amount" != 0 THEN 1
        ELSE 0
    END AS "has_etf"
    ,ROUND("invoice_amount", 2) AS "invoice_amount"
    ,ROUND("invoice_tax_amount", 2) AS "invoice_tax_amount"
    ,ROUND("invoice_amount_without_tax", 2) AS "invoice_amount_without_tax"
    ,ROUND("invoice_tax_exempt_amount", 2) AS "invoice_tax_exempt_amount"
    ,ROUND("invoice_payment_amount", 2) AS "invoice_payment_amount"
    ,ROUND("invoice_etf_amount", 2) AS "invoice_etf_amount"
    ,ROUND("invoice_refund_amount", 2) AS "invoice_refund_amount"
    ,ROUND("invoice_balance", 2) AS "invoice_balance"
    ,COALESCE(ROUND("invoice_adjustment_amount", 2), 0) AS "invoice_adjustment_amount"
    ,ROUND("invoice_credit_balanced_adjustment_amount", 2) AS "invoice_credit_balanced_adjustment_amount"
    ,ROUND("total_one_time_amount", 2) AS "total_one_time_amount"
    ,ROUND("total_recurring_amount", 2) AS "total_recurring_amount"
    ,ROUND("charge_subscription_amount", 2) AS "charge_subscription_amount"
    ,ROUND("tax_subscription_amount", 2) AS "tax_subscription_amount"
    ,ROUND("total_subscription_amount", 2) AS "total_subscription_amount"
    ,ROUND("charge_pass_amount", 2) AS "charge_pass_amount"
    ,ROUND("tax_pass_amount", 2) AS "tax_pass_amount"
    ,ROUND("total_pass_amount", 2) AS "total_pass_amount"
    ,ROUND("charge_ppv_amount", 2) AS "charge_ppv_amount"
    ,ROUND("tax_ppv_amount", 2) AS "tax_ppv_amount"
    ,ROUND("total_ppv_amount", 2) AS "total_ppv_amount"
    ,COALESCE(ROUND("charge_addon_amount", 2), 0) AS "charge_addon_amount"
    ,COALESCE(ROUND("tax_addon_amount", 2), 0) AS "tax_addon_amount"
    ,COALESCE(ROUND("total_addon_amount", 2), 0) AS "total_addon_amount"

    ,{{ usd_conversion("invoice_amount") }} AS "invoice_amount_usd"
    ,{{ usd_conversion("invoice_tax_amount") }} AS "invoice_tax_amount_usd"
    ,{{ usd_conversion("invoice_amount_without_tax") }} AS "invoice_amount_without_tax_usd"
    ,{{ usd_conversion("invoice_tax_exempt_amount") }} AS "invoice_tax_exempt_amount_usd"
    ,{{ usd_conversion("invoice_payment_amount") }} AS "invoice_payment_amount_usd"
    ,{{ usd_conversion("invoice_refund_amount") }} AS "invoice_refund_amount_usd"
    ,{{ usd_conversion("invoice_balance") }} AS "invoice_balance_usd"
    ,COALESCE({{ usd_conversion("invoice_adjustment_amount") }}, 0) AS "invoice_adjustment_amount_usd"
    ,{{ usd_conversion("invoice_credit_balanced_adjustment_amount") }} AS "invoice_credit_balanced_adjustment_amount_usd"
    ,{{ usd_conversion("total_one_time_amount") }} AS "total_one_time_amount_usd"
    ,{{ usd_conversion("total_recurring_amount") }} AS "total_recurring_amount_usd"
    ,{{ usd_conversion("charge_subscription_amount") }} AS "charge_subscription_amount_usd"
    ,{{ usd_conversion("tax_subscription_amount") }} AS "tax_subscription_amount_usd"
    ,{{ usd_conversion("total_subscription_amount") }} AS "total_subscription_amount_usd"
    ,{{ usd_conversion("charge_pass_amount") }} AS "charge_pass_amount_usd"
    ,{{ usd_conversion("tax_pass_amount") }} AS "tax_pass_amount_usd"
    ,{{ usd_conversion("total_pass_amount") }} AS "total_pass_amount_usd"
    ,{{ usd_conversion("charge_ppv_amount") }} AS "charge_ppv_amount_usd"
    ,{{ usd_conversion("tax_ppv_amount") }} AS "tax_ppv_amount_usd"
    ,{{ usd_conversion("total_ppv_amount") }} AS "total_ppv_amount_usd"
    ,COALESCE({{ usd_conversion("charge_addon_amount") }}, 0) AS "charge_addon_amount_usd"
    ,COALESCE({{ usd_conversion("tax_addon_amount") }}, 0) AS "tax_addon_amount_usd"
    ,COALESCE({{ usd_conversion("total_addon_amount") }}, 0) AS "total_addon_amount_usd"

    -- ADDON PARTNERS (Charge, tax and total)
    {% for partner in partners -%}
        {% set charge_partner_field_name = "charge_addon_amount_{}".format(partner)  %}
    {% set tax_partner_field_name = "tax_addon_amount_{}".format(partner)  %}
    {% set total_partner_field_name = "total_addon_amount_{}".format(partner)  %}
    ,COALESCE(ROUND("{{ charge_partner_field_name }}", 2), 0) AS "{{ charge_partner_field_name }}"
    ,COALESCE({{ usd_conversion(charge_partner_field_name) }}, 0) AS "charge_addon_amount_usd_{{ partner }}"
    ,COALESCE(ROUND("{{ tax_partner_field_name }}", 2), 0) AS "{{ tax_partner_field_name }}"
    ,COALESCE({{ usd_conversion(tax_partner_field_name) }}, 0) AS "tax_addon_amount_usd_{{ partner }}"
    ,COALESCE(ROUND("{{ total_partner_field_name }}", 2), 0) AS "{{ total_partner_field_name }}"
    ,COALESCE({{ usd_conversion(total_partner_field_name) }}, 0) AS "total_addon_amount_usd_{{ partner }}"
    {%- endfor %}
    ,"addon_partner_data"
    ,"addon_partner_list"

    ,"total_payment_attempt_count"
    ,"successful_payment_attempt_count"
    ,"failed_payment_attempt_count"
    ,"distinct_payment_method_count"
    ,"collect_group"
    ,"max_collect_attempt"

    ,"first_payment_attempt_timestamp"
    ,"first_payment_attempt_is_success"
    ,CASE
        WHEN "first_payment_method_type" IS NOT NULL THEN "first_payment_method_type"
        WHEN "invoice_payment_amount" > 0 THEN COALESCE("first_payment_method_type_detail", "subscription_source_system_name", "first_payment_payment_source_simple", NULL)
    END AS "first_payment_method_type"
    ,CASE
        WHEN "first_payment_method_type_detail" IS NOT NULL THEN "first_payment_method_type_detail"
        WHEN "invoice_payment_amount" > 0 THEN COALESCE("first_payment_method_type_detail", "subscription_source_system_name", "first_payment_payment_source", NULL)
    END AS "first_payment_method_type_detail"
    ,"first_payment_attempt_response"

    ,"last_payment_attempt_timestamp"
    ,"last_payment_attempt_is_success"
    ,CASE
        WHEN "last_payment_method_type" IS NOT NULL THEN "last_payment_method_type"
        WHEN "invoice_payment_amount" > 0 THEN COALESCE("first_payment_method_type_detail", "subscription_source_system_name", "first_payment_payment_source_simple", NULL)
    END AS "last_payment_method_type"
    ,CASE
        WHEN "last_payment_method_type_detail" IS NOT NULL THEN "last_payment_method_type_detail"
        WHEN "invoice_payment_amount" > 0 THEN COALESCE("first_payment_method_type_detail", "subscription_source_system_name", "first_payment_payment_source", NULL)
    END AS "last_payment_method_type_detail"
    ,"last_payment_attempt_response"

    ,"has_one_time_item"
    ,"has_recurring_item"

    ,"has_pass_item"
    ,"has_ppv_item"
    ,COALESCE("has_addon_item", 0) AS "has_addon_item"

    ,COALESCE("invoice_item_count", 0) AS "invoice_item_count"
    ,COALESCE("subscription_count", 0) AS "subscription_count"
    ,COALESCE("pass_count", 0) AS "pass_count"
    ,COALESCE("ppv_count", 0) AS "ppv_count"
    ,COALESCE("addon_count", 0) AS "addon_count"

    ,"subscription_entitlement_set_id_array"
    ,"pass_entitlement_set_id_array"
    ,"ppv_entitlement_set_id_array"
    ,"addon_entitlement_set_id_array"

    ,"billing_account_currency_code"
    ,"billing_account__skey"
    ,"subscription_info__skey"
    ,"subscription_term__skey"
    ,"subscription_free_trial__skey"
    ,"subscription_pause__skey"
    ,"subscription_add_on__skey"
    ,"subscription_source_system_name_derived__skey"
    ,"subscription_tracking_id__skey"
    ,"subscription_sign_up_campaign_id__skey"
    ,"first_post_sign_up_giftcode_campaign_name__skey"
    ,"last_post_sign_up_giftcode_campaign_name__skey"
    ,"invoice_tracking_id__skey"
    ,"invoice_source_system_name_derived__skey"
    ,"subscription_type"
    ,"subscription_tier"
    ,"subscription_source_system_name"
    ,"subscription_country"
    ,"subscription_territory"
    ,"subscription_status"
    ,"subscription_end_date"
    ,"subscription_age_months"
    ,CASE
        WHEN "subscription_type" = 'Monthly'
            AND "subscription_source_system_name" IS NULL
                AND "subscription_status" = 'Cancelled' THEN 1
        ELSE 0
    END AS "has_30_day_cancel_charge"
    ,COALESCE("taxation_adjustments", 0) AS "taxation_adjustments"
    ,COALESCE("invoice_item_adjustments", 0) AS "invoice_item_adjustments"
--     ,"recent_source_of_data"
    ,CASE
        WHEN "recent_source_of_data" = 'zr' THEN {{ zr_invol_churn() }}
        WHEN "recent_source_of_data" = 'ev' THEN {{ ev_invol_churn() }}
        ELSE 'Unknown'
    END AS "involuntary_churn_outcome"
    ,{{ next_consecutive_invoice_term() }} AS "next_consecutive_invoice_term"
    ,"billing_account_greatest_updated_timestamp"
    ,"invoice_item_greatest_updated_timestamp"
    ,"payment_greatest_updated_timestamp"
    ,"invoice_item_adjustment_greatest_updated_timestamp"
    ,"invoice_source"
    ,"payment_source"
    ,"invoice_is_reversed"
    ,"has_applied_item"
FROM final_data
