version: 2

models:
  - name: fct_invoice_payments
    description: "Fact table with details on payments"
    columns:
      - name: invoice_payment_id
        description: "Payment Id of the Invoice"
        quote: true

      - name: invoice_id
        description: "Id of the Invoice"
        quote: true

      - name: payment_id
        description: "Id of the payment"
        quote: true

      - name: dazn_user_id
        description: "DAZN User Id associated with the customer"
        quote: true

      - name: billing_account_id
        description: "Id of the account"
        quote: true

      - name: payment_number
        description: "Number associated to the payment"
        quote: true

      - name: payment_reference_id
        description: "Reference Id associated to the payment"
        quote: true

      - name: payment_method_id
        description: "Method Id associated to the payment"
        quote: true

      - name: payment_created_by_id
        description: "Id associated to the payment creation"
        quote: true

      - name: payment_source
        description: "Source of the payment"
        quote: true

      - name: payment_source_name
        description: "Source Name associated to the payment"
        quote: true

      - name: payment_type
        description: "Type/Mode of payment"
        quote: true

      - name: payment_status
        description: "State of payment if approved or error"
        quote: true

      - name: payment_effective_date
        description: "payment attempt date"
        quote: true

      - name: payment_created_date
        description: "Date when payment is initiated"
        quote: true

      - name: payment_created_timestamp
        description: "Timestamp when payment is initiated"
        quote: true

      - name: payment_updated_timestamp
        description: "Timestamp when payment is updated"
        quote: true

      - name: payment_currency
        description: "Currency associated with payment"
        quote: true

      - name: payment_amount
        description: "Amount paid"
        quote: true

      - name: payment_refund_amount
        description: "Refund amount availed for payment"
        quote: true

      - name: payment_cancelled_on
        description: "Date of payment cancellation"
        quote: true

      - name: payment_gateway
        description: "electronic financial transaction's technology platform"
        quote: true

      - name: payment_gateway_response
        description: "payment transaction's response details"
        quote: true

      - name: payment_gateway_response_code
        description: "payment transaction's response code"
        quote: true

      - name: payment_gateway_state
        description: "state of payment transactions"
        quote: true

      - name: subscription_name
        description: "Subscription Name also known as subscription Number"
        quote: true

      - name: payment_refund_details
        description: "Array of all refund transactions corresponding to the payments"
        quote: true

      - name: subscription_details
        description: "Array of all subscription details corresponding to the payments"
        quote: true

      - name: payment_fx_full_date
        description: "The date of the conversion to USD"
        quote: true

      - name: usd_exchange_rate
        description: "The exchange rate to USD"
        quote: true
