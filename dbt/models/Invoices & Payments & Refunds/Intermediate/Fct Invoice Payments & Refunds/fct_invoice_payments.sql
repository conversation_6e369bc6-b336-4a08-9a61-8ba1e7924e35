{{
    config(
        materialized='table',
        on_schema_change='sync_all_columns',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XL_WH',
        schema='PRESENTATION',
        cluster_by=['"payment_created_date"'],
        tags=['presentation-invoice-items']
    )
}}

WITH curr_usd AS (
    SELECT * FROM {{ ref_env('currency__daily_usd_fx_rates') }}
)

, inv_item AS (
     SELECT * FROM {{ ref_env('invoice_items__invoice_item_join') }}
)

,inv_data AS (
    SELECT
        "invoice_id"
        ,"invoice_date"
        ,"invoice_etf_amount" > 0 AS "has_etf"
        ,"recent_invoice_source_of_data"
        ,"invoice_source"
    FROM {{ ref_env('staging__invoice_current') }}
)

,payment_info AS (
    SELECT * FROM {{ ref_env('staging__payment_current') }}
)

,refund_source AS (
    SELECT * FROM {{ ref_env('staging__invoice_refund_current') }}
)

, refund AS (
    SELECT
        "refund_invoice_payment_invoice_payment_id"
        ,ARRAY_AGG(
            OBJECT_CONSTRUCT(
                'refund_id', "refund_id"
                ,'refund_amount', "refund_amount"
                ,'refund_gateway_response', coalesce("refund_gateway_response",'')
                ,'refund_gateway_response_code', coalesce("refund_gateway_response",'')
                ,'refund_reason_code',coalesce("refund_reason_code",'')
                ,'refund_date',"refund_date"
                ,'refund_status',"refund_status"
                ,'refund_gateway',"refund_gateway"
                ,'refund_method_type',"refund_method_type"
                ,'refund_created_by_id',"refund_created_by_id"
                ,'refund_source_type',"refund_source_type"
                ,'refund_reference_id',"refund_reference_id"
                ,'refund_number',"refund_number"
            )
        ) AS "payment_refund_details"
    FROM refund_source
    GROUP BY 1
)

, inv_sub AS (
    SELECT "invoice_id"
    ,ARRAY_AGG(
        OBJECT_CONSTRUCT(
            'subscription_name',"subscription_number"
            ,'subscription_id',"subscription_id"
            ,'invoice_item_charge_name',"invoice_item_charge_name"
        )
    ) AS "subscription_details"
    FROM inv_item
    GROUP BY 1
)

, final AS (
    SELECT
        CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
        ,payment_info."invoice_payment_id"
        ,payment_info."invoice_id"
        ,inv_data."invoice_date"
        ,payment_info."payment_id"
        ,payment_info."dazn_user_id"
        ,payment_info."billing_account_id"
        ,payment_info."payment_number"
        ,payment_info."payment_reference_id"
        ,payment_info."payment_method_id"
        ,payment_info."payment_created_by_id"
        ,payment_info."payment_source"
        ,payment_info."payment_source_name"
        ,payment_info."payment_type"
        ,payment_info."payment_status"
        ,payment_info."payment_effective_timestamp" AS "payment_effective_date"
        ,payment_info."payment_created_date"
        ,payment_info."payment_created_timestamp"
        ,payment_info."payment_updated_timestamp"
        ,payment_info."payment_currency"
        ,payment_info."payment_amount"
        ,payment_info."payment_refund_amount"
        ,payment_info."payment_cancelled_on"
        ,payment_info."payment_gateway"
        ,payment_info."payment_gateway_response"
        ,payment_info."payment_gateway_response_code"
        ,payment_info."payment_gateway_state"
        ,refund."payment_refund_details"
        ,inv_sub."subscription_details"
        ,curr_usd."full_date" AS "payment_fx_full_date"
        ,curr_usd."exchange_rate" AS "usd_exchange_rate"
        ,payment_info."payment_amount" * curr_usd."exchange_rate" AS "payment_amount_usd"
        ,payment_info."payment_refund_amount" * curr_usd."exchange_rate" AS "payment_refund_amount_usd"
        ,payment_info."payment_source_of_data"
        ,inv_data."recent_invoice_source_of_data"
        ,inv_data."invoice_source"
        ,inv_data."has_etf"
        ,refund."payment_refund_details" IS NOT NULL AS "has_refund"
    FROM payment_info
    LEFT JOIN inv_sub
        ON inv_sub."invoice_id" = payment_info."invoice_id"
    LEFT JOIN curr_usd
        ON payment_info."payment_currency" = curr_usd."currency_from"
        AND payment_info."payment_effective_timestamp"::DATE = curr_usd."full_date"
    LEFT JOIN refund
        ON refund."refund_invoice_payment_invoice_payment_id" = payment_info."invoice_payment_id"
    LEFT JOIN inv_data
        ON inv_sub."invoice_id" = inv_data."invoice_id"
)

SELECT * FROM final
