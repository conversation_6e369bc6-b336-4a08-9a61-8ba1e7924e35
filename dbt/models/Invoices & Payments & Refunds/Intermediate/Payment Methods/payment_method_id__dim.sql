{{
    config(
        materialized='view',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        schema='PRESENTATION',
        tags=['presentation-invoice-fact']
    )
}}

WITH payment_method_source AS (
    SELECT * FROM {{ ref_env('staging__payment_method_current') }}
)

SELECT
    "payment_method_id"
    ,"payment_method_type"
    ,"payment_method_bank_identification_number"
    ,"payment_method_credit_card_type"
    ,"payment_method_credit_card_expiration_month"
    ,"payment_method_credit_card_expiration_year"
    ,"payment_method_credit_card_mask_number"
    ,"payment_method_paypal_email"
    ,"payment_method_actual_payment_method"
FROM payment_method_source



