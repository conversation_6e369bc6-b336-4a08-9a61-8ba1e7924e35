{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key = '"invoice_id"',
        on_schema_change='sync_all_columns',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XL_WH',
        tags=['presentation-invoice-fact'],
        alias='fct_invoice_payments_agg'
    )
}}
-- TODO: need a description of what this model does

WITH payments_data AS (
    SELECT * FROM {{ ref_env('staging__payment_current') }}
)

,payment_methods_data AS (
    SELECT * FROM {{ ref_env('staging__payment_method_current') }}
)

-- we start with all the payments from the payment_source
-- We use the invoice_payment_source to get the invoice_id related to each payment
-- We get the payment method (only for direct customers) information for each payment
-- todo: consider moving payment logic to it's own model so can be re-used for other models.
,joined AS (
    SELECT
        "invoice_id"
        ,payments_data."payment_method_id"
        -- todo Do we only want one flag here, and tests for values of status, do we want T/F instead of 1/0
        ,CASE WHEN "payment_status" = 'Processed' THEN 1 ELSE 0 END AS "payment_is_success"
        ,CASE WHEN "payment_status" IN ('Error', 'Canceled', 'Voided') THEN 1 ELSE 0 END AS "payment_is_failure"
        ,COALESCE(
                "payment_method_credit_card_type"
                ,"payment_method_paypal_type"
                ,"payment_method_bank_transfer_type"
                ,"payment_method_actual_payment_method")
        AS "payment_method_type_clean"
        -- For each invoice_id we collect information of the first/last payment. Payments can change over the invoice. Example: payment method 1 fails, customer enter active grace, changes PM which works. Invoice will have two PMs
        ,FIRST_VALUE("payment_method_type_clean")
            OVER ( PARTITION BY "invoice_id"
                    ORDER BY "payment_created_timestamp" ASC NULLS FIRST -- Test for Created Timestamp Null
        ) AS "first_payment_method_type_detail"
        ,LAST_VALUE("payment_method_type_clean")
            OVER ( PARTITION BY "invoice_id"
                        ORDER BY "payment_created_timestamp" ASC NULLS FIRST
        ) AS "last_payment_method_type_detail"
        ,FIRST_VALUE("payment_method_type")
            OVER ( PARTITION BY "invoice_id"
                        ORDER BY "payment_created_timestamp" ASC NULLS FIRST
        ) AS "first_payment_method_type"
        ,LAST_VALUE("payment_method_type")
            OVER ( PARTITION BY "invoice_id"
                        ORDER BY "payment_created_timestamp" ASC NULLS FIRST
        ) AS "last_payment_method_type"
        ,LAST_VALUE("payment_comment") IGNORE NULLS
            OVER ( PARTITION BY "invoice_id"
                        ORDER BY "payment_created_timestamp" ASC NULLS FIRST
        ) AS "last_payment_comment"
        ,TRIM(SPLIT_PART(SPLIT_PART("last_payment_comment", '|', 2 ),':',2), ' ') AS "collect_group"
        ,TRIM(SPLIT_PART(SPLIT_PART("last_payment_comment", '|', 3 ),':',2), ' ') AS "max_collect_attempt"
        ,FIRST_VALUE("payment_is_success")
            OVER ( PARTITION BY "invoice_id"
                        ORDER BY "payment_created_timestamp" ASC NULLS FIRST
        ) AS "first_payment_attempt_is_success"
        ,LAST_VALUE("payment_is_success")
            OVER ( PARTITION BY "invoice_id"
                        ORDER BY "payment_created_timestamp" ASC NULLS FIRST
        ) AS "last_payment_attempt_is_success"
        ,FIRST_VALUE("payment_created_timestamp")
            OVER ( PARTITION BY "invoice_id"
                        ORDER BY "payment_created_timestamp" ASC NULLS FIRST
        ) AS "first_payment_attempt_timestamp"
        ,LAST_VALUE("payment_created_timestamp")
            OVER ( PARTITION BY "invoice_id"
                        ORDER BY "payment_created_timestamp" ASC NULLS FIRST
        ) AS "last_payment_attempt_timestamp"
        ,FIRST_VALUE("payment_gateway_response")
            OVER ( PARTITION BY "invoice_id"
                        ORDER BY "payment_created_timestamp" ASC NULLS FIRST
        ) AS "first_payment_attempt_response"
        ,LAST_VALUE("payment_gateway_response")
            OVER ( PARTITION BY "invoice_id"
                        ORDER BY "payment_created_timestamp" ASC NULLS FIRST
        ) AS "last_payment_attempt_response"
        ,"payment_created_timestamp" AS "payment_created_timestamp"
        ,"payment_method_updated_timestamp" AS "payment_method_updated_timestamp"
        ,"invoice_payment_updated_timestamp" AS "invoice_payment_updated_timestamp"
        ,GREATEST_IGNORE_NULLS(
            "invoice_payment_updated_timestamp"
            ,"payment_created_timestamp"
            ,"payment_method_updated_timestamp"
        ) AS "greatest_updated_timestamp"
        ,FIRST_VALUE("payment_payment_source")
            OVER ( PARTITION BY "invoice_id"
                        ORDER BY "payment_created_timestamp" ASC NULLS LAST
        ) AS "first_payment_payment_source"
        ,LAST_VALUE("payment_source") OVER (PARTITION BY "invoice_id" ORDER BY "invoice_payment_updated_timestamp" ASC NULLS FIRST) AS "recent_payment_source"
        ,LAST_VALUE("payment_source_of_data") OVER (PARTITION BY "invoice_id" ORDER BY "invoice_payment_updated_timestamp" ASC NULLS FIRST) AS "recent_payment_source_of_data"
    FROM payments_data
    LEFT JOIN payment_methods_data
        USING("payment_method_id")
)

,aggregated AS (
    SELECT
        "invoice_id"
        ,"first_payment_attempt_timestamp"
        ,"last_payment_attempt_timestamp"
        ,"first_payment_method_type_detail"
        ,"last_payment_method_type_detail"
        ,"first_payment_method_type"
        ,"last_payment_method_type"
        ,"collect_group"
        ,"max_collect_attempt"
        ,"first_payment_attempt_is_success"
        ,"last_payment_attempt_is_success"
        ,"first_payment_attempt_response"
        ,"last_payment_attempt_response"
        ,"first_payment_payment_source"
        ,"recent_payment_source"
        ,"recent_payment_source_of_data"
        ,MAX("greatest_updated_timestamp") AS "p_greatest_updated_timestamp"
        ,COUNT(DISTINCT("payment_method_id")) AS "distinct_payment_method_count"
        ,SUM("payment_is_success") AS "successful_payment_attempt_count"
        ,SUM("payment_is_failure") AS "failed_payment_attempt_count"
        ,COUNT(*) AS "total_payment_attempt_count"
    FROM joined
    GROUP BY "invoice_id" ,"first_payment_attempt_timestamp" ,"last_payment_attempt_timestamp" ,"first_payment_method_type_detail" ,"last_payment_method_type_detail" ,"first_payment_method_type" ,"last_payment_method_type" ,"collect_group" ,"max_collect_attempt" ,"first_payment_attempt_is_success" ,"last_payment_attempt_is_success" ,"first_payment_attempt_response" ,"last_payment_attempt_response" ,"first_payment_payment_source", "recent_payment_source","recent_payment_source_of_data"
)

SELECT * FROM aggregated


-- validation query:
-- select * from TRANSFORMATION_UAT.TRANSIENT.FCT_INVOICE_PAYMENTS_AGG
-- where "invoice_id" = '1519583'
