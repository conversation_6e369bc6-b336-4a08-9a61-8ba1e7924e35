{{
    config(
        materialized='table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_S_WH',
        tags=['presentation-invoice-fact']
    )
}}

{% set partners = get_addon_partner_list() %}

WITH invoice_items_source AS (
    SELECT * FROM {{ ref('fct_invoice_items') }}
    WHERE "invoice_item_product_type" = 'addon'
)

SELECT
    "invoice_id"
    ,1 AS "has_addon_item"
    ,SUM("invoice_item_charge_amount") AS "charge_addon_amount"
    ,SUM("invoice_item_tax_amount") AS "tax_addon_amount"
    ,SUM("invoice_item_charge_amount") + SUM("invoice_item_tax_amount") AS "total_addon_amount"
    -- We are not specifying these dynamic columns in the YML, therefore they will be automatically upper-cased
    -- by snowflake. We set them to upper case ourselves to avoid confusion when reading these queries.
    {% for partner in partners %}
    {% set partner_uppercase = "{}".format(partner.upper())  %}
    ,SUM(CASE WHEN "rateplan_partner_id" = '{{ partner }}' THEN "invoice_item_charge_amount" ELSE 0 END) AS "CHARGE_AMOUNT_{{ partner_uppercase }}"
    ,SUM(CASE WHEN "rateplan_partner_id" = '{{ partner }}' THEN "invoice_item_tax_amount" ELSE 0 END) AS "TAX_AMOUNT_{{ partner_uppercase }}"
    ,"CHARGE_AMOUNT_{{ partner_uppercase }}" + "TAX_AMOUNT_{{ partner_uppercase }}" AS "TOTAL_AMOUNT_{{ partner_uppercase }}"
    {% endfor %}
    ,ARRAY_AGG(DISTINCT "rateplan_partner_id") AS "addon_partner_list"
    ,ARRAY_CONSTRUCT(
        {% for partner in partners %}
        {% set partner_uppercase = "{}".format(partner.upper())  %}

        {{ ", " if not loop.first else "" }} CASE WHEN ARRAY_CONTAINS( '{{ partner }}'::variant, "addon_partner_list" )
                THEN OBJECT_CONSTRUCT(
                    'total_charge_amount', "CHARGE_AMOUNT_{{ partner_uppercase }}"
                    ,'total_tax_amount', "TAX_AMOUNT_{{ partner_uppercase }}"
                    ,'total_amount', "TOTAL_AMOUNT_{{ partner_uppercase }}"
                    ,'partner_id', '{{ partner }}'
                )
        END
        {% endfor %}
    ) AS "addon_partner_data"

FROM invoice_items_source
GROUP BY 1
