{{
    config(
        materialized='table',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_M_WH',
        tags=['presentation-invoice-items']
    )
}}

-- Here we merge the invoice item together with the exchange rates, we convert all charge amounts to dollars.
-- We finally calculate the charge&tax amounts in dollars and flag each row as being one_time or recurring.
-- todo: add test on recurring/one-time values for rateplan_charge_charge_type field, do we add it here or further upstream at source?

WITH usd_exchange AS (
    SELECT * FROM {{ ref_env('currency__daily_usd_fx_rates') }}
)

,invoice_item_joined AS (
    SELECT * FROM {{ ref('invoice_items__invoice_item_join') }}
)

SELECT
    invoice_item_joined."invoice_item_id"
    ,invoice_item_joined."invoice_id"
    ,invoice_item_joined."invoice_item_service_start_date"
    ,invoice_item_joined."invoice_item_service_end_date"
    ,invoice_item_joined."invoice_item_created_date"
    ,invoice_item_joined."invoice_item_charge_amount"
    ,invoice_item_joined."invoice_item_tax_amount"
    ,invoice_item_joined."invoice_item_applied_to_invoice_item_id"
    ,invoice_item_joined."rateplan_charge_id"
    ,invoice_item_joined."rateplan_id"
    ,invoice_item_joined."rateplan_charge_charge_type"
    ,invoice_item_joined."rateplan_charge_name"
    ,invoice_item_joined."rateplan_partner_id"
    ,invoice_item_joined."rateplan_partner_user_id"
    ,invoice_item_joined."rateplan_product_type"
    ,invoice_item_joined."entitlement_set_id"
    ,invoice_item_joined."billing_account_id"
    ,invoice_item_joined."subscription_id"
    ,invoice_item_joined."subscription_name"
    ,invoice_item_joined."billing_account_currency_code"
    ,usd_exchange."full_date" AS "fx_full_date"
    ,usd_exchange."currency_from"
    ,usd_exchange."currency_to"
    ,usd_exchange."exchange_rate"
    ,CASE
        WHEN invoice_item_joined."billing_account_currency_code" = 'USD' THEN invoice_item_joined."invoice_item_charge_amount"
        ELSE ROUND((invoice_item_joined."invoice_item_charge_amount" * usd_exchange."exchange_rate"), 2)
    END AS "usd_charge_amount"
    ,CASE
        WHEN invoice_item_joined."billing_account_currency_code" = 'USD' THEN invoice_item_joined."invoice_item_tax_amount"
        ELSE ROUND((invoice_item_joined."invoice_item_tax_amount" * usd_exchange."exchange_rate"), 2)
    END AS "usd_tax_amount"
    -- product types appeared in zuora (prod) for the first time on 2022-08-04, before then they are all nulls.
    -- Before 2022-08-04, one time charge types could only be PPV, after that date they can also be ADDONS
    ,CASE
        WHEN invoice_item_joined."rateplan_charge_charge_type" = 'Recurring' AND (invoice_item_joined."rateplan_product_type" IS NULL OR "rateplan_product_type" = 'subscription') THEN 'subscription'
        WHEN invoice_item_joined."rateplan_charge_charge_type" = 'OneTime' AND DATE_TRUNC(DAY, invoice_item_joined."rateplan_created_timestamp") < '2022-08-04' THEN 'ppv'
        ELSE invoice_item_joined."rateplan_product_type"
    END AS "invoice_item_product_type"
    ,GREATEST_IGNORE_NULLS(
        invoice_item_joined."invoice_item_updated_timestamp"
        ,invoice_item_joined."rateplan_charge_updated_timestamp"
        ,invoice_item_joined."subscription_id_updated_timestamp"
        ,invoice_item_joined."billing_account_last_updated_timestamp"
    ) AS "greatest_updated_timestamp"
    ,"invoice_tracking_id__skey"
    ,"invoice_source_system_name_derived__skey"
    ,CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
FROM invoice_item_joined
LEFT JOIN usd_exchange
    ON invoice_item_joined."billing_account_currency_code" = usd_exchange."currency_from"
    AND invoice_item_joined."invoice_item_created_date"::DATE = usd_exchange."full_date"
