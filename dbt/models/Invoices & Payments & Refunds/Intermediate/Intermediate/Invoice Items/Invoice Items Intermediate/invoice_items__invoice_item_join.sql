{{
    config(
        materialized='table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XL_WH',
        tags=['presentation-invoice-items']
    )
}}

WITH ev_invoice_item_source AS (
    SELECT * FROM {{ ref('dmp__invoice_items__invoice_item_join') }}
)

,zr_invoice_item_source AS (
    SELECT * FROM {{ ref('zr__invoice_items__invoice_item_join') }}
)

,zr_finalised_data AS (
    SELECT * FROM zr_invoice_item_source
    WHERE TRUE
        AND NOT EXISTS ( SELECT 1 FROM ev_invoice_item_source WHERE zr_invoice_item_source."invoice_id" = ev_invoice_item_source."invoice_id" )
)

,consolidated AS (
    SELECT
        *
        ,'ev' AS "invoice_item_source_of_data"
    FROM ev_invoice_item_source
    UNION ALL
    SELECT
        *
        ,'zr' AS "invoice_item_source_of_data"
    FROM zr_finalised_data
)

SELECT * FROM consolidated
