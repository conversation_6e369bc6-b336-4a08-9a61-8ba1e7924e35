{{
    config(
        materialized='table',
        unique_key='"invoice_item_id"',
        incremental_strategy='delete+insert',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['presentation-invoice-items']
    )
}}

WITH inv_item_before_join AS (
    SELECT * FROM {{ ref_env('staging__dmp__invoice_item_current') }}
    {% if is_incremental() %}
    WHERE TRUE
        AND "invoice_item_updated_timestamp" >= (SELECT DATEADD('day', -3, MAX("invoice_item_updated_timestamp")) FROM {{this}})
    {% endif %}
)

,zr_billing_account_mapping_data AS (
    SELECT
        "dazn_user_id"
        ,"billing_account_id"
    FROM {{ ref_env('staging__account_current') }}
)

SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,"invoice_item_id"
    ,"invoice_id"
    ,"invoice_item_service_start_date"
    ,"invoice_item_service_end_date"
    ,"invoice_item_created_date"
    ,COALESCE("invoice_item_charge_amount", 0) AS "invoice_item_charge_amount"
    ,"invoice_item_tax_amount"
    ,"subscription_id"
    ,"subscription_number"
    ,"invoice_item_applied_to_invoice_item_id"
    ,"rateplan_charge_id"
    ,"rateplan_id"
    ,"rateplan_charge_charge_type"
    ,"rateplan_charge_name"
    ,"rateplan_partner_id"
    ,"rateplan_partner_user_id"
    ,"rateplan_product_type"
    ,"rateplan_created_timestamp"
    ,"entitlement_set_id"
    ,COALESCE(zr_billing_account_mapping_data."billing_account_id", inv_item_before_join."billing_account_id") AS "billing_account_id"
    ,"subscription_name"
    ,"billing_account_currency_code"
    ,"invoice_item_updated_timestamp"
    ,"rateplan_charge_updated_timestamp"
    ,"subscription_id_updated_timestamp"
    ,"billing_account_last_updated_timestamp"
    ,"rateplan_addon_type"
    ,"invoice_item_charge_name"
    ,"data_source"
    ,"invoice_tracking_id__skey"
    ,"invoice_source_system_name_derived__skey"
FROM inv_item_before_join
LEFT JOIN zr_billing_account_mapping_data
    USING("dazn_user_id")
GROUP BY ALL
