{{
    config(
        materialized='incremental',
        unique_key='"invoice_item_id"',
        incremental_strategy='delete+insert',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_M_WH',
        tags=['presentation-invoice-items']
    )
}}

WITH invoice_item_source AS (
    SELECT
        "invoice_item_id"
        ,"invoice_id"
        ,"rateplan_charge_id"
        ,"invoice_item_service_start_date"
        ,"invoice_item_service_end_date"
        ,"invoice_item_created_timestamp"
        ,COALESCE("invoice_item_charge_amount", 0) AS "invoice_item_charge_amount"
        ,COALESCE("invoice_item_tax_amount", 0) AS "invoice_item_tax_amount"
        -- The subscription ID field is null when we have an invoice item from a one-time addon. We coalesce to the add-on specific sub id in order to populate this field.
        --,COALESCE("subscription_id", "invoice_item_addon_one_time_subscription_id") AS "subscription_id"
        ,"subscription_id"
        ,"subscription_number"
        ,"invoice_item_applied_to_invoice_item_id"
        ,"invoice_item_updated_timestamp"
        ,"invoice_item_addon_one_time_subscription_id"
        -- We assume that if this field is populated then it has to be an addon invoice item.
        ,"invoice_item_addon_one_time_subscription_id" IS NOT NULL AS "is_addon"
        ,"invoice_item_charge_name"
        ,"invoice_item_order_line_item_id"
        ,"invoice_item_source_item_type"
    FROM {{ ref_env('staging__zuora__invoice_item_current') }}
    {% if is_incremental() %}
    WHERE TRUE
        AND "invoice_item_updated_timestamp" >= (SELECT DATEADD('day', -3, MAX("invoice_item_updated_timestamp")) FROM {{ this }})
    {% endif %}
)

,rate_plan_charge_source AS (
    SELECT * FROM {{ ref_env('staging__zuora__rateplancharge_current') }}
)

,rate_plan_source AS (
    SELECT * FROM {{ ref_env('staging__zuora__rateplan_current') }}
)

,subscription_source AS (
    SELECT * FROM {{ ref_env('staging__zuora__subscription_id_current') }}
)

,account_source AS (
    SELECT * FROM {{ ref_env('staging__zuora__account_current') }}
)

,product_rate_plan AS (
    SELECT * FROM {{ ref_env('staging__zuora__productrateplan') }}
)

,addon_sub AS (
    SELECT
        inv_src."subscription_id" AS "subscription_id",
        inv_src."invoice_item_id" AS "inv_item_addon",
        COALESCE(rpc."entitlement_set_id", prp."entitlement_set_id") AS "entitlement_set_id",
        "rateplan_product_type",
        "rateplan_partner_id",
        "rateplan_partner_user_id",
        "rateplan_created_timestamp",
        "rateplan_addon_type" ,
        rpcc."rateplan_id",
        rpcc."rateplan_charge_id",
        "rateplan_charge_charge_type",
        "rateplan_charge_name"
    FROM invoice_item_source inv_src
    INNER JOIN  rate_plan_source rpc
        ON rpc."subscription_id" = inv_src."invoice_item_addon_one_time_subscription_id"
            AND "rateplan_product_type" = 'addon'
    LEFT JOIN product_rate_plan prp
        ON prp."product_rateplan_id" = rpc."product_rateplan_id"
    LEFT JOIN rate_plan_charge_source rpcc
        ON rpc."rateplan_id" = rpcc."rateplan_id"
            AND rpc."rateplan_created_timestamp"::DATE >= rpcc."rateplan_charge_effective_start_date"
            AND rpc."rateplan_created_timestamp"::DATE <=rpcc."rateplan_charge_effective_end_date"
    WHERE TRUE
        AND inv_src."is_addon" IS NOT NULL
    QUALIFY TRUE
        AND ROW_NUMBER() OVER (PARTITION BY inv_src."invoice_item_id",inv_src."subscription_id" ORDER BY "rateplan_created_timestamp" DESC) = 1
)

-- all the sources are "current" ie: they have one row per ID by definition. Left joining these tables on the ID can't
-- produce duplicates.
-- todo: Do we want to add a test on invoice_item_id even though in theory it should always be unique? How computationally intensive are these tests.
--Once all of the columns that we need are selected in the original statements, it is a simple left join on to the main table which in this case is the invoice item table

,order_line_item_source AS (
    SELECT * FROM {{ ref_env('staging__zuora__orderlineitem_current') }}
)

,product_rate_plan_charge_source AS (
    SELECT * FROM {{ ref_env('staging__zuora__productrateplancharge_current') }}
)

,subscription_name__scd AS (
    SELECT * FROM {{ ref_env('subscription_name__scd') }}
)

,subscription_extended AS (
    SELECT * FROM TRANSFORMATION_PROD.PRESENTATION.SUBSCRIPTION_EXTENDED
--     {{ ref_env('subscription_extended') }}
)

,sub_inv_item AS (
    SELECT
         CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
        ,invoice_item_source."invoice_item_id"
        ,invoice_item_source."invoice_id"
        ,invoice_item_source."invoice_item_service_start_date"
        ,invoice_item_source."invoice_item_service_end_date"
        ,DATE_TRUNC(day, invoice_item_source."invoice_item_created_timestamp") AS "invoice_item_created_date"
        ,invoice_item_source."invoice_item_charge_amount" AS "invoice_item_charge_amount"
        ,invoice_item_source."invoice_item_tax_amount"
        ,invoice_item_source."subscription_id"
        ,invoice_item_source."subscription_number"
        ,invoice_item_source."invoice_item_applied_to_invoice_item_id"
        ,COALESCE(rate_plan_charge_source."rateplan_charge_id",addon_sub."rateplan_charge_id") AS "rateplan_charge_id"
        ,COALESCE(rate_plan_charge_source."rateplan_id",addon_sub."rateplan_id") AS "rateplan_id"
        ,COALESCE(rate_plan_charge_source."rateplan_charge_charge_type",addon_sub."rateplan_charge_charge_type") AS "rateplan_charge_charge_type"
        ,COALESCE(rate_plan_charge_source."rateplan_charge_name",addon_sub."rateplan_charge_name") AS "rateplan_charge_name"
        ,COALESCE(rate_plan_source."rateplan_partner_id",addon_sub."rateplan_partner_id") AS "rateplan_partner_id"
        ,COALESCE(rate_plan_source."rateplan_partner_user_id",addon_sub."rateplan_partner_user_id") AS "rateplan_partner_user_id"
        -- This is a hack to tag any invoice items as addons even though the join to RPC fails due to a missing rpc in the invoice_item table.
        ,IFF(invoice_item_source."is_addon", 'addon', rate_plan_source."rateplan_product_type") AS "rateplan_product_type"
        ,COALESCE(rate_plan_source."rateplan_created_timestamp" ,addon_sub."rateplan_created_timestamp") AS "rateplan_created_timestamp"
        ,COALESCE(COALESCE(rate_plan_source."entitlement_set_id" ,addon_sub."entitlement_set_id" ),product_rate_plan."entitlement_set_id") AS "entitlement_set_id"
        ,subscription_source."billing_account_id"
        ,subscription_source."subscription_name"
        ,account_source."billing_account_currency_code"
        ,invoice_item_source."invoice_item_updated_timestamp"
        ,rate_plan_charge_source."rateplan_charge_updated_timestamp"
        ,subscription_source."subscription_id_updated_timestamp"
        ,account_source."billing_account_last_updated_timestamp"
        ,COALESCE(rate_plan_source."rateplan_addon_type" ,addon_sub."rateplan_addon_type" ) AS "rateplan_addon_type"
        ,invoice_item_source."invoice_item_charge_name"
        ,'rateplan' AS "data_source"
        ,rate_plan_source."rateplan_tracking_id" AS "invoice_tracking_id__skey"
        ,rate_plan_source."rateplan_source_system_name" AS  "invoice_source_system_name_derived__skey"
    FROM invoice_item_source
    LEFT JOIN rate_plan_charge_source
        ON rate_plan_charge_source."rateplan_charge_id" = invoice_item_source."rateplan_charge_id"
    LEFT JOIN rate_plan_source
        ON rate_plan_source."rateplan_id" = rate_plan_charge_source."rateplan_id"
    LEFT JOIN product_rate_plan
        ON  rate_plan_source."product_rateplan_id" = product_rate_plan."product_rateplan_id"
    LEFT JOIN subscription_source
        ON subscription_source."subscription_id" = invoice_item_source."subscription_id"
    LEFT JOIN account_source
        ON account_source."billing_account_id" = subscription_source."billing_account_id"
    LEFT JOIN addon_sub
        ON addon_sub."inv_item_addon" = invoice_item_source."invoice_item_id"
    WHERE TRUE
        AND COALESCE ("invoice_item_source_item_type",'NULL') !='OrderLineItem'
    --QUALIFY ROW_NUMBER() OVER (PARTITION BY "invoice_item_id" ORDER BY "invoice_item_updated_timestamp" DESC) = 1
)

,oli_inv_item AS (
    SELECT
         CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
        ,invoice_item_source."invoice_item_id"
        ,invoice_item_source."invoice_id"
        ,invoice_item_source."invoice_item_service_start_date"
        ,invoice_item_source."invoice_item_service_end_date"
        ,DATE_TRUNC(DAY, invoice_item_source."invoice_item_created_timestamp") AS "invoice_item_created_date"
        ,COALESCE(invoice_item_source."invoice_item_charge_amount",0) AS "invoice_item_charge_amount"
        ,COALESCE(invoice_item_source."invoice_item_tax_amount",0) AS "invoice_item_tax_amount"
        ,COALESCE(subscription_name__scd."subscription_id",subscription_extended."subscription_id") AS "subscription_id"
        ,invoice_item_source."subscription_number"
        ,invoice_item_source."invoice_item_applied_to_invoice_item_id"
        ,order_line_item_source."product_rateplan_charge_id" AS "rateplan_charge_id"
        ,order_line_item_source."order_line_item_id" AS "rateplan_id"
        ,product_rate_plan_charge_source."product_rateplan_charge_charge_type" AS "rateplan_charge_charge_type"
        ,product_rate_plan_charge_source."product_rateplan_charge_name" AS "rateplan_charge_name"
        ,NULL AS "rateplan_partner_id"
        ,NULL AS "rateplan_partner_user_id"
        ,'ppv' AS "rateplan_product_type"
        ,order_line_item_source."order_line_item_created_timestamp" AS "rateplan_created_timestamp"
        ,order_line_item_source."entitlement_set_id" AS "entitlement_set_id"
        ,order_line_item_source."billing_account_id"
        ,COALESCE(order_line_item_source."subscription_name",subscription_extended."subscription_name") AS "subscription_name"
        ,account_source."billing_account_currency_code"
        ,invoice_item_source."invoice_item_updated_timestamp"
        ,product_rate_plan_charge_source."product_rateplan_charge_updated_timestamp" AS "rateplan_charge_updated_timestamp"
        ,order_line_item_source."order_line_item_updated_timestamp" AS "subscription_id_updated_timestamp"
        ,account_source."billing_account_last_updated_timestamp"
        ,NULL AS "rateplan_addon_type"
        ,invoice_item_source."invoice_item_charge_name"
        ,'orderlineitem' AS "data_source"
        ,order_line_item_source."order_line_item_tracking_id" AS "invoice_tracking_id__skey"
        ,order_line_item_source."order_line_item_source_system" AS "invoice_source_system_name_derived__skey"
    FROM invoice_item_source
    LEFT JOIN order_line_item_source
        ON order_line_item_source."order_line_item_id" = invoice_item_source."invoice_item_order_line_item_id"
    LEFT JOIN product_rate_plan_charge_source
        ON order_line_item_source."product_rateplan_charge_id" = product_rate_plan_charge_source."product_rateplan_charge_id"
    LEFT JOIN account_source
        ON account_source."billing_account_id" = order_line_item_source."billing_account_id"
    LEFT JOIN  subscription_name__scd
        ON subscription_name__scd."subscription_name"=order_line_item_source."subscription_name"
        AND order_line_item_source."order_line_item_created_timestamp">=  subscription_name__scd."record_valid_from_timestamp"
        AND order_line_item_source."order_line_item_created_timestamp" < subscription_name__scd."record_valid_until_timestamp"
    LEFT JOIN subscription_extended
        ON order_line_item_source."order_line_item_order_id" = subscription_extended."subscription_order_id" and order_line_item_source."subscription_name" IS NULL
    WHERE TRUE
        AND COALESCE ("invoice_item_source_item_type",'NULL') ='OrderLineItem'
)
,final AS (
    SELECT * FROM sub_inv_item
    UNION ALL
    SELECT * FROM oli_inv_item
)

SELECT * FROM final
QUALIFY TRUE
    AND ROW_NUMBER() OVER (PARTITION BY "invoice_item_id" ORDER BY "invoice_item_updated_timestamp" DESC) = 1
