version: 2

models:
  - name: invoice_items__invoice_item_join
    description: "Joins all the zuora information together"
    columns:
      - name: invoice_item_id
        description: ""
        quote: true

      - name: invoice_id
        description: ""
        quote: true

      - name: invoice_item_service_start_date
        description: ""
        quote: true

      - name: invoice_item_service_end_date
        description: ""
        quote: true

      - name: invoice_item_created_date
        description: ""
        quote: true

      - name: invoice_item_charge_amount
        description: ""
        quote: true

      - name: invoice_item_tax_amount
        description: ""
        quote: true

      - name: subscription_id
        description: ""
        quote: true

      - name: subscription_number
        description: ""
        quote: true

      - name: invoice_item_applied_to_invoice_item_id
        description: ""
        quote: true

      - name: rateplan_charge_id
        description: ""
        quote: true

      - name: rateplan_id
        description: ""
        quote: true

      - name: rateplan_charge_charge_type
        description: ""
        quote: true

      - name: rateplan_charge_name
        description: ""
        quote: true

      - name: rateplan_partner_id
        description: ""
        quote: true

      - name: rateplan_partner_user_id
        description: ""
        quote: true

      - name: rateplan_product_type
        description: ""
        quote: true

      - name: rateplan_created_timestamp
        description: ""
        quote: true

      - name: entitlement_set_id
        description: ""
        quote: true

      - name: billing_account_id
        description: ""
        quote: true

      - name: subscription_name
        description: ""
        quote: true

      - name: billing_account_currency_code
        description: ""
        quote: true

      - name: invoice_item_updated_timestamp
        description: ""
        quote: true

      - name: rateplan_charge_updated_timestamp
        description: ""
        quote: true

      - name: subscription_id_updated_timestamp
        description: ""
        quote: true

      - name: billing_account_last_updated_timestamp
        description: ""
        quote: true

      - name: rateplan_addon_type
        description: ""
        quote: true

      - name: invoice_item_charge_name
        description: ""
        quote: true

      - name: data_source
        description: "data source whether it's from rateplan or orderlineitem."
        quote: true

      - name: invoice_tracking_id__skey
        description: "The Tracking ID associated to the landing page the user has purchased their AddOn through."
        quote: true

      - name: invoice_source_system_name_derived__skey
        description: "The Name of the Source System of the Rate Plan. For AddOns, this field is expected to remain unpopulated."
        quote: true
