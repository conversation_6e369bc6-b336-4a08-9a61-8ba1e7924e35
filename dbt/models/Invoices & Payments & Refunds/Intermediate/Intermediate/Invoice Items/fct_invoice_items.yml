version: 2

models:
  - name: fct_invoice_items
    description: "Enriched Invoice Item level table, containing information around invoice items and PPV flags, along with USD conversions."
    columns:
      - name: invoice_item_id
        description: "Id of the Invoice item"
        quote: true

      - name: invoice_id
        description: "Id of the Invoice"
        quote: true

      - name: invoice_item_service_start_date
        description: "Period the Invoice Item starts at"
        quote: true

      - name: invoice_item_service_end_date
        description: "Period the Invoice Item ends at"
        quote: true

      - name: invoice_item_created_date
        description: "Date the invoice item was created"
        quote: true

      - name: invoice_item_charge_amount
        description: "Amount charged for the invoice item"
        quote: true

      - name: invoice_item_tax_amount
        description: "Amount of tax for the invoice item"
        quote: true

      - name: subscription_id
        description: "Id of the Subscription"
        quote: true

      - name: invoice_item_applied_to_invoice_item_id
        description: ""
        quote: true

      - name: rateplan_charge_id
        description: "Id of the rate plan charge"
        quote: true

      - name: rateplan_id
        description: "Id of the rate plan"
        quote: true

      - name: rateplan_charge_charge_type
        description: "Charge Type, Recurring or OneTime"
        quote: true

      - name: rateplan_charge_name
        description: "The name of the RatePlan Charge directly relating to this invoice item"
        quote: true

      - name: rateplan_partner_id
        description: "The Partner ID of the Rate Plan, which refers to the Linear Channel the user has purchased"
        quote: true

      - name: rateplan_partner_user_id
        description: "The Partner User ID of the Rate Plan, which refers to the Linear Channel the user has purchased"
        quote: true

      - name: rateplan_product_type
        description: "The type of the product, can be ppv, addon or null."
        quote: true

      - name: entitlement_set_id
        description: "The entitlement set ID coming from the RatePlan directly relating to this Invoice Item"
        quote: true

      - name: billing_account_id
        description: "Id of the account"
        quote: true

      - name: subscription_name
        description: "Subscription Name also known as subscription Number"
        quote: true

      - name: billing_account_currency_code
        description: "Currency of the Invoice item"
        quote: true

      - name: fx_full_date
        description: "Full date of FX Conversion"
        quote: true

      - name: currency_from
        description: "Currency being converted"
        quote: true

      - name: currency_to
        description: "Currency converted to"
        quote: true

      - name: exchange_rate
        description: "FX Rate"
        quote: true

      - name: usd_charge_amount
        description: "Charge Amount in USD"
        quote: true

      - name: usd_tax_amount
        description: "Tax Amount in USD"
        quote: true

      - name: invoice_item_product_type
        description: "A categorical field. What product is linked to this invoice item, can be subscription, addon or ppv"
        quote: true

      - name: greatest_updated_timestamp
        description: "Greatest Updated Timestamp of Sources to build incrementally"
        quote: true

      - name: invoice_tracking_id__skey
        description: "The Tracking ID associated to the landing page the user has purchased their AddOn through."
        quote: true

      - name: invoice_source_system_name_derived__skey
        description: "The Name of the Source System of the Rate Plan. For AddOns, this field is expected to remain unpopulated."
        quote: true

      - name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the flow"
        quote: true
