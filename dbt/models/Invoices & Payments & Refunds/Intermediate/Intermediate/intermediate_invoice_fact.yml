version: 2

models:
  - name: fct_invoice_payments_agg
    description: ""
    columns:
      - name: invoice_id
        description: ""
        quote: true

      - name: first_payment_attempt_timestamp
        description: ""
        quote: true

      - name: last_payment_attempt_timestamp
        description: ""
        quote: true

      - name: first_payment_method_type_detail
        description: ""
        quote: true

      - name: last_payment_method_type_detail
        description: ""
        quote: true

      - name: first_payment_method_type
        description: ""
        quote: true

      - name: last_payment_method_type
        description: ""
        quote: true

      - name: collect_group
        description: ""
        quote: true

      - name: max_collect_attempt
        description: ""
        quote: true

      - name: first_payment_attempt_is_success
        description: ""
        quote: true

      - name: last_payment_attempt_is_success
        description: ""
        quote: true

      - name: first_payment_attempt_response
        description: ""
        quote: true

      - name: last_payment_attempt_response
        description: ""
        quote: true

      - name: p_greatest_updated_timestamp
        description: ""
        quote: true

      - name: distinct_payment_method_count
        description: ""
        quote: true

      - name: successful_payment_attempt_count
        description: ""
        quote: true

      - name: failed_payment_attempt_count
        description: ""
        quote: true

      - name: total_payment_attempt_count
        description: ""
        quote: true

      - name: first_payment_payment_source
        description: ""
        quote: true

  - name: fct_invoice_invoice_items_agg
    description: ""
    columns:
      - name: invoice_id
        description: ""
        quote: true

      - name: billing_account_currency_code
        description: ""
        quote: true

      - name: subscription_id
        description: ""
        quote: true

      - name: invoice_tracking_id__skey
        description: "The Tracking ID associated to the landing page the user has purchased their AddOn through."
        quote: true

      - name: invoice_source_system_name_derived__skey
        description: "The Name of the Source System of the Rate Plan. For AddOns, this field is expected to remain unpopulated."
        quote: true

      - name: entitlement_set_id_array
        description: "An array of all entitlement set IDs relating to this invoice"
        quote: true

      - name: subscription_entitlement_set_id_array
        description: "An array of all entitlement set IDs relating to the subscription under this invoice, can be multiple if the tier has changed"
        quote: true

      - name: ppv_entitlement_set_id_array
        description: "An array of all entitlement set IDs relating to PPVs under this invoice"
        quote: true

      - name: addon_entitlement_set_id_array
        description: "An array of all entitlement set IDs relating to Add Ons under this invoice, can be multiple if there are multiple Add Ons active"
        quote: true

      - name: has_applied_item
        description: ""
        quote: true

      - name: service_start_date
        description: ""
        quote: true

      - name: service_end_date
        description: ""
        quote: true

      - name: has_one_time_item
        description: ""
        quote: true

      - name: has_recurring_item
        description: ""
        quote: true

      - name: has_ppv_item
        description: ""
        quote: true

      - name: has_addon_item
        description: ""
        quote: true

      - name: total_recurring_amount
        description: ""
        quote: true

      - name: total_one_time_amount
        description: ""
        quote: true

      - name: charge_subscription_amount
        description: ""
        quote: true

      - name: tax_subscription_amount
        description: ""
        quote: true

      - name: total_subscription_amount
        description: ""
        quote: true

      - name: total_ppv_amount
        description: ""
        quote: true

      - name: total_addon_amount
        description: ""
        quote: true

      - name: invoice_item_count
        description: ""
        quote: true

      - name: subscription_count
        description: ""
        quote: true

      - name: addon_count
        description: ""
        quote: true

      - name: ppv_count
        description: ""
        quote: true

      - name: ii_greatest_updated_timestamp
        description: ""
        quote: true

  - name: fct_invoice_invoice_item_adjustment_agg
    description: ""
    columns:
      - name: invoice_id
        description: ""
        quote: true

      - name: taxation_adjustments
        description: ""
        quote: true

      - name: invoice_item_adjustments
        description: ""
        quote: true

      - name: invoice_adjustment_amount
        description: ""
        quote: true

      - name: iia_greatest_updated_timestamp
        description: ""
        quote: true

  - name: fct_invoice_addons
    description: ""
    columns:
      - name: invoice_id
        description: ""
        quote: true

      - name: has_addon_item
        description: ""
        quote: true

      - name: total_addon_amount
        description: ""
        quote: true

      - name: addon_partner_list
        description: ""
        quote: true

      - name: addon_partner_data
        description: ""
        quote: true
