{{
    config(
        materialized='table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['presentation-invoice-fact']
    )
}}

-- Adjustment affect invoice items. Can affect the amount or the tax.
-- This script aggregates all adjustment at invoice level and then applies it in the next step.
-- todo: consider making the item adjustments part of the invoice_item model rather than the invoice model.

WITH invoice_item_adjustment_source AS (
    SELECT * FROM {{ ref_env('staging__zuora__invoice_item_adjustment_current') }}
    -- We have to take the processed status' because we are taking sums of the adjustments for the final model, if we have adjustments that are not processed we will calculate the incorrect adjustment amounts.
    WHERE "invoice_item_adjustment_status" = 'Processed'
)

,aggregated AS (
    SELECT
        "invoice_id"
        -- Used for the check where sometimes we do not have a taxation adjustment for an invoice item adjustment
        -- Add test for Tax or Invoice Detail only
        ,SUM(IFF("invoice_item_adjustment_source_type" = 'Tax', 1, 0)) AS "taxation_adjustments"
        ,SUM(IFF("invoice_item_adjustment_source_type" = 'InvoiceDetail', 1, 0)) AS "invoice_item_adjustments"
        -- We take the credit and subtract from it the charges and this is the aggregate invoice's adjustment amount
        ,SUM(IFF("invoice_item_adjustment_type" = 'Credit',"invoice_item_adjustment_amount",0)) - SUM(IFF("invoice_item_adjustment_type" = 'Charge',"invoice_item_adjustment_amount",0)) AS "invoice_adjustment_amount"
        ,MAX("invoice_item_adjustment_updated_timestamp") AS "iia_greatest_updated_timestamp"
    FROM invoice_item_adjustment_source
    GROUP BY "invoice_id"
)

SELECT * FROM aggregated
