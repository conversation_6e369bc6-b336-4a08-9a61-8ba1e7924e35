{{
    config(
        materialized='table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_2XL_WH',
        tags=['presentation-invoice-fact']
    )
}}

WITH invoice_item_source AS (
    SELECT * FROM {{ ref('fct_invoice_items') }}
)

,windowed AS (
    SELECT
        "invoice_id"
        ,LAST_VALUE("billing_account_currency_code") IGNORE NULLS OVER (PARTITION BY "invoice_id" ORDER BY "invoice_item_created_date" ASC NULLS FIRST ) AS "billing_account_currency_code"
        -- When this is populated it means that the item has been applied to another item. This could be a free trial/giftcode.
        -- For example, a customer with a free trial will have two invoice items: One might be 9.99 and the other -9.99 with invoice_item_applied_to_invoice_item_id of the second being not null
        ,"invoice_item_applied_to_invoice_item_id"
        ,"invoice_item_service_start_date"
        ,"invoice_item_service_end_date"
        ,"invoice_item_product_type"
        ,"rateplan_charge_charge_type"
        ,"entitlement_set_id"
        ,CASE
            WHEN "rateplan_charge_charge_type" = 'Recurring' AND ("rateplan_product_type" IS NULL OR "rateplan_product_type" = 'subscription') THEN "entitlement_set_id"
            ELSE NULL
        END AS "subscription_entitlement_set_ids"
        ,CASE
            WHEN "rateplan_product_type" = 'ppv' OR ("rateplan_charge_charge_type" = 'OneTime' AND "invoice_item_created_date" < '2022-08-05') THEN "entitlement_set_id"
            ELSE NULL
        END AS "ppv_entitlement_set_ids"
        ,CASE
            WHEN "rateplan_product_type" = 'addon' THEN "entitlement_set_id"
            ELSE NULL
        END AS "addon_entitlement_set_ids"
        ,CASE
            WHEN "rateplan_product_type" = 'pass' THEN "entitlement_set_id"
            ELSE NULL
        END AS "pass_entitlement_set_ids"
        ,"invoice_item_charge_amount"
        ,"invoice_item_tax_amount"
        ,"invoice_item_charge_amount" + "invoice_item_tax_amount" AS "invoice_item_total_amount"
        ,"greatest_updated_timestamp" AS "ii_timestamp"
        -- the assumption is that a sub id doesn't change within an invoice, we take the last just in case.
        -- todo: can we verify that we only have one sub ID per invoice_id? If so we can avoid the last_value.
        ,LAST_VALUE("subscription_id") IGNORE NULLS OVER (PARTITION BY "invoice_id" ORDER BY "invoice_item_created_date" ASC NULLS FIRST ) AS "subscription_id"
        ,LAST_VALUE("invoice_tracking_id__skey") IGNORE NULLS OVER (PARTITION BY "invoice_id" ORDER BY "invoice_item_created_date" ASC NULLS FIRST ) AS "invoice_tracking_id__skey"
        ,LAST_VALUE("invoice_source_system_name_derived__skey") IGNORE NULLS OVER (PARTITION BY "invoice_id" ORDER BY "invoice_item_created_date" ASC NULLS FIRST ) AS "invoice_source_system_name_derived__skey"
    FROM invoice_item_source
)

-- TODO: given we group by several field, can we put a test in that ensures we get one invoice per row only?
--  billing_account_currency_code should be unique to invoice, the sub_id we're taking the last so has to be unique. We might not need a test.

-- Note: Addon information is coming from fct_invoice_addons.sql Invoice_item_join Intermediate model
SELECT
    "invoice_id"
    ,"billing_account_currency_code"
    ,"subscription_id"
    ,"invoice_tracking_id__skey"
    ,"invoice_source_system_name_derived__skey"
    ,MIN("invoice_item_service_start_date") AS "service_start_date" -- first start date across all invoice items
    ,MAX("invoice_item_service_end_date") AS "service_end_date" -- last end date across all invoice items
    ,ARRAY_AGG(DISTINCT "entitlement_set_id")
               WITHIN GROUP (
                   ORDER BY "entitlement_set_id") AS "entitlement_set_id_array"
    ,ARRAY_AGG(DISTINCT "subscription_entitlement_set_ids")
               WITHIN GROUP (
                   ORDER BY "subscription_entitlement_set_ids") AS "subscription_entitlement_set_id_array"
    ,ARRAY_AGG(DISTINCT "ppv_entitlement_set_ids")
               WITHIN GROUP (
                   ORDER BY "ppv_entitlement_set_ids") AS "ppv_entitlement_set_id_array"
    ,ARRAY_AGG(DISTINCT "addon_entitlement_set_ids")
               WITHIN GROUP (
                   ORDER BY "addon_entitlement_set_ids") AS "addon_entitlement_set_id_array"
    ,ARRAY_AGG(DISTINCT "pass_entitlement_set_ids")
               WITHIN GROUP (
                   ORDER BY "pass_entitlement_set_ids") AS "pass_entitlement_set_id_array"
    ,LEAST(1, COUNT_IF("invoice_item_applied_to_invoice_item_id" IS NOT NULL)) AS "has_applied_item" -- returns 1 if any invoice items have been applied
    ,LEAST(1, COUNT_IF("rateplan_charge_charge_type" = 'OneTime')) AS "has_one_time_item"
    ,LEAST(1, COUNT_IF("rateplan_charge_charge_type" = 'Recurring')) AS "has_recurring_item"
    ,LEAST(1, COUNT_IF("invoice_item_product_type" = 'ppv')) AS "has_ppv_item"
    ,LEAST(1, COUNT_IF("invoice_item_product_type" = 'pass')) AS "has_pass_item"

    -- todo, why use coalesce instead of ELSE? Possibly concern over the amounts being null?
    ,COALESCE(SUM(CASE WHEN "rateplan_charge_charge_type" = 'Recurring' THEN "invoice_item_total_amount" END) ,0) AS "total_recurring_amount"
    ,COALESCE(SUM(CASE WHEN "rateplan_charge_charge_type" = 'OneTime' THEN "invoice_item_total_amount" END) ,0) AS "total_one_time_amount"

    ,COALESCE(SUM(CASE WHEN "invoice_item_product_type" = 'subscription' THEN "invoice_item_charge_amount" END) ,0) AS "charge_subscription_amount"
    ,COALESCE(SUM(CASE WHEN "invoice_item_product_type" = 'subscription' THEN "invoice_item_tax_amount" END) ,0) AS "tax_subscription_amount"
    ,COALESCE(SUM(CASE WHEN "invoice_item_product_type" = 'subscription' THEN "invoice_item_total_amount" END) ,0) AS "total_subscription_amount"

    ,COALESCE(SUM(CASE WHEN "invoice_item_product_type" = 'pass' THEN "invoice_item_charge_amount" END) ,0) AS "charge_pass_amount"
    ,COALESCE(SUM(CASE WHEN "invoice_item_product_type" = 'pass' THEN "invoice_item_tax_amount" END) ,0) AS "tax_pass_amount"
    ,COALESCE(SUM(CASE WHEN "invoice_item_product_type" = 'pass' THEN "invoice_item_total_amount" END) ,0) AS "total_pass_amount"

    ,COALESCE(SUM(CASE WHEN "invoice_item_product_type" = 'ppv' THEN "invoice_item_charge_amount" END) ,0) AS "charge_ppv_amount"
    ,COALESCE(SUM(CASE WHEN "invoice_item_product_type" = 'ppv' THEN "invoice_item_tax_amount" END) ,0) AS "tax_ppv_amount"
    ,COALESCE(SUM(CASE WHEN "invoice_item_product_type" = 'ppv' THEN "invoice_item_total_amount" END) ,0) AS "total_ppv_amount"

    ,COUNT(DISTINCT CASE WHEN "invoice_item_product_type" = 'subscription' THEN "entitlement_set_id" ELSE NULL END) "subscription_count"
    ,COUNT(DISTINCT CASE WHEN "invoice_item_product_type" = 'pass' THEN "entitlement_set_id" ELSE NULL END) "pass_count"
    ,COUNT(DISTINCT CASE WHEN "invoice_item_product_type" = 'addon' THEN "entitlement_set_id" ELSE NULL END) "addon_count"
    ,COUNT(DISTINCT CASE WHEN "invoice_item_product_type" = 'ppv' THEN "entitlement_set_id" ELSE NULL END) "ppv_count"
    ,COUNT(*) AS "invoice_item_count" -- total number of invoice items.
    ,MAX("ii_timestamp") AS "ii_greatest_updated_timestamp" -- last updated timestamp across all invoice items
FROM windowed
GROUP BY 1, 2, 3, 4, 5
