{{
    config(
        materialized='table',
        database='TRANSFORMATION_' + snowflake_env(),
        schema='FREEMIUM',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_3XL_WH',
        tags=['presentation-playback-stream-freemium'],
        post_hook=['CREATE OR REPLACE TRANSIENT TABLE TRANSFORMATION_'+ snowflake_env()+'.FREEMIUM.CLONE_4_CDC_DAILY_ACQUISITIONS_DAZN_FREEMIUM CLONE TRANSFORMATION_'+ snowflake_env()+'.FREEMIUM.DAILY_ACQUISITIONS_DAZN_FREEMIUM']
    )
}}

WITH load_test_users AS (
    SELECT * FROM {{ ref_env('load_test_users') }}
)

,daily_users_intermediate AS (
    SELECT * FROM {{ ref_env('daily_users__intermediate') }}
    WHERE TRUE
        AND "batch_date" >= '2023-12-06'
)

,staging__segment__user_events_users AS (
    SELECT * FROM {{ ref_env('staging__segment__user_events_users') }}
)

,user_status_dim AS (
    SELECT * FROM {{ ref_env('user_status__dim') }}
)

,country_dim AS (
    SELECT * FROM {{ ref_env('region_dimension') }}
)

,daily_users_dazn_subs AS (
    SELECT * FROM {{ ref_env('daily_users_dazn_subs') }}
)

,daily_acquisitions_content_attribution AS (
    SELECT * FROM {{ ref('daily_acquisitions_content_attribution')}}
)

,daily_users_excluding_load_test_users AS (
    SELECT * FROM daily_users_intermediate
    LEFT JOIN load_test_users
        ON daily_users_intermediate."dazn_user_id" = load_test_users."dazn_id"
    WHERE TRUE
        AND load_test_users."dazn_id" IS NULL
)

,daily_users_excluding_load_test_users_with_no_dazn_subscription AS (
    SELECT * FROM daily_users_excluding_load_test_users
    WHERE TRUE
        -- the customer shall not have a DAZN subscription currently
        AND "has_dazn_subscription" IS DISTINCT FROM TRUE
)

,daily_users_intermediate_filtered AS (
    SELECT * FROM daily_users_excluding_load_test_users_with_no_dazn_subscription
    QUALIFY TRUE
        AND ROW_NUMBER() OVER (PARTITION BY "dazn_user_id" ORDER BY "batch_date" ASC) = 1
)

,final AS (
    SELECT
        daily_users_intermediate_filtered."batch_date" AS "freemium_start_date"
        ,daily_users_intermediate_filtered."dazn_user_id" AS "dazn_user_id"
        ,daily_users_intermediate_filtered."viewer_id" AS "viewer_id"
        ,country_dim."country" AS "country"
        ,CASE
            WHEN country_dim."country" IN ('Taiwan', 'France') THEN country_dim."country"
            ELSE country_dim."territory"
        END AS "user_territory"
        ,CASE
            WHEN  user_status_dim."source_type" = 'Freemium' THEN 'DAZN_Freemium_registered'
--             dazn_user who are available in daily users dazn subs are considered as frozen
            WHEN daily_users_dazn_subs."dazn_user_id" IS NOT NULL THEN 'Frozen'
            ELSE 'Partial'
        END AS "Freemium_Type"
        ,daily_acquisitions_content_attribution."attribution_competition_name" AS "attribution_competition_name"
        ,daily_acquisitions_content_attribution."attribution_fixture_name" AS "attribution_fixture_name"
        ,daily_acquisitions_content_attribution."attribution_article_type" AS "attribution_article_type"
        ,CURRENT_TIMESTAMP() AS "record_updated_timestamp"
    FROM daily_users_intermediate_filtered
    LEFT JOIN user_status_dim
        ON daily_users_intermediate_filtered."user_status__skey" = user_status_dim."user_status__skey"
    LEFT JOIN staging__segment__user_events_users
        ON daily_users_intermediate_filtered."dazn_user_id" = staging__segment__user_events_users."dazn_user_id"
    LEFT JOIN country_dim
        ON COALESCE(user_status_dim."home_country_code",staging__segment__user_events_users."home_country_code") = country_dim."join_key"
    LEFT JOIN daily_acquisitions_content_attribution
        ON daily_users_intermediate_filtered."dazn_user_id" = daily_acquisitions_content_attribution."dazn_user_id"
    LEFT JOIN daily_users_dazn_subs
        ON daily_users_intermediate_filtered."dazn_user_id" = daily_users_dazn_subs."dazn_user_id"
)

SELECT * FROM final
