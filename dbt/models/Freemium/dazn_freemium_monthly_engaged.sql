{{
    config(
        materialized='table',
        database='TRANSFORMATION_' + snowflake_env(),
        schema='FREEMIUM',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_M_WH',
        tags=['presentation-playback-stream-freemium'],
        post_hook=['CREATE OR REPLACE TRANSIENT TABLE TRANSFORMATION_'+ snowflake_env()+'.FREEMIUM.CLONE_4_CDC_DAZN_FREEMIUM_MONTHLY_ENGAGED CLONE TRANSFORMATION_'+ snowflake_env()+'.FREEMIUM.DAZN_FREEMIUM_MONTHLY_ENGAGED']
    )
}}

WITH daily_view_dazn_freemium AS (
    SELECT * FROM {{ ref('daily_view_dazn_freemium') }}
)

,distinct_count_month AS (
    SELECT
        DATE_TRUNC('month', "batch_date") AS "month"
        ,"country"
        ,"user_territory"
        ,"Freemium_Type"
        ,COUNT(DISTINCT "dazn_user_id") AS "unique_users"
    FROM daily_view_dazn_freemium
    GROUP BY ALL
)

,dazn_freemium_streams AS (
    SELECT * FROM {{ ref('dazn_freemium_streams') }}
)

,engaged_days AS (
    SELECT
        "dazn_user_id"
        ,"country"
        ,"user_territory"
        ,DATE_TRUNC('month', "stream_date") AS "stream_month"
        ,"Freemium_Type_adj"
        ,COUNT(DISTINCT "stream_date") AS "engaged_days"
    FROM dazn_freemium_streams
    WHERE TRUE
        AND "Freemium_Type_adj" IS NOT NULL
    GROUP BY ALL
)

,one_day AS (
    SELECT
        "Freemium_Type_adj"
        ,"country"
        ,"user_territory"
        ,"stream_month"
        ,COUNT(DISTINCT "dazn_user_id") AS "user_count_1_engaged_day"
    FROM engaged_days
    WHERE TRUE
        AND "engaged_days" = 1
    GROUP BY ALL
)

,two_to_three AS (
    SELECT
        "Freemium_Type_adj"
        ,"country"
        ,"user_territory"
        ,"stream_month"
        ,COUNT(DISTINCT "dazn_user_id") AS "user_count_2_to_3_engaged_days"
    FROM engaged_days
    WHERE TRUE
        AND "engaged_days" > 1 AND "engaged_days" <= 3
    GROUP BY ALL
)

,three_to_five AS (
    SELECT
        "Freemium_Type_adj"
        ,"country"
        ,"user_territory"
        ,"stream_month"
        ,COUNT(DISTINCT "dazn_user_id") AS "user_count_4_to_5_engaged_days"
    FROM engaged_days
    WHERE TRUE
        AND "engaged_days" > 3 AND "engaged_days" <= 5
    GROUP BY ALL
)

,five_to_ten AS (
    SELECT
        "Freemium_Type_adj"
        ,"country"
        ,"user_territory"
        ,"stream_month"
        ,COUNT(DISTINCT "dazn_user_id") AS "user_count_6_to_10_engaged_days"
    FROM engaged_days
    WHERE TRUE
        AND "engaged_days" > 5 AND "engaged_days" <= 10
    GROUP BY ALL
)

,more_than_10 AS (
    SELECT
        "Freemium_Type_adj"
        ,"country"
        ,"user_territory"
        ,"stream_month"
        ,COUNT(DISTINCT "dazn_user_id") AS "user_count_more_than_10_engaged_days"
    FROM engaged_days
    WHERE TRUE
        AND "engaged_days" > 10
    GROUP BY ALL
)

,final AS (
    SELECT
        a.*
        , one_day."user_count_1_engaged_day" AS "user_count_1_engaged_day"
        , two_to_three."user_count_2_to_3_engaged_days" AS "user_count_2_to_3_engaged_days"
        , three_to_five."user_count_4_to_5_engaged_days" AS "user_count_4_to_5_engaged_days"
        , five_to_ten."user_count_6_to_10_engaged_days" AS "user_count_6_to_10_engaged_days"
        , more_than_10."user_count_more_than_10_engaged_days" AS "user_count_more_than_10_engaged_days"
        , (IFNULL("unique_users", 0)-IFNULL("user_count_1_engaged_day", 0)-IFNULL("user_count_2_to_3_engaged_days", 0)-IFNULL("user_count_4_to_5_engaged_days", 0)-IFNULL("user_count_6_to_10_engaged_days", 0)-IFNULL("user_count_more_than_10_engaged_days", 0)) AS "not_engaged"
        ,CURRENT_TIMESTAMP() AS "record_updated_timestamp"
    FROM distinct_count_month a
    LEFT JOIN one_day
        ON a."month" = one_day."stream_month" AND a."Freemium_Type" = one_day."Freemium_Type_adj" AND a."country" = one_day."country" AND a."user_territory" = one_day."user_territory"
    LEFT JOIN two_to_three
        ON a."month" = two_to_three."stream_month" AND a."Freemium_Type" = two_to_three."Freemium_Type_adj" AND a."country" = two_to_three."country" AND a."user_territory" = two_to_three."user_territory"
    LEFT JOIN three_to_five
        ON a."month" = three_to_five."stream_month" AND a."Freemium_Type" = three_to_five."Freemium_Type_adj" AND a."country" = three_to_five."country" AND a."user_territory" = three_to_five."user_territory"
    LEFT JOIN five_to_ten
        ON a."month" = five_to_ten."stream_month" AND a."Freemium_Type" = five_to_ten."Freemium_Type_adj" AND a."country" = five_to_ten."country" AND a."user_territory" = five_to_ten."user_territory"
    LEFT JOIN more_than_10
        ON a."month" = more_than_10."stream_month" AND a."Freemium_Type" = more_than_10."Freemium_Type_adj" AND a."country" = more_than_10."country" AND a."user_territory" = more_than_10."user_territory"
)

SELECT * FROM final
