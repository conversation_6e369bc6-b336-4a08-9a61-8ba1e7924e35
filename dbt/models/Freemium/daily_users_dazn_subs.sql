{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"dazn_user_id"',
        database='TRANSFORMATION_' + snowflake_env(),
        schema='FREEMIUM',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_M_WH',
        tags=['presentation-playback-stream-freemium'],
        post_hook=['CREATE OR REPLACE TRANSIENT TABLE TRANSFORMATION_'+ snowflake_env()+'.FREEMIUM.CLONE_4_CDC_DAILY_USERS_DAZN_SUBS CLONE TRANSFORMATION_'+ snowflake_env()+'.FREEMIUM.DAILY_USERS_DAZN_SUBS']
    )
}}

{% set subscription_mart_variables = subscription_mart_variables() %}

WITH load_test_users AS (
    SELECT * FROM {{ ref_env('load_test_users') }}
)

,clone_daily_users_dazn_subs AS (
    SELECT * FROM {{ ref('clone_4_cdc_daily_users_dazn_subs') }}
)

,subs_daily_count_intermediate_fact AS (
    SELECT * FROM {{ ref_env('subscription_daily_counts_intermediate__fact') }} as a
    WHERE TRUE
        AND "dazn_user_id" IS NOT NULL
    {% if is_incremental() %}
--         AND "batch_date" BETWEEN DATEADD('day', -{{ subscription_mart_variables.rebuild_days}}-3, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
        AND NOT EXISTS (
            SELECT 1 FROM clone_daily_users_dazn_subs
            WHERE TRUE
                AND clone_daily_users_dazn_subs."dazn_user_id" = a."dazn_user_id"
        )
    {% endif %}
)

,subs_daily_count_intermediate_fact_filtered AS (
    SELECT * FROM subs_daily_count_intermediate_fact
    LEFT JOIN load_test_users
        ON subs_daily_count_intermediate_fact."dazn_user_id" = load_test_users."dazn_id"
    WHERE TRUE
        AND load_test_users."dazn_id" IS NULL
)

,subs_daily_status_dim AS (
    SELECT * FROM {{ ref_env('subscription_daily_status__dim') }}
)

,subs_info_dim AS (
    SELECT * FROM {{ ref_env('subscription_mart_info__dim') }}
)

,subs_info_dim_filtered AS (
    SELECT * FROM subs_info_dim
    WHERE TRUE
        AND "subscription_product_group" = 'DAZN'
)

,final AS (
    SELECT
        subs_daily_count_intermediate_fact_filtered."dazn_user_id"
        ,MIN("batch_date") as "first_sub_start_date"
        ,CURRENT_TIMESTAMP() AS "record_updated_timestamp"
    FROM subs_daily_count_intermediate_fact_filtered
    INNER JOIN subs_info_dim_filtered
        ON subs_daily_count_intermediate_fact_filtered."subscription_info__skey" = subs_info_dim_filtered."subscription_info__skey"
    LEFT JOIN subs_daily_status_dim
        ON subs_daily_count_intermediate_fact_filtered."subscription_daily_status__skey"= subs_daily_status_dim."subscription_daily_status__skey"
    WHERE TRUE
        AND subs_daily_status_dim."has_active_free_trial" IS DISTINCT FROM TRUE
        AND subs_daily_status_dim."is_paused" IS DISTINCT FROM TRUE
--         AND (subs_daily_status_dim."has_active_free_trial" = FALSE
--                 OR subs_daily_status_dim."has_active_free_trial" IS NULL
--         )
--         AND (subs_daily_status_dim."is_paused" = FALSE
--                 OR subs_daily_status_dim."is_paused" IS NULL
--         )
    GROUP BY ALL
)

SELECT * FROM final
