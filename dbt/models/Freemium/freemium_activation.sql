{{
    config(
        materialized='table',
        database='TRANSFORMATION_' + snowflake_env(),
        schema='FREEMIUM',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['presentation-playback-stream-freemium']
    )
}}

WITH dazn_freemium_streams AS (
    SELECT * FROM {{ ref('dazn_freemium_streams') }}
)

,first_engaged AS ( -- this is relevant for frozen and partials users
    SELECT
        "stream_date" AS "activation_date"
        ,"user_territory"
        ,"country"
        ,"viewer_id"
        ,"dazn_user_id"
        ,"Freemium_Type_adj" AS "Freemium_Type"
    FROM dazn_freemium_streams
    WHERE TRUE
        AND "Freemium_Type_adj" IN ('Frozen', 'Partial','Channels_paid')
    QUALIFY TRUE
        AND ROW_NUMBER() OVER (PARTITION BY "viewer_id" ORDER BY "stream_date" ASC) = 1
)

,daily_acquisitions_dazn_freemium AS (
    SELECT * FROM {{ ref('daily_acquisitions_dazn_freemium') }}
)

,first_acquisition AS ( -- this is relevant for freemium registrations
    SELECT
        "freemium_start_date" AS "activation_date"
        ,"user_territory"
        ,"country"
        ,"viewer_id"
        ,"dazn_user_id"
        ,"Freemium_Type"
    FROM daily_acquisitions_dazn_freemium
    WHERE TRUE
        AND "Freemium_Type" IN ('DAZN_Freemium_registered')
)

,final AS (
    SELECT * FROM first_engaged
    UNION ALL
    SELECT * FROM first_acquisition
)

,updated_final AS (
    SELECT *
        ,CURRENT_TIMESTAMP() AS "record_updated_timestamp"
    FROM final
)

SELECT * FROM updated_final
