{{
    config(
        materialized='table',
        database='TRANSFORMATION_' + snowflake_env(),
        schema='FREEMIUM',
        cluster_by=['"batch_date"'],
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_2XL_WH',
        tags=['presentation-playback-stream-freemium'],
        post_hook=['CREATE OR REPLACE TRANSIENT TABLE TRANSFORMATION_'+ snowflake_env()+'.FREEMIUM.CLONE_4_CDC_DAILY_VIEW_DAZN_FREEMIUM CLONE TRANSFORMATION_'+ snowflake_env()+'.FREEMIUM.DAILY_VIEW_DAZN_FREEMIUM']
    )
}}

WITH load_test_users AS (
    SELECT * FROM {{ ref_env('load_test_users') }}
)

,daily_users_intermediate AS (
    SELECT * FROM {{ ref_env('daily_users__intermediate') }}
    WHERE TRUE
        AND "batch_date" >= '2023-12-06'
)

,daily_users_intermediate_excluding_load_test_users AS (
    SELECT * FROM daily_users_intermediate
    LEFT JOIN load_test_users
        ON daily_users_intermediate."dazn_user_id" = load_test_users."dazn_id"
    WHERE TRUE
        AND load_test_users."dazn_id" IS NULL
)

,daily_users_intermediate_filtered AS (
    SELECT * FROM daily_users_intermediate_excluding_load_test_users
    WHERE TRUE
        -- the customer shall not have a DAZN subscription currently
        AND "has_dazn_subscription" IS DISTINCT FROM TRUE
)

,daily_users_dazn_subs AS (
    SELECT * FROM {{ ref_env('daily_users_dazn_subs') }}
)

,user_status_dim AS (
    SELECT * FROM {{ ref_env('user_status__dim') }}
)
--
-- ,staging__segment__user_events_users AS (
--     SELECT * FROM {{ ref_env('staging__segment__user_events_users') }}
-- )

,country_dim AS (
    SELECT * FROM {{ ref_env('region_dimension') }}
)

,dazn_freemium_daily_user_product_group_info AS (
    SELECT * FROM {{ ref('dazn_freemium_daily_user_product_group_info') }}
)

,output_daily_users_freemium AS (
    SELECT
        daily_users_intermediate_filtered."batch_date" AS "batch_date"
        ,LAG("batch_date", 1) OVER (PARTITION BY daily_users_intermediate_filtered."dazn_user_id" ORDER BY "batch_date") AS "batch_date_lagged"
        ,daily_users_intermediate_filtered."dazn_user_id" AS "dazn_user_id"
        ,daily_users_intermediate_filtered."viewer_id" AS "viewer_id"
        ,country_dim."country" AS "country"
        ,CASE
            WHEN country_dim."country" IN ('Taiwan', 'France') THEN country_dim."country"
            ELSE country_dim."territory"
        END AS "user_territory"
        ,CASE
            WHEN user_status_dim."source_type" = 'Freemium' THEN 'DAZN_Freemium_registered'
            WHEN dazn_freemium_daily_user_product_group_info."channels_paid_definition" = 'Channels_paid' THEN 'Channels_paid'
            WHEN daily_users_intermediate_filtered."batch_date" >= daily_users_dazn_subs."first_sub_start_date" THEN 'Frozen'
--             WHEN daily_users_intermediate_filtered."dazn_user_id" IN (SELECT "dazn_user_id" FROM daily_users_dazn_subs GROUP BY 1) THEN 'Frozen'
            ELSE 'Partial'
        END AS "Freemium_Type"
    FROM daily_users_intermediate_filtered
    LEFT JOIN user_status_dim
        ON daily_users_intermediate_filtered."user_status__skey" = user_status_dim."user_status__skey"
--     LEFT JOIN staging__segment__user_events_users
--         ON daily_users_intermediate_filtered."dazn_user_id" = staging__segment__user_events_users."dazn_user_id"
    LEFT JOIN country_dim
        ON /*COALESCE(*/user_status_dim."home_country_code"/*,staging__segment__user_events_users."home_country_code")*/  = country_dim."join_key"
    LEFT JOIN daily_users_dazn_subs
        ON daily_users_intermediate_filtered."dazn_user_id" = daily_users_dazn_subs."dazn_user_id"
    LEFT JOIN dazn_freemium_daily_user_product_group_info
        ON daily_users_intermediate_filtered."dazn_user_id" = dazn_freemium_daily_user_product_group_info."product_group_dazn_user_id"
            AND daily_users_intermediate_filtered."batch_date" = dazn_freemium_daily_user_product_group_info."product_group_batch_date"
)

,final AS (
    SELECT
        *
        ,LAG("Freemium_Type", 1) OVER (PARTITION BY "dazn_user_id" ORDER BY "batch_date") AS "Freemium_Type_lagged"
        -- bringing through the lagged freemium statuts
        ,CURRENT_TIMESTAMP() AS "record_updated_timestamp"
    FROM output_daily_users_freemium
)

SELECT * FROM final
