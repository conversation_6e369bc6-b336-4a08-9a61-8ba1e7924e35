{{
    config(
        materialized='table',
        database='TRANSFORMATION_' + snowflake_env(),
        schema='FREEMIUM',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_M_WH',
        tags=['presentation-playback-stream-freemium'],
        post_hook=['CREATE OR REPLACE TRANSIENT TABLE TRANSFORMATION_'+ snowflake_env()+'.FREEMIUM.CLONE_4_CDC_DAZN_FREEMIUM_WEEKLY_ENGAGED CLONE TRANSFORMATION_'+ snowflake_env()+'.FREEMIUM.DAZN_FREEMIUM_WEEKLY_ENGAGED']
    )
}}

WITH daily_view_dazn_freemium AS (
    SELECT * FROM {{ ref('daily_view_dazn_freemium') }}
)

,distinct_count_weekly AS (
    SELECT
        DATE_TRUNC('week', "batch_date") AS "week"
        ,"country"
        ,"user_territory"
        ,"Freemium_Type"
        ,COUNT(DISTINCT "dazn_user_id") AS "unique_users"
    FROM daily_view_dazn_freemium
    GROUP BY ALL
)

,dazn_freemium_streams AS (
    SELECT * FROM {{ ref('dazn_freemium_streams') }}
)

,engaged_days AS (
    SELECT
        "dazn_user_id"
        ,"country"
        ,"user_territory"
        ,DATE_TRUNC('week', "stream_date") AS "stream_week"
        ,"Freemium_Type_adj"
        ,COUNT(DISTINCT "stream_date") AS "engaged_days"
    FROM dazn_freemium_streams
    WHERE TRUE
        AND "Freemium_Type_adj" IS NOT NULL
    GROUP BY ALL
)

,one_day AS (
    SELECT
        "Freemium_Type_adj"
        ,"country"
        ,"user_territory"
        ,"stream_week"
        ,COUNT(DISTINCT "dazn_user_id") AS "user_count_1_engaged_day"
    FROM engaged_days
    WHERE TRUE
        AND "engaged_days" = 1
    GROUP BY ALL
)

,two_day AS (
    SELECT
        "Freemium_Type_adj"
        ,"country"
        ,"user_territory"
        ,"stream_week"
        ,COUNT(DISTINCT "dazn_user_id") AS "user_count_2_engaged_days"
    FROM engaged_days
    WHERE TRUE
        AND "engaged_days" = 2
    GROUP BY ALL
)

,three_day AS (
    SELECT
        "Freemium_Type_adj"
        ,"country"
        ,"user_territory"
        ,"stream_week"
        ,COUNT(DISTINCT "dazn_user_id") AS "user_count_3_engaged_days"
    FROM engaged_days
    WHERE TRUE
        AND "engaged_days" = 3
    GROUP BY ALL
)

,four_day AS (
    SELECT
        "Freemium_Type_adj"
        ,"country"
        ,"user_territory"
        ,"stream_week"
        ,COUNT(DISTINCT "dazn_user_id") AS "user_count_4_engaged_days"
    FROM engaged_days
    WHERE TRUE
        AND "engaged_days" = 4
    GROUP BY ALL
)

,five_day AS (
    SELECT
        "Freemium_Type_adj"
        ,"country"
        ,"user_territory"
        ,"stream_week"
        ,COUNT(DISTINCT "dazn_user_id") AS "user_count_5_engaged_days"
    FROM engaged_days
    WHERE TRUE
        AND "engaged_days" = 5
    GROUP BY ALL
)

,six_day AS (
    SELECT
        "Freemium_Type_adj"
        ,"country"
        ,"user_territory"
        ,"stream_week"
        ,COUNT(DISTINCT "dazn_user_id") AS "user_count_6_engaged_days"
    FROM engaged_days
    WHERE TRUE
        AND "engaged_days" = 6
    GROUP BY ALL
)

,seven_day AS (
    SELECT
        "Freemium_Type_adj"
        ,"country"
        ,"user_territory"
        ,"stream_week"
        ,COUNT(DISTINCT "dazn_user_id") AS "user_count_7_engaged_days"
    FROM engaged_days
    WHERE TRUE
        AND "engaged_days" = 7
    GROUP BY ALL
)

,final AS (
    SELECT
        a.*
        ,one_day."user_count_1_engaged_day" AS "user_count_1_engaged_day"
        ,two_day."user_count_2_engaged_days" AS "user_count_2_engaged_days"
        ,three_day."user_count_3_engaged_days" AS "user_count_3_engaged_days"
        ,four_day."user_count_4_engaged_days" AS "user_count_4_engaged_days"
        ,five_day."user_count_5_engaged_days" AS "user_count_5_engaged_days"
        ,six_day."user_count_6_engaged_days" AS "user_count_6_engaged_days"
        ,seven_day."user_count_7_engaged_days" AS "user_count_7_engaged_days"
        ,(IFNULL("unique_users", 0)-IFNULL("user_count_1_engaged_day", 0)-IFNULL("user_count_2_engaged_days", 0)-IFNULL("user_count_3_engaged_days", 0)-IFNULL("user_count_4_engaged_days", 0)-IFNULL("user_count_5_engaged_days", 0)-IFNULL("user_count_6_engaged_days", 0)-IFNULL("user_count_7_engaged_days", 0)) AS "not_engaged"
        ,CURRENT_TIMESTAMP() AS "record_updated_timestamp"
    FROM distinct_count_weekly a
        LEFT JOIN one_day
    ON a."week" = one_day."stream_week" AND a."Freemium_Type" = one_day."Freemium_Type_adj" AND a."country" = one_day."country" AND a."user_territory" = one_day."user_territory"
        LEFT JOIN two_day
        ON a."week" = two_day."stream_week" AND a."Freemium_Type" = two_day."Freemium_Type_adj" AND a."country" = two_day."country" AND a."user_territory" = two_day."user_territory"
        LEFT JOIN three_day
        ON a."week" = three_day."stream_week" AND a."Freemium_Type" = three_day."Freemium_Type_adj" AND a."country" = three_day."country" AND a."user_territory" = three_day."user_territory"
        LEFT JOIN four_day
        ON a."week" = four_day."stream_week" AND a."Freemium_Type" = four_day."Freemium_Type_adj" AND a."country" = four_day."country" AND a."user_territory" = four_day."user_territory"
        LEFT JOIN five_day
        ON a."week" = five_day."stream_week" AND a."Freemium_Type" = five_day."Freemium_Type_adj" AND a."country" = five_day."country" AND a."user_territory" = five_day."user_territory"
        LEFT JOIN six_day
        ON a."week" = six_day."stream_week" AND a."Freemium_Type" = six_day."Freemium_Type_adj" AND a."country" = six_day."country" AND a."user_territory" = six_day."user_territory"
        LEFT JOIN seven_day
        ON a."week" = seven_day."stream_week" AND a."Freemium_Type" = seven_day."Freemium_Type_adj" AND a."country" = seven_day."country" AND a."user_territory" = seven_day."user_territory"
)

SELECT * FROM final
