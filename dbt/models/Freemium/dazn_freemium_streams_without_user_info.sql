{{
    config(
        materialized='table',
        database='TRANSFORMATION_' + snowflake_env(),
        schema='FREEMIUM',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_M_WH',
        cluster_by=['"stream_date"'],
        tags=['presentation-playback-stream-freemium']
    )
}}

WITH playback_stream_fact AS (
    SELECT * FROM {{ ref_env('MART__PLAYBACK_STREAM__FACT') }}
    WHERE TRUE
        AND "playback_stream_date" >= '2023-12-06'
        {% if is_incremental() %}
        AND "playback_stream_date" BETWEEN DATEADD('day', -7, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
        {% endif %}
)

,content_item_dim AS (
    SELECT * FROM {{ ref_env('MART__CONTENT_ITEM__DIM') }}
)

,content_item_dim_filtered AS (
    SELECT * FROM content_item_dim
    WHERE TRUE
        AND "competition_name" IS DISTINCT FROM 'NFL'
)

,load_test_users AS (
    SELECT * FROM {{ ref_env('load_test_users') }}
)

,customer_identity_dim AS (
    SELECT * FROM {{ ref_env('MART__CUSTOMER_IDENTITY__DIM') }}
)

,customer_identity_dim_filtered AS (
    SELECT * FROM customer_identity_dim
    LEFT JOIN load_test_users
    ON customer_identity_dim."dazn_user_id" = load_test_users."dazn_id"
    WHERE TRUE
        AND load_test_users."dazn_id" IS NULL
)

,entitlements_dim AS (
    SELECT * FROM {{ ref_env('MART__ENTITLEMENTS__DIM') }}
)

,playback_stream_location_dim AS (
    SELECT * FROM {{ ref_env('MART__PLAYBACK_STREAM_LOCATION__DIM') }}
)

,article_dim AS (
    SELECT * FROM {{ ref_env('mart__gmr_article__dim') }}
)

,article_dim_filtered AS (
    SELECT * FROM article_dim
    WHERE TRUE
        AND "article_entitlement_ids" = '[]'
)

,user_status_dim AS (
    SELECT * FROM {{ ref_env('user_status__dim') }}
)

,country_dim AS (
    SELECT * FROM {{ ref_env('region_dimension') }}
)

,device_info_dim AS (
    SELECT * FROM {{ ref_env('MART__DEVICE_INFO__DIM') }}
 )

,streaming_data AS (
    SELECT
        playback_stream_fact."playback_duration_milliseconds" AS "playback_duration_milliseconds"
        ,playback_stream_fact."total_streaming_duration_milliseconds" AS "total_streaming_duration_milliseconds"
        ,playback_stream_fact."playback_start_timestamp" AS "playback_start_timestamp"
        ,playback_stream_fact."playback_stream_date" AS "stream_date"
        ,playback_stream_fact."dazn_session_id" as "dazn_session_id"
        ,playback_stream_fact."playback_stream_details__skey" AS "playback_stream_details__skey"
        ,customer_identity_dim_filtered."dazn_user_id" AS "dazn_user_id"
        ,customer_identity_dim_filtered."viewer_id" AS "viewer_id"
        ,content_item_dim_filtered."fixture_name" AS "fixture_name"
        ,content_item_dim_filtered."fixture_id" AS "fixture_id"
        ,content_item_dim_filtered."competition_name" AS "competition_name"
        ,content_item_dim_filtered."article_id" AS "article_id"
        ,content_item_dim_filtered."article_title_local" AS "article_title_local"
        ,content_item_dim_filtered."article_type" AS "article_type"
        ,content_item_dim_filtered."article_title_english" AS "article_title_english"
        ,content_item_dim_filtered."fixture_start_timestamp"::DATE AS "fixture_date"
        ,entitlements_dim."available_entitlement_set_ids" AS "available_entitlement_set_ids"
        ,CASE
            WHEN region_dimension."country" IN ('Taiwan', 'France') THEN region_dimension."country"
            ELSE region_dimension."territory"
        END AS "playback_stream_territory"
        ,playback_stream_location_dim."playback_stream_country" AS "playback_stream_country"
        ,CASE
            WHEN country_dim."country" IN ('Taiwan', 'France') THEN country_dim."country"
            ELSE country_dim."territory"
        END AS "user_territory"
        ,country_dim."country" AS "user_country"
        ,device_info_dim."device_category" AS "device_category"
        ,device_info_dim."device_description" AS "device_description"
        ,device_info_dim."device_marketing_name" AS "device_marketing_name"
        ,device_info_dim."device_model" AS "device_model"
        ,device_info_dim."device_platform" AS "device_platform"
        ,CURRENT_TIMESTAMP() AS "record_updated_timestamp"
    FROM playback_stream_fact
    INNER JOIN content_item_dim_filtered
        ON playback_stream_fact."content_item__skey" = content_item_dim_filtered."content_item__skey"
    INNER JOIN article_dim_filtered
        ON content_item_dim_filtered."article_id" = article_dim_filtered."article_id"
    INNER JOIN customer_identity_dim_filtered
        ON playback_stream_fact."customer_identity__skey" = customer_identity_dim_filtered."customer_identity__skey"
    LEFT JOIN entitlements_dim
        ON playback_stream_fact."entitlements__skey" = entitlements_dim."entitlements__skey"
    LEFT JOIN playback_stream_location_dim
        ON playback_stream_fact."playback_stream_location__skey" = playback_stream_location_dim."playback_stream_location__skey"
    LEFT JOIN country_dim AS region_dimension
        ON playback_stream_location_dim."playback_stream_country" = region_dimension."join_key"
    LEFT JOIN user_status_dim
        ON playback_stream_fact."user_status__skey" = user_status_dim."user_status__skey"
    LEFT JOIN country_dim
        ON user_status_dim."home_country_code" = country_dim."join_key"
    LEFT JOIN device_info_dim
        ON playback_stream_fact."device_info__skey" = device_info_dim."device_info__skey"
)

SELECT * FROM streaming_data
