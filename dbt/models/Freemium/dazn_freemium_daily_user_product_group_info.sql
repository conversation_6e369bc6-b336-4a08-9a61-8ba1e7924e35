{{
    config(
        materialized='table',
        database='TRANSFORMATION_' + snowflake_env(),
        schema='FREEMIUM',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        tags=['presentation-playback-stream-freemium']
    )
}}

WITH load_test_users AS (
    SELECT * FROM {{ ref_env('load_test_users') }}
)

,subs_daily_count_intermediate_fact AS (
    SELECT * FROM {{ ref_env('subscription_daily_counts_intermediate__fact') }}
)

,subs_daily_count_intermediate_fact_filtered AS (
    SELECT * FROM subs_daily_count_intermediate_fact
    LEFT JOIN load_test_users
        ON subs_daily_count_intermediate_fact."dazn_user_id" = load_test_users."dazn_id"
    WHERE TRUE
        AND load_test_users."dazn_id" IS NULL
)

,subs_info_dim AS (
    SELECT * FROM {{ ref_env('subscription_mart_info__dim') }}
)

,final AS (
    SELECT "batch_date" AS "product_group_batch_date"
        ,"dazn_user_id" AS "product_group_dazn_user_id"
        ,CASE WHEN COUNT_IF("subscription_product_group" = 'DAZN') = 0 AND COUNT_IF("subscription_product_group" != 'DAZN') > 0
            THEN 'Channels_paid'
        END AS "channels_paid_definition"
        ,CURRENT_TIMESTAMP() AS "record_updated_timestamp"
    FROM subs_daily_count_intermediate_fact_filtered
    LEFT JOIN subs_info_dim
        USING ("subscription_info__skey")
    GROUP BY ALL
    HAVING "channels_paid_definition" = 'Channels_paid'
)

SELECT * FROM final
