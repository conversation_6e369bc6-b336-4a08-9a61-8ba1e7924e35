{{
    config(
        materialized='table',
        database='TRANSFORMATION_' + snowflake_env(),
        schema='FREEMIUM',
        cluster_by=['"stream_date"'],
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_M_WH',
        tags=['presentation-playback-stream-freemium']
    )
}}

WITH streams_data AS (
    SELECT * FROM {{ ref('dazn_freemium_streams_without_user_info') }}
    WHERE TRUE
        {% if is_incremental() %}
        AND "playback_stream_date" BETWEEN DATEADD('day', -7, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
        {% endif %}
)

,daily_view_dazn_freemium AS (
    SELECT * FROM {{ ref('daily_view_dazn_freemium') }}
)

,freemium_streams_not_final AS (
    SELECT
        streams_data."playback_duration_milliseconds" AS "playback_duration_milliseconds"
        ,streams_data."total_streaming_duration_milliseconds" AS "total_streaming_duration_milliseconds"
        ,streams_data."stream_date" AS "stream_date"
        ,streams_data."dazn_session_id" AS "dazn_session_id"
        ,streams_data."dazn_user_id" AS "dazn_user_id"
        ,streams_data."viewer_id" AS "viewer_id"
        ,streams_data."fixture_name" AS "fixture_name"
        ,streams_data."fixture_id" AS "fixture_id"
        ,streams_data."competition_name" AS "competition_name"
        ,streams_data."article_id" AS "article_id"
        ,streams_data."article_title_local" AS "article_title_local"
        ,streams_data."article_type" AS "article_type"
        ,streams_data."article_title_english" AS "article_title_english"
        ,streams_data."fixture_date" AS "fixture_date"
        ,streams_data."available_entitlement_set_ids" AS "available_entitlement_set_ids"
        ,streams_data."playback_stream_territory" AS "playback_stream_territory"
        ,streams_data."playback_stream_country" AS "playback_stream_country"
        ,streams_data."device_category" AS "device_category"
        ,streams_data."device_description" AS "device_description"
        ,streams_data."device_marketing_name" AS "device_marketing_name"
        ,streams_data."device_model" AS "device_model"
        ,streams_data."device_platform" AS "device_platform"
        ,streams_data."user_territory" AS "user_territory"
        ,streams_data."user_country" AS "country"
        ,streams_data."playback_stream_details__skey" AS "playback_stream_details__skey"
        ,COALESCE(daily_view_dazn_freemium."Freemium_Type",daily_view_dazn_freemium."Freemium_Type_lagged") AS "Freemium_Type_adj" -- to solve the issue that we dont always have the freemium type on the day of the engagement
        ,CURRENT_TIMESTAMP() AS "record_updated_timestamp"
    FROM streams_data
    LEFT JOIN daily_view_dazn_freemium -- changed this from inner join to left join
        ON streams_data."dazn_user_id" = daily_view_dazn_freemium."dazn_user_id"
            AND streams_data."stream_date" = daily_view_dazn_freemium."batch_date"
)

SELECT * FROM freemium_streams_not_final

-- should we exclude freemium_type_adj = NULL??
-- this would be engagement on freemium content that does come from users
--  that were neither in free status on the day or the day before
