{{
    config(
        materialized='table',
        database='TRANSFORMATION_' + snowflake_env(),
        schema='FREEMIUM',
        cluster_by=['"batch_date"'],
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_3XL_WH',
        tags=['presentation-playback-stream-freemium'],
        post_hook=['CREATE OR REPLACE TRANSIENT TABLE TRANSFORMATION_'+ snowflake_env()+'.FREEMIUM.CLONE_4_CDC_DAILY_VIEW_DAZN_FREEMIUM_FINAL CLONE TRANSFORMATION_'+ snowflake_env()+'.FREEMIUM.DAILY_VIEW_DAZN_FREEMIUM_FINAL']
    )
}}

WITH dazn_freemium_streams AS (
    SELECT * FROM {{ ref('dazn_freemium_streams') }}
)

,daily_engaged AS (
    SELECT
        "stream_date"
        ,"viewer_id"
    FROM dazn_freemium_streams
    GROUP BY ALL
)

,first_engaged AS (
    SELECT
        "stream_date" AS "first_time_engaged_with_freemium_content",
        "Freemium_Type_adj",
        "viewer_id"
    FROM dazn_freemium_streams
    WHERE TRUE
        AND "Freemium_Type_adj" IS NOT NULL
    QUALIFY TRUE
        AND ROW_NUMBER() OVER (PARTITION BY "viewer_id" ORDER BY "stream_date" ASC) = 1
)

,daily_view_dazn_freemium AS (
    SELECT * FROM {{ ref('daily_view_dazn_freemium') }}
)

,last_subscription AS (
    SELECT * from {{ ref_env('dazn_freemium_last_subscription') }}
)

,daily_view_dazn_freemium_enriched AS (
    SELECT daily_view_dazn_freemium."batch_date" AS "batch_date"
        ,daily_view_dazn_freemium."batch_date_lagged" AS "batch_date_lagged"
        ,daily_view_dazn_freemium."dazn_user_id" AS "dazn_user_id"
        ,daily_view_dazn_freemium."viewer_id" AS "viewer_id"
        ,daily_view_dazn_freemium."country" AS "country"
        ,daily_view_dazn_freemium."user_territory" AS "user_territory"
        ,daily_view_dazn_freemium."Freemium_Type" AS "Freemium_Type"
        ,daily_view_dazn_freemium."Freemium_Type_lagged" AS "Freemium_Type_lagged"
        ,last_subscription."previous_subscription_end_date" AS "previous_subscription_end_date"
    FROM daily_view_dazn_freemium
    ASOF JOIN last_subscription
        MATCH_CONDITION(daily_view_dazn_freemium."batch_date">=last_subscription."previous_subscription_end_date")
        ON daily_view_dazn_freemium."dazn_user_id" = last_subscription."dazn_user_id"
)

,daily_acquisitions_content_attribution AS (
    SELECT * FROM {{ ref_env('daily_acquisitions_content_attribution') }}
)

,users_account_created AS (
    SELECT "dazn_user_id"
        ,"user_account_created_timestamp"::DATE AS "user_account_created_date"
    FROM {{ ref_env('user_account_created') }}
)

,final AS (
    SELECT
        freemium_enriched.*
        ,"first_time_engaged_with_freemium_content"
        ,CASE
            WHEN first_engaged."first_time_engaged_with_freemium_content" <= freemium_enriched."batch_date" THEN TRUE
            ELSE FALSE
        END AS "engaged_all_time_flag"
        ,CASE
            WHEN daily_engaged."viewer_id" IS NOT NULL THEN TRUE
            ELSE FALSE
        END AS "daily_engaged_flag"
        ,LAG("daily_engaged_flag", 1) OVER (PARTITION BY freemium_enriched."dazn_user_id" ORDER BY freemium_enriched."batch_date") AS "prev_daily_engaged_flag"
        ,CASE
            WHEN "prev_daily_engaged_flag" = TRUE AND "daily_engaged_flag" = TRUE AND DATEDIFF('day', freemium_enriched."batch_date_lagged", freemium_enriched."batch_date") = 1  THEN TRUE
            ELSE FALSE
        END AS "continuous_engaged_freemium" -- definition: did the user engaged with Freemium content two days in a row?
        ,users_account_created."user_account_created_date" AS "user_account_created_date"
        ,daily_acquisitions_content_attribution."attribution_competition_name" AS "attribution_competition_name"
        ,daily_acquisitions_content_attribution."attribution_fixture_name" AS "attribution_fixture_name"
        ,daily_acquisitions_content_attribution."attribution_article_type" AS "attribution_article_type"
    FROM daily_view_dazn_freemium_enriched freemium_enriched
    LEFT JOIN first_engaged
        ON freemium_enriched."viewer_id" = first_engaged."viewer_id"
    LEFT JOIN daily_engaged
        ON freemium_enriched."viewer_id" = daily_engaged."viewer_id"
            AND freemium_enriched."batch_date" = daily_engaged."stream_date"
    LEFT JOIN users_account_created
        ON freemium_enriched."dazn_user_id" = users_account_created."dazn_user_id"
    LEFT JOIN daily_acquisitions_content_attribution
        ON freemium_enriched."dazn_user_id" = daily_acquisitions_content_attribution."dazn_user_id"
)

,updated_final AS (
    SELECT
        "batch_date"
        ,"batch_date_lagged"
        ,"dazn_user_id"
        ,"viewer_id"
        ,"user_account_created_date"
        ,"daily_engaged_flag"
        ,"prev_daily_engaged_flag" -- remove, as there is no dependent downstream
        ,"continuous_engaged_freemium"
        ,"country"
        ,"user_territory"
        ,"Freemium_Type" AS "Freemium_Type_Old"
        ,"attribution_competition_name"
        ,"attribution_fixture_name"
        ,"attribution_article_type"
        ,"Freemium_Type_lagged"
        ,"first_time_engaged_with_freemium_content"
        ,"engaged_all_time_flag"
        ,CASE
            WHEN DATEDIFF('day', "batch_date_lagged", "batch_date") > 1 AND "batch_date_lagged" IS NOT NULL THEN NULL
            ELSE "Freemium_Type_Old"
        END AS "Freemium_Type"
        ,"previous_subscription_end_date"
        ,CURRENT_TIMESTAMP() AS "record_updated_timestamp"
    FROM final
)

SELECT * FROM updated_final
