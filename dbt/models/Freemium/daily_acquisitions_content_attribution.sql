{{
    config(
        materialized='table',
        database='TRANSFORMATION_' + snowflake_env(),
        schema='FREEMIUM',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['presentation-playback-stream-freemium']
    )
}}

WITH dazn_freemium_streams_without_user_info AS (
    SELECT * FROM {{ ref('dazn_freemium_streams_without_user_info') }}
    WHERE TRUE
    {% if is_incremental() %} -- Double check & try simplying the model.
        AND NOT (
            "dazn_user_id" IN (
                SELECT "dazn_user_id" FROM {{ this }}
                WHERE TRUE
                    AND "attribution_stream_date" < DATEADD('day',-7,CURRENT_DATE())
                )
            ) -- LOAD
    {% endif %}
)

,final AS (
    SELECT
        "dazn_user_id"
        ,"competition_name" AS "attribution_competition_name"
        ,"stream_date" AS "attribution_stream_date"
        ,"fixture_name" AS "attribution_fixture_name"
        ,"article_type" AS "attribution_article_type"
        ,CURRENT_TIMESTAMP() AS "record_updated_timestamp"
    FROM dazn_freemium_streams_without_user_info
    QUALIFY TRUE
        AND ROW_NUMBER() OVER (PARTITION BY "dazn_user_id" ORDER BY "playback_start_timestamp" ASC) = 1
)

SELECT * FROM final
