{{
    config(
        materialized='table',
        database='TRANSFORMATION_' + snowflake_env(),
        schema='FREEMIUM',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['presentation-playback-stream-freemium']
    )
}}

WITH subs_daily_changes_intermediate AS (
    SELECT * FROM {{ ref_env('subscription_daily_changes_intermediate__fact') }}
    QUALIFY TRUE
        AND ROW_NUMBER() OVER (PARTITION BY "subscription_name" ORDER BY "batch_date" DESC) = 1
)

,subscription_mart_info_dim AS (
    SELECT * FROM {{ ref_env('subscription_mart_info__dim') }}
    WHERE TRUE
        AND "subscription_product_group" = 'DAZN'
)

,subscription_term_changes_dim AS (
    SELECT * FROM {{ ref_env('subscription_term_changes__dim') }}
    WHERE TRUE
        AND "subscription_ended"
)

,last_subscription AS (
    SELECT
        subs_daily_changes_intermediate."dazn_user_id" AS "dazn_user_id"
        ,subs_daily_changes_intermediate."batch_date" AS "previous_subscription_end_date"
        ,subs_daily_changes_intermediate."subscription_name" AS "subscription_name"
    FROM subs_daily_changes_intermediate
    INNER JOIN subscription_mart_info_dim
        ON subs_daily_changes_intermediate."subscription_info__skey" = subscription_mart_info_dim."subscription_info__skey"
    INNER JOIN subscription_term_changes_dim
        ON subs_daily_changes_intermediate."subscription_term_changes__skey" = subscription_term_changes_dim."subscription_term_changes__skey"
)

,final AS (
    SELECT *
        ,LEAD("previous_subscription_end_date",1,CURRENT_DATE())
            OVER (PARTITION BY "dazn_user_id" ORDER BY "previous_subscription_end_date" ASC) AS "previous_subscription_end_date_valid_upto"
        ,CURRENT_TIMESTAMP() AS "record_updated_timestamp"
    FROM last_subscription
)

SELECT * FROM final
