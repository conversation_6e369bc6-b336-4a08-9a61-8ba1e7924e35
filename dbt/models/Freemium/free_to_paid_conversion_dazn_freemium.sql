{{
    config(
        materialized='table',
        database='TRANSFORMATION_' + snowflake_env(),
        schema='FREEMIUM',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_3XL_WH',
        tags=['presentation-playback-stream-freemium'],
        post_hook=['CREATE OR REPLACE TRANSIENT TABLE TRANSFORMATION_'+ snowflake_env()+'.FREEMIUM.CLONE_4_CDC_FREE_TO_PAID_CONVERSION_DAZN_FREEMIUM CLONE TRANSFORMATION_'+ snowflake_env()+'.FREEMIUM.FREE_TO_PAID_CONVERSION_DAZN_FREEMIUM']
    )
}}

WITH load_test_users AS (
    SELECT * FROM {{ ref_env('load_test_users') }}
)

,daily_users_intermediate AS (
    SELECT * FROM {{ ref_env('daily_users__intermediate') }}
    WHERE TRUE
        AND "batch_date" >= '2023-12-06'
)

,daily_users_intermediate_excluding_load_test_users AS (
    SELECT * FROM daily_users_intermediate
    LEFT JOIN load_test_users
        ON daily_users_intermediate."dazn_user_id" = load_test_users."dazn_id"
    WHERE TRUE
        AND load_test_users."dazn_id" IS NULL
)

,subscription_campaign_mapping_dim AS (
    SELECT * FROM {{ ref_env('daily_counts__subscription_campaign_mapping__dim') }}
)

,billing_account_dim AS (
    SELECT * FROM {{ ref_env('billing_account__dim') }}
)

,subscription_mart_charge_dim AS (
    SELECT * FROM {{ ref_env('subscription_mart_charge__dim') }}
)

,subscription_mart_info_dim AS (
    SELECT * FROM {{ ref_env('subscription_mart_info__dim') }}
)

,subscription_source_system_name_mapping_dim AS (
    SELECT * FROM {{ ref_env('daily_counts__subscription_source_system_name_mapping__dim') }}
)

, user_status_dim AS (
    SELECT * FROM {{ ref_env('user_status__dim') }}
)

, country_dim AS (
    SELECT * FROM {{ ref_env('region_dimension') }}
)

,users_account_created AS (
    SELECT "dazn_user_id"
        ,"user_account_created_timestamp"::DATE AS "user_account_created_date"
    FROM {{ ref_env('user_account_created') }}
)

, daily_users_dazn_subs AS (
    SELECT * FROM {{ ref_env('daily_users_dazn_subs')}}
)

, daily_view_dazn_freemium_final AS  (
    SELECT * FROM {{ ref('daily_view_dazn_freemium_final')}}
)

,last_subscription AS (
    SELECT * from {{ ref_env('dazn_freemium_last_subscription') }}
)

, dazn_freemium_streams AS (
    SELECT * FROM {{ ref('dazn_freemium_streams') }}
)

, daily_engaged AS (
    SELECT
        "viewer_id"
        ,"stream_date"
    FROM dazn_freemium_streams
    GROUP BY ALL
)

,first_engaged AS (
    SELECT
        "viewer_id"
        ,MIN("stream_date") AS "first_time_engaged"
    FROM daily_engaged
    GROUP BY ALL
--     -- WHERE "Freemium_Type_adj" IS NOT NULL
--     QUALIFY TRUE
--         AND ROW_NUMBER() OVER (PARTITION BY "viewer_id" ORDER BY "stream_date" ASC) = 1
)

,changes_df AS (
    SELECT
        "batch_date"
        ,"dazn_user_id"
        ,"viewer_id"
        ,country_dim."country" AS "country"
        ,CASE
            WHEN country_dim."country" IN ('Taiwan', 'France') THEN country_dim."country"
            ELSE country_dim."territory"
          END AS "user_territory"
        ,CASE
            WHEN "has_dazn_subscription" = 'Yes' THEN NULL
            WHEN  user_status_dim."source_type" = 'Freemium' THEN 'DAZN_Freemium_registered'
            WHEN "dazn_user_id" IN (SELECT "dazn_user_id" FROM daily_users_dazn_subs GROUP BY 1) THEN 'Frozen'
            ELSE 'Partial'
        END AS "Freemium_Type"
        ,"has_dazn_subscription"
        ,LAG("has_dazn_subscription") OVER (PARTITION BY "dazn_user_id" ORDER BY "batch_date" ASC) AS "has_dazn_subscription_day_before"
        ,CASE
          WHEN daily_users_intermediate_excluding_load_test_users."dazn_subscription_info__skey" IS NULL THEN 'No DAZN Subscription'
          WHEN (subscription_campaign_mapping_dim."campaign_detailed_type") NOT IN ('Direct', 'OEM') THEN (subscription_campaign_mapping_dim."campaign_detailed_type")
          ELSE IFNULL((subscription_source_system_name_mapping_dim."partner_detailed_type"), 'Direct')
        END AS "dazn_attribution_detailed_type"
        ,CASE
          WHEN daily_users_intermediate_excluding_load_test_users."dazn_subscription_info__skey" IS NULL THEN 'No DAZN Subscription'
          WHEN (subscription_campaign_mapping_dim."campaign_detailed_type") NOT IN ('Direct', 'OEM') THEN (subscription_campaign_mapping_dim."campaign_partner_name")
          ELSE IFNULL((subscription_source_system_name_mapping_dim."partner_name"), 'Direct')
        END AS "dazn_attribution_partner_name"
        ,CASE
          WHEN daily_users_intermediate_excluding_load_test_users."dazn_subscription_info__skey" IS NULL THEN 'No DAZN Subscription'
          WHEN (subscription_campaign_mapping_dim."campaign_detailed_type") NOT IN ('Direct', 'OEM') THEN (subscription_campaign_mapping_dim."campaign_type")
          ELSE IFNULL((subscription_source_system_name_mapping_dim."partner_type"), 'Direct')
        END AS "dazn_attribution_type"
        ,subscription_mart_charge_dim."subscription_type" AS "subscription_type"
        ,subscription_mart_info_dim."subscription_tier" AS "subscription_tier"
    FROM daily_users_intermediate_excluding_load_test_users
    LEFT JOIN subscription_campaign_mapping_dim
        ON daily_users_intermediate_excluding_load_test_users."dazn_subscription_giftcode_campaign_name__skey" = subscription_campaign_mapping_dim."subscription_giftcode_campaign_name__skey"
    LEFT JOIN subscription_source_system_name_mapping_dim
        ON daily_users_intermediate_excluding_load_test_users."dazn_subscription_source_system_name_derived__skey" = subscription_source_system_name_mapping_dim."subscription_source_system_name_derived__skey"
    LEFT JOIN subscription_mart_charge_dim
        ON daily_users_intermediate_excluding_load_test_users."dazn_subscription_charge__skey" = subscription_mart_charge_dim."subscription_charge__skey"
    LEFT JOIN subscription_mart_info_dim
        ON daily_users_intermediate_excluding_load_test_users."dazn_subscription_info__skey" = subscription_mart_info_dim."subscription_info__skey"
    LEFT JOIN user_status_dim
        ON daily_users_intermediate_excluding_load_test_users."user_status__skey" = user_status_dim."user_status__skey"
    LEFT JOIN country_dim
        ON user_status_dim."home_country_code" = country_dim."join_key"
)

,changes_new AS (
    SELECT
        changes_df.*
        ,daily_view."Freemium_Type" AS "Freemium_Type_day_before"
    FROM changes_df
    LEFT JOIN daily_view_dazn_freemium_final AS daily_view
    ON changes_df."dazn_user_id" = daily_view."dazn_user_id"
        AND DATEADD('day', -1, changes_df."batch_date") = daily_view."batch_date"
)

,final AS (
    SELECT
        changes_new.*
         ,CASE
             WHEN "Freemium_Type_day_before" = 'DAZN_Freemium_registered' THEN 'Freemium_to_paid'
             WHEN "Freemium_Type_day_before" = 'Channels_paid' THEN 'Channels_paid_to_paid'
             WHEN "Freemium_Type_day_before" = 'Frozen' THEN 'Frozen_to_paid'
             WHEN "Freemium_Type_day_before" = 'Partial' THEN 'Partial_to_paid'
             ELSE NULL
         END AS "conversion_type"
         ,TRUE AS "free_to_paid_conversion"
         ,"user_account_created_date"
         ,CASE
             WHEN daily_engaged."viewer_id" IS NOT NULL THEN TRUE
             ELSE FALSE
         END AS "engaged_on_day_of_conversion"
        ,CASE
            WHEN first_engaged."first_time_engaged" <= changes_new."batch_date" THEN TRUE
            ELSE FALSE
        END AS "engaged_all_time_flag"
    FROM changes_new
    LEFT JOIN daily_engaged
        ON changes_new."viewer_id" = daily_engaged."viewer_id"
            AND changes_new."batch_date" = daily_engaged."stream_date"
    LEFT JOIN first_engaged
        ON changes_new."viewer_id" = first_engaged."viewer_id"
    LEFT JOIN users_account_created
        ON changes_new."dazn_user_id" = users_account_created."dazn_user_id"
    WHERE TRUE
        AND "has_dazn_subscription_day_before" IS NOT NULL
        AND "has_dazn_subscription_day_before" = 'No'
        AND "has_dazn_subscription" = 'Yes'
)

,updated_final AS (
    SELECT final.*
        ,last_subscription."previous_subscription_end_date"
        ,CURRENT_TIMESTAMP() AS "record_updated_timestamp"
    FROM final
    ASOF JOIN last_subscription
        MATCH_CONDITION(final."batch_date">=last_subscription."previous_subscription_end_date")
        ON final."dazn_user_id" = last_subscription."dazn_user_id"
--     QUALIFY TRUE
--         AND ROW_NUMBER() OVER (PARTITION BY final."dazn_user_id", final."batch_date" ORDER BY last_subscription."previous_subscription_end_date" DESC) = 1
)

SELECT * FROM updated_final
