{{
    config(
        database='SKY_IT_REVENUE_SHARE__' + snowflake_env(),
        schema='SOURCE',
        tags=['presentation-sky-it-revenue-share']
    )
}}

{% set sky_it_variables = sky_it_variables() %}

WITH playback_stream_fact AS (
    SELECT * FROM {{ ref_env('MART__PLAYBACK_STREAM__FACT') }}
)

,content_item_dim AS (
    SELECT * FROM {{ ref_env('MART__CONTENT_ITEM__DIM') }}
)

,device_info_dim AS (
    SELECT * FROM {{ ref_env('MART__DEVICE_INFO__DIM') }}
)

, id_mapping_source AS (
    SELECT DISTINCT
        "billing_account_id"
        ,"viewer_id"
    FROM {{ ref_env('customer_identity_dim') }}
)

SELECT
    playback_stream_fact."viewer_id"
    ,id_mapping_source."billing_account_id"
    ,playback_stream_fact."playback_stream_date"
    ,playback_stream_fact."user_agent"
    ,playback_stream_fact."playback_duration_milliseconds"
    ,playback_stream_fact."user_agent" IN ({{ in_clause_from_list(sky_it_variables.user_agents.values() | sum(start=[])) }}) AS "is_sky_device"
FROM playback_stream_fact
LEFT JOIN content_item_dim
    ON playback_stream_fact."content_item__skey" = content_item_dim."content_item__skey"
LEFT JOIN device_info_dim
    ON playback_stream_fact."device_info__skey" = device_info_dim."device_info__skey"
INNER JOIN id_mapping_source
    ON id_mapping_source."viewer_id" = playback_stream_fact."viewer_id"
WHERE
    content_item_dim."outlet" = 'italy'
    AND
    device_info_dim."device_category" = 'Living Room'
    AND
    playback_stream_fact."playback_duration_milliseconds" > 0
    -- we can save ourselves some processing by only including the streams since the start of the sky deal.
    AND
    playback_stream_fact."playback_stream_date" >= '{{ sky_it_variables.sky_deal_start_date }}'
