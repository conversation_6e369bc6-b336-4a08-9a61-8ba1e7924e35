{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"batch_date"',
        database='SKY_IT_REVENUE_SHARE__' + snowflake_env(),
        schema='SOURCE',
        tags=['presentation-sky-it-revenue-share']
    )
}}

{% set sky_it_variables = sky_it_variables() %}

WITH dim_date AS (
    SELECT * FROM {{ ref_env('dim_date') }}
    WHERE
        -- Filter for when fully refreshing
        "date_day" BETWEEN '{{ sky_it_variables.sky_deal_start_date }}' AND DATEADD('day', -1, CURRENT_DATE)
)

,subscription_name__scd AS (
    SELECT * FROM {{ ref_env('subscription_name__scd') }}
    WHERE
        -- in Italy
        "subscription_territory" = 'Italy'
        -- and has the Sky AddOn
        AND
        ARRAY_CONTAINS('SKY_IT'::variant, "subscription_add_on_partner_ids")
)

,active_sky_subs AS (
    SELECT * FROM {{ ref('subscription_daily_active_sky_it__dim') }}
)

-- get all currently active italy non-sky sub
-- TODO: direct only subs?
SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,dim_date."date_day" AS "batch_date"
    ,HASH(
        dim_date."date_day"
        ,subscription_name__scd."subscription_name"
    ) AS "non_sky_it_customer__skey"
    ,subscription_name__scd."billing_account_id"
    ,subscription_name__scd."subscription_id"
    ,subscription_name__scd."subscription_name"
    ,subscription_name__scd."record_valid_from_timestamp"
    ,subscription_name__scd."record_valid_until_timestamp"
    ,subscription_name__scd."subscription_source_system_name"
    ,subscription_name__scd."subscription_tier"
    ,subscription_name__scd."subscription_tracking_id"
    ,subscription_name__scd."has_subscription_add_on"
    ,subscription_name__scd."subscription_territory"
    ,subscription_name__scd."billing_account_currency_code"
FROM dim_date
INNER JOIN subscription_name__scd
    -- currently active subs at time of end of the day of the batch date
    ON subscription_name__scd."record_valid_from_timestamp" <= DATEADD('day', 1, dim_date."date_day")
        AND subscription_name__scd."record_valid_until_timestamp" > DATEADD('day', 1, dim_date."date_day")
LEFT JOIN active_sky_subs
    ON subscription_name__scd."subscription_name" = active_sky_subs."subscription_name"
        AND dim_date."date_day" = active_sky_subs."batch_date"
WHERE
    {% if is_incremental() %}
        dim_date."date_day" = '{{ var('batch_date') }}'
        AND
    {% endif %}
    active_sky_subs."subscription_name" IS NULL
