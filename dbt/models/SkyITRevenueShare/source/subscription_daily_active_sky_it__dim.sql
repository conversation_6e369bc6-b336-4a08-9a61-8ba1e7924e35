{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"batch_date"',
        database='SKY_IT_REVENUE_SHARE__' + snowflake_env(),
        schema='SOURCE',
        tags=['presentation-sky-it-revenue-share']
    )
}}

{% set sky_it_variables = sky_it_variables() %}

WITH subscription_name_scd AS (
    SELECT * FROM {{ ref_env('subscription_name__scd') }}
)

-- get all italy subscriptions
,all_italy_subs AS (
    SELECT
        "billing_account_id"
        ,"subscription_id"
        ,"subscription_name"
        ,"record_valid_from_timestamp"
        ,"record_valid_until_timestamp"
        ,"subscription_name_original_created_timestamp"
        ,"subscription_source_system_name"
        ,"subscription_tier"
        ,"subscription_tracking_id"
        ,"subscription_sign_up_campaign_id"
        -- has the subscription got a Tracking Id that's in the list in the macro in dbt/macros/SkyITRevenueShare/sky_it_variables.sql
        ,"subscription_tracking_id" IN ({{ in_clause_from_list(sky_it_variables.tracking_ids.values() | sum(start=[])) }}) AS "has_sky_it_tracking_id"
        ,"subscription_sign_up_campaign_id" IN ({{ in_clause_from_list(sky_it_variables.campaign_ids) }}) AS "has_sky_it_campaign_id"
        ,"has_subscription_add_on"
        ,"subscription_territory"
        ,"billing_account_currency_code"
    FROM subscription_name_scd
    WHERE
        "subscription_territory" = 'Italy'

)

,sky_italy_subs AS (
    SELECT *
    FROM all_italy_subs
    WHERE
        (
            "has_sky_it_tracking_id"
            OR
            "has_sky_it_campaign_id"
        )

)

-- get the currently active subs
,current_italy_subs AS (
    SELECT
        "billing_account_id"
        ,"subscription_id"
        ,"subscription_name"
        ,"record_valid_from_timestamp"
        ,"record_valid_until_timestamp"
        ,"subscription_name_original_created_timestamp"
        ,"subscription_source_system_name"
        ,"subscription_tier"
        ,"subscription_tracking_id"
        ,"subscription_sign_up_campaign_id"
        ,"has_sky_it_tracking_id"
        ,"has_sky_it_campaign_id"
        ,"has_subscription_add_on"
        ,"subscription_territory"
        ,"billing_account_currency_code"
    FROM all_italy_subs
    WHERE TRUE
        {% if is_incremental() %}
        -- if the model is incremental we only need records that have a valid until after the batch date.
        AND
        '{{ var('batch_date') }}'::DATE >= "record_valid_from_timestamp"::DATE
        AND
        '{{ var('batch_date') }}'::DATE < "record_valid_until_timestamp"::DATE
        {% else %}
        -- if it's a full rebuild we get any record valid until after the start of the sky deal.
        AND "record_valid_until_timestamp"::DATE >= '{{ sky_it_variables.sky_deal_start_date }}'
    {% endif %}
)

-- for each currently active sub find all previous subs
-- note this will produce duplciates for each sub, but we deal with this in the next step
,joined_subs AS (
    SELECT
        current_italy_subs."billing_account_id"
        ,current_italy_subs."subscription_id"
        ,current_italy_subs."subscription_name"
        ,current_italy_subs."record_valid_from_timestamp"
        ,current_italy_subs."record_valid_until_timestamp"
        ,current_italy_subs."subscription_name_original_created_timestamp"
        ,current_italy_subs."subscription_source_system_name"
        ,current_italy_subs."subscription_tier"
        ,current_italy_subs."subscription_tracking_id"
        ,current_italy_subs."subscription_sign_up_campaign_id"
        ,current_italy_subs."has_sky_it_tracking_id"
        ,current_italy_subs."has_sky_it_campaign_id"
        ,current_italy_subs."has_subscription_add_on"
        ,current_italy_subs."subscription_territory"
        ,current_italy_subs."billing_account_currency_code"
        ,sky_italy_subs."subscription_name" AS "sky_sub_name"
        ,sky_italy_subs."record_valid_from_timestamp" AS "sky_sub_valid_from"
        ,sky_italy_subs."record_valid_until_timestamp" AS "sky_sub_valid_until"
        ,sky_italy_subs."subscription_tracking_id" AS "sky_sub_tracking_id"
        ,sky_italy_subs."has_sky_it_tracking_id" AS "sky_sub_has_sky_it_tracking_id"
        ,all_italy_subs."subscription_name" AS "any_sub_name"
        ,all_italy_subs."record_valid_from_timestamp" AS "any_sub_valid_from"
        ,all_italy_subs."record_valid_until_timestamp" AS "any_sub_valid_until"
        ,all_italy_subs."subscription_tracking_id" AS "any_sub_tracking_id"
        ,all_italy_subs."has_sky_it_tracking_id" AS "sky_sub_has_sky_it_tracking_id"
    FROM current_italy_subs
    -- join only SKY previous subs
    LEFT JOIN sky_italy_subs
        ON sky_italy_subs."billing_account_id" = current_italy_subs."billing_account_id"
            AND sky_italy_subs."subscription_name" != current_italy_subs."subscription_name"
            AND sky_italy_subs."record_valid_from_timestamp" < current_italy_subs."record_valid_from_timestamp"
    -- join all previous subs
    LEFT JOIN all_italy_subs
        ON all_italy_subs."billing_account_id" = current_italy_subs."billing_account_id"
            AND all_italy_subs."subscription_name" != current_italy_subs."subscription_name"
            AND all_italy_subs."record_valid_from_timestamp" < current_italy_subs."record_valid_from_timestamp"
)

, dim_date AS (
    SELECT * FROM {{ ref_env('dim_date') }}
    WHERE TRUE
        {% if is_incremental() %}
        -- if the model is incremental we only need the airflow's batch date.
        AND "date_day" = '{{ var('batch_date') }}'
        {% else %}
        -- if it's a full rebuild we get all dates since the start of the sky deal until today
        AND "date_day" BETWEEN '{{ sky_it_variables.sky_deal_start_date }}' AND DATEADD('day', -1, CURRENT_DATE)
    {% endif %}
)

-- get last sub details and classify them
SELECT DISTINCT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,dim_date."date_day" AS "batch_date"
    ,HASH(
        "batch_date"
        ,joined_subs."subscription_name"
    ) AS "sky_it_customer__skey"
    ,joined_subs."billing_account_id"
    ,joined_subs."subscription_id"
    ,joined_subs."subscription_name"
    ,joined_subs."record_valid_from_timestamp"
    ,joined_subs."record_valid_until_timestamp"
    ,joined_subs."subscription_name_original_created_timestamp"
    ,joined_subs."subscription_source_system_name"
    ,joined_subs."subscription_tier"
    ,joined_subs."subscription_tracking_id"
    ,joined_subs."subscription_sign_up_campaign_id"
    ,joined_subs."has_sky_it_tracking_id"
    ,joined_subs."has_sky_it_campaign_id"
    ,joined_subs."has_subscription_add_on"
    ,joined_subs."subscription_territory"
    ,joined_subs."billing_account_currency_code"
    -- get the last value for previous sky subs
    ,LAST_VALUE(joined_subs."sky_sub_name") OVER (PARTITION BY joined_subs."subscription_name", "batch_date" ORDER BY joined_subs."sky_sub_valid_from") AS "last_sky_sub_name"
    ,LAST_VALUE(joined_subs."sky_sub_valid_from") OVER (PARTITION BY joined_subs."subscription_name", "batch_date" ORDER BY joined_subs."sky_sub_valid_from") AS "last_sky_sub_valid_from"
    ,LAST_VALUE(joined_subs."sky_sub_valid_until") OVER (PARTITION BY joined_subs."subscription_name", "batch_date" ORDER BY joined_subs."sky_sub_valid_from") AS "last_sky_sub_valid_until"
    ,LAST_VALUE(joined_subs."sky_sub_tracking_id") OVER (PARTITION BY joined_subs."subscription_name", "batch_date" ORDER BY joined_subs."sky_sub_valid_from") AS "last_sky_sub_tracking_id"
    -- get the last value for any previous sub
    ,LAST_VALUE(joined_subs."any_sub_name") OVER (PARTITION BY joined_subs."subscription_name", "batch_date" ORDER BY joined_subs."any_sub_valid_from") AS "last_any_sub_name"
    ,LAST_VALUE(joined_subs."any_sub_valid_from") OVER (PARTITION BY joined_subs."subscription_name", "batch_date" ORDER BY joined_subs."any_sub_valid_from") AS "last_any_sub_valid_from"
    ,LAST_VALUE(joined_subs."any_sub_valid_until") OVER (PARTITION BY joined_subs."subscription_name", "batch_date" ORDER BY joined_subs."any_sub_valid_from") AS "last_any_sub_valid_until"
    ,LAST_VALUE(joined_subs."any_sub_tracking_id") OVER (PARTITION BY joined_subs."subscription_name", "batch_date" ORDER BY joined_subs."any_sub_valid_from") AS "last_any_sub_tracking_id"
    -- apply conditions to determine what classification the account belongs to
    ,CASE
        WHEN
            -- if the current sub has sky tracking ID
            (
                joined_subs."has_sky_it_tracking_id"
                OR
                joined_subs."has_sky_it_campaign_id"
            )
            -- and there hasn't been any previous subs for that account
            AND "last_any_sub_name" IS NULL
            -- then it's a sky new sub
            THEN TRUE
        ELSE FALSE
    END AS "sky_new_sub"
    ,CASE
        WHEN
            -- if the current sub has sky tracking ID
            (
                joined_subs."has_sky_it_tracking_id"
                OR
                joined_subs."has_sky_it_campaign_id"
            )
            -- and the last sub was more than 120 days
            AND DATEDIFF('day', "last_any_sub_valid_until", joined_subs."subscription_name_original_created_timestamp") > 120
            -- then it's a sky resub
            THEN TRUE
        ELSE FALSE
    END AS "sky_resub"
    ,CASE
        WHEN
            -- if it's a direct Sub
            joined_subs."subscription_source_system_name" IS NULL
            -- and there is any previous sky sub in the last 120 days
            AND DATEDIFF('day', "last_sky_sub_valid_until", joined_subs."subscription_name_original_created_timestamp") <= 120
            -- then it's a resub that was previously sky
            THEN TRUE
        ELSE FALSE
    END AS "resub_prev_sky"
    ,CASE
        WHEN "sky_new_sub" THEN 'sky_new_sub'
        WHEN "sky_resub" THEN 'sky_resub'
        WHEN "resub_prev_sky" THEN 'resub_prev_sky'
    END AS "sky_it_customer_classification"

FROM dim_date
LEFT JOIN joined_subs
    ON
        dim_date."date_day" >= joined_subs."record_valid_from_timestamp"::DATE
        AND
        dim_date."date_day" < joined_subs."record_valid_until_timestamp"::DATE
QUALIFY "sky_it_customer_classification" IS NOT NULL
