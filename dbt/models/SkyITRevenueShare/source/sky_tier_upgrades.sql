{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"attribution_date"',
        database='SKY_IT_REVENUE_SHARE__' + snowflake_env(),
        schema='SOURCE',
        tags=['presentation-sky-it-revenue-share']
    )
}}

{% set sky_it_variables = sky_it_variables() %}

WITH subscription_source AS (
    SELECT * FROM {{ ref_env('subscription_name__scd') }}
)

, subscription_daily_active_sky_it__dim AS (
    SELECT * FROM {{ ref('subscription_daily_active_sky_it__dim') }}
)

-- we're checking for customers who change {delta} days ago, the batch date and the attribution date will always have {delta} days of difference.
, tier_changes AS (
    SELECT
        CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
        ,"record_valid_from_timestamp" + INTERVAL '{{ sky_it_variables.sky_attribution_days_delta }} days' AS "attribution_timestamp"
        ,"attribution_timestamp"::DATE AS "attribution_date"
        -- our primary key will be a combination of subscription and attribution date.
        ,HASH(
            "attribution_date"
            ,"subscription_name"
        ) AS "sky_it_customer__skey"
        ,"billing_account_id"
        ,"subscription_id"
        ,"subscription_name"
        ,"subscription_territory"
        ,"billing_account_currency_code"
        ,"record_valid_from_timestamp" AS "tier_upgrade_timestamp"
        ,"tier_upgrade_timestamp"::DATE AS "tier_upgrade_date"
        ,"subscription_monthly_recurring_revenue" AS "new_mrr_on_upgrade"
        ,LAG("subscription_monthly_recurring_revenue") OVER (PARTITION BY "subscription_name" ORDER BY "record_valid_from_timestamp", "record_valid_until_timestamp") AS "old_mrr_on_upgrade"
    FROM subscription_source
    -- we can filter for only subscription effective until after our date of interest.
    -- This way we don't run the window function on the whole scd.
    WHERE
        DATE_TRUNC(DAY,"record_valid_until_timestamp") >= '{{ sky_it_variables.sky_deal_start_date }}'
        -- We only consider subs who don't have a sky tracking ID and don't have a sky campaign id.
        AND
        NVL("subscription_tracking_id",'') NOT IN ({{ in_clause_from_list(sky_it_variables.tracking_ids.values() | sum(start=[])) }})
        AND
        NVL("subscription_sign_up_campaign_id",'') NOT IN ({{ in_clause_from_list(sky_it_variables.campaign_ids) }})
    QUALIFY
        -- we want all rows where there's a change in tier
        "subscription_tier" != LAG("subscription_tier", 1) OVER (PARTITION BY "subscription_name" ORDER BY "record_valid_from_timestamp", "record_valid_until_timestamp")
        AND
        -- And when the new tier is included in the 'allowed list'
        "subscription_tier" IN ({{ in_clause_from_list(sky_it_variables.allowed_target_tiers) }})
        AND
        -- And only when the MRR has a positive change (we don't want to include tier downgrades if ever this becomes possible)
        "new_mrr_on_upgrade" > "old_mrr_on_upgrade"
        {% if is_incremental() %}
        -- if we're building on a specific batch date then we're only looking for changes that happened X days ago.
        -- otherwise we'll take all the changes, for a full rebuild.
        AND "tier_upgrade_date" = '{{ var('batch_date') }}'::DATE - INTERVAL '{{ sky_it_variables.sky_attribution_days_delta }} days'
        {% else %}
        -- if it's a full rebuild, we're only interested in customers who changed on or before 61 days ago.
        -- 61 days because the batch_date variable in airflow is the day before the current date.
        AND "tier_upgrade_date" <= DATEADD('day', -1, CURRENT_DATE) - INTERVAL '{{ sky_it_variables.sky_attribution_days_delta }} days'
    {% endif %}
    ORDER BY "subscription_name", "record_valid_from_timestamp"
)

-- Remove all subs who appear in the daily_active_sky_it_dim
SELECT tier_changes.* FROM tier_changes
LEFT JOIN subscription_daily_active_sky_it__dim
    ON
        tier_changes."subscription_name" = subscription_daily_active_sky_it__dim."subscription_name"
        AND
        tier_changes."attribution_date" = subscription_daily_active_sky_it__dim."batch_date"
WHERE subscription_daily_active_sky_it__dim."subscription_name" IS NULL
