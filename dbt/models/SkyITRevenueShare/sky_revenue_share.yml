version: 2

models:
  - name: subscription_daily_active_sky_it__dim
    description: "A dimension table detailing the relationship/classification each italian subscription has with Sky IT"
    columns:
      - &META__DBT_INSERT_DTTS
        name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table"
        quote: true

      - &batch_date
        name: batch_date
        description: "The date of the batch of data that has been inserted"
        quote: true

      - &sky_it_customer__skey
        name: sky_it_customer__skey
        description: "A created unique key for the dataset based off a concatenation of Batch Date and Subscription Name"
        quote: true
        tests:
          - unique
          - not_null

      - &billing_account_id
        name: billing_account_id
        description: "The Zuora Account ID coming from the Zuora Account entity E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - &subscription_id
        name: subscription_id
        description: "The ID of the Subscription, Subscription Names can have multiple IDs relating to different types of changes"
        quote: true

      - &subscription_name
        name: subscription_name
        description: "The Name of the Subscription E.g. A-S15283950"
        quote: true

      - &record_valid_from_timestamp
        name: record_valid_from_timestamp
        description: "The datetime this data point is valid from (inclusive)"
        quote: true

      - &record_valid_until_timestamp
        name: record_valid_until_timestamp
        description: "The datetime this data point is valid until (exlusive)"
        quote: true

      - name: subscription_name_original_created_timestamp
        description: "The timestamp of the creation of the original/first Subscription ID for this Subscription Name"
        quote: true

      - &subscription_source_system_name
        name: subscription_source_system_name
        description: "The name of the partner managing the subscription externally"
        quote: true

      - &subscription_tier
        name: subscription_tier
        description: "The Entitlement Set Id of the Rateplan, which is filtered to the associated RatePlanCharge's Flat Fee Pricing Model and Recurring Type, so producing the Tier for the Subscription"
        quote: true

      - &subscription_tracking_id
        name: subscription_tracking_id
        description: "The tracking ID for this subscription ID, if any"
        quote: true

      - &subscription_sign_up_campaign_id
        name: subscription_tracking_id
        description: "The campaign ID for this subscription ID, if any"
        quote: true

      - name: has_sky_it_tracking_id
        description: "Flag for if the tracking ID is identified to be one related to Sky IT"
        quote: true

      - name: has_sky_it_campaign_id
        description: "The campaign ID for this subscription ID, if any"
        quote: true

      - &has_subscription_add_on
        name: has_subscription_add_on
        description: "A flag for if there is any AddOn active at the time the Subscription Id is created"
        quote: true

      - &subscription_territory
        name: subscription_territory
        description: "The territory of the subscription, pulled in by a join to region_dimension, E.g. Spain, DACH, Japan, United States, UK and Ireland, ..."
        quote: true

      - &billing_account_currency_code
        name: billing_account_currency_code
        description: "The currency code for the Zuora Account, E.g. EUR, USD, ..."
        quote: true

      - name: last_sky_sub_name
        description: "The most recent previously active Subscription Name that contains Sky IT tracking IDs before the current Subscription"
        quote: true

      - name: last_sky_sub_valid_from
        description: "The Valid From of the most recent previously active Subscription Name that contains Sky IT tracking IDs before the current Subscription"
        quote: true

      - name: last_sky_sub_valid_until
        description: "The Valid Until of the most recent previously active Subscription Name that contains Sky IT tracking IDs before the current Subscription"
        quote: true

      - name: last_sky_sub_tracking_id
        description: "The Tracking ID of the most recent previously active Subscription Name that contains Sky IT tracking IDs before the current Subscription"
        quote: true

      - name: last_any_sub_name
        description: "The most recent previously active Subscription Name before the current Subscription"
        quote: true

      - name: last_any_sub_valid_from
        description: "The Valid From of the most recent previously active Subscription Name before the current Subscription"
        quote: true

      - name: last_any_sub_valid_until
        description: "The Valid Until of the most recent previously active Subscription Name before the current Subscription"
        quote: true

      - name: last_any_sub_tracking_id
        description: "The Tracking ID of the most recent previously active Subscription Name before the current Subscription"
        quote: true

      - name: sky_new_sub
        description: "If the current sub has sky tracking ID and there hasn't been any previous subs for that account then it's a sky new sub"
        quote: true

      - name: sky_resub
        description: "If the current sub has sky tracking ID and the last sub was more than 120 days then it's a sky resub"
        quote: true

      - name: resub_prev_sky
        description: "If the current sub does not have the sky tracking ID and is direct but they had a previous sub with the sky tracking ID within the last 120 days then it's a resub that was previously sky"
        quote: true

      - name: sky_it_customer_classification
        description: "Combination of the three classifications to uniquely identify the subscription"
        quote: true

  - name: subscription_daily_active_non_sky_it__dim
    description: "A dimension table detailing the non-Sky IT Tracking ID Subs"
    columns:
      - *META__DBT_INSERT_DTTS

      - *batch_date

      - name: non_sky_it_customer__skey
        description: "A created unique key for the dataset based off a concatenation of Batch Date and Subscription Name"
        quote: true
        tests:
          - unique

      - *billing_account_id

      - *subscription_id

      - *subscription_name

      - *record_valid_from_timestamp

      - *record_valid_until_timestamp

      - *subscription_source_system_name

      - *subscription_tier

      - *subscription_tracking_id

      - *has_subscription_add_on

      - *subscription_territory

      - *billing_account_currency_code

  - name: sky_tier_upgrades
    description: "A Sky source table containing all subscription that changed it's tier to an allowed tier (see variables in dbt macros for a list of allowed tiers) since august 2022."
    columns:
      - *META__DBT_INSERT_DTTS

      - &attribution_timestamp
        name: attribution_timestamp
        description: "The date when a sub becomes 'attributed' to sky. This is exactly 60 days after the upgrade date."
        quote: true

      - &attribution_date
        name: attribution_date
        description: "The date when a sub becomes 'attributed' to sky. This is exactly 60 days after the upgrade date."
        quote: true

      - *sky_it_customer__skey

      - *billing_account_id

      - *subscription_id

      - *subscription_name

      - *subscription_territory

      - *billing_account_currency_code

      - &tier_upgrade_timestamp
        name: tier_upgrade_timestamp
        description: "The timestamp when the customer upgraded tier to one of the allowed tiers."
        quote: true

      - &tier_upgrade_date
        name: tier_upgrade_date
        description: "The date when the customer upgraded tier to one of the allowed tiers."
        quote: true

      - &new_mrr_on_upgrade
        name: new_mrr_on_upgrade
        description: "The MRR of the subscription after the change to one of the allowed tiers."
        quote: true

      - &old_mrr_on_upgrade
        name: old_mrr_on_upgrade
        description: "The MRR of the subscription before the change to one of the allowed tiers."
        quote: true

  - name: sky_plays_it
    description: " A view containing the streaming data relevant to the subscriptions found in the sky_tier_upgrades dataset"
    columns:
      - &viewer_id
        name: viewer_id
        description: "Viewer ID from conviva, this can be used to join on customer history scd"
        quote: true

      - *billing_account_id

      - name: playback_stream_date
        description: "The date when the playback stream was initiated."
        quote: true

      - &user_agent
        name: user_agent
        description: "The user agent string - contains information about the software and hardware running on the device that is accessing the playback stream."
        quote: true

      - name: playback_duration_milliseconds
        description: "The amount of time that content was played during a stream, measured in milliseconds. Excludes buffering time."
        quote: true

      - name: is_sky_device
        description: "Boolean flag, true if this is a sky device. Defined using the sky variable in the presentation-layer dbt macros."
        quote: true


  - name: sky_plays_attribution
    description: >
      "
      For each subscription in the Tier Upgrade table, we derive the device that the customer used the most.
      Once we have this record, we only keep records that are not Sky Subscriptions (tracking_id <> sky) and where their
      most used device is a Sky Device.
      "
    columns:
      - *META__DBT_INSERT_DTTS

      - *attribution_timestamp

      - *attribution_date

      - *sky_it_customer__skey

      - *billing_account_id

      - *subscription_id

      - *subscription_name

      - *subscription_territory

      - *billing_account_currency_code

      - *tier_upgrade_timestamp

      - *tier_upgrade_date

      - *new_mrr_on_upgrade

      - *old_mrr_on_upgrade

      - *user_agent

      - name: ttl_playback_duration_ms
        description: "The total amount of milliseconds streamed by this customer with this user agent device"
        quote: true

      - name: is_active_on_attribution_date
        description: "Boolean, true if the subscription is active on the day of the attribution"
        quote: true


  - name: sky_plays_attribution_with_invoices
    description: >
      "
      A step to add all invoices that were posted for customers who changed tiers. The invoices
      will only be those posted on or after the date of the change.
      Note that this view will have duplicated the previous set of results due to a sub having multiple
      invoices
      "
    columns:
      - *META__DBT_INSERT_DTTS

      - *attribution_timestamp

      - *attribution_date

      - *sky_it_customer__skey

      - *billing_account_id

      - *subscription_id

      - *subscription_name

      - *subscription_territory

      - *billing_account_currency_code

      - *tier_upgrade_timestamp

      - *tier_upgrade_date

      - *new_mrr_on_upgrade

      - *old_mrr_on_upgrade

      - *user_agent

      - name: invoice_id
        description: "Id of the Invoice"
        quote: true

      - name: invoice_posted_timestamp
        description: "Timestamp the Invoice was Posted"
        quote: true

      - name: invoice_posted_date
        description: "Date the Invoice was Posted"
        quote: true

      - name: charge_subscription_amount
        description: "Total amount related to subscription charges"
        quote: true

      - name: tax_subscription_amount
        description: "Total amount related to subscription tax"
        quote: true

      - name: invoice_payment_amount
        description: "Payment Amount of the Invoice"
        quote: true

      - name: invoice_amount
        description: "Amount of the invoice"
        quote: true

      - name: invoice_attribution_type
        description: "The type of the invoice, can be during the attribution period or on or after the attribution date."
        quote: true

      - name: invoice_on_attribution
        description: "Boolean marker for an invoice being posted on or after the attribution timestamp"
        quote: true

      - name: invoice_during_attribution
        description: "Boolean marker for an invoice being posted in between the upgrade and the attribution timestamps"
        quote: true

      - name: invoice_paid
        description: "Boolean marker for an invoice being paid"
        quote: true


  - name: sky_it_upgrade_tier_attribution_report
    description: >
      "
      This report will contain all the charges and taxes posted and paid by customers who are attributed to sky.
      An attribution is defined by a customer upgrading to one of the allowed tiers (see list of allowed tiers in the dbt macro variable file).
      , and that their main device (in terms of viewing time) over the 60 days following the change has to be a sky device.
      They have to still be active 60 days after the change of tier.
      The report contains all invoices posted each day for these customers as well as those invoices that were posted
      during the attribution period that we have to retrospectively share the revenue of.
      "
    columns:
      - *META__DBT_INSERT_DTTS

      - &date
        name: date
        description: "The date aggregation of this report, either the batch date of the subscription classification or the date invoice was posted"
        quote: true

      - *tier_upgrade_date

      - *attribution_date

      - *billing_account_currency_code

      - *subscription_tier

      - *subscription_territory

      - *old_mrr_on_upgrade

      - *new_mrr_on_upgrade

      - name: batch_subscriptions_count
        description: "Distinct count of subscriptions with an invoice posted on this date"
        quote: true

      - name: batch_invoice_count
        description: "Number of invoices posted on the batch date for customers who are already attributed and generating regular invoices"
        quote: true

      - name: batch_subscription_charges_sum
        description: "Total subscription charges (excluding tax) for invoices posted on this date"
        quote: true

      - name: batch_subscription_tax_sum
        description: "Total subscription tax charges for invoices posted on this date"
        quote: true

      - name: batch_paid_subscription_charges_sum
        description: "Total subscription charges (excluding tax) for invoices posted on this date and paid for."
        quote: true

      - name: batch_paid_subscription_tax_sum
        description: "Total subscription tax charges for invoices posted on this date and paid for"
        quote: true

      - name: backdated_subscriptions_count
        description: "Distinct count of subscriptions that were attributed on this batch date"
        quote: true

      - name: backdated_invoices_count
        description: "Number of invoices posted during the attribution period for subscriptions who were attributed on this batch date"
        quote: true

      - name: backdated_subscription_charges_sum
        description: "Total subscription charges (excluding tax) for invoices posted during the attribution period. Only subscriptions who are attributed on this batch date are included in this measure"
        quote: true

      - name: backdated_subscription_tax_sum
        description: "Total subscription tax charges for invoices posted during the attribution period. Only subscriptions who are attributed on this batch date are included in this measure"
        quote: true

      - name: backdated_paid_subscription_charges_sum
        description: "Total subscription charges (excluding tax) for invoices posted during the attribution period and paid for. Only subscriptions who are attributed on this batch date are included in this measure"
        quote: true

      - name: backdated_paid_subscription_tax_sum
        description: "Total subscription tax charges for invoices posted during the attribution period and paid for. Only subscriptions who are attributed on this batch date are included in this measure"
        quote: true

      - name: total_invoices
        description: "Total number of invoices posted for this dimension, includes both batch and backdated invoices"
        quote: true

      - name: total_subscription_amount
        description: "Total subscription amount, including tax, posted for this dimension"
        quote: true

      - name: total_paid_subscription_amount
        description: "Total subscription amount that was paid, including tax, posted for this dimension"
        quote: true

      - name: payment_rate
        description: "The total subscription amount divided by the total paid subscription amount"
        quote: true

      - name: mrr_difference
        description: "The difference in MRRs before and after the change"
        quote: true

      - &record_updated_at
        name: record_updated_at
        description: "The datetime this row was inserted into this table"
        quote: true

  - name: sky_it_subscriber_revenue__report
    description: "Aggregated report representing the invoice details for all Sky IT subscribers on a certain date"
    columns:
      - *date

      - &month
        name: month
        description: "The month of the date in in yyyy-mm-01 format"
        quote: true

      - name: sky_attribution_type
        description: "Combination of the three classifications to uniquely identify the subscription"
        quote: true

      - *subscription_tier

      - name: subscription_tracking_id_type
        description: "The classification of the tracking ID based on which user journey it's gone through"
        quote: true

      - *subscription_sign_up_campaign_id

      - *has_subscription_add_on

      - *billing_account_currency_code

      - *subscription_territory

      - &total_subscription_count
        name: total_subscription_count
        description: "The total count of the subscriptions on this date with the above dimensions"
        quote: true

      - &opening_month_subscription_count
        name: opening_month_subscription_count
        description: "The total_subscription_count when the date is the first date in the dataset for this month"
        quote: true

      - &closing_month_subscription_count
        name: closing_month_subscription_count
        description: "The total_subscription_count when the date is the last date in the dataset for this month"
        quote: true

      - &distinct_invoices_posted
        name: distinct_invoices_posted
        description: "The total count of invoices for subscriptions posted on this date with the above dimensions"
        quote: true

      - name: total_subscription_charges
        description: "The total amount of subscription charges in invoices for subscriptions posted on this date with the above dimensions"
        quote: true

      - name: total_subscription_tax_amount
        description: "The total amount of subscription tax in invoices for subscriptions posted on this date with the above dimensions"
        quote: true

      - &total_add_on_charges
        name: total_add_on_charges
        description: "The total amount of AddOn charges in invoices for subscriptions posted on this date with the above dimensions"
        quote: true

      - &total_add_on_tax_amount
        name: total_add_on_tax_amount
        description: "The total amount of AddOn tax in invoices for subscriptions posted on this date with the above dimensions"
        quote: true

      - name: total_subscription_charge_paid_invoices
        description: "The total amount of subscription charges paid in invoices for subscriptions posted on this date with the above dimensions"
        quote: true

      - &total_add_on_charges_paid_invoices
        name: total_add_on_charges_paid_invoices
        description: "The total amount of AddOn charges paid in invoices for subscriptions posted on this date with the above dimensions"
        quote: true

      - *record_updated_at

  - name: sky_it_subscriber_upsold_revenue__report
    description: "Aggregeated report representing the AddOn invoice details for non-Sky IT subscribers on a certain date"
    columns:
      - *date

      - *month

      - *has_subscription_add_on

      - *billing_account_currency_code

      - *subscription_territory

      - *total_subscription_count

      - *opening_month_subscription_count

      - *closing_month_subscription_count

      - *distinct_invoices_posted

      - *total_add_on_charges

      - *total_add_on_tax_amount

      - *total_add_on_charges_paid_invoices

      - *record_updated_at

  - name: sky_it_soip_adjustments__report
    description: "Aggregeated report representing the SOIP adjustments need to estimate the Sky IP user's AddOn rate from the non-SOIP AddOn rate over time"
    columns:
      - *date

      - *subscription_territory

      - &subscription_is_batch_50
        name: subscription_is_batch_50
        description: "Boolean flag for if this viewer/subscription has a Zuora account in Batch50, implying it's a test account"
        quote: true

      - name: is_sky_subscriber
        description: "Boolean flag for if this viewer has an active subscription that's attributed to Sky on the given batch_date"
        quote: true

      - name: has_used_IP_device
        description: "Boolean flag for if this viewer has watched more than one minute of content on a Sky IP device in the current month of the batch_date"
        quote: true

      - name: has_used_non_IP_device
        description: "Boolean flag for if this viewer has watched more than one minute of content on a Sky non-IP device in the current month of the batch_date"
        quote: true

      - *has_subscription_add_on

      - name: has_soip_tracking_id
        description: "Boolean flag for if this viewer/subscription has been signed up with the SOIP tracking ID"
        quote: true

      - name: total_viewer_count
        description: "The total count of the viewers on this date with the above dimensions, potentiaslly containing duplicate viewers, if they have overlapping subscriptions"
        quote: true

      - name: total_viewer_distinct_count
        description: "The total distinct count of the viewers on this date with the above dimensions, counting one viewer even if they have two active overlapping subscriptions at this time"
        quote: true

      - name: non_ip_device_subscription_count
        description: "The count of the Italian subscriptions that have streamed on Sky IT non-IP devices on the given date"
        quote: true

      - name: non_ip_device_add_on_subscription_count
        description: "The count of the Italian subscriptions that have streamed on Sky IT non-IP devices and have an active add on on the given date"
        quote: true

      - name: ip_device_subscription_count
        description: "The count of the Italian subscriptions that have streamed on Sky IT IP devices on the given date"
        quote: true

      - *record_updated_at
