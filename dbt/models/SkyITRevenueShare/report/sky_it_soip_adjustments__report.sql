{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"date"',
        database='SKY_IT_REVENUE_SHARE__' + snowflake_env(),
        schema='REPORT',
        tags=['presentation-sky-it-revenue-share']
    )
}}

-- bring in variables from the macro in dbt/macros/SkyITRevenueShare/sky_it_variables.sql
{% set sky_it_variables = sky_it_variables() %}

WITH dim_date AS (
    SELECT * FROM {{ ref_env('dim_date') }}
    WHERE
        {% if is_incremental() %}
        -- if the model is incremental we only need records that have a valid until after the batch date.
        "date_day" = '{{ var('batch_date') }}'
        {% else %}
        -- if it's a full refresh we get any date after the start of the sky deal
        "date_day" BETWEEN '2022-08-08' AND DATEADD('day', -1, CURRENT_DATE)
    {% endif %}
)

,playback_stream_fact AS (
    SELECT * FROM {{ ref_env('MART__PLAYBACK_STREAM__FACT') }}
    WHERE
        {% if is_incremental() %}
        -- if the model is incremental we only need streams in the current calendar month for the batch date
        "playback_stream_date" BETWEEN DATE_TRUNC('month', '{{ var('batch_date') }}'::DATE) AND '{{ var('batch_date') }}'
        {% else %}
        -- if it's a full refresh we get any date after the start of the sky deal
        "playback_stream_date" BETWEEN '2022-08-08' AND DATEADD('day', -1, CURRENT_DATE)
        {% endif %}
        -- Streams only on Sky devices (SOIP and non-SOIP)
        AND
        "user_agent" IN ({{ in_clause_from_list(sky_it_variables.user_agents.values() | sum(start=[])) }})
)

,content_item_dim_italy AS (
    SELECT * FROM {{ ref_env('MART__CONTENT_ITEM__DIM') }}
    WHERE
        -- Streams only in Italy
        "outlet" = 'italy'
)

,cust_id_dim AS (
    SELECT * FROM {{ ref_env('customer_identity_dim_current') }}
)

,sub_name_scd AS (
    SELECT * FROM {{ ref_env('subscription_name__scd') }}
    WHERE
        -- only need ot bring thorugh subs in Italy
        "subscription_territory" = 'Italy'
)

,daily_sky_it_sub_dim AS (
    SELECT * FROM {{ ref('subscription_daily_active_sky_it__dim') }}
    {% if is_incremental() %}
        -- if the model is incremental we only need streams in the current calendar month for the batch date
        WHERE "batch_date" BETWEEN DATE_TRUNC('month', '{{ var('batch_date') }}'::DATE) AND '{{ var('batch_date') }}'
    {% endif %}
)

,sky_playback AS (
    SELECT
        dim_date."date_day" AS "date"
        ,playback_stream_fact."viewer_id"
        ,sub_name_scd."billing_account_id"
        ,sub_name_scd."subscription_territory"
        ,sub_name_scd."billing_account_is_batch_50" AS "subscription_is_batch_50"
        ,sub_name_scd."subscription_name"
        ,daily_sky_it_sub_dim."billing_account_id" IS NOT NULL AS "is_sky_subscriber"
        ,sub_name_scd."subscription_tracking_id"
        ,sub_name_scd."has_subscription_add_on"
        -- has the subscription got the specific SOIP Tracking Id
        ,IFNULL(sub_name_scd."subscription_tracking_id", '-') IN ({{ in_clause_from_list(sky_it_variables.tracking_ids['soip']) }}) AS "has_soip_tracking_id"
        -- how much content has the viewer viewed on the specific SOIP devices
        ,SUM(CASE WHEN playback_stream_fact."user_agent" IN ({{ in_clause_from_list(sky_it_variables.user_agents['soip']) }}) THEN playback_stream_fact."playback_duration_milliseconds" ELSE 0 END) AS "total_soip_playback_duration_milliseconds"
        -- how much content has the viewer viewed on the specific non-SOIP devices
        ,SUM(CASE WHEN playback_stream_fact."user_agent" IN ({{ in_clause_from_list(sky_it_variables.user_agents['non_soip']) }}) THEN playback_stream_fact."playback_duration_milliseconds" ELSE 0 END) AS "total_non_soip_playback_duration_milliseconds"
        ,SUM(playback_stream_fact."playback_duration_milliseconds") AS "total_playback_duration_milliseconds"
    FROM dim_date
    INNER JOIN playback_stream_fact
        -- join playback for the whole calendar month of the batch date
        ON playback_stream_fact."playback_stream_date" BETWEEN DATE_TRUNC('month', dim_date."date_day") AND dim_date."date_day"
    INNER JOIN content_item_dim_italy
        ON playback_stream_fact."content_item__skey" = content_item_dim_italy."content_item__skey"
    LEFT JOIN cust_id_dim
        ON playback_stream_fact."viewer_id" = cust_id_dim."viewer_id"
    LEFT JOIN sub_name_scd
        ON
            cust_id_dim."billing_account_id" = sub_name_scd."billing_account_id"
            -- Join subs at the time of the stream date
            AND
            sub_name_scd."record_valid_from_timestamp" <= playback_stream_fact."playback_start_timestamp"
            AND
            sub_name_scd."record_valid_until_timestamp" > playback_stream_fact."playback_start_timestamp"
    LEFT JOIN daily_sky_it_sub_dim
        ON
            cust_id_dim."billing_account_id" = daily_sky_it_sub_dim."billing_account_id"
            AND
            playback_stream_fact."playback_stream_date" = daily_sky_it_sub_dim."batch_date"
    GROUP BY 1,2,3,4,5,6,7,8,9
    -- Have streamed at least 1 minute of content on either of these user agent sets
    HAVING "total_soip_playback_duration_milliseconds" >= 60000 OR "total_non_soip_playback_duration_milliseconds" >= 60000
)

SELECT
    "date"
    ,"subscription_territory"
    ,"subscription_is_batch_50"
    ,"is_sky_subscriber"
    ,"total_soip_playback_duration_milliseconds" > 0 AS "has_used_IP_device"
    ,"total_non_soip_playback_duration_milliseconds" > 0 AS "has_used_non_IP_device"
    ,"has_subscription_add_on"
    ,"has_soip_tracking_id"
    ,COUNT("viewer_id") AS "total_viewer_count"
    ,COUNT(DISTINCT "viewer_id") AS "total_viewer_distinct_count"
    ,COUNT_IF("subscription_is_batch_50" = FALSE AND "subscription_territory" = 'Italy' AND "has_used_non_IP_device") AS "non_ip_device_subscription_count"
    ,COUNT_IF("subscription_is_batch_50" = FALSE AND "subscription_territory" = 'Italy' AND "has_used_non_IP_device" AND "has_subscription_add_on") AS "non_ip_device_add_on_subscription_count"
    ,COUNT_IF("subscription_is_batch_50" = FALSE AND "subscription_territory" = 'Italy' AND "is_sky_subscriber" = FALSE AND "has_used_IP_device" AND "has_subscription_add_on" = FALSE) AS "ip_device_subscription_count"
    ,CURRENT_TIMESTAMP() AS "record_updated_at"
FROM sky_playback
GROUP BY 1,2,3,4,5,6,7,8
