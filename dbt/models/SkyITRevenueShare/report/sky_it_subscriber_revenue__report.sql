{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"date"',
        database='SKY_IT_REVENUE_SHARE__' + snowflake_env(),
        schema='REPORT',
        tags=['presentation-sky-it-revenue-share']
    )
}}

WITH daily_sky_it_sub_dim AS (
    SELECT * FROM {{ ref('subscription_daily_active_sky_it__dim') }}
)

,invoice_fact AS (
    SELECT * FROM {{ ref_env('fct_invoice') }}
    WHERE "invoice_posted_timestamp"::DATE >= '2022-08-01'
)

SELECT
    daily_sky_it_sub_dim."batch_date"::DATE AS "date"
    ,DATE_TRUNC('month', "date") AS "month"
    ,daily_sky_it_sub_dim."sky_it_customer_classification" AS "sky_attribution_type"
    ,daily_sky_it_sub_dim."subscription_tier"
    ,CASE
        WHEN daily_sky_it_sub_dim."subscription_tracking_id" = 'de276c21-b9f9-4572-8aa5-acd422db40a2' THEN 'non_soip'
        WHEN daily_sky_it_sub_dim."subscription_tracking_id" = '5746246d-fa5f-45dd-878a-ca19ec702cb0' THEN 'soip'
        ELSE 'other'
    END AS "subscription_tracking_id_type"
    ,daily_sky_it_sub_dim."subscription_sign_up_campaign_id"
    ,daily_sky_it_sub_dim."has_subscription_add_on"
    ,daily_sky_it_sub_dim."billing_account_currency_code"
    ,daily_sky_it_sub_dim."subscription_territory"
    ,COUNT(daily_sky_it_sub_dim."subscription_name") AS "total_subscription_count"
    ,CASE WHEN "date" = MIN("date") OVER (PARTITION BY "month") THEN "total_subscription_count" ELSE 0 END AS "opening_month_subscription_count"
    ,CASE WHEN "date" = MAX("date") OVER (PARTITION BY "month") THEN "total_subscription_count" ELSE 0 END AS "closing_month_subscription_count"
    ,COUNT(DISTINCT invoice_fact."invoice_id") AS "distinct_invoices_posted"
    ,SUM(invoice_fact."charge_subscription_amount") AS "total_subscription_charges"
    ,SUM(invoice_fact."tax_subscription_amount") AS "total_subscription_tax_amount"
    ,SUM(invoice_fact."charge_addon_amount_SKY_IT") AS "total_add_on_charges"
    ,SUM(invoice_fact."tax_addon_amount_SKY_IT") AS "total_add_on_tax_amount"
    ,SUM(CASE WHEN invoice_fact."invoice_payment_amount" = invoice_fact."invoice_amount" OR invoice_fact."invoice_amount" < 0 THEN invoice_fact."charge_subscription_amount" ELSE 0 END) AS "total_subscription_charge_paid_invoices"
    ,SUM(CASE WHEN invoice_fact."invoice_payment_amount" = invoice_fact."invoice_amount" OR invoice_fact."invoice_amount" < 0 THEN invoice_fact."charge_addon_amount_SKY_IT" ELSE 0 END) AS "total_add_on_charges_paid_invoices"
    ,CURRENT_TIMESTAMP AS "record_updated_at"
FROM daily_sky_it_sub_dim
LEFT JOIN invoice_fact
    ON daily_sky_it_sub_dim."subscription_name" = invoice_fact."subscription_name"
        AND daily_sky_it_sub_dim."batch_date" = invoice_fact."invoice_posted_timestamp"::DATE
WHERE 1 = 1
{% if is_incremental() %}
    AND
    daily_sky_it_sub_dim."batch_date" BETWEEN DATEADD('month', -1, DATE_TRUNC('month', '{{ var('batch_date') }}'::DATE)) AND '{{ var('batch_date') }}'
{% endif %}
GROUP BY 1,2,3,4,5,6,7,8,9
