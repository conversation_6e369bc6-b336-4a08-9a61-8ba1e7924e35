{{
    config(
        materialized='table',
        database='SKY_IT_REVENUE_SHARE__' + snowflake_env(),
        schema='REPORT',
        tags=['presentation-sky-it-revenue-share']
    )
}}

{% set sky_it_variables = sky_it_variables() %}

WITH invoices AS (
    SELECT * FROM {{ ref('sky_plays_attribution_with_invoices') }}
)

, dim_date AS (
    SELECT * FROM {{ ref_env('dim_date') }}
    WHERE "date_day" BETWEEN '{{ sky_it_variables.sky_deal_start_date }}'::DATE AND DATEADD('day', -1, CURRENT_DATE)
)

SELECT
    dim_date."date_day" AS "date"
    ,invoices."tier_upgrade_date"
    ,invoices."attribution_date"
    ,invoices."billing_account_currency_code"
    ,invoices."subscription_territory"
    ,invoices."old_mrr_on_upgrade"
    ,invoices."new_mrr_on_upgrade"
    -- Metrics related to attributed customers generating invoices after the attribution date.
    ,COUNT(DISTINCT IFF(invoices."invoice_on_attribution", invoices."subscription_name", NULL)) AS "batch_subscriptions_count"
    ,COUNT(DISTINCT IFF(invoices."invoice_on_attribution", invoices."invoice_id", NULL)) AS "batch_invoice_count"
    ,SUM(IFF(invoices."invoice_on_attribution", invoices."charge_subscription_amount", 0)) AS "batch_subscription_charges_sum"
    ,SUM(IFF(invoices."invoice_on_attribution", invoices."tax_subscription_amount", 0)) AS "batch_subscription_tax_sum"
    ,SUM(IFF(invoices."invoice_on_attribution" AND invoices."invoice_paid", invoices."charge_subscription_amount", 0)) AS "batch_paid_subscription_charges_sum"
    ,SUM(IFF(invoices."invoice_on_attribution" AND invoices."invoice_paid", invoices."tax_subscription_amount", 0)) AS "batch_paid_subscription_tax_sum"
    -- Metrics related to invoices generated by attributed customers during their attribution period.
    ,COUNT(DISTINCT IFF(invoices."invoice_during_attribution", invoices."subscription_name", NULL)) AS "backdated_subscription_count"
    ,COUNT(DISTINCT IFF(invoices."invoice_during_attribution", invoices."invoice_id", NULL)) AS "backdated_invoice_count"
    ,SUM(IFF(invoices."invoice_during_attribution", invoices."charge_subscription_amount", 0)) AS "backdated_subscription_charges_sum"
    ,SUM(IFF(invoices."invoice_during_attribution", invoices."tax_subscription_amount", 0)) AS "backdated_subscription_tax_sum"
    ,SUM(IFF(invoices."invoice_during_attribution" AND invoices."invoice_paid", invoices."charge_subscription_amount", 0)) AS "backdated_paid_subscription_charges_sum"
    ,SUM(IFF(invoices."invoice_during_attribution" AND invoices."invoice_paid", invoices."tax_subscription_amount", 0)) AS "backdated_paid_subscription_tax_sum"
    -- Totals
    ,"batch_invoice_count" + "backdated_invoice_count" AS "total_invoices"
    ,"batch_subscription_charges_sum" + "batch_subscription_tax_sum" + "backdated_subscription_charges_sum" + "backdated_subscription_tax_sum" AS "total_subscription_amount"
    ,"batch_paid_subscription_charges_sum" + "batch_paid_subscription_tax_sum" + "backdated_paid_subscription_charges_sum" + "backdated_paid_subscription_tax_sum" AS "total_paid_subscription_amount"
    ,IFF("total_subscription_amount" = 0, 0, "total_paid_subscription_amount" / "total_subscription_amount") AS "payment_rate"
    ,invoices."new_mrr_on_upgrade" - invoices."old_mrr_on_upgrade" AS "mrr_difference"
    ,CURRENT_TIMESTAMP AS "record_updated_at"
FROM dim_date
-- Look for all invoices posted on the batch date
LEFT JOIN invoices
    ON
        -- either those invoice posted on this batch date, for customers already attributed
        (
            dim_date."date_day" = invoices."invoice_posted_date"
            AND invoices."invoice_attribution_type" = 'invoice_on_attribution'
        )
        -- Or those invoices posted during the attribution for customers attributed on this batch date.
        OR
        (
            dim_date."date_day" = invoices."attribution_date"
            AND invoices."invoice_attribution_type" = 'invoice_during_attribution'
        )
GROUP BY 1,2,3,4,5,6,7
ORDER BY 1,2
