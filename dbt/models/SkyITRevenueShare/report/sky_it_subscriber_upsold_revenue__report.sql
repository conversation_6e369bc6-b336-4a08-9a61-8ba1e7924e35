{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"date"',
        database='SKY_IT_REVENUE_SHARE__' + snowflake_env(),
        schema='REPORT',
        tags=['presentation-sky-it-revenue-share']
    )
}}

WITH subscription_daily_active_non_sky_it__dim AS (
    SELECT * FROM {{ ref('subscription_daily_active_non_sky_it__dim') }}
)

,invoice_fact AS (
    SELECT * FROM {{ ref_env('fct_invoice') }}
    WHERE "invoice_posted_timestamp"::DATE >= '2022-08-01'
)

SELECT
    subscription_daily_active_non_sky_it__dim."batch_date" AS "date"
    ,DATE_TRUNC('month', "date") AS "month"
    ,subscription_daily_active_non_sky_it__dim."has_subscription_add_on"
    ,subscription_daily_active_non_sky_it__dim."subscription_tier"
    ,subscription_daily_active_non_sky_it__dim."billing_account_currency_code"
    ,subscription_daily_active_non_sky_it__dim."subscription_territory"
    ,COUNT(subscription_daily_active_non_sky_it__dim."subscription_name") AS "total_subscription_count"
    ,CASE WHEN "date" = MIN("date") OVER (PARTITION BY "month") THEN "total_subscription_count" ELSE 0 END AS "opening_month_subscription_count"
    ,CASE WHEN "date" = MAX("date") OVER (PARTITION BY "month") THEN "total_subscription_count" ELSE 0 END AS "closing_month_subscription_count"
    ,COUNT(DISTINCT CASE WHEN invoice_fact."charge_addon_amount_SKY_IT" != 0 THEN invoice_fact."invoice_id" END) AS "distinct_invoices_posted"
    ,SUM(invoice_fact."charge_addon_amount_SKY_IT") AS "total_add_on_charges"
    ,SUM(invoice_fact."tax_addon_amount_SKY_IT") AS "total_add_on_tax_amount"
    ,SUM(CASE WHEN invoice_fact."invoice_payment_amount" = invoice_fact."invoice_amount" OR invoice_fact."invoice_amount" < 0 THEN invoice_fact."charge_addon_amount_SKY_IT" ELSE 0 END) AS "total_add_on_charges_paid_invoices"
    ,CURRENT_TIMESTAMP AS "record_updated_at"
FROM subscription_daily_active_non_sky_it__dim
LEFT JOIN invoice_fact
    ON subscription_daily_active_non_sky_it__dim."subscription_name" = invoice_fact."subscription_name"
        AND subscription_daily_active_non_sky_it__dim."batch_date" = invoice_fact."invoice_posted_timestamp"::DATE
WHERE
    {% if is_incremental() %}
        subscription_daily_active_non_sky_it__dim."batch_date" BETWEEN DATEADD('month', -1, DATE_TRUNC('month', '{{ var('batch_date') }}'::DATE)) AND '{{ var('batch_date') }}'
        AND
    {% endif %}
    1 = 1
GROUP BY 1,2,3,4,5,6
