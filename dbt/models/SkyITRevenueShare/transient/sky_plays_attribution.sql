{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"attribution_date"',
        database='SKY_IT_REVENUE_SHARE__' + snowflake_env(),
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_S_WH',
        schema='TRANSIENT',
        tags=['presentation-sky-it-revenue-share']
    )
}}

{% set sky_it_variables = sky_it_variables() %}

WITH tier_upgrade_source AS (
    SELECT * FROM {{ ref('sky_tier_upgrades') }}
    {% if is_incremental() %}
        -- if the model is incremental we only select the batch date.
        -- otherwise we don't filter and we get all the changes we have
        WHERE "attribution_date" = '{{ var('batch_date') }}'
    {% endif %}
)

-- plays already contain streams for only the subs in the tier upgrades dataset, so we don't have to worry about filtering.
, plays_attribution_source AS (
    SELECT * FROM {{ ref('sky_plays_it') }}
)

, subscription_source AS (
    SELECT * FROM {{ ref_env('subscription_name__scd') }}
    {% if is_incremental() %}
        -- if the model is incremental we only need records that have a valid until after the batch date.
    WHERE "record_valid_until_timestamp"::DATE >= '{{ var('batch_date') }}'
    {% else %}
    -- if it's a full rebuild we get any record valid until after the start of the sky deal.
    WHERE "record_valid_until_timestamp"::DATE >= '{{ sky_it_variables.sky_deal_start_date }}'
    {% endif %}
)


-- Join the plays data onto the tier changes data. Aggregate on user agent to find playing time per device.
, device_ranking AS (
    SELECT
        tier_upgrade_source.*
        ,plays_attribution_source."user_agent"
        ,plays_attribution_source."is_sky_device"
        ,SUM(plays_attribution_source."playback_duration_milliseconds") AS "ttl_playback_duration_ms"
    FROM tier_upgrade_source
    LEFT JOIN plays_attribution_source
        ON
            plays_attribution_source."billing_account_id" = tier_upgrade_source."billing_account_id"
            AND
            -- we keep all the streams that occurred between the upgrade date and the attribution date
            plays_attribution_source."playback_stream_date" >= tier_upgrade_source."tier_upgrade_date"
            AND
            plays_attribution_source."playback_stream_date" <= tier_upgrade_source."attribution_date"
    GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15
)

-- select only the device with the most playing time. This returns the table to one row per subscription name.
, most_used_device AS (
    SELECT * FROM device_ranking
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "sky_it_customer__skey" ORDER BY "ttl_playback_duration_ms" DESC) = 1
)

-- use the subscription name scd to check if the sub is still active on the batch date.

SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,most_used_device."attribution_timestamp"
    ,most_used_device."attribution_date"
    ,most_used_device."sky_it_customer__skey"
    ,most_used_device."billing_account_id"
    ,most_used_device."subscription_id"
    ,most_used_device."subscription_name"
    ,most_used_device."subscription_territory"
    ,most_used_device."billing_account_currency_code"
    ,most_used_device."tier_upgrade_timestamp"
    ,most_used_device."tier_upgrade_date"
    ,most_used_device."new_mrr_on_upgrade"
    ,most_used_device."old_mrr_on_upgrade"
    ,most_used_device."user_agent"
    ,most_used_device."ttl_playback_duration_ms"
    ,IFF(subscription_source."subscription_name" IS NULL, FALSE, TRUE) AS "is_active_on_attribution_date"
FROM most_used_device
LEFT JOIN subscription_source
    ON
        most_used_device."subscription_name" = subscription_source."subscription_name"
        AND
        most_used_device."attribution_date" >= subscription_source."record_valid_from_timestamp"
        AND
        most_used_device."attribution_date" < subscription_source."record_valid_until_timestamp"
WHERE
    most_used_device."is_sky_device"
    AND
    "is_active_on_attribution_date"
