{{
    config(
        database='SKY_IT_REVENUE_SHARE__' + snowflake_env(),
        schema='TRANSIENT',
        tags=['presentation-sky-it-revenue-share']
    )
}}

{% set sky_it_variables = sky_it_variables() %}

WITH plays_attribution_source AS (
    SELECT * FROM {{ ref('sky_plays_attribution') }}
)

, invoice_source AS (
    SELECT * FROM {{ ref_env('fct_invoice') }}
    WHERE TRUE
        -- we're only interested in invoices that have a subscription charge.
        AND
        "total_subscription_amount" > 0
        -- and invoices posted after the sky deal started
        AND
        "invoice_posted_timestamp"::DATE >= '{{ sky_it_variables.sky_deal_start_date }}'::DATE
)

-- We collect every invoice that was posted on or after the upgrade date.
SELECT
    plays_attribution_source.*
    ,invoice_source."invoice_id"
    ,invoice_source."invoice_posted_timestamp"
    ,invoice_source."invoice_posted_timestamp"::DATE AS "invoice_posted_date"
    ,invoice_source."charge_subscription_amount"
    ,invoice_source."tax_subscription_amount"
    ,invoice_source."invoice_payment_amount"
    ,invoice_source."invoice_amount"
    ,CASE
        WHEN invoice_source."invoice_id" IS NULL THEN 'no_invoice'
        WHEN invoice_source."invoice_posted_timestamp" < plays_attribution_source."tier_upgrade_timestamp" THEN 'invoice_before_upgrade'
        WHEN invoice_source."invoice_posted_timestamp" >= plays_attribution_source."tier_upgrade_timestamp" AND "invoice_posted_date" < plays_attribution_source."attribution_timestamp" THEN 'invoice_during_attribution'
        WHEN invoice_source."invoice_posted_timestamp" >= plays_attribution_source."attribution_timestamp" THEN 'invoice_on_attribution'
        ELSE 'other' --we shouldn't have any, can use to check if we missed any
    END AS "invoice_attribution_type"
    ,("invoice_attribution_type" = 'invoice_on_attribution') AS "invoice_on_attribution"
    ,("invoice_attribution_type" = 'invoice_during_attribution') AS "invoice_during_attribution"
    ,(invoice_source."invoice_payment_amount" = invoice_source."invoice_amount") AS "invoice_paid"
FROM plays_attribution_source
LEFT JOIN invoice_source
    ON plays_attribution_source."subscription_name" = invoice_source."subscription_name"
-- we can remove any invoice that was posted before the tier upgrade date.
WHERE "invoice_attribution_type" != 'invoice_before_upgrade'
