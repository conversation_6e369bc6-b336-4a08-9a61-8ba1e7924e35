# Sky IT Revenue Share Reports

## Plays attributions

### Key terms

- `date`: the batch date for the report, each day we insert new rows with this date.
- `upgrade_date`: the date when a subscription upgraded from silver to gold, defines the cohort
- `attribution_date`: The date when a subscription can be attributed to sky, 60 days after they upgraded.
- `invoice_posted_date`: The date when an invoice is posted.
- `cohort`: a group of users who upgraded on a specific date.
- `backdated`: fields specific to invoices posted during the attribution period
- `batch`: fields specific to invoices posted on the report date.

The data will contain one row per cohort per day since their attribution date.
For a subscription that upgrades, all invoices (that contain a subscription charge) posted during the attribution period
will be available in the `backdated` fields the first time the cohort appears in the
report. This implies that the `backdated` fields will only be populated when `date` = `attribution_date`.
All invoices posted after the attribution date will be populating the `batch`
fields and populate the rows where `date` = `invoice_posted_date`.

A cohort would appear in the following way in the data;

``` table
  date         tier_upgrade_date      attribution_date      batch_invoice_count  backdated_invoice_count
  2022-10-06      2022-08-07      2022-10-06 00:00:00+00:00           0                   179
  2022-10-07      2022-08-07      2022-10-06 00:00:00+00:00           83                  0
  2022-10-08      2022-08-07      2022-10-06 00:00:00+00:00           3                   0
  2022-10-09      2022-08-07      2022-10-06 00:00:00+00:00           1                   0
```
