{{
    config(
        materialized='table',
        schema='PRESENTATION',
        tags=['presentation-date']
    )
}}

WITH date_dimension AS (
    {{ dbt_date.get_date_dimension('2016-01-01', '2026-01-01') }}
)

, nfl_week_names AS (
    SELECT * FROM {{ ref('nfl_week_names') }}
)

, date_attributes AS (
    SELECT
        "DATE_DAY" AS "date_day"
        ,"PRIOR_DATE_DAY" AS "prior_date_day"
        ,"NEXT_DATE_DAY" AS "next_date_day"
        ,"PRIOR_YEAR_DATE_DAY" AS "prior_year_date_day"
        ,"PRIOR_YEAR_OVER_YEAR_DATE_DAY" AS "prior_year_over_year_date_day"
        ,"DAY_OF_WEEK_ISO" AS "day_of_week"
        ,"DAY_OF_WEEK_NAME" AS "day_of_week_name"
        ,"DAY_OF_WEEK_NAME_SHORT" AS "day_of_week_name_short"
        ,"DAY_OF_MONTH" AS "day_of_month"
        ,"DAY_OF_YEAR" AS "day_of_year"
        ,"ISO_WEEK_START_DATE" AS "week_start_date"
        ,"ISO_WEEK_END_DATE" AS "week_end_date"
        ,"PRIOR_YEAR_ISO_WEEK_START_DATE" AS "prior_year_week_start_date"
        ,"PRIOR_YEAR_ISO_WEEK_END_DATE" AS "prior_year_week_end_date"
        ,"ISO_WEEK_OF_YEAR" AS "week_of_year"
        ,"PRIOR_YEAR_ISO_WEEK_OF_YEAR" AS "prior_year_week_of_year"
        ,"MONTH_OF_YEAR" AS "month_of_year"
        ,"MONTH_NAME" AS "month_name"
        ,"MONTH_NAME_SHORT" AS "month_name_short"
        ,"MONTH_START_DATE" AS "month_start_date"
        ,"MONTH_END_DATE" AS "month_end_date"
        ,"PRIOR_YEAR_MONTH_START_DATE" AS "prior_year_month_start_date"
        ,"PRIOR_YEAR_MONTH_END_DATE" AS "prior_year_month_end_date"
        ,"QUARTER_OF_YEAR" AS "quarter_of_year"
        ,"QUARTER_START_DATE" AS "quarter_start_date"
        ,"QUARTER_END_DATE" AS "quarter_end_date"
        ,"YEAR_NUMBER" AS "year_number"
        ,"YEAR_START_DATE" AS "year_start_date"
        ,"YEAR_END_DATE" AS "year_end_date"
        ,"date_day" = "week_end_date" AS "is_week_end"
        ,"date_day" = "month_end_date" AS "is_month_end"
        ,"date_day" = "year_end_date" AS "is_year_end"
        ,COALESCE(DATEADD(day,1,LAG("week_start_date") OVER (ORDER BY "DATE_DAY")),'2015-12-29') AS "nfl_week_start_date"
        ,COALESCE(DATEADD(day,1,LAG("week_end_date") OVER (ORDER BY "DATE_DAY")),'2016-01-04') AS "nfl_week_end_date"
        ,CASE
            WHEN "day_of_week_name" = 'Monday' THEN 7
            ELSE "day_of_week"-1
        END AS "nfl_day_of_week"
    FROM date_dimension
)

,enriched_nfl_week_attributes AS (
    SELECT date_attributes."date_day"
        ,date_attributes."prior_date_day"
        ,date_attributes."next_date_day"
        ,date_attributes."prior_year_date_day"
        ,date_attributes."prior_year_over_year_date_day"
        ,date_attributes."day_of_week"
        ,date_attributes."day_of_week_name"
        ,date_attributes."day_of_week_name_short"
        ,date_attributes."day_of_month"
        ,date_attributes."day_of_year"
        ,date_attributes."week_start_date"
        ,date_attributes."week_end_date"
        ,date_attributes."prior_year_week_start_date"
        ,date_attributes."prior_year_week_end_date"
        ,date_attributes."week_of_year"
        ,date_attributes."prior_year_week_of_year"
        ,date_attributes."month_of_year"
        ,date_attributes."month_name"
        ,date_attributes."month_name_short"
        ,date_attributes."month_start_date"
        ,date_attributes."month_end_date"
        ,date_attributes."prior_year_month_start_date"
        ,date_attributes."prior_year_month_end_date"
        ,date_attributes."quarter_of_year"
        ,date_attributes."quarter_start_date"
        ,date_attributes."quarter_end_date"
        ,date_attributes."year_number"
        ,date_attributes."year_start_date"
        ,date_attributes."year_end_date"
        ,date_attributes."is_week_end"
        ,date_attributes."is_month_end"
        ,date_attributes."is_year_end"
        ,date_attributes."nfl_week_start_date"
        ,date_attributes."nfl_week_end_date"
        ,date_attributes."nfl_day_of_week"
        ,nfl_week_names."nfl_season"
        ,nfl_week_names."nfl_week_order"
        ,nfl_week_names."nfl_week_name"
        ,nfl_week_names."nfl_full_week_name"
    FROM date_attributes
    LEFT JOIN nfl_week_names
        USING ("nfl_week_start_date")
)

SELECT * FROM enriched_nfl_week_attributes
