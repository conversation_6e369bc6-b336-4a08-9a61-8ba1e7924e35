-- Define the table materialization
{{
    config(
        materialized='table',
        transient=true,
        database='TRANSIENT__B2C__PLAYS__' + snowflake_env(),
        schema='PLAYS',
        alias='TRANSIENT__PLAYS_1',
        tags=['presentation-playback-stream-mart'],
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH'
    )
}}

WITH conviva_sessions AS (
    SELECT * FROM {{ source('CURRENT_TABLE', 'SESSION_SOURCE_DATA__CURRENT') }}
)

-- SQL script start
SELECT
    -- Select relevant columns from conviva
    --"META_BATCH_DATE"::timestamptz AS "edm_batch_date"
    --,"EDM_INSERT_DTTS"::timestamptz AS "inserted_at"
    "ViewerId"
    ,"Asset"
    ,"DeviceOs"
    ,"State"
    ,"City"
    ,"Isp"
    ,"StartTime_unixtime"
    ,"StartUpTime_ms"
    ,"PlayingTime_ms"
    ,"BufferingTime_ms"
    ,"Interrupts"
    ,"AverageBitrate_kbps"
    ,"StartUpError"
    ,"IpAddress"
    ,"Cdn" AS "cdn_name"
    ,"Browser"
    ,"ConvivaSessionId"
    ,"StreamUrl"
    ,"ErrorList"
    ,"PercentageComplete"
    ,"ConnectionInducedReBufferingTime_ms"
    ,"VideoRestartTime_ms"
    ,"ReJoinedCount"
    ,"VPF"
    ,"VPFErrorList"
    ,"ContentLength_ms"
    ,"EndedStatus"
    ,"EndTime_unixtime"
    ,"C3DeviceUA"
    ,"DaznUserAgent"
    ,"C3DeviceModel"
    ,"DvFw"
    ,"ApplicationType"
    ,"ApplicationVersion"
    ,"AutoPlay"
    ,"C3DeRs"
    ,"C3Im"
    ,"C3PlayerName"
    ,"C3VideoIsLive"
    ,"ClosedCaptionLanguage"
    ,"CommentatoryLanguage"
    ,"Concurrency"
    ,"DeviceConnectionType"
    ,"DRMType"
    ,"Multivew"
    ,"NativePlayerVersion"
    ,"StreamingProtocol"
    ,"DvMrk"
    ,"IsC3DeviceUADoubleDecoded"
    ,"IsDaznUserAgentDoubleDecoded"
    ,"DaznDeviceId"
    ,"DaznSessionId"
    ,"FreeToView"
    ,"curated_IpAddressType"
    ,"FixtureId"
    -- Calculate a unique identifier for each stream by concatenating the conviva session id and the start time of the stream
    ,"ConvivaSessionId" || "StartTime_unixtime" AS "stream_uuid"
    -- Calculate the connection induced rebuffering time capped by capping the str_connection_induced_rebuffering_time to 180,000 ms
    ,LEAST("ConnectionInducedReBufferingTime_ms", (60000 * 30)) AS "connection_induced_rebuffering_time_capped"
    -- Capitalize the initial letter of the Country column
    ,INITCAP("Country") AS "str_country"
    -- Tranform the StartTime_unixtime column from unixtime to timestamp
    ,DATEADD(s, "StartTime_unixtime", '1970-01-01') AS "start_time"
    -- Calculate the end time of a stream (if the related column is null) by adding StartUpTime_ms, PlayingTime_ms, BufferingTime_ms to the StartTime_unixtime
    ,DATEADD(s, COALESCE("EndTime_unixtime", ("StartTime_unixtime" + "StartUpTime_ms" / 1000 + "PlayingTime_ms" / 1000 + "BufferingTime_ms" / 1000)), '1970-01-01') AS "end_time"
    -- Calculate the streaming time by summing StartUpTime_ms, PlayingTime_ms, BufferingTime_ms
    ,"StartUpTime_ms" + "PlayingTime_ms" + "BufferingTime_ms" AS "total_streaming_time"
    -- Calculate the user agent by retrieving the C3DeviceUA if the DaznUserAgent column is null
    ,COALESCE("DaznUserAgent", "C3DeviceUA") AS "user_agent"
    -- Create a user agent key by hashing the user agent. This speeds up the join with device dimension
    ,MD5("user_agent") AS "user_agent_key"
    -- Calculate the full date if the stream
    ,DATE_TRUNC('day', DATEADD(s, "StartTime_unixtime", '1970-01-01')) AS "full_date"
    -- Extract the article id from the Asset column
    ,TRIM(SPLIT_PART(SPLIT_PART((SUBSTRING("Asset", CHARINDEX('[',"Asset") + LENGTH('['), LENGTH("Asset"))),']',1), '-',1)) AS "article_id"
    -- Retrieve the Asn only if it is an integer
    ,(TRY_TO_DECIMAL("Asn"))::BIGINT AS "Asn"
    -- Create a unique identifier for each row of the table
    ,UUID_STRING() AS "dss_uuid"
    -- Clean and standardize the ClosedCaptionLanguage column
    ,{{ caption_language_cleaning('ClosedCaptionLanguage') }} AS "closed_caption_language"
    -- Clean and standardize the CommentatoryLanguage column
    ,{{ commentatory_language_cleaning('CommentatoryLanguage') }} AS "commentary_language"
    -- Clean and standardize the NativePlayerVersion column
    ,{{ native_player_version_cleaning('NativePlayerVersion') }} AS "native_player_version"
    -- Calculate the origin server based on the time of the stream (before or after 2017-09-23), the StreamUrl and the C3DeRs
    ,{{ origin_server_parser('StartTime_unixtime', 'StreamUrl', 'C3DeRs') }} AS "origin_server"
    -- Clean and standardize the AutoPlay column
    ,{{ automatic_or_manual_play_parser('AutoPlay') }} AS "automatic_or_manual_play"
    -- Calculate if a play request is engaged (2 minutes of autoplay or manually selected)
    ,{{ engaged_play('total_streaming_time', 'automatic_or_manual_play', 'PlayingTime_ms') }} AS "engaged_play"
    -- Calculate the cdn based on the time of the stream (before or after 2017-09-23) and the StreamUrl
    , {{ cdn_parser('StartTime_unixtime', 'StreamUrl') }} AS "str_cdn"
    -- Calculate the pbb flag based on the StreamUrl column
    ,{{ pbb_parser('StreamUrl') }} AS "pbb_flag"
    -- Calculate the ond flag based on the StreamUrl column
    ,{{ ond_parser('StreamUrl') }} AS "ond_flag"
    -- Calculate the mob manifest flag based on the StreamUrl column
    ,{{ mob_parser('StreamUrl') }} AS "mob_manifest_flag"
    -- Clean and standardize the C3VideoIsLive column
    ,{{ live_or_on_demand_parser('C3VideoIsLive') }} AS "live_or_on_demand"
    -- Clean and standardize the DeviceConnectionType column
    ,{{ connection_type_parser('DeviceConnectionType') }} AS "connection_type"
    -- Clean and standardize the ApplicationVersion column
    ,{{ application_version_cleaning('ApplicationVersion') }} AS "application_version"
    -- Calculate the application major version as the first part of the application version
    ,{{ string_split('application_version','.',1, 'Unknown') }} AS "application_major_version"
    -- Calculate the application minor version as the second part of the application version
    ,{{ string_split('application_version','.',2, 'Unknown') }} AS "application_minor_version"
    -- Calculate the application patch version as the third part of the application version
    ,{{ string_split('application_version','.',3, 'Unknown') }} AS "application_patch_version"
    -- Removed lenght with the = operator; there are some inconsistencies in the naming some names are lower case some are upper case
    -- Clean and standardize the ApplicationType column
    ,{{ application_type_cleaning('ApplicationType') }} AS "application_type"
    -- fixed: we were using lower but in the IN statement we used uppercase; also naming inconsistencies
    -- Calculate the application category from the application type
    ,{{ application_category_parser('application_type') }} AS "application_category"
    -- Calculate if a play request is a genuine attempt from the PlayingTime_ms and StartUpError columns
    ,{{ genuine_attempt_parser('PlayingTime_ms', 'StartUpError') }} AS "genuine_attempt_flag"
    -- Calculate the ebvs flag from the PlayingTime_ms and StartUpError columns
    ,{{ ebvs_parser('PlayingTime_ms', 'StartUpError') }} AS "ebvs_flag"
    -- Calculate the play flag from the PlayingTime column
    ,{{ not_null_flag('PlayingTime_ms') }} AS "play_flag"
    -- Calculate the startup error type
    ,{{ startup_error_type_parser('StartUpTime_ms') }} AS "startup_error_type"
    -- Remove the error codes from the StartUpTime_ms
    ,CASE
        WHEN "StartUpTime_ms" >= 0 THEN "StartUpTime_ms"
    END AS "startup_time"
    -- Retrieve the cdn_name column if str_cdn is null
    ,COALESCE("str_cdn", "cdn_name") AS "str_cdn_name"
    ,"entitlementSetId" AS "playback_entitlement_set_id"
    ,"DAISessionStart" AS "is_dai_session"
    ,"adManagerName" AS "ad_manager_name"
FROM conviva_sessions
WHERE
    "Asset" LIKE '[%]%'
    AND
    {{ unixtime_build_trigger(var('build_mode') , 7 , 'StartTime_unixtime') }}
QUALIFY ROW_NUMBER() OVER (PARTITION BY "ConvivaSessionId", "StartTime_unixtime" ORDER BY "EndTime_unixtime" DESC NULLS LAST) = 1

--dbt run --select TRANSIENT__PLAYS_1 --vars '{"batch_date":"2022-03-31"}'
