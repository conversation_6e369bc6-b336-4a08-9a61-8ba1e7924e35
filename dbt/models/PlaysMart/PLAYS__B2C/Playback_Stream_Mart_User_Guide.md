# Playback Stream Mart User Guide

## Overview

The Playback Stream mart supports the analysis of customer engagement based on measurements of video playback via OTT streams on the DAZN platform. Datasets within the mart are designed to meet the reporting requirements previously supported by the `PRD_PROD`.`ANALYTICS`.`plays_fact_dn` table, and can answer business questions that aim to determine:

- trending content within a region, over a given period
    <details>
      <summary>View Query</summary>

    ```sql
    SELECT "article_title_english"
        ,COUNT(*) AS "playback_stream_count"
        ,SUM("playback_duration_milliseconds")/60000 AS "playback_duration_minutes"
    FROM "PLAYBACK_STREAM__B2C__MART__PROD"."FACT"."PLAYBACK_STREAM__FACT" psf
        LEFT JOIN "PLAYBACK_STREAM__B2C__MART__PROD"."CONTENT"."CONTENT_ITEM__DIM" cid ON psf."content_item__skey" = cid."content_item__skey"
    WHERE "playback_stream_date" BETWEEN '2022-07-03' AND '2022-07-10'
    GROUP BY 1;
    ```
    </details>

- average streaming duration for a specific sport
    <details>
      <summary>View Query</summary>

    ```sql
    SELECT "sport_name"
        ,AVG("playback_duration_milliseconds")/60000 AS "average_playback_duration_minutes"
    FROM "PLAYBACK_STREAM__B2C__MART__PROD"."FACT"."PLAYBACK_STREAM__FACT" psf
        LEFT JOIN "PLAYBACK_STREAM__B2C__MART__PROD"."CONTENT"."CONTENT_ITEM__DIM" cid ON psf."content_item__skey" = cid."content_item__skey"
    WHERE "playback_stream_date" BETWEEN '2022-07-03' AND '2022-07-10'
    GROUP BY 1;
    ```
    </details>

- the type of content streamed on devices of a particular type
    <details>
      <summary>View Query</summary>

    ```sql
    SELECT "device_description"
        ,"shoulder_content_or_live"
        ,COUNT(*) AS "playback_stream_count"
        ,AVG("playback_duration_milliseconds")/60000 AS "average_playback_duration_minutes"
    FROM "PLAYBACK_STREAM__B2C__MART__PROD"."FACT"."PLAYBACK_STREAM__FACT" psf
        LEFT JOIN "PLAYBACK_STREAM__B2C__MART__PROD"."CONTENT"."CONTENT_ITEM__DIM" cid ON psf."content_item__skey" = cid."content_item__skey"
        LEFT JOIN "PLAYBACK_STREAM__B2C__MART__PROD"."OPERATIONS"."DEVICE_INFO__DIM" did ON psf."device_info__skey" = did."device_info__skey"
    WHERE "playback_stream_date" BETWEEN '2022-07-03' AND '2022-07-10'
        AND "device_category" = 'Living Room'
    GROUP BY 1,2;
    ```
    </details>

- the quality of playback experience based on duration of video buffering, interruptions to streaming, playback failures etc.
    <details>
      <summary>View Query</summary>

    ```sql
    SELECT "device_description"
        ,SUM("buffering_duration_milliseconds")/60000 AS "buffering_duration_minutes"
        ,SUM("interrupts_count")
        ,SUM("is_startup_error")
        ,AVG("playback_duration_milliseconds")/60000 AS "average_playback_duration_minutes"
    FROM "PLAYBACK_STREAM__B2C__MART__PROD"."FACT"."PLAYBACK_STREAM__FACT" psf
        LEFT JOIN "PLAYBACK_STREAM__B2C__MART__PROD"."OPERATIONS"."DEVICE_INFO__DIM" did ON psf."device_info__skey" = did."device_info__skey"
    LEFT JOIN "PLAYBACK_STREAM__B2C__MART__PROD"."PLAYBACK_STREAM"."PLAYBACK_STREAM_ISSUE__DIM" psid ON psf."playback_stream_issue__skey" = psid."playback_stream_issue__skey"
    WHERE "playback_stream_date" BETWEEN '2022-07-03' AND '2022-07-10'
        AND "device_category" = 'Living Room'
    GROUP BY 1;
    ```
    </details>


Link to the Cheat Sheet Notebook with more sample queries [here](https://deepnote.com/@dazn-analytics-engineering/Plays-Mart-Cheat-Sheet-bc1afa33-6cf8-4fb3-99b2-44668aa0d5f7).

### Data Sources

The contents of the mart originate from multiple data sources:

- **DeviceAtlas**: Characteristics of hardware and software developed outside DAZN belonging to devices used for playback streaming.
- **Conviva**: Playback quality monitoring metrics and related attributes.
- **PCMS**: Information about the content being transmitted during playback, as obtained from GMR article payloads, which contain data about MFL entities (fixtures, competitions, sports etc.) and their associated listings in What's On.

## Data Model

Datasets within the Playback Stream mart are logically structured into a star dimensional model, consisting of one fact table that references multiple dimensions which are otherwise unrelated. Storing the data in this configuration makes future developments easier to integrate within the overall structure, requires less maintenance and enables faster, more efficient queries. It also gives consumers the option to select for data relevant to their analysis, by querying the fact table on its own or joining it to only a subset of dimensions.

![Playback Stream Mart Connections](./Playback_Stream_Mart_Data_Model.jpg)

Descriptions for all column and table-level entities in the Playback Stream mart are documented [here](./PLAYS__B2C.yaml).

### Facts

Facts store quantitative data or measurements related to a specific business process.
Fact table rows each represent a measurement event, at a level of detail that is consistent across all records i.e. the table grain.

#### Facts in the Playback Stream mart

The mart contains a single fact table `PLAYBACK_STREAM__FACT`, located under the `FACT` schema, which records playback metrics at the grain of **a playback stream**. We define a playback stream as **the continuous transmission of a single content item in response to a play request received from one customer**.

**A. Measure categories**:

- timestamps of specific events occurring as part of a stream e.g. the start, end and restart of video playback.
- duration of streaming states e.g. total playback time, duration of buffering at startup.
- counts of specific event occurrences e.g. number of playback interruptions.

**B. External identifiers**:

- Conviva session id
- Entitlement set id
- DAZN device id
- device user agent string

**C. Linked dimensions**:

- [Playback Stream Mart User Guide](#playback-stream-mart-user-guide)
  - [Overview](#overview)
    - [Data Sources](#data-sources)
  - [Data Model](#data-model)
    - [Facts](#facts)
      - [Facts in the Playback Stream mart](#facts-in-the-playback-stream-mart)
    - [Dimensions](#dimensions)
      - [General characteristics](#general-characteristics)
      - [A. Conformed dimensions](#a-conformed-dimensions)
        - [Playback Stream Location](#playback-stream-location)
        - [Device Info](#device-info)
        - [Application](#application)
        - [Content Item](#content-item)
      - [B. Non-conformed dimensions](#b-non-conformed-dimensions)
        - [Playback Stream Detail](#playback-stream-detail)
        - [Playback Stream Issue](#playback-stream-issue)
  - [Considerations & Caveats](#considerations--caveats)
  - [Glossary](#glossary)
    - [Column naming](#column-naming)
    - [Table and Table View naming](#table-and-table-view-naming)

### Dimensions

#### General characteristics

Dimensions store discrete data that describes - and may be used to categorise - the measurable events recorded in a fact table.

Each record of a dimension table or table view represents **a single instance of the entity listed in the table name**. For example:

- *A single record from the CONTENT_ITEM__DIM dimension captures information about 1 and only 1 piece of content, reflecting its latest state.*
- *A single record from the PLAYBACK_STREAM_DETAIL__DIM dimension contains attributes descriptive of a single instance of a playback stream.*

Dimension records are uniquely identified by a **primary key** [^1]. The column storing these values is always positioned as **the first non-metadata column** and is named after the row-level entity, suffixed with the string '__skey'. For example:

- *The primary key column of the CONTENT_ITEM__DIM dimension is listed as 'content_item__skey'.*
- *The primary key column of the DEVICE_INFO__DIM dimension is listed as 'device_info__skey'.*

Given that the role of dimensions is to store reference data about facts, all dimension tables and table views within the Playback Stream mart link to the `PLAYBACK_STREAM__FACT` table via **foreign keys**. To enable these joins, the fact table stores the primary key values of each related dimension in a foreign key column that retains the name of primary key column from the source dimension. For example:

- *To retrieve the characteristics of devices that were used for streaming DAZN content, the `PLAYBACK_STREAM__FACT` table can be joined with the `DEVICE_INFO__DIM` table view on the `device_info__skey` column, which is present in both.*

#### A. Conformed dimensions

Conformed dimensions have the same meaning when used to describe different facts. Dimensions of this type are surfaced in the mart as **table views**, grouped under schemas by parent domain. Each conformed dimension may be joined onto the fact table to obtain additional information about playback streams or related entities.

Conformed dimensions available in the Playback Stream mart:

##### Playback Stream Location

- *Parent domain*: Global
- *Location within the Playback Stream mart*: `GLOBAL`.`PLAYBACK_STREAM_LOCATION__DIM`
- *Original location*: `PLAYBACK_STREAM__B2C__DOMAIN__<env>`.`GLOBAL`.`GEO__DIM`
- *Dimension type*: Role-playing dimension
- *Captures history?* No. Every row is a unique combination of values.
- *Join column name in fact table*: `playback_stream_location__skey`

##### Device Info

- *Parent domain*: Operations
- *Location within the Playback Stream mart*: `OPERATIONS`.`DEVICE_INFO__DIM`
- *Original location*: `PLAYBACK_STREAM__B2C__DOMAIN__<env>`.`OPERATIONS`.`DEVICE_INFO__DIM`
- *Dimension type*: Current
- *Captures history?* No. Every row is a unique combination of values.
- *Join column name in fact table*: `device_info__skey`

##### Application

- *Parent domain*: Operations
- *Location within the Playback Stream mart*: `OPERATIONS`.`APPLICATION__DIM`
- *Original location*: `PLAYBACK_STREAM__B2C__DOMAIN__<env>`.`OPERATIONS`.`APPLICATION__DIM`
- *Dimension type*: Current
- *Captures history?* No. Every row is a unique combination of values.
- *Join column name in fact table*: `application__skey`

##### Content Item

- *Parent domain*: Content
- *Location within the Playback Stream mart*: `CONTENT`.`CONTENT_ITEM__DIM`
- *Original location*: `PLAYBACK_STREAM__B2C__DOMAIN__<env>`.`CONTENT`.`CONTENT_ITEM__DIM`
- *Dimension type*: Current
- *Captures history?* No. The data only captures the latest observation on a content item..
- *Join column name in fact table*: `content_item__skey`

#### B. Non-conformed dimensions

Non-conformed dimensions are collections of attributes specific to a particular fact. This type of dimension cannot meaningfully describe or be referenced by more than one fact table. As a result, non-conformed dimensions are **materialized directly as tables inside the same mart that stores the fact table to which they relate**, under a dedicated schema named after that fact table entity.

Non-conformed dimensions availale in Playback Stream mart:

##### Playback Stream Detail

- *Parent domain*: n/a
- *Location within the Playback Stream mart*: `PLAYBACK_STREAM`.`PLAYBACK_STREAM_DETAIL__DIM`
- *Dimension type*: Current
- *Captures history?* No. Every row is a unique combination of values.
- *Join column name in fact table*: `playback_stream_detail__skey`

##### Playback Stream Issue

- *Parent domain*: n/a
- *Location within the Playback Stream mart*: `PLAYBACK_STREAM`.`PLAYBACK_STREAM_ISSUE__DIM`
- *Dimension type*: Current
- *Captures history?* No. Every row is a unique combination of values.
- *Join column name in fact table*: `playback_stream_issue__skey`

## Considerations & Caveats

- The `CONTENT_ITEM__DIM` dimension contains only the latest observation on a content item, which may be incomplete. Attributes with missing values might be populated in records capturing previous versions of a particular content item, which are not surfaced in the `CONTENT_ITEM__DIM` table view.

## Glossary

### Column naming

Naming element | Description
------------ | -------------
"META__" prefix | Metadata column
"__skey" suffix | Surrogate key column
"__hash" suffix | Column storing values generated via HASH() function

### Table and Table View naming

Naming element | Description
------------ | -------------
"__FACT" suffix | Fact table
"__DIM" suffix | Dimension table or table view which stores one record per entity, representing its current or latest state. It does not capture the history of data changes.
"__SCD" suffix | Dimension table or table view which stores both current and historical versions of entities.
"__BRIDGE" suffix | Used for linking tables with different grains.

[1^]: Also referred to as a \'**surrogate key**\' due to it being a system generated value, with no business meaning, that serves as a unique identifier for rows in a table.
