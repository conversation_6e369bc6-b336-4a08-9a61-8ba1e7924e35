{{
    config(
        materialized='incremental',
        unique_key='"playback_stream_details__skey"',
        database='PLAYBACK_STREAM__B2C__MART__' + snowflake_env(),
        schema='PLAYBACK_STREAM',
        alias='<PERSON><PERSON><PERSON>BACK_STREAM_DETAILS__DIM',
        tags=['presentation-playback-stream-mart']
    )
}}

SELECT DISTINCT
    OBJECT_CONSTRUCT('record_inserted_timestamp',CURRENT_TIMESTAMP()) AS "META__DATA"
    ,HASH(
        "origin_server"
        ,"connection_type"
        ,"genuine_attempt_flag"
        ,"ebvs_flag"
        ,"cdn_name"
        ,"curated_ip_address_type"
        ,"drm_type"
        ,"multiview"
        ,"streaming_protocol"
        ,"automatic_or_manual_play"
        ,"engaged_play"
        ,"hd_flag"
        ,"sd_flag"
        ,"is_dai_session"
        ,"commentary_language"
    ) AS "playback_stream_details__skey"
    /* original plays stream origin */
    ,"origin_server" AS "origin_server"
    ,"cdn_name" AS "content_delivery_network_name"
    ,"streaming_protocol" AS "streaming_protocol"
    ,"connection_type" AS "connection_type"
    ,"curated_ip_address_type" AS "ip_address_type"
    ,"drm_type" AS "digital_rights_management_type"
    ,"automatic_or_manual_play" AS "playback_trigger_type"
    /* flags */
    ,"genuine_attempt_flag" AS "is_genuine_attempt"
    ,"ebvs_flag" AS "has_exit_before_video_start"
    ,"multiview" AS "is_multiview"
    ,"engaged_play" AS "is_engaged_play"
    ,"hd_flag" AS "is_hd"
    ,"sd_flag" AS "is_sd"
    ,"is_dai_session"
    ,"commentary_language"
FROM
    {{ ref('TRANSIENT__PLAYS_2') }}
{% if is_incremental() %}
    WHERE "playback_stream_details__skey" NOT IN (SELECT "playback_stream_details__skey" FROM {{ this }})
{% endif %}
