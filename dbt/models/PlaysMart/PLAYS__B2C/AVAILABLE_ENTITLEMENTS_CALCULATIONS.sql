{{
    config(
        materialized='ephemeral',
        tags=['presentation-playback-stream-mart']
    )
}}

WITH plays_fact AS (
    SELECT * FROM {{ ref('TRANSIENT__PLAYS_1') }}
)

, customer_identity AS (
    SELECT * FROM {{ ref('customer_identity_dim_current') }}
    QUALIFY ROW_NUMBER() OVER(PARTITION BY "viewer_id" ORDER BY "effective_from" DESC) = 1
)

, user_entitlement_scd AS (
    SELECT * FROM {{ ref('dim__user_entitlement_scd') }}
)

SELECT
    plays_fact."article_id" AS "article_id"
    ,plays_fact."ViewerId" AS "viewer_id"
    ,plays_fact."start_time"
    ,ARRAY_AGG(DISTINCT user_entitlement_scd."entitlement_set_id") WITHIN GROUP (ORDER BY user_entitlement_scd."entitlement_set_id") AS "available_entitlements"
FROM
    plays_fact
LEFT JOIN
    customer_identity
    ON plays_fact."ViewerId" = customer_identity."viewer_id"
LEFT JOIN
    user_entitlement_scd
    ON user_entitlement_scd."crm_account_id" = customer_identity."crm_account_id"
        AND plays_fact."start_time" >= user_entitlement_scd."entitlement_set_effective_from"
        AND plays_fact."start_time" < user_entitlement_scd."entitlement_set_effective_until"
WHERE user_entitlement_scd."crm_account_id" IS NOT NULL
GROUP BY
    1, 2, 3
