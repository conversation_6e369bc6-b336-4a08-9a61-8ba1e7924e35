{{
    config(
        materialized='incremental',
        unique_key='"geo__skey"',
        database='PLAYBACK_STREAM__B2C__DOMAIN__' + snowflake_env(),
        schema='GLOBAL',
        alias='GEO__DIM',
        tags=['presentation-playback-stream-mart']
    )
}}

SELECT DISTINCT
    OBJECT_CONSTRUCT('record_inserted_timestamp',CURRENT_TIMESTAMP()) AS "META__DATA"
    ,HASH(
        "state"
        ,"city"
        ,"country"
    ) AS "geo__skey"
    ,HASH(
        "country"
    ) AS "country__hash"
    ,"country" AS "country"
    ,"state" AS "state"
    ,"city" AS "city"
    ,"territory" AS "territory"
    ,"region" AS "region"
FROM
    {{ ref('TRANSIENT__PLAYS_2') }}
{% if is_incremental() %}
    WHERE "geo__skey" NOT IN (SELECT "geo__skey" FROM {{ this }})
{% endif %}
