version: 2

models:
  - name: mart__won_vodevent__dim
    description: "Dimension table from WON data. Contains VOD event data at fixture & region level."
    columns:
      - name: vod_event__skey
        description: "Hashed Unique id of this non-live event in WON i.e. External Reference"
        quote: true

      - name: vod_event_id
        description: "Unique id of this non-live event in WON i.e. External Reference"
        quote: true

      - name: vod_event_title
        description: "The title of the event"
        quote: true

      - name: vod_event_status
        description: "The status of the regional transmission in WON"
        quote: true

      - name: vod_event_start_timestamp
        description: "The date/time that the VOD content will be available on DAZN, created as the combination of all fields that have been populated for this data point"
        quote: true

      - name: vod_event_end_timestamp
        description: "The UTC time the event end, created as the combination of all fields that have been populated for this data point"
        quote: true

      - name: vod_event_is_obligated
        description: "mustPlay field from transmission"
        quote: true

      - name: vod_event_has_ftv_allowed
        description: "Boolean value that indicates whether the non-live event can be offered as 'free to view'"
        quote: true

      - name: vod_event_advertising
        description: "Represents the advertising field from Transmission"
        quote: true

      - name: vod_event_duration
        description: "Expected duration of the live event"
        quote: true

      - name: vod_event_schedule_version
        description: "Schedule version of Transmission from WON (Active, Alternative) - Alternative is used for pre-planning"
        quote: true

      - name: region_id
        description: "ID that represents the DAZN region and MCS Product"
        quote: true

      - name: region_name
        description: "Name of the DAZN region"
        quote: true

      - name: region_territory
        description: "Name of DAZN region in sync with 'region_dimension' and 'TRANSIENT__PLAYS_2' tables"
        quote: true

      - name: region_default_timezone
        description: "The default timezone of the region"
        quote: true

      - name: region_ad_market
        description: "Region identifier uniquely used for advertising purposes"
        quote: true

      - name: region_organisation_id
        description: "ID that represents the related DCMS organisation"
        quote: true

      - name: region_outlet_key
        description: "The authentication key used for the relevant DAZN Feed outlet for given DAZN region"
        quote: true

      - name: region_m2a_key
        description: "Region identifier uniquely used by M2A"
        quote: true

      - name: vod_media_id
        description: "Reference ID to the associated WON media asset, created using the COALESCE of the two rightId and rightsId fields, needed as there was a change of the field name"
        quote: true

      - name: vod_media_label
        description: "Label used to identify the purpose of the media asset, created using the COALESCE of the two wonMediaLabel and label fields, needed as there was a change of the field name"
        quote: true

      - name: vod_media_old_file_name
        description: "Filename required for the produced media asset"
        quote: true

      - name: vod_media_team_assigned
        description: "The team assigned to product the media asset"
        quote: true

      - name: vod_media_has_reversion_required
        description: "Boolean value indicating whether the media asset needs to be re-versioned into another media asset with different audio language"
        quote: true

      - name: vod_media_has_closed_captions_required
        description: "Boolean value indicating if closed captions need to be added to produced media asset"
        quote: true

      - name: vod_media_audio_languages
        description: "Audio Languages used within the Media"
        quote: true

      - name: vod_media_audio_languages_value
        description: "The first value from the audioLanguages array representing the primary audio Languages used within the Media"
        quote: true

      - name: vod_product_id
        description: "Reference ID to the associated WON product"
        quote: true

      - name: vod_product_title
        description: "Title used as reference to the original product title"
        quote: true

      - name: vod_product_type
        description: "The type of product"
        quote: true

      - name: fixture_id
        description: "ID that represents the fixture associated to the scheduled transmission"
        quote: true

      - name: vod_product_version
        description: "Version of the product"
        quote: true

      - name: competition
        description: "Array containing metadata relating to the competition of the product"
        quote: true

      - name: competition_id
        description: "ID of the related MFL competition"
        quote: true

      - name: competition_name
        description: "Name of the related MFL competition, created using the COALESCE of the two competition_name and competitionName fields, needed as there was a change of the field name"
        quote: true

      - name: vod_product_ruleset_name
        description: "Name of the related MFL ruleset"
        quote: true

      - name: vod_product_location
        description: "Location of the related MFL Competition"
        quote: true

      - name: contestant_ids
        description: "Array of IDs of the related MFL contestants"
        quote: true

      - name: sport_id
        description: "ID of the related MFL sport"
        quote: true

      - name: sport_name
        description: "Name of the related MFL sport"
        quote: true

      - name: vod_product_content_distinction_id
        description: "External reference of the relevant Content Distinction (aka Video Type), created using the COALESCE of the two contentDistinction_uuid and contentDistinctionReference fields, needed as there was a change of the field name"
        quote: true

      - name: vod_product_content_distinction_name
        description: "Name of the relevant Content Distinction (aka Video Type), created using the COALESCE of the two contentDistinction_name and contentDistinction fields, needed as there was a change of the field name"
        quote: true

      - name: vod_product_source_language
        description: "Audio language on the source video received by DAZN"
        quote: true

      - name: vod_product_has_closed_captions_available
        description: "Boolean value indicated if there are closed captions on the source video received by DAZN"
        quote: true

      - name: vod_product_closed_captions_language
        description: "Language of the closed captions provided on the source video received by DAZN"
        quote: true

      - name: vod_product_arrival_method
        description: "Method in which DAZN received the source video"
        quote: true

      - name: vod_product_synopsis
        description: "Short description of the non-live event"
        quote: true

      - name: vod_product_expected_duration
        description: "Expected duration of the live event"
        quote: true

      - name: vod_product_contractual_compliance
        description: "List of keys of prohibited ad types defined by rights compliance"
        quote: true

      - name: vod_right_title
        description: "Title of the linked product"
        quote: true

      - name: vod_right_start_date
        description: "Start date of the exploitation right"
        quote: true

      - name: vod_right_end_date
        description: "End date of the exploitation right"
        quote: true

      - name: vod_right_type
        description: "The type of the right E.g. OTT, ..."
        quote: true

      - name: vod_right_run_count
        description: "Number of runs (aka commitments) allowed for the given exploitation right"
        quote: true

      - name: vod_right_status
        description: "The status of the exploitation right"
        quote: true

      - name: tournament_calendar_id
        description: "ID of the related MFL tournament calendar"
        quote: true

      - name: vod_right_has_ftv_allowed
        description: "Boolean value that indicates whether live events for the given right can be offered as 'free to view'"
        quote: true

      - name: vod_right_has_download_allowed
        description: "Boolean value that indicates whether live events for the given right can be offered as 'downloadable'"
        quote: true

      - name: vod_right_has_b2b_allowed
        description: "Boolean value indicating whether the rights are allowed for DAZN's B2B product"
        quote: true

      - name: vod_right_has_b2c_allowed
        description: "Boolean value indicating whether the rights are allowed for DAZN's B2C product"
        quote: true

      - name: vod_right_is_cleared_for_dazn_player
        description: "Boolean value that indicates if the content can be re-used on the DAZN Player product"
        quote: true

      - name: vod_right_is_cleared_for_social
        description: "Boolean value that indicates if the content can be re-used on social media"
        quote: true

      - name: vod_right_is_pre_recorded
        description: "Boolean value that indicates if the content has been pre-recorded"
        quote: true

      - name: vod_right_disallowed_country_codes
        description: "An array of countries blocked from consuming events for the given rights"
        quote: true

      - name: vod_right_allowed_audio_languages
        description: "An array of audio languages allowed to be applied against the event"
        quote: true

      - name: vod_right_allowed_country_codes
        description: "A list of countries allowed to consume events for the given rights"
        quote: true

      - name: vod_right_allowed_country_codes_0
        description: "The first value of the array in field allowedCountryCodes representing countries allowed to consume events for the given rights"
        quote: true

      - name: vod_right_allowed_country_codes_1
        description: "The second value of the array in field allowedCountryCodes representing countries allowed to consume events for the given rights"
        quote: true

      - name: vod_right_allowed_country_codes_2
        description: "The third value of the array in field allowedCountryCodes representing countries allowed to consume events for the given rights"
        quote: true

      - name: vod_right_non_exclusive_regions
        description: "A list of non-exclusive regions for the given right"
        quote: true

      - name: vod_right_non_exclusive_regions_0
        description: "The first value of the array in field nonExclusiveRegions representing non-exclusive regions for the given right"
        quote: true

      - name: vod_right_non_exclusive_regions_1
        description: "The second value of the array in field nonExclusiveRegions representing non-exclusive regions for the given right"
        quote: true

      - name: vod_right_non_exclusive_regions_2
        description: "The third value of the array in field nonExclusiveRegions representing non-exclusive regions for the given right"
        quote: true

      - name: contract_name
        description: "Name of the contract, created using the COALESCE of the two contractName and name fields, needed as there was a change of the field name"
        quote: true

      - name: contract_status
        description: "Status of the contract"
        quote: true

      - name: contract_start_date
        description: "Global start date of the contract"
        quote: true

      - name: contract_end_date
        description: "Global end date of the contract"
        quote: true

      - name: rights_holder_id
        description: "Reference ID of the contract's rights holder"
        quote: true

      - name: rights_holder_name
        description: "Name of the rights holder, created using the COALESCE of the two rightsHolderName and name fields, needed as there was a change of the field name"
        quote: true

  - name: mart__won_liveevent__dim
    description: "Dimension table from WON data. Contains live event data at fixture & region level."
    columns:
      - name: live_event__skey
        description: "Hashed Unique id of this event in WON i.e. External Reference"
        quote: true

      - name: live_event_id
        description: "Unique id of this event in WON i.e. External Reference"
        quote: true

      - name: live_event_title
        description: "The title of the event"
        quote: true

      - name: dummy_fixture_id
        description: "ID that represents the dummy fixture if one is relevant for the scheduled transmission, created using the COALESCE of the two mflDaznDummyFixtureId and dummyFixtureUuid fields, needed as there was a change of the field name"
        quote: true

      - name: fixture_id
        description: "ID of the related MFL fixture directly associated with this live event, taking the dummy fixture ID if there is one"
        quote: true

      - name: region_id
        description: "ID that represents the DAZN region and MCS Product"
        quote: true

      - name: region_name
        description: "Name of the DAZN region"
        quote: true

      - name: region_territory
        description: "Name of DAZN region in sync with 'region_dimension' and 'TRANSIENT__PLAYS_2' tables"
        quote: true

      - name: region_ad_market
        description: "Region identifier uniquely used for advertising purposes"
        quote: true

      - name: region_m2a_key
        description: "Region identifier uniquely used by M2A"
        quote: true

      - name: region_outlet
        description: "Name of the DAZN region modified to match the outlet from conviva data."
        quote: true

      - name: live_event_status
        description: "The status of the regional transmission in WON"
        quote: true

      - name: live_event_start_timestamp
        description: "The UTC time of the Pre-KO of the event"
        quote: true

      - name: live_event_end_timestamp
        description: "The UTC time of the event end"
        quote: true

      - name: live_event_platform
        description: "Used to distinguish between scheduled events that are for the OTT platform and others"
        quote: true

      - name: live_event_type_name
        description: "Represents the TX Event Type used by Bookings for appropriate channel assignment E.g. TX50, PBB50, LTD50, OND59, TX59, DCI 50, DCI 59"
        quote: true

      - name: live_event_type
        description: "Used to distinguish a scheduled event as LIVE or ASLIVE (Delayed)"
        quote: true

      - name: live_event_broadcast_tier
        description: "The broadcast tier set against the event to indicate the level of production to be applied by DAZN"
        quote: true

      - name: live_event_vob_key
        description: "Represents the voice-over booth resources that will be required for the event"
        quote: true

      - name: live_event_frame_rate
        description: "Indicates the frame rate in which the event will be delivered on the OTT product"
        quote: true

      - name: live_event_workflow_type
        description: "Represents the Alternative Workflow used by Planning to indicate a special scenario"
        quote: true

      - name: live_event_gallery_name
        description: "Indicates whether it is a gallery event requiring specific Gallery resources"
        quote: true

      - name: live_event_gallery_id
        description: "ID of the Gallery object"
        quote: true

      - name: live_event_schedule_version
        description: "Schedule version of Transmission from WON (Active, Alternative) - Alternative is used for pre-planning"
        quote: true

      - name: live_event_is_co_exclusive
        description: "Boolean representing if DAZN shares exclusivity for the live event"
        quote: true

      - name: live_event_is_obligated
        description: "Surfaces whether transmission is obligated to be played"
        quote: true

      - name: live_event_has_ftv_allowed
        description: "Boolean value that indicates whether the live event can be offered as 'free to view"
        quote: true

      - name: live_event_has_ftv_catchup_allowed
        description: "Boolean value that indicates whether the catchup asset of the live event can be offered as 'free to view"
        quote: true

      - name: live_event_is_dci
        description: "Boolean value if the event is DCI or not"
        quote: true

      - name: live_event_dci_group_name
        description: "Represents the DCI group that the event forms"
        quote: true

      - name: live_event_closed_caption_languages
        description: "A map of one or more closed caption languages made available for this event keyed by ISO two-letter language codes"
        quote: true

      - name: live_event_is_world_feed
        description: "Indicates whether the World Feed commentary can be used or not"
        quote: true

      - name: live_event_commentary_languages_code
        description: "The first value from the commentaryLangs array representing the primary commentary language made available for this event"
        quote: true

      - name: live_event_world_feed_languages_code
        description: "The first value from the worldFeedLanguages array representing the primary world feed commentary language made available for this event"
        quote: true

      - name: primary_commentary_language
        description: "The primary commentary language derived for this event"
        quote: true

      - name: live_event_advertising_compliance_rules
        description: "Array representing the advertising compliance rules of the live event"
        quote: true

      - name: live_event_announced_start_timestamp
        description: "Announced time in UTC dropdown on transmission"
        quote: true

      - name: live_event_sponsoring
        description: "Represents the sponsoring field from Transmission"
        quote: true

      - name: live_event_ht_filler
        description: "Represents the HT Filler that needs to be planned for"
        quote: true

      - name: live_event_regional_supplier
        description: "Indicates that within a certain territory the content is actually being supplied by another rights holder"
        quote: true

      - name: live_encoding_id
        description: "Live encoding ID. unique per payload but not for allocation type. Used to map to the live event table"
        quote: true

      - name: live_product_id
        description: "Reference to the Live Product associated with this live event"
        quote: true

      - name: live_product_type
        description: "The type of product (E.g. Series, Episode, ...)"
        quote: true

      - name: competition_id
        description: "ID of the related MFL competition"
        quote: true

      - name: live_product_ruleset_name
        description: "Name of the related MFL ruleset"
        quote: true

      - name: tournament_calendar_id
        description: "ID of the related MFL tournament calendar"
        quote: true

      - name: sport_id
        description: "ID of the related MFL sport"
        quote: true

      - name: sport_name
        description: "Name of the related MFL sport"
        quote: true

      - name: live_product_support_tier
        description: "Support Tier from WON"
        quote: true

      - name: live_product_order_of_play
        description: "Order of Play from WON"
        quote: true

      - name: live_product_expected_duration
        description: "Expected duration of the live event"
        quote: true

      - name: live_product_contractual_compliance
        description: "List of keys of prohibited ad types defined by rights compliance"
        quote: true

      - name: live_right_title
        description: "Title of the linked product"
        quote: true

      - name: live_right_start_date
        description: "Start date of the exploitation right, created using the COALESCE of the two startDate and startDateFormula fields, needed as there was a change of the field name"
        quote: true

      - name: live_right_end_date
        description: "End date of the exploitation right, created using the COALESCE of the two endDate and endDateFormula fields, needed as there was a change of the field name"
        quote: true

      - name: live_right_type
        description: "The type of the right E.g. OTT, ..."
        quote: true

      - name: live_right_planned_fixtures
        description: "Number of runs (aka commitments) allowed for the given exploitation right"
        quote: true

      - name: live_right_status
        description: "The status of the exploitation right"
        quote: true

      - name: live_right_is_live
        description: "Boolean value which indicates if the exploitation right applies to LIVE events"
        quote: true

      - name: live_right_is_as_live
        description: "Boolean value which indicates if the exploitation right applies to ASLIVE (Delayed) events"
        quote: true

      - name: live_right_has_download_allowed
        description: "Boolean value that indicates whether live events for the given right can be offered as 'downloadable"
        quote: true

      - name: live_right_has_b2c_allowed
        description: "Boolean value indicating whether the rights are allowed for DAZN's B2C product"
        quote: true

      - name: live_right_has_b2b_allowed
        description: "Boolean value indicating whether the rights are allowed for DAZN's B2B product"
        quote: true

      - name: live_right_has_b2b_catchup_allowed
        description: "Boolean value indicating whether the rights are allowed on DAZN's B2B product as catchup content"
        quote: true

      - name: live_right_allowed_country_codes
        description: "An array of countries allowed to consume events for the given rights"
        quote: true

      - name: live_right_disallowed_country_codes
        description: "An array of countries blocked from consuming events for the given rights"
        quote: true

      - name: live_right_non_exclusive_regions
        description: "An array of non-exclusive regions for the given right"
        quote: true

      - name: live_right_exclusivity_flag
        description: "Category defining content exclusivity for an event across regions"
        quote: true

      - name: live_right_allowed_audio_languages
        description: "An array of audio languages allowed to be applied against the event"
        quote: true

      - name: contract_name
        description: "Name of the contract, created using the COALESCE of the two contractName and name fields, needed as there was a change of the field name"
        quote: true

      - name: contract_status
        description: "Status of the contract"
        quote: true

      - name: contract_start_date
        description: "Global start date of the contract"
        quote: true

      - name: contract_end_date
        description: "Global end date of the contract"
        quote: true

      - name: rights_holder_id
        description: "Reference ID of the contract's rights holder"
        quote: true

      - name: rights_holder_name
        description: "Name of the rights holder, created using the COALESCE of the two rightsHolderName and name fields, needed as there was a change of the field name"
        quote: true

      - name: live_event_regional_supplier_id
        description: "Indicates that within a certain territory the content is actually being supplied by another rights holder - only ID"
        quote: true

      - name: live_event_cc_languages_code
        description: "A map of one or more closed caption languages made available for this event keyed by ISO two-letter language codes"
        quote: true

      - name: live_event_cc_supplier_details
        description: "Supplier id and name of the closed caption language providers"
        quote: true

  - name: mart__mfl_fixture__dim
    description: "Dimension table from MFL data at fixture level."
    columns:
      - name: fixture_id
        comment: "ID that represents the fixture associated with the scheduled transmission"
        quote: true

      - name: fixture_description
        comment: "The description of the MFL Fixture"
        quote: true

      - name: fixture_start_timestamp
        comment: "The timestamp of the start of the MFL Fixture, constructed using the combination of the date and time fields"
        quote: true

      - name: fixture_status
        comment: "The status of the MFL Fixture"
        quote: true

      - name: sport_id
        comment: "ID of the related MFL sport"
        quote: true

      - name: sport_name
        comment: "Name of the related MFL sport"
        quote: true

      - name: competition_id
        comment: "ID of the related MFL competition"
        quote: true

      - name: competition_name
        comment: "Name of the related MFL competition, created using the COALESCE of the two competition_name and competitionName fields, needed as there was a change of the field name"
        quote: true

      - name: competition_country_id
        comment: "ID of the country in which the competition is held"
        quote: true

      - name: competition_country_name
        comment: "Name of the country in which the competition is held"
        quote: true

      - name: tournament_calendar_id
        comment: "ID of the related MFL tournament calendar"
        quote: true

      - name: tournament_calendar_name
        comment: "The name of the Tournament Calendar"
        quote: true

      - name: tournament_calendar_start_date
        comment: "The date of the start of the Tournament Calendar"
        quote: true

      - name: tournament_calendar_end_date
        comment: "The date of the end of the Tournament Calendar"
        quote: true

      - name: ruleset_id
        comment: "The UUID of the ruleset directly relating to the MFL Competition"
        quote: true

      - name: ruleset_name
        comment: "The name of the ruleset in MFL E.g. Mens, Womens, Juniors, ..."
        quote: true

      - name: stage_id
        comment: "The UUID of the Stage in MFL"
        quote: true

      - name: stage_name
        comment: "The name of the Stage in MFL E.g. Final, Semi-final, Group, ..."
        quote: true

      - name: stage_start_date
        comment: "The date of the start of the Stage"
        quote: true

      - name: stage_end_date
        comment: "The date of the end of the Stage"
        quote: true

      - name: venue_id
        comment: "The UUID of the Venue in MFL"
        quote: true

      - name: venue_name_short
        comment: "The short name of the venue in MFL"
        quote: true

      - name: venue_name_long
        comment: "The long name of the venue in MFL"
        quote: true

      - name: contestant_0_id
        comment: "ID of the 1st contestant in the payload"
        quote: true

      - name: contestant_0_name
        comment: "Name of the 1st contestant in the payload"
        quote: true

      - name: contestant_0_country_name
        comment: "Country of the 1st contestant in the payload"
        quote: true

      - name: contestant_1_id
        comment: "ID of the 2nd contestant in the payload"
        quote: true

      - name: contestant_1_name
        comment: "Name of the 2nd contestant in the payload"
        quote: true

      - name: contestant_1_country_name
        comment: "Country of the 2nd contestant in the payload"
        quote: true

      - name: contestant_2_id
        comment: "ID of the 3rd contestant in the payload"
        quote: true

      - name: contestant_2_name
        comment: "Name of the 3rd contestant in the payload"
        quote: true

      - name: contestant_2_country_name
        comment: "Country of the 3rd contestant in the payload"
        quote: true

      - name: contestant_3_id
        comment: "ID of the 4th contestant in the payload"
        quote: true

      - name: contestant_3_name
        comment: "Name of the 4th contestant in the payload"
        quote: true

      - name: contestant_3_country_name
        comment: "Country of the 4th contestant in the payload"
        quote: true

      - name: contestant_4_id
        comment: "ID of the 5th contestant in the payload"
        quote: true

      - name: contestant_4_name
        comment: "Name of the 5th contestant in the payload"
        quote: true

      - name: contestant_4_country_name
        comment: "Country of the 5th contestant in the payload"
        quote: true

      - name: contestant_5_id
        comment: "ID of the 6th contestant in the payload"
        quote: true

      - name: contestant_5_name
        comment: "Name of the 6th contestant in the payload"
        quote: true

      - name: contestant_5_country_name
        comment: "Country of the 6th contestant in the payload"
        quote: true

  - name: mart__gmr_article__dim
    description: "GMR article dimension. From DOTS data source"
    columns:
      - name: last_updated_timestamp
        description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
        quote: true

      - name: article_id
        description: "The ID of the article"
        quote: true

      - name: article_title
        description: "The title of the article"
        quote: true

      - name: article_start_timestamp
        description: "The start time of the article (converted to a TIMESTAMP)"
        quote: true

      - name: article_end_timestamp
        description: "The end time of the article (converted to a TIMESTAMP)"
        quote: true

      - name: article_aggregated_start_timestamp
        description: "TBC"
        quote: true

      - name: article_publish_timestamp
        description: "TBC"
        quote: true

      - name: article_unpublish_timestamp
        description: "TBC"
        quote: true

      - name: article_territory
        description: "The territory of the article"
        quote: true

      - name: article_country_title_localised
        description: "TBC"
        quote: true

      - name: article_description
        description: "The description of the article"
        quote: true

      - name: article_type_name
        description: "The name of the type of the article"
        quote: true

      - name: competition_title_localised
        description: "The localised title of the competition directly relating to the article, if any"
        quote: true

      - name: contestant_0_title_localised
        description: "The localised title of the first contestant in the contestants array, if any"
        quote: true

      - name: contestant_1_title_localised
        description: "The localised second of the first contestant in the contestants array, if any"
        quote: true

      - name: contestant_2_title_localised
        description: "The localised third of the first contestant in the contestants array, if any"
        quote: true

      - name: fixture_id
        description: "The ID of the fixture directly relating to the article, if any (will match to the MFL Fixture ID)"
        quote: true

      - name: fixture_start_timestamp
        description: "The unixtimestamp of the start of the fixture directly relating to the article, if any"
        quote: true

      - name: fixture_status
        description: "The status of the fixture directly relating to the article, if any"
        quote: true

      - name: fixture_title_localised
        description: "The localised title of the fixture directly relating to the article, if any"
        quote: true

      - name: sport_title_localised
        description: "The localised title of the sport directly relating to the article, if any"
        quote: true

      - name: ruleset_title_localised
        description: "The localised title of the ruleset directly relating to the article, if any"
        quote: true

      - name: tournament_title_localised
        description: "The localised title of the tournament directly relating to the article, if any"
        quote: true

      - name: tournament_start_timestamp
        description: "The converted timestamp of the start of the tournament directly relating to the article, if any"
        quote: true

      - name: tournament_end_timestamp
        description: "The converted timestamp of the end of the tournament directly relating to the article, if any"
        quote: true

      - name: venue_title_localised
        description: "The localised title of the venue directly relating to the article, if any"
        quote: true

      - name: stage_title_localised
        description: "The localised title of the stage directly relating to the article, if any"
        quote: true

      - name: stage_start_timestamp
        description: "The timestamp of the start of the stage directly relating to the article, if any"
        quote: true

      - name: stage_end_timestamp
        description: "The timestamp of the start of the stage directly relating to the article, if any"
        quote: true

      - name: article_entitlement_ids
        description: "The entitlement IDs needed to watch the article"
        quote: true

      - name: article_language
        description: "TBC"
        quote: true

      - name: article_country_availability
        description: "An array of countries this article is available to watch in"
        quote: true

      - name: article_is_age_restricted
        description: "Boolean, true if the article is age restricted"
        quote: true

      - name: article_is_hide_in_ui
        description: "Boolean, true if the article is hidden in the UI/platform"
        quote: true

      - name: header_image_id
        description: "Image ID of the background header"
        quote: true

      - name: competition_background_image_id
        description: "Image ID of the background competition"
        quote: true

      - name: sport_background_image_id
        description: "Image ID of the background sport"
        quote: true

      - name: home_contestant_background_image_id
        description: "Image ID of the background home contestant"
        quote: true

      - name: away_contestant_background_image_id
        description: "Image ID of the background away contestant"
        quote: true

      - name: is_content_freemium
        description: "Boolean, true if the article is freemium"
        quote: true

      - name: article_media_id
        description: "Article media id"
        quote: true

      - name: is_content_b2b
        description: "Boolean, true if the article is b2b"
        quote: true

      - name: is_content_b2c
        description: "Boolean, true if the article is b2c"
        quote: true

      - name: is_content_linear
        description: "Boolean, true if the article is linear"
        quote: true

      - name: article_format
        description: "article formate wethere it's Short Highlights or Condensed or NA"
        quote: true

      - name: is_play4free
        description: "Boolean, true if the article is play4free"
        quote: true
