{{
    config(
        materialized='view',
        database='PLAYBACK_STREAM__B2C__MART__' + snowflake_env(),
        schema='CONTENT',
        tags=['presentation-playback-stream-mart'],
        alias='FIXTURE__DIM'
    )
}}

WITH CTE_mfl_fixture__dim AS (
    SELECT * FROM {{ ref('mfl_fixture__dim') }}
)

SELECT
    "fixture_id"
    ,"fixture_description"
    ,"fixture_start_timestamp"
    ,"fixture_status"
    ,"sport_id"
    ,"sport_name"
    ,"competition_id"
    ,"competition_name"
    ,"competition_country_id"
    ,"competition_country_name"
    ,"tournament_calendar_id"
    ,"tournament_calendar_name"
    ,"tournament_calendar_start_date"
    ,"tournament_calendar_end_date"
    ,"ruleset_id"
    ,"ruleset_name"
    ,"stage_id"
    ,"stage_name"
    ,"stage_start_date"
    ,"stage_end_date"
    ,"venue_id"
    ,"venue_name_short"
    ,"venue_name_long"
    ,"contestant_0_id"
    ,"contestant_0_name"
    ,"contestant_0_country_name"
    ,"contestant_1_id"
    ,"contestant_1_name"
    ,"contestant_1_country_name"
    ,"contestant_2_id"
    ,"contestant_2_name"
    ,"contestant_2_country_name"
    ,"contestant_3_id"
    ,"contestant_3_name"
    ,"contestant_3_country_name"
    ,"contestant_4_id"
    ,"contestant_4_name"
    ,"contestant_4_country_name"
    ,"contestant_5_id"
    ,"contestant_5_name"
    ,"contestant_5_country_name"
FROM CTE_mfl_fixture__dim
