{{
    config(
        materialized='view',
        database='PLAYBACK_STREAM__B2C__MART__' + snowflake_env(),
        schema='CONTENT',
        tags=['presentation-playback-stream-mart'],
        alias='ARTICLE__DIM'
    )
}}

WITH CTE_gmr_article__dim AS (
    SELECT * FROM {{ ref('gmr_article__dim') }}
)

SELECT
    "last_updated_timestamp"
    ,"article_id"
    ,"article_title"
    ,"article_start_timestamp"
    ,"article_end_timestamp"
    ,"article_aggregated_start_timestamp"
    ,"article_publish_timestamp"
    ,"article_unpublish_timestamp"
    ,"article_territory"
    ,"article_country_title_localised"
    ,"article_description"
    ,"article_type_name"
    ,"competition_title_localised"
    ,"contestant_0_title_localised"
    ,"contestant_1_title_localised"
    ,"contestant_2_title_localised"
    ,"fixture_id"
    ,"fixture_start_timestamp"
    ,"fixture_status"
    ,"fixture_title_localised"
    ,"sport_title_localised"
    ,"ruleset_title_localised"
    ,"tournament_title_localised"
    ,"tournament_start_timestamp"
    ,"tournament_end_timestamp"
    ,"venue_title_localised"
    ,"stage_title_localised"
    ,"stage_start_timestamp"
    ,"stage_end_timestamp"
    ,"article_entitlement_ids"
    ,"article_language"
    ,"article_country_availability"
    ,"article_is_age_restricted"
    ,"article_is_hide_in_ui"
    ,CASE
        WHEN "payload":"images"[0]:"type"::STRING IN ('image-header','IMAGE-HEADER') THEN "payload":"images"[0]:"id"::STRING
        WHEN "payload":"images"[1]:"type"::STRING IN ('image-header','IMAGE-HEADER') THEN "payload":"images"[1]:"id"::STRING
        WHEN "payload":"images"[2]:"type"::STRING IN ('image-header','IMAGE-HEADER') THEN "payload":"images"[2]:"id"::STRING
    END AS "header_image_id"
    ,CASE
        WHEN "payload":"competition":"images"[0]:"type"::STRING = 'image-background' THEN "payload":"competition":"images"[0]:"id"::STRING
        WHEN "payload":"competition":"images"[1]:"type"::STRING = 'image-background' THEN "payload":"competition":"images"[1]:"id"::STRING
        WHEN "payload":"competition":"images"[2]:"type"::STRING = 'image-background' THEN "payload":"competition":"images"[2]:"id"::STRING
        WHEN "payload":"competition":"images"[3]:"type"::STRING = 'image-background' THEN "payload":"competition":"images"[3]:"id"::STRING
    END AS "competition_background_image_id"
    ,CASE
        WHEN "payload":"sport":"images"[0]:"type"::STRING= 'image-background' THEN "payload":"sport":"images"[0]:"id"::STRING
        WHEN "payload":"sport":"images"[1]:"type"::STRING= 'image-background' THEN "payload":"sport":"images"[1]:"id"::STRING
    END AS "sport_background_image_id"
    ,CASE
        WHEN "payload":"contestants"[0]:"images"[0]:"type"::STRING = 'image-background' THEN "payload":"contestants"[0]:"images"[0]:"id"::STRING
        WHEN "payload":"contestants"[0]:"images"[1]:"type"::STRING = 'image-background' THEN "payload":"contestants"[0]:"images"[1]:"id"::STRING
    END AS "home_contestant_background_image_id"
    ,CASE
        WHEN "payload":"contestants"[1]:"images"[0]:"type"::STRING = 'image-background' THEN "payload":"contestants"[1]:"images"[0]:"id"::STRING
        WHEN "payload":"contestants"[1]:"images"[1]:"type"::STRING = 'image-background' THEN "payload":"contestants"[1]:"images"[1]:"id"::STRING
    END AS "away_contestant_background_image_id"
    ,"article_media_id" AS "article_media_id" 
    ,"is_content_freemium"
    ,"is_content_b2c"
    ,"is_content_b2b"
    ,"is_content_linear"
    ,"article_format" AS "article_format"
    ,"is_play4free"
FROM CTE_gmr_article__dim
