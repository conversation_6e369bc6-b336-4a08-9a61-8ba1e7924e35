{{
    config(
        materialized='view',
        database='PLAYBACK_STREAM__B2C__MART__' + snowflake_env(),
        schema='SUBSCRIPTION',
        alias='SUBSCRIPTION_CAMPAIGN_MAPPING__DIM',
        tags=['presentation-playback-stream-mart']
    )
}}

SELECT
    "subscription_sign_up_campaign_id__skey" AS "subscription_giftcode_campaign_name__skey"
    ,*
FROM {{ ref('subscription_campaign_mapping__dim') }}
