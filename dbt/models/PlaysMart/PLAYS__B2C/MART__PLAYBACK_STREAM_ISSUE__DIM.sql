{{
    config(
        materialized='incremental',
        unique_key='"playback_stream_issue__skey"',
        database='PLAYBACK_STREAM__B2C__MART__' + snowflake_env(),
        schema='PLAYBACK_STREAM',
        alias='<PERSON><PERSON><PERSON>BA<PERSON>K_STREAM_ISSUE__DIM',
        tags=['presentation-playback-stream-mart']
    )
}}

SELECT DISTINCT
    OBJECT_CONSTRUCT('record_inserted_timestamp',CURRENT_TIMESTAMP()) AS "META__DATA"
    ,HASH(
        "startup_error"
        ,"startup_error_type"
        ,"startup_error_list"
        ,"vpf_error_list"
    ) AS "playback_stream_issue__skey"
    ,"startup_error" AS "is_startup_error"
    ,"startup_error_type"
    ,"startup_error_list"
    ,"vpf" AS "is_video_playback_failure_error"
    ,"vpf_error_list" AS "video_playback_failure_error_list"
FROM
    {{ ref('TRANSIENT__PLAYS_2') }}
{% if is_incremental() %}
    WHERE "playback_stream_issue__skey" NOT IN (SELECT "playback_stream_issue__skey" FROM {{ this }})
{% endif %}
