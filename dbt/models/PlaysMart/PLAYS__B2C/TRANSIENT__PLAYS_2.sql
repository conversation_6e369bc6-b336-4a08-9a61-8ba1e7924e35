{{
    config(
        materialized='table',
        transient=true,
        database='TRANSIENT__B2C__PLAYS__' + snowflake_env(),
        schema='PLAYS',
        alias='TRANSIENT__PLAYS_2',
        tags=['presentation-playback-stream-mart'],
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH'
    )
}}

{% set sources = ["dazn", "nfl"] %}

WITH plays_fact AS (
    SELECT * FROM {{ ref('TRANSIENT__PLAYS_1') }}
)

, region_dimension AS (
    SELECT * FROM {{ ref('region_dimension') }}
)

, content_dimension AS (
    SELECT * FROM {{ source('ANALYTICS', 'content_dimension') }}
)

, device_dimension AS (
    SELECT * FROM {{ source('ANALYTICS', 'device_dimension_adjusted') }}
)

, available_entitlements AS (
    SELECT * FROM {{ ref('AVAILABLE_ENTITLEMENTS_CALCULATIONS') }}
)

, articles_entitlement_scd AS (
    SELECT
        "article_id"
        ,"effective_from"
        ,"effective_until"
    FROM {{ ref('dim__article_entitlement_id_scd') }}
)

, customer_identity AS (
    SELECT * FROM {{ ref('customer_identity_dim_current') }}
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "viewer_id" ORDER BY "effective_from" DESC)=1
)

, daily_users AS (
    SELECT * FROM {{ ref('daily_users') }}
    -- Pulling in data from the last 15 days, to handle the late arriving data
    WHERE {{ timestamp_build_trigger(var('build_mode') , 15 , 'batch_date') }}
)

, subscriptions AS (
    SELECT
        "billing_account_id"
        ,TRUE AS "is_b2b"
    FROM {{ ref('staging__zuora__subscription_name_current') }}
    WHERE "subscription_source_system_name" = 'Commercial Premises'
    GROUP BY 1
)

, gmr_article_dim AS (
    SELECT * FROM {{ref( 'mart__gmr_article__dim' )}}
)

SELECT
    plays_fact."ViewerId" AS "viewer_id"
    ,plays_fact."State" AS "state"
    ,plays_fact."City" AS "city"
    ,plays_fact."Isp" AS "isp_name"
    ,plays_fact."StartUpTime_ms" AS "startup_time_ms"
    ,plays_fact."startup_error_type" AS "startup_error_type"
    ,plays_fact."startup_time" AS "startup_time"
    ,plays_fact."PlayingTime_ms" AS "playing_time"
    ,plays_fact."BufferingTime_ms" AS "buffering_time"
    ,plays_fact."Interrupts" AS "interrupts"
    ,plays_fact."AverageBitrate_kbps" AS "avg_bitrate"
    ,plays_fact."StartUpError" AS "startup_error"
    ,plays_fact."IpAddress" AS "ip_address_hash"
    ,plays_fact."cdn_name" AS "cdn_name"
    ,plays_fact."ConvivaSessionId" AS "conviva_session_id"
    ,plays_fact."StreamUrl" AS "stream_url"
    ,plays_fact."ErrorList" AS "startup_error_list"
    ,plays_fact."PercentageComplete" AS "percent_complete"
    ,plays_fact."ConnectionInducedReBufferingTime_ms" AS "connection_induced_rebuffering_time"
    ,plays_fact."connection_induced_rebuffering_time_capped" AS "connection_induced_rebuffering_time_capped"
    ,plays_fact."VideoRestartTime_ms" AS "video_restart_time"
    ,plays_fact."ReJoinedCount" AS "rejoined_count"
    ,plays_fact."VPF" AS "vpf"
    ,plays_fact."VPFErrorList" AS "vpf_error_list"
    ,plays_fact."ContentLength_ms" AS "content_length"
    ,plays_fact."C3PlayerName" AS "player_name"
    ,plays_fact."Concurrency" AS "concurrency"
    ,plays_fact."DRMType" AS "drm_type"
    ,plays_fact."Multivew" AS "multiview"
    ,plays_fact."StreamingProtocol" AS "streaming_protocol"
    ,plays_fact."DaznDeviceId" AS "dazn_device_id"
    ,plays_fact."DaznSessionId" AS "dazn_session_id"
    ,plays_fact."FreeToView" AS "free_to_view"
    ,plays_fact."curated_IpAddressType" AS "curated_ip_address_type"
    ,plays_fact."str_country" AS "country"
    ,plays_fact."start_time" AS "start_time"
    ,plays_fact."end_time" AS "end_time"
    ,plays_fact."total_streaming_time" AS "total_streaming_time"
    ,plays_fact."closed_caption_language" AS "closed_caption_language"
    ,plays_fact."commentary_language" AS "commentary_language"
    ,plays_fact."native_player_version" AS "native_player_version"
    ,plays_fact."origin_server" AS "origin_server"
    ,plays_fact."automatic_or_manual_play" AS "automatic_or_manual_play"
    ,plays_fact."engaged_play" AS "engaged_play"
    ,plays_fact."pbb_flag" AS "pbb_flag"
    ,plays_fact."ond_flag" AS "ond_flag"
    ,plays_fact."mob_manifest_flag" AS "mob_manifest_flag"
    ,plays_fact."user_agent" AS "user_agent"
    ,plays_fact."full_date" AS "full_date"
    ,plays_fact."article_id" AS "article_id"
    ,plays_fact."live_or_on_demand" AS "live_or_on_demand"
    ,plays_fact."connection_type" AS "connection_type"
    ,plays_fact."application_version" AS "application_version"
    ,plays_fact."application_major_version" AS "application_major_version"
    ,plays_fact."application_minor_version" AS "application_minor_version"
    ,plays_fact."application_patch_version" AS "application_patch_version"
    ,plays_fact."application_type" AS "application_type"
    ,plays_fact."application_category" AS "application_category"
    ,plays_fact."genuine_attempt_flag" AS "genuine_attempt_flag"
    ,plays_fact."ebvs_flag" AS "ebvs_flag"
    ,plays_fact."play_flag" AS "play_flag"
    ,plays_fact."dss_uuid" AS "dss_uuid"
    ,plays_fact."Asn" AS "asn"
    ,plays_fact."FixtureId" AS "conviva_fixture_id"
    ,plays_fact."stream_uuid" AS "stream_uuid"
    ,plays_fact."user_agent_key"
    ,plays_fact."ad_manager_name"
    ,region_dimension."territory" AS "territory"
    ,region_dimension."region" AS "region"
    ,{{ hd_flag_parser('origin_server', 'article_type', 'full_date', 'avg_bitrate', 'total_streaming_time', 'fixture_name', 'pbb_flag', 'ond_flag', 'mob_manifest_flag') }} AS "hd_flag"
    ,{{ hd_capable_flag_parser('playing_time', 'article_type', 'avg_bitrate', 'total_streaming_time') }} AS "hd_capable_flag"
    ,{{ sd_flag_parser('article_type', 'pbb_flag', 'ond_flag', 'mob_manifest_flag', 'avg_bitrate', 'total_streaming_time', 'full_date') }} AS "sd_flag"
    ,content_dimension."linear_channel" AS "linear_channel"
    ,content_dimension."effective_from" AS "content_effective_from"
    ,content_dimension."sport_id" AS "sport_id"
    ,device_dimension."device_vendor"
    ,device_dimension."device_model"
    ,device_dimension."device_marketing_name"
    ,device_dimension."device_height"
    ,device_dimension."device_width"
    ,device_dimension."device_diagonal_screen_size"
    ,device_dimension."device_release_year"
    ,device_dimension."device_is_touchscreen"
    ,device_dimension."device_manufacturer"
    ,device_dimension."device_full_description_lr"
    ,device_dimension."tv_flag"
    ,device_dimension."device_full_description"
    ,device_dimension."device_hardware_type"
    ,device_dimension."device_category"
    ,device_dimension."os_name"
    ,device_dimension."os_version"
    ,device_dimension."os_major_version"
    ,device_dimension."os_minor_version"
    ,device_dimension."browser_name"
    ,device_dimension."browser_rendering_engine"
    ,device_dimension."browser_version"
    ,device_dimension."browser_major_version"
    ,device_dimension."browser_minor_version"
    ,device_dimension."browser_patch_version"
    ,device_dimension."os_patch_version"
    ,device_dimension."device_platform"
    ,CURRENT_TIMESTAMP() AS "dbt_processed_at"
    ,NULL AS "available_on_b2b"
    ,NULL AS "error_code"
    ,NULL AS "connection_induced_rebuffering_time_raw"
    ,CASE WHEN subscriptions."is_b2b" THEN 'B2B' ELSE 'B2C' END AS "business_type"
    ,plays_fact."playback_entitlement_set_id"
    ,available_entitlements."available_entitlements" AS "available_entitlement_set_ids"
    ,articles_entitlement_scd."effective_from" AS "article_required_entitlements_effective_from"
    --Handled boolean valid values
    --,plays_fact."is_dai_session"
    ,decode(plays_fact."is_dai_session",'true','true','false','false',null) "is_dai_session"
    ,daily_users."user_status__skey"
    -- Loop through each source (DAZN, NFL, ...?) defined in line 13 of this model
    {% for source in sources %}
    ,daily_users."{{source}}_subscription_name"
    ,daily_users."{{source}}_billing_account__skey"
    ,daily_users."{{source}}_subscription_daily_status__skey"
    ,daily_users."{{source}}_subscription_info__skey"
    ,daily_users."{{source}}_subscription_term__skey"
    ,daily_users."{{source}}_subscription_charge__skey"
    ,daily_users."{{source}}_subscription_source_system_name_derived__skey"
    ,daily_users."{{source}}_subscription_tracking_id__skey"
    ,daily_users."{{source}}_subscription_giftcode_campaign_name__skey"
    ,daily_users."last_{{source}}_subscription_name"
    ,daily_users."user_last_{{source}}_subscription__skey"
    {% endfor %}
    ,COALESCE(gmr_article_dim."fixture_id",plays_fact."FixtureId") as "fixture_id" -- more accurate Fixture ID's than Conviva
FROM plays_fact
LEFT JOIN region_dimension
    ON plays_fact."str_country" = region_dimension."join_key"
LEFT JOIN content_dimension
    ON plays_fact."article_id" = content_dimension."article_id"
    AND plays_fact."start_time" >= content_dimension."effective_from"
    AND plays_fact."start_time" < content_dimension."effective_until"
LEFT JOIN gmr_article_dim
    ON plays_fact."article_id" = gmr_article_dim."article_id"
LEFT JOIN device_dimension
    ON plays_fact."user_agent_key" = device_dimension."user_agent_key"
LEFT JOIN available_entitlements
    ON available_entitlements."article_id" = plays_fact."article_id"
    AND available_entitlements."viewer_id" = plays_fact."ViewerId"
    AND available_entitlements."start_time" = plays_fact."start_time"
LEFT JOIN articles_entitlement_scd
    ON plays_fact."article_id" = articles_entitlement_scd."article_id"
    AND plays_fact."start_time" >= articles_entitlement_scd."effective_from"
    AND plays_fact."start_time" < articles_entitlement_scd."effective_until"
LEFT JOIN customer_identity
    ON plays_fact."ViewerId" = customer_identity."viewer_id"
LEFT JOIN subscriptions
    ON customer_identity."billing_account_id" = subscriptions."billing_account_id"
LEFT JOIN daily_users
    ON customer_identity."dazn_user_id" = daily_users."dazn_user_id"
    AND plays_fact."full_date" = daily_users."batch_date"
