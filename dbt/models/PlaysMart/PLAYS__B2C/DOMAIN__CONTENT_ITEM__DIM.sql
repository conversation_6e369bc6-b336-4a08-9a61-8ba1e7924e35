{{
    config(
        materialized='table',
        database='PLAYBACK_STREAM__B2C__DOMAIN__' + snowflake_env(),
        schema='CONTENT',
        alias='CONTENT_ITEM__DIM',
        tags=['presentation-playback-stream-mart']
    )
}}

{% if is_incremental() %}
WITH existing_metadata AS (
    SELECT
        "article_id" AS "existing_article_id"
        ,"META__DATA" AS "existing_meta_data"
    FROM {{ this }}
)
{% endif %}

SELECT
    {% if is_incremental() %}
    OBJECT_CONSTRUCT('record_inserted_timestamp', COALESCE("existing_meta_data":record_inserted_timestamp, CURRENT_TIMESTAMP()))
    AS "META__DATA"
{% else %}
        OBJECT_CONSTRUCT('record_inserted_timestamp', CURRENT_TIMESTAMP())
        AS "META__DATA"
    {% endif %}
    ,HASH(
        "article_id"
    ) AS "content_item__skey"
    ,"effective_from" AS "record_valid_from_timestamp"
    ,"effective_until" AS "record_valid_to_timestamp"
    ,"content_source" AS "content_item_origin"
    ,"won_content_distinction" AS "content_distinction"
    ,"won_external_reference" AS "won_external_id"

    ,"str_advertised_timestamp" AS "advertised_timestamp"
    ,"won_tile_start_plan" AS "tile_planned_start_timestamp"
    ,"won_tile_end_plan" AS "tile_planned_end_timestamp"
    ,"outlet"
    ,"article_id"
    ,"article_type"
    ,"str_article_type_id" AS "article_type_id"
    ,"article_language" AS "article_language_code"
    ,"article_link_translation_language" AS "article_link_translation_language_code"
    ,"won_commentary_language" AS "commentary_language_code"
    ,"english_article_title" AS "article_title_english"
    ,"local_article_title" AS "article_title_local"
    ,"article_quality"
    ,"article_promotion"

    ,"fixture_id"
    ,"fixture_name"
    ,"fixture_date" AS "fixture_start_timestamp"
    ,"venue_short_name"
    ,"venue_long_name"
    ,"venue_country"
    ,"competition_id"
    ,"competition_name"
    ,"competition_country"
    ,"tournament_calendar_name"
    ,"tournament_calendar_start_date"
    ,"tournament_calendar_end_date"
    ,"stage_name"
    ,"stage_start_date"
    ,"stage_end_date"
    ,HASH(
        "sport_id"
    ) AS "sport_id__hash"
    ,"sport_id"
    ,"sport_name"
    ,"ruleset_name"
    ,"home_contestant_id"
    ,"home_contestant_name"
    ,"home_contestant_country"
    ,"away_contestant_id"
    ,"away_contestant_name"
    ,"away_contestant_country"

    ,"livestream_id"
    ,"shoulder_content_or_live"
    ,"embargoed" AS "is_embargoed"
    ,"age_restricted" AS "is_age_restricted"
    ,"won_exclusivity_flag" AS "is_exclusive"
    ,"won_dci" AS "is_allowed_dci"
    ,"b2b_flag" AS "is_allowed_b2b"
    ,"downloadable_content" AS "is_allowed_download"
    ,"free_to_view" AS "is_allowed_free_to_view"
    ,"linear_channel" AS "is_linear_channel"
    ,"short_highlights" AS "is_short_highlights"
    ,"tile_flag" AS "has_tile_text"
    ,"special_event_rail" AS "has_special_event_rail"

    ,"won_event_status" AS "transmission_status"
    ,"won_vob_key" AS "voiceover_booth_key"
    ,"won_gallery_name" AS "gallery_resource_name"
    ,"won_broadcast_tier" AS "broadcast_tier"
    ,"won_support_tier" AS "support_tier"
    ,"won_alternative_workflow" AS "alternative_workflow"
    ,"won_advertising" AS "advertising_asset_label"

    ,"rightsholder_name"
    ,"won_contract_name" AS "contract_name"
    ,"won_contract_start" AS "contract_start_date"
    ,"won_contract_end" AS "contract_end_date"
    ,"tx_event_type"
    ,"info_freemium"
    ,"is_ppv"
    ,"article_market_name"

FROM
    {{ source('ANALYTICS','content_dimension') }}
{% if is_incremental() %}
    LEFT JOIN existing_metadata em ON src."article_id" = em."existing_article_id"
{% endif %}
-- getting DDL error on duplicate rows so adding a dedup step
QUALIFY ROW_NUMBER() OVER (PARTITION BY "content_item__skey" ORDER BY "effective_until" DESC) = 1
