{{
    config(
        materialized='incremental',
        on_schema_change='append_new_columns',
        incremental_strategy='delete+insert',
        unique_key='"playback_stream_date"',
        database='PLAYBACK_STREAM__B2C__MART__' + snowflake_env(),
        schema='FACT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        alias='PLAYBACK_STREAM__FACT',
        tags=['presentation-playback-stream-mart']
    )
}}

{% set sources = ["dazn", "nfl"] %}

WITH transient__plays_2 AS (
    SELECT * FROM {{ ref('TRANSIENT__PLAYS_2') }}
)

{% if is_incremental() %}
, existing_metadata AS (
    SELECT
        "playback_start_timestamp" AS "existing_playback_start_timestamp"
        ,"conviva_session_id" AS "existing_conviva_session_id"
        ,"META__DATA" AS "existing_meta_data"
    FROM {{ this }}
)
{% endif %}

SELECT
    {% if is_incremental() %}
        OBJECT_CONSTRUCT('record_inserted_timestamp', COALESCE("existing_meta_data":record_inserted_timestamp, CURRENT_TIMESTAMP()))
        AS "META__DATA"
    {% else %}
        OBJECT_CONSTRUCT('record_inserted_timestamp', CURRENT_TIMESTAMP())
        AS "META__DATA"
    {% endif %}
    ,HASH(
        transient__plays_2."start_time"
        ,transient__plays_2."conviva_session_id"
    ) AS "playback_stream__skey"

    ,HASH(
        transient__plays_2."origin_server"
        ,transient__plays_2."connection_type"
        ,transient__plays_2."genuine_attempt_flag"
        ,transient__plays_2."ebvs_flag"
        ,transient__plays_2."cdn_name"
        ,transient__plays_2."curated_ip_address_type"
        ,transient__plays_2."drm_type"
        ,transient__plays_2."multiview"
        ,transient__plays_2."streaming_protocol"
        ,transient__plays_2."automatic_or_manual_play"
        ,transient__plays_2."engaged_play"
        ,transient__plays_2."hd_flag"
        ,transient__plays_2."sd_flag"
        ,transient__plays_2."is_dai_session"
        ,transient__plays_2."commentary_language"
    ) AS "playback_stream_details__skey"
    //
    ,HASH(
        transient__plays_2."state"
        ,transient__plays_2."city"
        ,transient__plays_2."country"
    ) AS "playback_stream_location__skey"
    //
    ,HASH(
        transient__plays_2."startup_error"
        ,transient__plays_2."startup_error_type"
        ,transient__plays_2."startup_error_list"
        ,transient__plays_2."vpf_error_list"
    ) AS "playback_stream_issue__skey"
    //
    ,HASH(
        transient__plays_2."device_vendor"
        ,transient__plays_2."device_model"
        ,transient__plays_2."device_marketing_name"
        ,transient__plays_2."device_height"
        ,transient__plays_2."device_width"
        ,transient__plays_2."device_diagonal_screen_size"
        ,transient__plays_2."device_release_year"
        ,transient__plays_2."device_is_touchscreen"
        ,transient__plays_2."device_manufacturer"
        ,transient__plays_2."device_category"
        ,transient__plays_2."device_hardware_type"
        ,transient__plays_2."device_platform"
        ,transient__plays_2."device_full_description"
        ,transient__plays_2."hd_capable_flag"
        ,transient__plays_2."browser_name"
        ,transient__plays_2."browser_rendering_engine"
        ,transient__plays_2."browser_version"
        ,transient__plays_2."browser_major_version"
        ,transient__plays_2."browser_minor_version"
        ,transient__plays_2."browser_patch_version"
        ,transient__plays_2."os_name"
        ,transient__plays_2."os_version"
        ,transient__plays_2."os_major_version"
        ,transient__plays_2."os_minor_version"
        ,transient__plays_2."os_patch_version"
    ) AS "device_info__skey"
    //
    ,HASH(
        transient__plays_2."application_type"
        ,transient__plays_2."application_category"
        ,transient__plays_2."application_version"
        ,transient__plays_2."application_major_version"
        ,transient__plays_2."application_minor_version"
        ,transient__plays_2."application_patch_version"
        ,transient__plays_2."native_player_version"
        ,transient__plays_2."player_name"
    ) AS "application__skey"
    //
    ,HASH(
        transient__plays_2."article_id"
    ) AS "content_item__skey"
    //
    ,HASH(
        transient__plays_2."country"
    ) AS "playback_stream_country__hash"
    //
    ,HASH(
        transient__plays_2."sport_id"
    ) AS "sport_id__hash"
    //
    ,CASE
        WHEN transient__plays_2."available_entitlement_set_ids" IS NULL THEN NULL
        ELSE HASH(
            transient__plays_2."article_id"
            ,transient__plays_2."available_entitlement_set_ids"
            ,transient__plays_2."article_required_entitlements_effective_from"
        )
    END AS "entitlements__skey"
    //
    ,HASH(
        transient__plays_2."viewer_id"
    ) AS "customer_identity__skey"
    //fact info
    ,CAST(transient__plays_2."full_date" AS date) AS "playback_stream_date"
    ,transient__plays_2."start_time" AS "playback_start_timestamp"
    ,transient__plays_2."end_time" AS "playback_end_timestamp"

    ,transient__plays_2."playing_time" AS "playback_duration_milliseconds"
    ,transient__plays_2."startup_time" AS "startup_duration_milliseconds"
    ,transient__plays_2."buffering_time" AS "buffering_duration_milliseconds"
    ,transient__plays_2."video_restart_time" AS "video_restart_duration_milliseconds"
    ,transient__plays_2."connection_induced_rebuffering_time" AS "total_connection_induced_rebuffering_duration_milliseconds"
    ,transient__plays_2."total_streaming_time" AS "total_streaming_duration_milliseconds"

    ,transient__plays_2."avg_bitrate" AS "average_bitrate"
    ,transient__plays_2."interrupts" AS "interrupts_count"
    ,transient__plays_2."rejoined_count" AS "rejoins_count"

    ,transient__plays_2."playback_entitlement_set_id"
    ,transient__plays_2."dazn_device_id"
    ,transient__plays_2."conviva_session_id"
    ,transient__plays_2."user_agent"
    ,transient__plays_2."viewer_id"
    ,transient__plays_2."dazn_session_id"
    ,transient__plays_2."ip_address_hash"

    ,transient__plays_2."user_status__skey"
    -- Loop through each source (DAZN, NFL, ...?) defined in line 14 of this model
    {% for source in sources %}
    ,transient__plays_2."{{source}}_subscription_name"
    ,transient__plays_2."{{source}}_billing_account__skey"
    ,transient__plays_2."{{source}}_subscription_daily_status__skey"
    ,transient__plays_2."{{source}}_subscription_info__skey"
    ,transient__plays_2."{{source}}_subscription_term__skey"
    ,transient__plays_2."{{source}}_subscription_charge__skey"
    ,transient__plays_2."{{source}}_subscription_source_system_name_derived__skey"
    ,transient__plays_2."{{source}}_subscription_tracking_id__skey"
    ,transient__plays_2."{{source}}_subscription_giftcode_campaign_name__skey"
    ,transient__plays_2."last_{{source}}_subscription_name"
    ,transient__plays_2."user_last_{{source}}_subscription__skey"
    {% endfor %}
    ,upper(transient__plays_2."free_to_view") AS "is_free_to_view"
    ,transient__plays_2."content_length" AS "content_length"
    -- ,transient__plays_2."article_id" AS "article_id"
    -- ,transient__plays_2."fixture_id" AS "fixture_id"
    -- ,HASH(transient__plays_2."fixture_id",transient__plays_2."territory") AS "live_event__skey"
FROM transient__plays_2
{% if is_incremental() %}
LEFT JOIN existing_metadata
    ON transient__plays_2."start_time" = existing_metadata."existing_playback_start_timestamp"
    AND transient__plays_2."conviva_session_id" = existing_metadata."existing_conviva_session_id"
{% endif %}
QUALIFY ROW_NUMBER() OVER (PARTITION BY "playback_start_timestamp", transient__plays_2."conviva_session_id" ORDER BY "playback_duration_milliseconds" DESC) = 1
