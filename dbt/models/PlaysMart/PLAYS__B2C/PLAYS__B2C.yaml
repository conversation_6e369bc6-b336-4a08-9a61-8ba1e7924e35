version: 2

models:
  - name: TRANSIENT__PLAYS_1
    description: "Transient table containing the first step of the transformations for the plays mart deduping the conviva session"
    columns:
      - name: VIEWERID
        description: "TBC"
        quote: true

      - name: ASSET
        description: "TBC"
        quote: true

      - name: DEVICEOS
        description: "TBC"
        quote: true

      - name: STATE
        description: "TBC"
        quote: true

      - name: CITY
        description: "TBC"
        quote: true

      - name: ISP
        description: "TBC"
        quote: true

      - name: STARTTIME_UNIXTIME
        description: "TBC"
        quote: true

      - name: STARTUPTIME_MS
        description: "TBC"
        quote: true

      - name: PLAYINGTIME_MS
        description: "TBC"
        quote: true

      - name: BUFFERINGTIME_MS
        description: "TBC"
        quote: true

      - name: INTERRUPTS
        description: "TBC"
        quote: true

      - name: AVERAGEBITRATE_KBPS
        description: "TBC"
        quote: true

      - name: STARTUPERROR
        description: "TBC"
        quote: true

      - name: IPADDRESS
        description: "TBC"
        quote: true

      - name: CDN_NAME
        description: "TBC"
        quote: true

      - name: BROWSER
        description: "TBC"
        quote: true

      - name: CONVIVASESSIONID
        description: "TBC"
        quote: true

      - name: STREAMURL
        description: "TBC"
        quote: true

      - name: ERRORLIST
        description: "TBC"
        quote: true

      - name: PERCENTAGECOMPLETE
        description: "TBC"
        quote: true

      - name: CONNECTIONINDUCEDREBUFFERINGTIME_MS
        description: "TBC"
        quote: true

      - name: VIDEORESTARTTIME_MS
        description: "TBC"
        quote: true

      - name: REJOINEDCOUNT
        description: "TBC"
        quote: true

      - name: VPF
        description: "TBC"
        quote: true

      - name: VPFERRORLIST
        description: "TBC"
        quote: true

      - name: CONTENTLENGTH_MS
        description: "TBC"
        quote: true

      - name: ENDEDSTATUS
        description: "TBC"
        quote: true

      - name: ENDTIME_UNIXTIME
        description: "TBC"
        quote: true

      - name: C3DEVICEUA
        description: "TBC"
        quote: true

      - name: DAZNUSERAGENT
        description: "TBC"
        quote: true

      - name: C3DEVICEMODEL
        description: "TBC"
        quote: true

      - name: DVFW
        description: "TBC"
        quote: true

      - name: APPLICATIONTYPE
        description: "TBC"
        quote: true

      - name: APPLICATIONVERSION
        description: "TBC"
        quote: true

      - name: AUTOPLAY
        description: "TBC"
        quote: true

      - name: C3DERS
        description: "TBC"
        quote: true

      - name: C3IM
        description: "TBC"
        quote: true

      - name: C3PLAYERNAME
        description: "TBC"
        quote: true

      - name: C3VIDEOISLIVE
        description: "TBC"
        quote: true

      - name: CLOSEDCAPTIONLANGUAGE
        description: "TBC"
        quote: true

      - name: COMMENTATORYLANGUAGE
        description: "TBC"
        quote: true

      - name: CONCURRENCY
        description: "TBC"
        quote: true

      - name: DEVICECONNECTIONTYPE
        description: "TBC"
        quote: true

      - name: DRMTYPE
        description: "TBC"
        quote: true

      - name: MULTIVEW
        description: "TBC"
        quote: true

      - name: NATIVEPLAYERVERSION
        description: "TBC"
        quote: true

      - name: STREAMINGPROTOCOL
        description: "TBC"
        quote: true

      - name: DVMRK
        description: "TBC"
        quote: true

      - name: ISC3DEVICEUADOUBLEDECODED
        description: "TBC"
        quote: true

      - name: ISDAZNUSERAGENTDOUBLEDECODED
        description: "TBC"
        quote: true

      - name: DAZNDEVICEID
        description: "TBC"
        quote: true

      - name: DAZNSESSIONID
        description: "TBC"
        quote: true

      - name: FREETOVIEW
        description: "TBC"
        quote: true

      - name: CURATED_IPADDRESSTYPE
        description: "TBC"
        quote: true

      - name: FIXTUREID
        description: "TBC"
        quote: true

      - name: STREAM_UUID
        description: "TBC"
        quote: true

      - name: CONNECTION_INDUCED_REBUFFERING_TIME_CAPPED
        description: "TBC"
        quote: true

      - name: STR_COUNTRY
        description: "TBC"
        quote: true

      - name: START_TIME
        description: "TBC"
        quote: true

      - name: END_TIME
        description: "TBC"
        quote: true

      - name: TOTAL_STREAMING_TIME
        description: "TBC"
        quote: true

      - name: USER_AGENT
        description: "TBC"
        quote: true

      - name: USER_AGENT_KEY
        description: "TBC"
        quote: true

      - name: FULL_DATE
        description: "TBC"
        quote: true

      - name: ARTICLE_ID
        description: "TBC"
        quote: true

      - name: ASN
        description: "TBC"
        quote: true

      - name: DSS_UUID
        description: "TBC"
        quote: true

      - name: CLOSED_CAPTION_LANGUAGE
        description: "TBC"
        quote: true

      - name: COMMENTARY_LANGUAGE
        description: "TBC"
        quote: true

      - name: NATIVE_PLAYER_VERSION
        description: "TBC"
        quote: true

      - name: ORIGIN_SERVER
        description: "TBC"
        quote: true

      - name: AUTOMATIC_OR_MANUAL_PLAY
        description: "TBC"
        quote: true

      - name: ENGAGED_PLAY
        description: "TBC"
        quote: true

      - name: STR_CDN
        description: "TBC"
        quote: true

      - name: PBB_FLAG
        description: "TBC"
        quote: true

      - name: OND_FLAG
        description: "TBC"
        quote: true

      - name: MOB_MANIFEST_FLAG
        description: "TBC"
        quote: true

      - name: LIVE_OR_ON_DEMAND
        description: "TBC"
        quote: true

      - name: CONNECTION_TYPE
        description: "TBC"
        quote: true

      - name: APPLICATION_VERSION
        description: "TBC"
        quote: true

      - name: APPLICATION_MAJOR_VERSION
        description: "TBC"
        quote: true

      - name: APPLICATION_MINOR_VERSION
        description: "TBC"
        quote: true

      - name: APPLICATION_PATCH_VERSION
        description: "TBC"
        quote: true

      - name: APPLICATION_TYPE
        description: "TBC"
        quote: true

      - name: APPLICATION_CATEGORY
        description: "TBC"
        quote: true

      - name: GENUINE_ATTEMPT_FLAG
        description: "TBC"
        quote: true

      - name: EBVS_FLAG
        description: "TBC"
        quote: true

      - name: PLAY_FLAG
        description: "TBC"
        quote: true

      - name: STARTUP_ERROR_TYPE
        description: "TBC"
        quote: true

      - name: STARTUP_TIME
        description: "TBC"
        quote: true

      - name: STR_CDN_NAME
        description: "TBC"
        quote: true

      - name: PLAYBACK_ENTITLEMENT_SET_ID
        description: "TBC"
        quote: true

      - name: IS_DAI_SESSION
        description: "TBC"
        quote: true

  - name: TRANSIENT__PLAYS_2
    description: "Transient table containing the second step of transformations for the plays mart joining multiple sources in (like content, devices, subscriptions, ...) in order to create the skeys"
    columns:
      - name: VIEWER_ID
        description: "TBC"
        quote: true

      - name: STATE
        description: "TBC"
        quote: true

      - name: CITY
        description: "TBC"
        quote: true

      - name: ISP_NAME
        description: "TBC"
        quote: true

      - name: STARTUP_TIME_MS
        description: "TBC"
        quote: true

      - name: STARTUP_ERROR_TYPE
        description: "TBC"
        quote: true

      - name: STARTUP_TIME
        description: "TBC"
        quote: true

      - name: PLAYING_TIME
        description: "TBC"
        quote: true

      - name: BUFFERING_TIME
        description: "TBC"
        quote: true

      - name: INTERRUPTS
        description: "TBC"
        quote: true

      - name: AVG_BITRATE
        description: "TBC"
        quote: true

      - name: STARTUP_ERROR
        description: "TBC"
        quote: true

      - name: IP_ADDRESS_HASH
        description: "TBC"
        quote: true

      - name: CDN_NAME
        description: "TBC"
        quote: true

      - name: CONVIVA_SESSION_ID
        description: "TBC"
        quote: true

      - name: STREAM_URL
        description: "TBC"
        quote: true

      - name: STARTUP_ERROR_LIST
        description: "TBC"
        quote: true

      - name: PERCENT_COMPLETE
        description: "TBC"
        quote: true

      - name: CONNECTION_INDUCED_REBUFFERING_TIME
        description: "TBC"
        quote: true

      - name: CONNECTION_INDUCED_REBUFFERING_TIME_CAPPED
        description: "TBC"
        quote: true

      - name: VIDEO_RESTART_TIME
        description: "TBC"
        quote: true

      - name: REJOINED_COUNT
        description: "TBC"
        quote: true

      - name: VPF
        description: "TBC"
        quote: true

      - name: VPF_ERROR_LIST
        description: "TBC"
        quote: true

      - name: CONTENT_LENGTH
        description: "TBC"
        quote: true

      - name: PLAYER_NAME
        description: "TBC"
        quote: true

      - name: CONCURRENCY
        description: "TBC"
        quote: true

      - name: DRM_TYPE
        description: "TBC"
        quote: true

      - name: MULTIVIEW
        description: "TBC"
        quote: true

      - name: STREAMING_PROTOCOL
        description: "TBC"
        quote: true

      - name: DAZN_DEVICE_ID
        description: "TBC"
        quote: true

      - name: DAZN_SESSION_ID
        description: "TBC"
        quote: true

      - name: FREE_TO_VIEW
        description: "TBC"
        quote: true

      - name: CURATED_IP_ADDRESS_TYPE
        description: "TBC"
        quote: true

      - name: COUNTRY
        description: "TBC"
        quote: true

      - name: START_TIME
        description: "TBC"
        quote: true

      - name: END_TIME
        description: "TBC"
        quote: true

      - name: TOTAL_STREAMING_TIME
        description: "TBC"
        quote: true

      - name: CLOSED_CAPTION_LANGUAGE
        description: "TBC"
        quote: true

      - name: COMMENTARY_LANGUAGE
        description: "TBC"
        quote: true

      - name: NATIVE_PLAYER_VERSION
        description: "TBC"
        quote: true

      - name: ORIGIN_SERVER
        description: "TBC"
        quote: true

      - name: AUTOMATIC_OR_MANUAL_PLAY
        description: "TBC"
        quote: true

      - name: ENGAGED_PLAY
        description: "TBC"
        quote: true

      - name: PBB_FLAG
        description: "TBC"
        quote: true

      - name: OND_FLAG
        description: "TBC"
        quote: true

      - name: MOB_MANIFEST_FLAG
        description: "TBC"
        quote: true

      - name: USER_AGENT
        description: "TBC"
        quote: true

      - name: FULL_DATE
        description: "TBC"
        quote: true

      - name: ARTICLE_ID
        description: "TBC"
        quote: true

      - name: LIVE_OR_ON_DEMAND
        description: "TBC"
        quote: true

      - name: CONNECTION_TYPE
        description: "TBC"
        quote: true

      - name: APPLICATION_VERSION
        description: "TBC"
        quote: true

      - name: APPLICATION_MAJOR_VERSION
        description: "TBC"
        quote: true

      - name: APPLICATION_MINOR_VERSION
        description: "TBC"
        quote: true

      - name: APPLICATION_PATCH_VERSION
        description: "TBC"
        quote: true

      - name: APPLICATION_TYPE
        description: "TBC"
        quote: true

      - name: APPLICATION_CATEGORY
        description: "TBC"
        quote: true

      - name: GENUINE_ATTEMPT_FLAG
        description: "TBC"
        quote: true

      - name: EBVS_FLAG
        description: "TBC"
        quote: true

      - name: PLAY_FLAG
        description: "TBC"
        quote: true

      - name: DSS_UUID
        description: "TBC"
        quote: true

      - name: ASN
        description: "TBC"
        quote: true

      - name: CONVIVA_FIXTURE_ID
        description: "TBC"
        quote: true

      - name: STREAM_UUID
        description: "TBC"
        quote: true

      - name: USER_AGENT_KEY
        description: "TBC"
        quote: true

      - name: TERRITORY
        description: "TBC"
        quote: true

      - name: REGION
        description: "TBC"
        quote: true

      - name: HD_FLAG
        description: "TBC"
        quote: true

      - name: HD_CAPABLE_FLAG
        description: "TBC"
        quote: true

      - name: SD_FLAG
        description: "TBC"
        quote: true

      - name: LINEAR_CHANNEL
        description: "TBC"
        quote: true

      - name: CONTENT_EFFECTIVE_FROM
        description: "TBC"
        quote: true

      - name: SPORT_ID
        description: "TBC"
        quote: true

      - name: DEVICE_VENDOR
        description: "TBC"
        quote: true

      - name: DEVICE_MODEL
        description: "TBC"
        quote: true

      - name: DEVICE_MARKETING_NAME
        description: "TBC"
        quote: true

      - name: DEVICE_HEIGHT
        description: "TBC"
        quote: true

      - name: DEVICE_WIDTH
        description: "TBC"
        quote: true

      - name: DEVICE_DIAGONAL_SCREEN_SIZE
        description: "TBC"
        quote: true

      - name: DEVICE_RELEASE_YEAR
        description: "TBC"
        quote: true

      - name: DEVICE_IS_TOUCHSCREEN
        description: "TBC"
        quote: true

      - name: DEVICE_MANUFACTURER
        description: "TBC"
        quote: true

      - name: DEVICE_FULL_DESCRIPTION_LR
        description: "TBC"
        quote: true

      - name: TV_FLAG
        description: "TBC"
        quote: true

      - name: DEVICE_FULL_DESCRIPTION
        description: "TBC"
        quote: true

      - name: DEVICE_HARDWARE_TYPE
        description: "TBC"
        quote: true

      - name: DEVICE_CATEGORY
        description: "TBC"
        quote: true

      - name: OS_NAME
        description: "TBC"
        quote: true

      - name: OS_VERSION
        description: "TBC"
        quote: true

      - name: OS_MAJOR_VERSION
        description: "TBC"
        quote: true

      - name: OS_MINOR_VERSION
        description: "TBC"
        quote: true

      - name: BROWSER_NAME
        description: "TBC"
        quote: true

      - name: BROWSER_RENDERING_ENGINE
        description: "TBC"
        quote: true

      - name: BROWSER_VERSION
        description: "TBC"
        quote: true

      - name: BROWSER_MAJOR_VERSION
        description: "TBC"
        quote: true

      - name: BROWSER_MINOR_VERSION
        description: "TBC"
        quote: true

      - name: BROWSER_PATCH_VERSION
        description: "TBC"
        quote: true

      - name: OS_PATCH_VERSION
        description: "TBC"
        quote: true

      - name: DEVICE_PLATFORM
        description: "TBC"
        quote: true

      - name: DBT_PROCESSED_AT
        description: "TBC"
        quote: true

      - name: AVAILABLE_ON_B2B
        description: "TBC"
        quote: true

      - name: ERROR_CODE
        description: "TBC"
        quote: true

      - name: CONNECTION_INDUCED_REBUFFERING_TIME_RAW
        description: "TBC"
        quote: true

      - name: BUSINESS_TYPE
        description: "TBC"
        quote: true

      - name: PLAYBACK_ENTITLEMENT_SET_ID
        description: "TBC"
        quote: true

      - name: AVAILABLE_ENTITLEMENT_SET_IDS
        description: "TBC"
        quote: true

      - name: ARTICLE_REQUIRED_ENTITLEMENTS_EFFECTIVE_FROM
        description: "TBC"
        quote: true

      - name: IS_DAI_SESSION
        description: "TBC"
        quote: true

      - name: user_status__skey
        description: "The User Status Dim SKEY for the user/viewer of the stream at the end of the day"
        quote: true

      - name: dazn_subscription_name
        description: "The DAZN Subscription Name (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: dazn_billing_account__skey
        description: "The DAZN Billing Account Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: dazn_subscription_daily_status__skey
        description: "The DAZN Subscriptoin Daily Status Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: dazn_subscription_info__skey
        description: "The DAZN Subscription Info Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: dazn_subscription_term__skey
        description: "The DAZN Subscription term Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: dazn_subscription_charge__skey
        description: "The DAZN Subscription Charge Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: dazn_subscription_source_system_name_derived__skey
        description: "The DAZN Subscription Source Name Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: dazn_subscription_tracking_id__skey
        description: "The DAZN Subscription Tracking Id Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: dazn_subscription_giftcode_campaign_name__skey
        description: "The DAZN Subscription Giftcode Campaign Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: last_dazn_subscription_name
        description: "The DAZN Subscription Name of the last active Subscription for the user/viewer of the stream at the end of the day"
        quote: true

      - name: user_last_dazn_subscription__skey
        description: "The DAZN Last Subscription Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: nfl_subscription_name
        description: "The NFL Subscription Name (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: nfl_billing_account__skey
        description: "The NFL Billing Account Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: nfl_subscription_daily_status__skey
        description: "The NFL Subscriptoin Daily Status Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: nfl_subscription_info__skey
        description: "The NFL Subscription Info Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: nfl_subscription_term__skey
        description: "The NFL Subscription term Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: nfl_subscription_charge__skey
        description: "The NFL Subscription Charge Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: nfl_subscription_source_system_name_derived__skey
        description: "The NFL Subscription Source Name Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: nfl_subscription_tracking_id__skey
        description: "The NFL Subscription Tracking Id Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: nfl_subscription_giftcode_campaign_name__skey
        description: "The NFL Subscription Giftcode Campaign Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: last_nfl_subscription_name
        description: "The NFL Subscription Name of the last active Subscription for the user/viewer of the stream at the end of the day"
        quote: true

      - name: user_last_nfl_subscription__skey
        description: "The NFL Last Subscription Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: gmr_fixture_id
        description: "This field contains the fixture id from GMR articles which is a more accurate field for fixture id's rather than conviva fixture id as it contains article id's as well."
        quote: true

  - name: DOMAIN__GEO__DIM
    description: "Conformed dimension. Each record represents a city location."
    columns:
      - name: geo__skey
        description: "Surrogate key which uniquely identifies a table row."
        quote: true

      - name: country__hash
        description: "Hash of the country column value."
        quote: true

      - name: country
        description: "Name of the country in which the city is located."
        quote: true

      - name: state
        description: "Name of the state in which the city is located - may contain abbreviations."
        quote: true

      - name: city
        description: "Name of the city."
        quote: true

      - name: territory
        description: "Name of the Territory."
        quote: true

      - name: region
        description: "Name of the Region."
        quote: true

  - name: MART__PLAYBACK_STREAM_LOCATION__DIM
    description: "Role playing dimension. Each record represents a city that locates the geographic source of streams, derived from IP address."
    columns:
      - name: playback_stream_location__skey
        description: "Surrogate key which uniquely identifies a table row."
        quote: true

      - name: playback_stream_country__hash
        description: "Hash of the country column value."
        quote: true

      - name: playback_stream_country
        description: "Name of the country corresponding to the IP address from which the stream originates."
        quote: true

      - name: playback_stream_state
        description: "Name of the state in which the city is located - may contain abbreviations."
        quote: true

      - name: playback_stream_city
        description: "Name of the city corresponding to the IP address from which the stream originates."
        quote: true

      - name: playback_stream_territory
        description: "Name of the Territory corresponding to the IP address from which the stream originates."
        quote: true

      - name: playback_stream_region
        description: "Name of the Region corresponding to the IP address from which the stream originates."
        quote: true

  - name: DOMAIN__DEVICE_INFO__DIM
    description: "Conformed dimension. Each record is a unique combination of values which describe a device used to access DAZN content, specifically referencing the characteristics of hardware and software developed outside DAZN, including details about the operating system and browser."
    columns:
      - name: device_info__skey
        description: "Surrogate key which uniquely identifies a table row."
        quote: true

      - name: device_vendor_name
        description: "Name of the company or organisation that provides the device to the market."
        quote: true

      - name: device_model
        description: "The model name of the device."
        quote: true

      - name: device_marketing_name
        description: "Market recognised name for the device."
        quote: true

      - name: device_display_height_pixels
        description: "The total number of addressable pixels in the vertical direction of a rectangular display when held in its default orientation."
        quote: true

      - name: device_display_width_pixels
        description: "The total number of addressable pixels in the horizontal direction of a rectangular display when held in its default orientation."
        quote: true

      - name: device_diagonal_screen_size_inches
        description: "The diagonal dimension of the screen, measured in inches."
        quote: true

      - name: device_release_year
        description: "The year when the device was released or announced on the market."
        quote: true

      - name: device_is_touchscreen
        description: "Indicates whether the device may be interacted with by touching the screen."
        quote: true

      - name: device_manufacturer_name
        description: "Name of the primary organisation creating - and not necessarily assembling - the device."
        quote: true

      - name: device_category
        description: "High-level categorisation of the device e.g. living room, web, mobile."
        quote: true

      - name: device_hardware_type
        description: "The primary hardware type of the device e.g. tablet, mobile phone."
        quote: true

      - name: device_platform
        description: "The commercial name of the developer platform, if applicable."
        quote: true

      - name: device_description
        description: "The most granular description for the device, including information about the browser and operating system."
        quote: true

      - name: device_is_hd_capable
        description: "Indicates whether the device can display high definition video."
        quote: true

      - name: browser_name
        description: "The name or type of the browser on the device."
        quote: true

      - name: browser_rendering_engine
        description: "The name or type of the rendering engine used by the browser."
        quote: true

      - name: browser_version
        description: "The browser version on the device."
        quote: true

      - name: browser_major_version
        description: "First component of the browser version."
        quote: true

      - name: browser_minor_version
        description: "Second component of the browser version."
        quote: true

      - name: browser_patch_version
        description: "Third component of the browser version."
        quote: true

      - name: os_name
        description: "The name of the operating system installed on the device."
        quote: true

      - name: os_version
        description: "The operating system initial version installed on the device."
        quote: true

      - name: os_major_version
        description: "First component of the operating system version."
        quote: true

      - name: os_minor_version
        description: "Second component of the operating system version."
        quote: true

      - name: os_patch_version
        description: "Third component of the operating system version."
        quote: true

      - name: META__DATA
        description: "Variant column containing JSON structured meta data on the record, such as a timestamp of when the record was inserted"
        quote: true

  - name: MART__DEVICE_INFO__DIM
    description: "Conformed dimension. Each record is a unique combination of values which describe a device used to access DAZN content, specifically referencing the characteristics of hardware and software developed outside DAZN, including details about the operating system and browser."
    columns:
      - name: device_info__skey
        description: "Surrogate key which uniquely identifies a table row."
        quote: true

      - name: device_vendor_name
        description: "Name of the company or organisation that provides the device to the market."
        quote: true

      - name: device_model
        description: "The model name of the device."
        quote: true

      - name: device_marketing_name
        description: "Market recognised name for the device."
        quote: true

      - name: device_display_height_pixels
        description: "The total number of addressable pixels in the vertical direction of a rectangular display when held in its default orientation."
        quote: true

      - name: device_display_width_pixels
        description: "The total number of addressable pixels in the horizontal direction of a rectangular display when held in its default orientation."
        quote: true

      - name: device_diagonal_screen_size_inches
        description: "The diagonal dimension of the screen, measured in inches."
        quote: true

      - name: device_release_year
        description: "The year when the device was released or announced on the market."
        quote: true

      - name: device_is_touchscreen
        description: "Indicates whether the device may be interacted with by touching the screen."
        quote: true

      - name: device_manufacturer_name
        description: "Name of the primary organisation creating - and not necessarily assembling - the device."
        quote: true

      - name: device_category
        description: "High-level categorisation of the device e.g. living room, web, mobile."
        quote: true

      - name: device_hardware_type
        description: "The primary hardware type of the device e.g. tablet, mobile phone."
        quote: true

      - name: device_platform
        description: "The commercial name of the developer platform, if applicable."
        quote: true

      - name: device_description
        description: "The most granular description for the device, including information about the browser and operating system."
        quote: true

      - name: device_is_hd_capable
        description: "Indicates whether the device can display high definition video."
        quote: true

      - name: browser_name
        description: "The name or type of the browser on the device."
        quote: true

      - name: browser_rendering_engine
        description: "The name or type of the rendering engine used by the browser."
        quote: true

      - name: browser_version
        description: "The browser version on the device."
        quote: true

      - name: browser_major_version
        description: "First component of the browser version."
        quote: true

      - name: browser_minor_version
        description: "Second component of the browser version."
        quote: true

      - name: browser_patch_version
        description: "Third component of the browser version."
        quote: true

      - name: os_name
        description: "The name of the operating system installed on the device."
        quote: true

      - name: os_version
        description: "The operating system initial version installed on the device."
        quote: true

      - name: os_major_version
        description: "First component of the operating system version."
        quote: true

      - name: os_minor_version
        description: "Second component of the operating system version."
        quote: true

      - name: os_patch_version
        description: "Third component of the operating system version."
        quote: true

      - name: META__DATA
        description: "Variant column containing JSON structured meta data on the record, such as a timestamp of when the record was inserted"
        quote: true

  - name: DOMAIN__APPLICATION__DIM
    description: "Conformed dimension. Each record is a unique combination of values describing an instance of software developed by DAZN, used to access DAZN content."
    columns:
      - name: application__skey
        description: "Surrogate key which uniquely identifies a table row."
        quote: true

      - name: application_type
        description: "The type of the application specifying its native platform."
        quote: true

      - name: application_category
        description: "High-level categorisation of the app e.g. living room, web, mobile."
        quote: true

      - name: application_version
        description: "The version of the application."
        quote: true

      - name: application_major_version
        description: "First component of the application version."
        quote: true

      - name: application_minor_version
        description: "Second component of the application version."
        quote: true

      - name: application_patch_version
        description: "Third component of the application version."
        quote: true

      - name: native_player_version
        description: "The version of the player (native to the platform or device) used for video playback."
        quote: true

      - name: player_name
        description: "The name of the player application used for video playback."
        quote: true

      - name: META__DATA
        description: "Variant column containing JSON structured meta data on the record, such as a timestamp of when the record was inserted"
        quote: true

  - name: MART__APPLICATION__DIM
    description: "Conformed dimension. Each record is a unique combination of values describing an instance of software developed by DAZN, used to access DAZN content."
    columns:
      - name: application__skey
        description: "Surrogate key which uniquely identifies a table row."
        quote: true

      - name: application_type
        description: "The type of the application specifying its native platform."
        quote: true

      - name: application_category
        description: "High-level categorisation of the app e.g. living room, web, mobile."
        quote: true

      - name: application_version
        description: "The version of the application."
        quote: true

      - name: application_major_version
        description: "First component of the application version."
        quote: true

      - name: application_minor_version
        description: "Second component of the application version."
        quote: true

      - name: application_patch_version
        description: "Third component of the application version."
        quote: true

      - name: native_player_version
        description: "The version of the player (native to the platform or device) used for video playback."
        quote: true

      - name: player_name
        description: "The name of the player application used for video playback."
        quote: true

      - name: META__DATA
        description: "Variant column containing JSON structured meta data on the record, such as a timestamp of when the record was inserted"
        quote: true

  - name: DOMAIN__CONTENT_ITEM__DIM
    description: "Conformed dimension. Each record represents the latest version of a content item that was made available for consumption / playback on the DAZN platform. There may be multiple records associated with a content item if one or more attributes have changed value over time, but only a single record may represent the current version of a content item."
    columns:
      - name: content_item__skey
        description: "Surrogate key which uniquely identifies a content item."
        quote: true

      - name: record_valid_from_timestamp
        description: "Timestamp indicating when the record (representing a version of the content item) came into effect."
        quote: true

      - name: record_valid_to_timestamp
        description: "Timestamp indicating when the record (representing a version of the content item) expired. This value points to a date in the future if the record is current."
        quote: true

      - name: content_item_origin
        description: "The source of the content item record e.g. PCMS, backfill."
        quote: true

      - name: content_distinction
        description: "A more granular categorisation for the content type."
        quote: true

      - name: article_id
        description: "Source identifier for the article that corresponds to the content item."
        quote: true

      - name: won_external_id
        description: "Identifier for the content item within the What's On external system."
        quote: true

      - name: advertised_timestamp
        description: "Advertised timestamp (local timezone) for the start of the content item, as shown on the Coming Up tile."
        quote: true

      - name: tile_planned_start_timestamp
        description: "The planned time for when the content item tile will become available on the platform."
        quote: true

      - name: tile_planned_end_timestamp
        description: "The planned time for when the content item tile will be taken down from the platform."
        quote: true

      - name: outlet
        description: "Name of the region to which the content is being delivered."
        quote: true

      - name: article_type
        description: "Categorisation of the article based on the content format e.g. Short Highlights, Catch Up, Film."
        quote: true

      - name: article_type_id
        description: "Source identifier for the article type."
        quote: true

      - name: article_language_code
        description: "Code corresponding to the local language in which the article is made available."
        quote: true

      - name: article_link_translation_language_code
        description: "Code corresponding to the translation language of the linked article, if applicable."
        quote: true

      - name: commentary_language_code
        description: "Code of the primary language of the commentary available for the content item."
        quote: true

      - name: article_title_english
        description: "The title of the article in English."
        quote: true

      - name: article_title_local
        description: "The title of the article in the local language, corresponding to the 'article_langage_code' column value."
        quote: true

      - name: article_quality
        description: "Indicates the production and broadcast levels of article."
        quote: true

      - name: article_promotion
        description: "Specifies the type of promotion applied to the article, if any e.g. Editor's Picks, Standout."
        quote: true

      - name: fixture_id
        description: "Source identifier for the fixture associated with the content item."
        quote: true

      - name: fixture_name
        description: "The name or description of the fixture associated with the content item."
        quote: true

      - name: fixture_start_timestamp
        description: "Timestamp indicating the when the fixture began."
        quote: true

      - name: venue_short_name
        description: "Short name of the venue where the fixture took place."
        quote: true

      - name: venue_long_name
        description: "Long name of the venue where the fixture took place."
        quote: true

      - name: venue_country
        description: "Name of the country where the fixture venue is located."
        quote: true

      - name: competition_id
        description: "Source identifier for the competition associated with the fixture."
        quote: true

      - name: competition_name
        description: "Name of the competition associated with the fixture."
        quote: true

      - name: competition_country
        description: "Name of the country where the competition is located."
        quote: true

      - name: tournament_calendar_name
        description: "Name of tournament calendar associated with the competition, which specifies the season."
        quote: true

      - name: tournament_calendar_start_date
        description: "Date indicating when the tournament calendar begins."
        quote: true

      - name: tournament_calendar_end_date
        description: "Date indicating when the tournament calendar ends."
        quote: true

      - name: stage_name
        description: "Name of the stage associated with the fixture."
        quote: true

      - name: stage_start_date
        description: "Date indicating when the stage begins."
        quote: true

      - name: stage_end_date
        description: "Date indicating when the stage ends."
        quote: true

      - name: sport_id__hash
        description: "Hash of the sport_id column value."
        quote: true

      - name: sport_id
        description: "Source identifier for the sport corresponding to the fixture."
        quote: true

      - name: sport_name
        description: "Name of the sport corresponding to the fixture."
        quote: true

      - name: ruleset_name
        description: "Name of the ruleset corresponding to the sport associated with the fixture."
        quote: true

      - name: home_contestant_id
        description: "Source identifier for the home contestant participating in the fixture."
        quote: true

      - name: home_contestant_name
        description: "Name of the home contestant participating in the fixture."
        quote: true

      - name: home_contestant_country
        description: "Name of the home contestant's country."
        quote: true

      - name: away_contestant_id
        description: "Source identifier for the guest contestant participating in the fixture."
        quote: true

      - name: away_contestant_name
        description: "Name of the guest contestant participating in the fixture."
        quote: true

      - name: away_contestant_country
        description: "Name of the guest contestant's country."
        quote: true

      - name: livestream_id
        description: "Source identifier for the livestream via which the content was delivered, if applicable."
        quote: true

      - name: shoulder_content_or_live
        description: "Indicates whether the content item is made available as shoulder content or live."
        quote: true

      - name: is_embargoed
        description: "Indicates whether the content item can only be made available as preview or catch-up and not as Live."
        quote: true

      - name: is_age_restricted
        description: "Indicates whether an age restriction rating is associated with the content item."
        quote: true

      - name: is_exclusive
        description: "Indicates whether the content item is exclusive to a region."
        quote: true

      - name: is_allowed_dci
        description: "Indicates whether the content item may be subject ads insertion."
        quote: true

      - name: is_allowed_download
        description: "Indicates whether the content item may be offered as downloadable."
        quote: true

      - name: is_allowed_free_to_view
        description: "Indicates whether the content item can be offered as free to view."
        quote: true

      - name: is_linear_channel
        description: "Indicates whether the content was delivered via linear channel."
        quote: true

      - name: is_short_highlights
        description: "Indicates whether the content item is available as short highlights."
        quote: true

      - name: has_tile_text
        description: "Indicates whether the article tile contains text."
        quote: true

      - name: has_special_event_rail
        description: "Indicates whether the article is featured on a special event rail."
        quote: true

      - name: transmission_status
        description: "The status of the content transmission e.g. Scheduled release."
        quote: true

      - name: voiceover_booth_key
        description: "Key corresponding to the voice-over booth resources required for the event."
        quote: true

      - name: gallery_resource_name
        description: "Name of the gallery resource required for the event, where applicable."
        quote: true

      - name: broadcast_tier
        description: "The broadcast tier set against the content item to indicate the level of production applied by DAZN."
        quote: true

      - name: support_tier
        description: "Value indicative of the priority assigned to the content item by technical support."
        quote: true

      - name: alternative_workflow
        description: "Indicates a special scenario that is associated with the content item."
        quote: true

      - name: advertising_asset_label
        description: "The label of the advertising asset that is played out during the content item."
        quote: true

      - name: rightsholder_name
        description: "Name of the content item rights holder."
        quote: true

      - name: contract_name
        description: "Name of the parent contract with a given rights holder."
        quote: true

      - name: contract_start_date
        description: "Global start date of the contract."
        quote: true

      - name: contract_end_date
        description: "Global end date of the contract."
        quote: true

      - name: "tx_event_type"
        description: "TBC"
        quote: true

      - name: "info_freemium"
        description: "Indicates whether it's Freemium or Freemium - France Excluded"
        quote: true

      - name: "is_ppv"
        description: "Indicates whether it's PPV or not."
        quote: true

      - name: "article_market_name"
        description: "article market."
        quote: true

      - name: META__DATA
        description: "Variant column containing JSON structured meta data on the record, such as a timestamp of when the record was inserted"
        quote: true

  - name: MART__CONTENT_ITEM__DIM
    description: "Conformed dimension. Each record represents the latest version of a content item that was made available for consumption / playback on the DAZN platform. There may be multiple records associated with a content item if one or more attributes have changed value over time, but only a single record may represent the current version of a content item."
    columns:
      - name: content_item__skey
        description: "Surrogate key which uniquely identifies a content item."
        quote: true

      - name: record_valid_from_timestamp
        description: "Timestamp indicating when the record (representing a version of the content item) came into effect."
        quote: true

      - name: record_valid_to_timestamp
        description: "Timestamp indicating when the record (representing a version of the content item) expired. This value points to a date in the future if the record is current."
        quote: true

      - name: content_item_origin
        description: "The source of the content item record e.g. PCMS, backfill."
        quote: true

      - name: content_distinction
        description: "A more granular categorisation for the content type."
        quote: true

      - name: article_id
        description: "Source identifier for the article that corresponds to the content item."
        quote: true

      - name: won_external_id
        description: "Identifier for the content item within the What's On external system."
        quote: true

      - name: advertised_timestamp
        description: "Advertised timestamp (local timezone) for the start of the content item, as shown on the Coming Up tile."
        quote: true

      - name: tile_planned_start_timestamp
        description: "The planned time for when the content item tile will become available on the platform."
        quote: true

      - name: tile_planned_end_timestamp
        description: "The planned time for when the content item tile will be taken down from the platform."
        quote: true

      - name: outlet
        description: "Name of the region to which the content is being delivered."
        quote: true

      - name: article_type
        description: "Categorisation of the article based on the content format e.g. Short Highlights, Catch Up, Film."
        quote: true

      - name: article_type_id
        description: "Source identifier for the article type."
        quote: true

      - name: article_language_code
        description: "Code corresponding to the local language in which the article is made available."
        quote: true

      - name: article_link_translation_language_code
        description: "Code corresponding to the translation language of the linked article, if applicable."
        quote: true

      - name: commentary_language_code
        description: "Code of the primary language of the commentary available for the content item."
        quote: true

      - name: article_title_english
        description: "The title of the article in English."
        quote: true

      - name: article_title_local
        description: "The title of the article in the local language, corresponding to the 'article_langage_code' column value."
        quote: true

      - name: article_quality
        description: "Indicates the production and broadcast levels of article."
        quote: true

      - name: article_promotion
        description: "Specifies the type of promotion applied to the article, if any e.g. Editor's Picks, Standout."
        quote: true

      - name: fixture_id
        description: "Source identifier for the fixture associated with the content item."
        quote: true

      - name: fixture_name
        description: "The name or description of the fixture associated with the content item."
        quote: true

      - name: fixture_start_timestamp
        description: "Timestamp indicating the when the fixture began."
        quote: true

      - name: venue_short_name
        description: "Short name of the venue where the fixture took place."
        quote: true

      - name: venue_long_name
        description: "Long name of the venue where the fixture took place."
        quote: true

      - name: venue_country
        description: "Name of the country where the fixture venue is located."
        quote: true

      - name: competition_id
        description: "Source identifier for the competition associated with the fixture."
        quote: true

      - name: competition_name
        description: "Name of the competition associated with the fixture."
        quote: true

      - name: competition_country
        description: "Name of the country where the competition is located."
        quote: true

      - name: tournament_calendar_name
        description: "Name of tournament calendar associated with the competition, which specifies the season."
        quote: true

      - name: tournament_calendar_start_date
        description: "Date indicating when the tournament calendar begins."
        quote: true

      - name: tournament_calendar_end_date
        description: "Date indicating when the tournament calendar ends."
        quote: true

      - name: stage_name
        description: "Name of the stage associated with the fixture."
        quote: true

      - name: stage_start_date
        description: "Date indicating when the stage begins."
        quote: true

      - name: stage_end_date
        description: "Date indicating when the stage ends."
        quote: true

      - name: sport_id__hash
        description: "Hash of the sport_id column value."
        quote: true

      - name: sport_id
        description: "Source identifier for the sport corresponding to the fixture."
        quote: true

      - name: sport_name
        description: "Name of the sport corresponding to the fixture."
        quote: true

      - name: ruleset_name
        description: "Name of the ruleset corresponding to the sport associated with the fixture."
        quote: true

      - name: home_contestant_id
        description: "Source identifier for the home contestant participating in the fixture."
        quote: true

      - name: home_contestant_name
        description: "Name of the home contestant participating in the fixture."
        quote: true

      - name: home_contestant_country
        description: "Name of the home contestant's country."
        quote: true

      - name: away_contestant_id
        description: "Source identifier for the guest contestant participating in the fixture."
        quote: true

      - name: away_contestant_name
        description: "Name of the guest contestant participating in the fixture."
        quote: true

      - name: away_contestant_country
        description: "Name of the guest contestant's country."
        quote: true

      - name: livestream_id
        description: "Source identifier for the livestream via which the content was delivered, if applicable."
        quote: true

      - name: shoulder_content_or_live
        description: "Indicates whether the content item is made available as shoulder content or live."
        quote: true

      - name: is_embargoed
        description: "Indicates whether the content item can only be made available as preview or catch-up and not as Live."
        quote: true

      - name: is_age_restricted
        description: "Indicates whether an age restriction rating is associated with the content item."
        quote: true

      - name: is_exclusive
        description: "Indicates whether the content item is exclusive to a region."
        quote: true

      - name: is_allowed_dci
        description: "Indicates whether the content item may be subject ads insertion."
        quote: true

      - name: is_allowed_b2b
        description: "Indicates whether the content item may be offered as part of DAZN's B2B product."
        quote: true

      - name: is_allowed_download
        description: "Indicates whether the content item may be offered as downloadable."
        quote: true

      - name: is_allowed_free_to_view
        description: "Indicates whether the content item can be offered as free to view."
        quote: true

      - name: is_linear_channel
        description: "Indicates whether the content was delivered via linear channel."
        quote: true

      - name: is_short_highlights
        description: "Indicates whether the content item is available as short highlights."
        quote: true

      - name: has_tile_text
        description: "Indicates whether the article tile contains text."
        quote: true

      - name: has_special_event_rail
        description: "Indicates whether the article is featured on a special event rail."
        quote: true

      - name: transmission_status
        description: "The status of the content transmission e.g. Scheduled release."
        quote: true

      - name: voiceover_booth_key
        description: "Key corresponding to the voice-over booth resources required for the event."
        quote: true

      - name: gallery_resource_name
        description: "Name of the gallery resource required for the event, where applicable."
        quote: true

      - name: broadcast_tier
        description: "The broadcast tier set against the content item to indicate the level of production applied by DAZN."
        quote: true

      - name: support_tier
        description: "Value indicative of the priority assigned to the content item by technical support."
        quote: true

      - name: alternative_workflow
        description: "Indicates a special scenario that is associated with the content item."
        quote: true

      - name: advertising_asset_label
        description: "The label of the advertising asset that is played out during the content item."
        quote: true

      - name: rightsholder_name
        description: "Name of the content item rights holder."
        quote: true

      - name: contract_name
        description: "Name of the parent contract with a given rights holder."
        quote: true

      - name: contract_start_date
        description: "Global start date of the contract."
        quote: true

      - name: contract_end_date
        description: "Global end date of the contract."
        quote: true

      - name: "tx_event_type"
        description: "TBC"
        quote: true

      - name: "info_freemium"
        description: "Indicates whether it's Freemium or Freemium - France Excluded"
        quote: true

      - name: "is_ppv"
        description: "Indicates whether it's PPV or not."
        quote: true

      - name: "article_market_name"
        description: "article market."
        quote: true

      - name: META__DATA
        description: "Variant column containing JSON structured meta data on the record, such as a timestamp of when the record was inserted"
        quote: true

  - name: MART__PLAYBACK_STREAM__FACT
    description: "Fact table. Each record corresponds to a playback stream and provides quantitative information as well as references to related dimensions."
    columns:
      - name: playback_stream__skey
        description: "Surrogate key which uniquely identifies a table row."
        quote: true

      - name: playback_stream_details__skey
        description: "Foreign key, references primary (surrogate) key values of the Playback Stream Origin dimension."
        quote: true

      - name: playback_stream_location__skey
        description: "Foreign key, references primary (surrogate) key values of the Playback Stream Location dimension."
        quote: true

      - name: playback_stream_issue__skey
        description: "Foreign key, references primary (surrogate) key values of the Playback Stream Issue dimension."
        quote: true

      - name: device_info__skey
        description: "Foreign key, references primary (surrogate) key values of the Device Info dimension."
        quote: true

      - name: application__skey
        description: "Foreign key, references primary (surrogate) key values of the Application dimension."
        quote: true

      - name: content_item__skey
        description: "Foreign key, references primary (surrogate) key values of the Content Item dimension."
        quote: true

      - name: entitlements__skey
        description: "Foreign key, references primary (surrogate) key values of the Entitlements dimension."
        quote: true

      - name: customer_identity__skey
        description: "Foreign key, references primary (surrogate) key values of the Customer Identity dimension."
        quote: true

      - name: playback_stream_country__hash
        description: "Hashed value of the name of the country country corresponding to the IP address from which the stream originates."
        quote: true

      - name: sport_id__hash
        description: "Hashed value of the name of the sport that corresponds to the content item being delivered via the playback stream."
        quote: true

      - name: playback_stream_date
        description: "The date when the playback stream was initiated."
        quote: true

      - name: playback_start_timestamp
        description: "Timestamp indicating when the playback stream started."
        quote: true

      - name: playback_end_timestamp
        description: "Timestamp indicating when the playback stream ended."
        quote: true

      - name: playback_duration_milliseconds
        description: "The amount of time that content was played during a stream, measured in milliseconds. Excludes buffering time."
        quote: true

      - name: startup_duration_milliseconds
        description: "The amount of time taken for the video to display at startup, measured in milliseconds."
        quote: true

      - name: buffering_duration_milliseconds
        description: "The amount of time in a stream spent buffering not related to customer interaction e.g. resuming or seeking to a point in time, measured in milliseconds."
        quote: true

      - name: video_restart_duration_milliseconds
        description: "The amount of time spent restarting video playback during a stream, measured in milliseconds."
        quote: true

      - name: total_connection_induced_rebuffering_duration_milliseconds
        description: "Total amount of time in a stream spent in connection induced re-buffering, measured in milliseconds. Includes re-buffering time due to seek and video start."
        quote: true

      - name: total_streaming_duration_milliseconds
        description: "The total duration of the stream, including playback and buffering, measured in milliseconds."
        quote: true

      - name: average_bitrate
        description: "Video bit rate averaged over the total duration of the stream."
        quote: true

      - name: interrupts_count
        description: "The number of times playback was interrupted for re-buffering. This excludes instances when playback is paused and resumed by the customer, if no buffering occurs as a result."
        quote: true

      - name: rejoins_count
        description: "The number of times a customer rejoined the stream."
        quote: true

      - name: playback_entitlement_set_id
        description: "Source identifier of the entitlement set associated with the content played during the stream."
        quote: true

      - name: dazn_device_id
        description: "Internal identifier for client devices used to stream content."
        quote: true

      - name: conviva_session_id
        description: "Source-defined identifier (Conviva) for the playback session associated with the stream."
        quote: true

      - name: user_agent
        description: "The user agent string - contains information about the software and hardware running on the device that is accessing the playback stream."
        quote: true

      - name: META__DATA
        description: "Variant column containing JSON structured meta data on the record, such as a timestamp of when the record was inserted"
        quote: true

      - name: viewer_id
        description: "Viewer ID from conviva, this can be used to join on customer history scd"
        quote: true

      - name: dazn_session_id
        description: "Unique identifier for a user session. This will be a key that can be used to correlate Conviva Data with events from Google Analytics"
        quote: true

      - name: ip_address_hash
        description: "Hashed value of the stream ip address."
        quote: true

      - name: user_status__skey
        description: "The User Status Dim SKEY for the user/viewer of the stream at the end of the day"
        quote: true

      - name: dazn_subscription_name
        description: "The DAZN Subscription Name (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: dazn_billing_account__skey
        description: "The DAZN Billing Account Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: dazn_subscription_daily_status__skey
        description: "The DAZN Subscriptoin Daily Status Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: dazn_subscription_info__skey
        description: "The DAZN Subscription Info Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: dazn_subscription_term__skey
        description: "The DAZN Subscription term Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: dazn_subscription_charge__skey
        description: "The DAZN Subscription Charge Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: dazn_subscription_source_system_name_derived__skey
        description: "The DAZN Subscription Source Name Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: dazn_subscription_tracking_id__skey
        description: "The DAZN Subscription Tracking Id Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: dazn_subscription_giftcode_campaign_name__skey
        description: "The DAZN Subscription Giftcode Campaign Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: last_dazn_subscription_name
        description: "The DAZN Subscription Name of the last active Subscription for the user/viewer of the stream at the end of the day"
        quote: true

      - name: user_last_dazn_subscription__skey
        description: "The DAZN Last Subscription Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: nfl_subscription_name
        description: "The NFL Subscription Name (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: nfl_billing_account__skey
        description: "The NFL Billing Account Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: nfl_subscription_daily_status__skey
        description: "The NFL Subscriptoin Daily Status Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: nfl_subscription_info__skey
        description: "The NFL Subscription Info Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: nfl_subscription_term__skey
        description: "The NFL Subscription term Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: nfl_subscription_charge__skey
        description: "The NFL Subscription Charge Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: nfl_subscription_source_system_name_derived__skey
        description: "The NFL Subscription Source Name Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: nfl_subscription_tracking_id__skey
        description: "The NFL Subscription Tracking Id Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: nfl_subscription_giftcode_campaign_name__skey
        description: "The NFL Subscription Giftcode Campaign Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: last_nfl_subscription_name
        description: "The NFL Subscription Name of the last active Subscription for the user/viewer of the stream at the end of the day"
        quote: true

      - name: user_last_nfl_subscription__skey
        description: "The NFL Last Subscription Dim SKEY (if any) for the user/viewer of the stream at the end of the day"
        quote: true

      - name: is_free_to_view
        description: "Flag column which will tell whether the stream is allowed to watch free or not"
        quote: true

      - name: content_length
        description: "Total length of the content in milliseconds"
        quote: true

      # - name: content_gmr__skey
      #   description: "skey to join the models gmr. "
      #   quote: true

      # - name: content_fixture__skey
      #   description: "skey to join the models mfl. "
      #   quote: true

      # - name: content_won__skey
      #   description: "skey to join the models won(Live & VOD). "
      #   quote: true

  - name: DOMAIN__ENTITLEMENTS__DIM
    description: "Conformed dimension. Each record represents the Entitlements information for an article id, this includes the available, required and used entitlement set ids"
    columns:
      - name: META__DATA
        description: "Surrogate key which uniquely identifies a table row."
        quote: true

      - name: entitlements__skey
        description: "Surrogate key which uniquely identifies a table row."
        quote: true

      - name: article_id
        description: "Source identifier for the article that corresponds to the content item."
        quote: true

      - name: available_entitlement_set_ids
        description: "The available entitlement set ids for a particular viewer"
        quote: true

      - name: required_entitlement_set_ids
        description: "The required entitlement set ids to view this content"
        quote: true

      - name: used_entitlement_set_ids
        description: "The intersection of available and the required entitlement set ids"
        quote: true

      - name: entitlement_ids
        description: "The entitlement set ids for this content from the article entitlement scd"
        quote: true

  - name: MART__ENTITLEMENTS__DIM
    description: "Conformed dimension. Each record represents the Entitlements information for an article id, this includes the available, required and used entitlement set ids"
    columns:
      - name: META__DATA
        description: "Surrogate key which uniquely identifies a table row."
        quote: true

      - name: entitlements__skey
        description: "Surrogate key which uniquely identifies a table row."
        quote: true

      - name: article_id
        description: "Source identifier for the article that corresponds to the content item."
        quote: true

      - name: available_entitlement_set_ids
        description: "The available entitlement set ids for a particular viewer"
        quote: true

      - name: required_entitlement_set_ids
        description: "The required entitlement set ids to view this content"
        quote: true

      - name: used_entitlement_set_ids
        description: "The intersection of available and the required entitlement set ids"
        quote: true

      - name: entitlement_ids
        description: "The entitlement set ids for this content from the article entitlement scd"
        quote: true

  - name: MART__CUSTOMER_IDENTITY__DIM
    description: "This is a dimension table that contains info about different customer IDs from Zuora and Salesforce datasource for current dim"
    columns:
      - name: customer_identity__skey
        description: "Foreign key, references primary (surrogate) key values of the Customer Identity dimension."
        quote: true

      - name: billing_account_id
        description: "Zuora Billing Account ID"
        quote: true

      - name: crm_account_id
        description: "Unique ID of the record (also known as Salesforce Account ID)"
        quote: true

      - name: dazn_user_id
        description: "Unique ID of the DAZN User"
        quote: true

      - name: viewer_id
        description: "Viewer ID value copied from User table (combines EXT and non EXT values, whichever is actually used)"
        quote: true

      - name: partner_id
        description: "This is the partner ID. Used for Docomo"
        quote: true

      - name: effective_from
        description: "Zuora account entry created date"
        quote: true

  - name: MART__PLAYBACK_STREAM_DETAILS__DIM
    description: "This is a dimension table that contains info about the playback session"
    columns:
      - name: META__DATA
        description: "Meta Data such as Insert Timestamp"
        quote: true

      - name: playback_stream_details__skey
        description: "Foreign key, references primary (surrogate) key values of the Playback Stream Details dimension."
        quote: true

      - name: origin_server
        description: "Origin Server of the stream e.g. DCA, DCB"
        quote: true

      - name: content_delivery_network_name
        description: "CDN name for the stream e.g. LEVEL 3"
        quote: true

      - name: connection_type
        description: "Users internet connection type e.g. WiFi, 4G"
        quote: true

      - name: ip_address_type
        description: "IP Address Version e.g. IPV4, IPV6"
        quote: true

      - name: digital_rights_management_type
        description: "DRM Type"
        quote: true

      - name: playback_trigger_type
        description: "Playback Trigger Type e.g. Automatic or Manual"
        quote: true

      - name: is_genuine_attempt
        description: "Flag indicating if the playback was a genuine attempt, i.e was a succesful play, or an EBVS or a playback failure"
        quote: true

      - name: has_exit_before_video_start
        description: "Flag indicating stream was exited before video start"
        quote: true

      - name: is_multiview
        description: "Flag indicating a multiview stream"
        quote: true

      - name: is_engaged_play
        description: "Flag indicating an engaged play i.e. a manually started play or any play lasting >2 minutes"
        quote: true

      - name: is_hd
        description: "Flag indicating a stream was in High Definition"
        quote: true

      - name: is_sd
        description: "Flag indicating a stream was in Standard Definition"
        quote: true

      - name: is_dai_session
        description: "Flag indicating a Dynamic Ad Insertion enabled session"
        quote: true

      - name: commentary_language
        description: "The language of the commentary"
        quote: true

  - name: MART__PLAYBACK_STREAM_ISSUE__DIM
    description: "This is a dimension table that contains info about the playback session"
    columns:
      - name: META__DATA
        description: "Meta Data such as Insert Timestamp"
        quote: true

      - name: playback_stream_issue__skey
        description: "Foreign key, references primary (surrogate) key values of the Playback Stream Issue dimension."
        quote: true

      - name: is_startup_error
        description: "Flag indicating a startup error has occured"
        quote: true

      - name: startup_error_type
        description: "Type of startup error e.g. Play Unsuccessful"
        quote: true

      - name: startup_error_list
        description: "Specific Error(s) encountered in startup"
        quote: true

      - name: is_video_playback_failure_error
        description: "Flag indicating a playback error has occured"
        quote: true

      - name: video_playback_failure_error_list
        description: "Specific Error(s) encountered in playback"
        quote: true

  - name: plays_mart__user_status__dim
    description: "Dimension table surfacing information on user status fields, like country_code and nfl_user status"
    columns:
      - name: user_status__skey
        description: "The surrogate key (skey) used to join on the user_status__dim"
        quote: true

      - name: user_account_created_week
        description: "The minimum week_end_date we see the users being created in our systems, derived from Salesforce before migration to Segment"
        quote: true

      - name: home_country_code
        description: "The two-letter Country Code of the user. E.g. GB, IT, DE, ..."
        quote: true

      - name: product_status_dazn
        description: "The product status for the user on the DAZN platform"
        quote: true

      - name: product_status_nfl
        description: "The product status for the user on the NFL platform"
        quote: true

      - name: is_validated_dazn_email
        description: "Flag for if the user has verified their email on the DAZN platform"
        quote: true

      - name: is_nfl_authenticated_email
        description: "Flag for if the user has authenticated their email on the NFL platform"
        quote: true

      - name: nfl_user
        description: "The type of NFL user (E.g. New/Existing), NULL if Existing"
        quote: true

      - name: source_type
        description: "The source type of the user (E.g. NFL)"
        quote: true

  - name: plays_mart__user_last_dazn_subscription__dim
    description: "Dimension table surfacing information on users last DAZN subscription"
    columns:
      - name: user_last_dazn_subscription__skey
        description: "The surrogate key (skey) used to join on the *user_last_dazn_subscription__dim"
        quote: true

      - name: last_dazn_subscription_end_week
        description: "The Subscription End Week of the last DAZN Subscription to be active before the batch_date"
        quote: true

      - name: last_dazn_subscription_type
        description: "The Subscription Type of the last DAZN Subscription to be active before the batch_date"
        quote: true

      - name: last_dazn_subscription_tier
        description: "The Subscription Tier of the last DAZN Subscription to be active before the batch_date"
        quote: true

  - name: plays_mart__user_last_nfl_subscription__dim
    description: "Dimension table surfacing information on users last NFL subscription"
    columns:
      - name: user_last_nfl_subscription__skey
        description: "The surrogate key (skey) used to join on the *user_last_nfl_subscription__dim"
        quote: true

      - name: last_nfl_subscription_end_week
        description: "The Subscription End Week of the last NFL Subscription to be active before the batch_date"
        quote: true

      - name: last_nfl_subscription_type
        description: "The Subscription Type of the last NFL Subscription to be active before the batch_date"
        quote: true

      - name: last_nfl_subscription_tier
        description: "The Subscription Tier of the last NFL Subscription to be active before the batch_date"
        quote: true

  - name: plays_mart__billing_account__dim
    description: "Dimension table surfacing information on billing account ex: country, territory.."
    columns:
      - name: billing_account__skey
        description: "The surrogate key (skey) used to join on the billing_account__dim"
        quote: true

      - name: subscription_country
        description: "The country of the subscription, originating from the Zuora contact. E.g. Spain, Germany, Japan, United States, United Kingdom, ..."
        quote: true

      - name: subscription_territory
        description: "The territory of the subscription, pulled in by a join to region_dimension. E.g. Spain, DACH, Japan, United States, UK and Ireland, ..."
        quote: true

      - name: billing_account_currency_code
        description: "The currency code for the Zuora Account, E.g. EUR, USD, ..."
        quote: true

      - name: billing_account_is_batch_50
        description: "Boolean flag, true if the account has ever been on Batch50, which implies it is a test account"
        quote: true

      - name: min_billing_account_subscription_name_created_week
        description: "The week of the very first date that the subscription was first created"
        quote: true

      - name: billing_account_has_advanced_payment_manager
        description: "A flag for if subscriptions on the same account have different payment methods"
        quote: true

      - name: user_account_created_week
        description: "The minimum week_end_date we see the users being created in our systems, derived from Salesforce before migration to Segment"
        quote: true

      - name: country__skey
        description: "The skey for the COUNTRY__DIM that comes directly from the seed of region_dimension for country-territory mappings, this skey is actually just the exact value of the subscription_country"
        quote: true

  - name: plays_mart__subscription_charge__dim
    description: "Dimension table surfacing information on the subscription charge like term type, billing period..."
    columns:
      - name: subscription_charge__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_charge__dim"
        quote: true

      - name: subscription_type
        description: "A derived field from the term type and the billing period, or external giftcodes, to categorise the subscription type, can be Monthly, Annual, Instalment or Externally Billed"
        quote: true

      - name: subscription_monthly_recurring_revenue
        description: "The monthly recurring revenue, which is the amount the customer pays for their subscription ignoring Discounts and PPV, adjusting the subscription length down to one month and rounded to two decimal places"
        quote: true

      - name: next_subscription_monthly_recurring_revenue
        description: "The next effective monthly recurring revenue, that could be different from the current monthly recurring revenue and will be effective in the future"
        quote: true

      - name: subscription_bill_cycle_day
        description: "The billing cycle day of the month that the customer is charged"
        quote: true

      - name: subscription_monthly_recurring_revenue_daily_rank
        description: "The rank of the MRR, high to low, on the current batch_date across the dimensions of territory, currency, sub_type and tier"
        quote: true

  - name: plays_mart__subscription_daily_status__dim
    description: "Dimension table surfacing information on the subscription daily status like pause, addon, discount.."
    columns:
      - name: subscription_daily_status__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_daily_status__dim"
        quote: true

      - name: is_paused
        description: "Boolean flag, true if the subscription is paused on the exact date_day of the spine"
        quote: true

      - name: has_active_post_sign_up_discount
        description: "Boolean flag, true if the subscription has an active post-sign-up discount on the exact date_day of the spine"
        quote: true

      - name: has_active_free_trial
        description: "Boolean flag, true if the subscription has an active free trial on the exact date_day of the spine"
        quote: true

      - name: has_active_introductory_discount
        description: "Boolean flag, true if the subscription has an active introductory discount on the exact date_day of the spine"
        quote: true

      - name: discounted_monthly_recurring_revenue
        description: "The MRR with discounts applied: MRR * active_discount_percentage"
        quote: true

      - name: daily_revenue_estimate
        description: "The discounted MRR brought down to the daily level: Disc MRR / days_in_month"
        quote: true

  - name: plays_mart__subscription_info__dim
    description: "Dimension table surfacing information on the subscription like tier, trip number.."
    columns:
      - name: subscription_info__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_info__dim"
        quote: true

      - name: is_soft_cancelled
        description: "The status of the subscription is cancelled and it won't renew on the current end date"
        quote: true

      - name: subscription_tier
        description: "The Entitlement Set Id of the Rateplan, which is filtered to the associated RatePlanCharge's Flat Fee Pricing Model and Recurring Type, so producing the Tier for the Subscription"
        quote: true

      - name: billing_account_trip_number
        description: "The count of previous subscriptions from this billing account including this subscription for all product groups"
        quote: true

      - name: is_resubscription
        description: "Identifies if this is a resubscription for this product group or not"
        quote: true

      - name: subscription_product_group
        description: "Can be either 'NFL' whereas it is an NFL subscription, or 'DAZN' otherwise."
        quote: true

  - name: plays_mart__subscription_term__dim
    description: "Dimension table surfacing information on subscription term like subscription start week and end week"
    columns:
      - name: subscription_term__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_term__dim"
        quote: true

      - name: subscription_start_week
        description: "The week end date the subscription starts, including any free trial"
        quote: true

      - name: subscription_end_week
        description: "The latest week end date Zuora has seen this subscription end, taking into account retrospective cancellation (when the cancel date is before the subscription is updated)"
        quote: true

  - name: plays_mart__subscription_campaign_mapping__dim
    description: "Dimension used for mapping the subscription campaigns (sign-up and post sign-up) to their partner and relevant details"
    columns:
      - name: subscription_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: subscription_sign_up_campaign_id__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: first_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: last_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: campaign_type
        description: "The type of subscription campaign, E.g. Direct, Indirect, Agents, ..."
        quote: true

      - name: campaign_detailed_type
        description: "The detailed type of subscription campaign, E.g. Direct, Operators, Retail, ..."
        quote: true

      - name: campaign_partner_name
        description: "The partner associated with the subscrpition campaign, E.g. TIM, Orange, Telefonica, ..."
        quote: true

  - name: plays_mart__subscription_source_system_name_mapping__dim
    description: "Dimension used for mapping the subscription source system names to their partner and relevant details"
    columns:
      - name: subscription_source_system_name_derived__skey
        description: "The surrogate key (skey) used to join on the sub_source_system dim"
        quote: true

      - name: partner_type
        description: "The type of partner, E.g. Direct, Indirect, Agents, ..."
        quote: true

      - name: partner_detailed_type
        description: "The detailed type of partner, E.g. Direct, Operators, Retail, ..."
        quote: true

      - name: partner_name
        description: "The cleaned up version of the source system name, that we will call partner, E.g. TIM, Orange, Telefonica, ..."
        quote: true

  - name: plays_mart__subscription_tracking_id_mapping__dim
    description: "Dimension used for mapping the subscription tracking IDs to their partner and relevant details"
    columns:
      - name: subscription_tracking_id__skey
        description: "The surrogate key (skey) used to join on the tracking_id dim"
        quote: true

      - name: tracking_id_type
        description: "The type of subscription tracking ID, E.g. Direct, Indirect, Agents, ..."
        quote: true

      - name: tracking_id_detailed_type
        description: "The detailed type of subscription tracking ID, E.g. Direct, Operators, Retail, ..."
        quote: true

      - name: tracking_id_partner_name
        description: "The partner associated with the subscrpition tracking ID, E.g. TIM, Orange, Telefonica, ..."
        quote: true

      - name: tracking_id_campaign_name
        description: "The campaign name of the subscription Tracking ID"
        quote: true

  - name: plays_mart__country__dim
    description: "Dimension containing a mapping of countries to territories"
    columns:
      - name: country__skey
        description: "The surrogate key (skey) used to join on the country__dim"
        quote: true

      - name: country
        description: "The long form country name for this country mapped from the join_key"
        quote: true

      - name: territory
        description: "The DAZN territory mapping for this corresponding country mapped from the join_key"
        quote: true

      - name: region
        description: "The DAZN region mapping for this corresponding country mapped from the join_key"
        quote: true

      - name: country_code
        description: "The two-letter country code for this country mapped form the join_key"
        quote: true

      - name: join_key
        description: "A field containing both long form country names and short two-letter country codes that you can use to be the only field used to join to other datasest and not cause duplication"
        quote: true

      - name: has_nfl_gpi
        description: "1 or 0 Flag for if this country is a country where NFL GPI is available"
        quote: true
