{{
    config(
        materialized='incremental',
        unique_key='"entitlements__skey"',
        transient=false,
        database='PLAYBACK_STREAM__B2C__DOMAIN__' + snowflake_env(),
        schema='ENTITLEMENTS',
        alias='ENTITLEMENTS__DIM',
        tags=['presentation-playback-stream-mart']
    )
}}

-- flattened list of entitlement ids for each entitlement set at a point in time
WITH required_entitlements AS (
    SELECT
        dim__entitlement_sets_scd."entitlement_set_id"
        ,dim__entitlement_sets_scd."effective_from"
        ,dim__entitlement_sets_scd."effective_until"
        , F.value AS "set_entitlement_ids"
    FROM {{ ref('dim__entitlement_sets_scd') }}
    ,TABLE(FLATTEN({{ ref('dim__entitlement_sets_scd') }}."entitlement_ids")) AS F
)

-- flattened list of entitlement ids for each article id set at a point in time, including article ids with no entitlements, as long as they had one at some point
, articles AS (
    SELECT
        dim__article_entitlement_id_scd."article_id"
        ,dim__article_entitlement_id_scd."effective_from"
        ,dim__article_entitlement_id_scd."effective_until"
        , F.value AS "article_entitlement_ids"
    FROM {{ ref('dim__article_entitlement_id_scd') }}
    ,TABLE(FLATTEN({{ ref('dim__article_entitlement_id_scd') }}."entitlement_ids", OUTER => TRUE)) AS F
)

-- every article id and the available entitlement sets for any user who played the article at the start of the stream
, available_entitlements AS (
    SELECT
        "article_id"
        ,"available_entitlement_set_ids"
        ,"start_time"
    FROM {{ ref('TRANSIENT__PLAYS_2') }}
)

-- join together and group required entitlement set ids into an array, keeping the required article entitlement effective from for the join key
, final AS (
    SELECT
        available_entitlements."article_id"
        ,articles."effective_from" AS "article_required_entitlements_effective_from"
        ,available_entitlements."available_entitlement_set_ids"
        ,ARRAY_AGG(DISTINCT required_entitlements."entitlement_set_id") WITHIN GROUP (ORDER BY required_entitlements."entitlement_set_id") AS "required_entitlement_set_ids"
        ,ARRAY_INTERSECTION(available_entitlements."available_entitlement_set_ids", "required_entitlement_set_ids") AS "used_entitlement_set_ids"
        ,ARRAY_AGG(DISTINCT articles."article_entitlement_ids") WITHIN GROUP (ORDER BY articles."article_entitlement_ids") AS "entitlement_ids"
    FROM available_entitlements
    LEFT JOIN articles
        ON available_entitlements."article_id" = articles."article_id"
        AND available_entitlements."start_time" >= articles."effective_from"
        AND available_entitlements."start_time" < articles."effective_until"
    LEFT JOIN required_entitlements
        ON articles."article_entitlement_ids" = required_entitlements."set_entitlement_ids"
        AND articles."effective_from" >= required_entitlements."effective_from"
        AND articles."effective_until" < required_entitlements."effective_until"
    WHERE available_entitlements."available_entitlement_set_ids" IS NOT NULL
    GROUP BY 1, 2, 3
)

-- add meta data after grouping entitlements
SELECT
    OBJECT_CONSTRUCT('record_inserted_timestamp',CURRENT_TIMESTAMP()) AS "META__DATA"
    ,HASH(
        "article_id"
        ,"available_entitlement_set_ids"
        ,"article_required_entitlements_effective_from"
    ) AS "entitlements__skey"
    ,*
FROM final
{% if is_incremental() %}
WHERE "entitlements__skey" NOT IN (SELECT "entitlements__skey" FROM {{ this }})
{% endif %}
