{{
    config(
        materialized='incremental',
        unique_key='"application__skey"',
        database='PLAYBACK_STREAM__B2C__DOMAIN__' + snowflake_env(),
        schema='OPERATIONS',
        alias='APPLICATION__DIM',
        tags=['presentation-playback-stream-mart']
    )
}}

SELECT DISTINCT
    OBJECT_CONSTRUCT('record_inserted_timestamp',CURRENT_TIMESTAMP()) AS "META__DATA"
    ,HASH(
        "application_type"
        ,"application_category"
        ,"application_version"
        ,"application_major_version"
        ,"application_minor_version"
        ,"application_patch_version"
        ,"native_player_version"
        ,"player_name"
    ) AS "application__skey"
    ,"application_type"
    ,"application_category"
    ,"application_version"
    ,"application_major_version"
    ,"application_minor_version"
    ,"application_patch_version"
    ,"native_player_version"
    ,"player_name"
FROM
    {{ ref('TRANSIENT__PLAYS_2') }}
{% if is_incremental() %}
    WHERE "application__skey" NOT IN (SELECT "application__skey" FROM {{ this }})
{% endif %}
