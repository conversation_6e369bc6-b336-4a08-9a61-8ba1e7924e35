{{
    config(
        materialized='incremental',
        unique_key='"device_info__skey"',
        database='PLAYBACK_STREAM__B2C__DOMAIN__' + snowflake_env(),
        schema='OPERATIONS',
        alias='DEVICE_INFO__DIM',
        tags=['presentation-playback-stream-mart']
    )
}}

SELECT DISTINCT
    OBJECT_CONSTRUCT('record_inserted_timestamp',CURRENT_TIMESTAMP()) AS "META__DATA"
    ,HASH(
        "device_vendor"
        ,"device_model"
        ,"device_marketing_name"
        ,"device_height"
        ,"device_width"
        ,"device_diagonal_screen_size"
        ,"device_release_year"
        ,"device_is_touchscreen"
        ,"device_manufacturer"
        ,"device_category"
        ,"device_hardware_type"
        ,"device_platform"
        ,"device_full_description"
        ,"hd_capable_flag"
        ,"browser_name"
        ,"browser_rendering_engine"
        ,"browser_version"
        ,"browser_major_version"
        ,"browser_minor_version"
        ,"browser_patch_version"
        ,"os_name"
        ,"os_version"
        ,"os_major_version"
        ,"os_minor_version"
        ,"os_patch_version"
    ) AS "device_info__skey"
    ,"device_vendor" AS "device_vendor_name"
    ,"device_model"
    ,"device_marketing_name"
    ,"device_height" AS "device_display_height_pixels"
    ,"device_width" AS "device_display_width_pixels"
    ,"device_diagonal_screen_size" AS "device_diagonal_screen_size_inches"
    ,"device_release_year"
    ,"device_is_touchscreen"
    ,"device_manufacturer" AS "device_manufacturer_name"
    ,"device_category"
    ,"device_hardware_type"
    ,"device_platform"
    ,"device_full_description" AS "device_description"
    ,"hd_capable_flag" AS "device_is_hd_capable"
    ,"browser_name"
    ,"browser_rendering_engine"
    ,"browser_version"
    ,"browser_major_version"
    ,"browser_minor_version"
    ,"browser_patch_version"
    ,"os_name"
    ,"os_version"
    ,"os_major_version"
    ,"os_minor_version"
    ,"os_patch_version"
FROM
    {{ ref('TRANSIENT__PLAYS_2') }}
{% if is_incremental() %}
    WHERE "device_info__skey" NOT IN (SELECT "device_info__skey" FROM {{ this }})
{% endif %}
