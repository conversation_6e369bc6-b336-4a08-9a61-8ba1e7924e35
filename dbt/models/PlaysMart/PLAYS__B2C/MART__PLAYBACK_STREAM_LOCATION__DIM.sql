
{{
    config(
        materialized='view',
        transient=true,
        database='PLAYBACK_STREAM__B2C__MART__' + snowflake_env(),
        schema='GLOBAL',
        alias='PLAYBACK_STREAM_LOCATION__DIM',
        tags=['presentation-playback-stream-mart']
    )
}}

SELECT
    "META__DATA"
    ,"geo__skey" AS "playback_stream_location__skey"
    ,"country__hash" AS "playback_stream_country__hash"
    ,"country" AS "playback_stream_country"
    ,"state" AS "playback_stream_state"
    ,"city" AS "playback_stream_city"
    ,"territory" AS "playback_stream_territory"
    ,"region" AS "playback_stream_region"
FROM
    {{ ref('DOMAIN__GEO__DIM') }}
