version: 2

sources:
  - name: CURRENT_TABLE
    database: CURATED__B2C__CONVIVAEXPERIENCESSD__{{ env_var('PRD_SNOWFLAKE_DATABASE') | replace('PRD_', '') }}
    description: "Provides session-level information for every video play or attempted play in a given day."
    tables:
      - name: SESSION_SOURCE_DATA__CURRENT
        description: "Provides session-level information for every video play or attempted play in a given day."
        columns:
          - name: META_UUID
            description: "Unique Id, this is used to do the upserts"
            quote: true
          - name: META_DBT_INSERT_DTTS
            description: "The insert time by dbt"
            quote: true
          - name: META_BATCH_DATE
            description: "The LAST open batch date if an update occurs otherwise this is the batch date"
            quote: true
          - name: META_SOFT_DELETED
            description: "Used to show if the record has been deleted from source"
            quote: true
          - name: ViewerId
            description: "A DAZN specific identifier of a user, coming from the player upon establishing a streaming connection. It matches the viewer ID populated across various other systems. Value is set by DAZN."
            quote: true
          - name: Asset
            description: "Concatenated Asset ID and Asset Name of the content played. Value is set by DAZN."
            quote: true
          - name: DeviceOs
            description: "Device operating system, collected automatically."
            quote: true
          - name: Country
            description: "Name of the country where the connection for the video streaming came from, based on IP address."
            quote: true
          - name: State
            description: "Name of the state or a region where the connection for the video streaming came from, based on IP address."
            quote: true
          - name: City
            description: "Name of the city or a town where the connection for the video streaming came from, based on IP address."
            quote: true
          - name: Isp
            description: "Name of the Internet Service Provider (ISP)."
            quote: true
          - name: StartTime_unixtime
            description: "The time when Conviva received the first heartbeat for the ad session. The format is Unix epoch time in seconds."
            quote: true
          - name: StartUpTime_ms
            description: "The amount of time taken for the customer stream to start, represented in milliseconds. Measured from Content selection to first image (Exception is Web 2.0 and Mobile where architectural limitations mean it is measured from time Manifest is received to first picture). Number of seconds between the start of the Conviva monitoring and the first played video frame. If there is a pre-roll ad, the ad time is not counted as part of the startup time. Negative values are used as status codes, where -1 indicates an unsuccessful play (no startup time) and -3 indicates the session connected but the client didn't send us the necessary information to determine when the video began playing."
            quote: true
          - name: PlayingTime_ms
            description: "The amount of time a player is actively displaying video content during a session, represented in milliseconds. Value does not include rebuffering time."
            quote: true
          - name: BufferingTime_ms
            description: "The amount of rebuffering time during the session, represented in milliseconds. Value does not include the initial buffering at startup. "
            quote: true
          - name: Interrupts
            description: "The number of times the session was interrupted for rebuffering. If a viewer paused and resumed a session then that is not counted as an interrupt, but if that action caused any buffering then it will be counted as an interrupt."
            quote: true
          - name: AverageBitrate_kbps
            description: "Average bitrate at which content was delivered during the session. The ability to determine bitrate depends on the player integration. Not all players are capable of delivering bitrate information (e.g. Apple TV, LG Web OS, Panasonic TV etc.)."
            quote: true
          - name: StartUpError
            description: "Also known as VSF (video startup failure). If value is 0 then video played and there was no startup error. If value is 1 then video failed to play and there was a startup error (see “Error list”). This value is transformed into a True/False boolean on ingestion into Data Warehouse."
            quote: true
          - name: IpAddress
            description: "The viewer's video playing device public IP address. For European customers, due to legal/privacy reasons, the IP address is not shown."
            quote: true
          - name: Cdn
            description: "A string identifier or name for the Content Delivery Network (CDN) during the session."
            quote: true
          - name: Browser
            description: "Name of the browser used by the viewer's device. If no browser is involved in the streaming, such as with a mobile app or connected TV, the value will be \"Non-Browser Apps.\""
            quote: true
          - name: ConvivaSessionId
            description: "Unique Conviva session identifier for the video session in which this ad is attempting to play. The format is five integer numbers separated by colons (:). Client ID is part of the Conviva session id - the first 4 blocks of the numbers separated by : represents client id."
            quote: true
          - name: StreamUrl
            description: "The last manifest URL used in session. "
            quote: true
          - name: ErrorList
            description: "A list of fatal errors that occurred at stream start for this session, separated by \"&\". A session with Startup Time value of -1 and Playing Time = 0 and no error list, corresponds to an Exit Before Video Start (EBVS) which is a type of playback failure wherein Conviva does not receive a specific error message."
            quote: true
          - name: PercentageComplete
            description: "The percentage of video content that the viewer watched during the session. This value is calculated by dividing the total playing time for the session by the total content length. Values range from -1 to 100, where -1 means that no valid percentage complete could be obtained for the session. This value is not available for live sessions (because in live scenarios the content length is not known by the video player), or if the content length isn't reported by the device. If a viewer spends more time watching the content than its length (generally due to rewinding and re-watching), for example spending 65 minutes on a 60-minute asset, the percent complete will be capped at 100."
            quote: true
          - name: ConnectionInducedReBufferingTime_ms
            description: "The total amount of time spent by the user in connection induced re-buffering, represented in milliseconds. Value does not include rebuffering time that is not due to seek or video start."
            quote: true
          - name: VideoRestartTime_ms
            description: "Video Restart Time (VRT) is the average video restart time for a user, represented in milliseconds. This is on average how long it took for the user stream to restart after every interruption."
            quote: true
          - name: ReJoinedCount
            description: "The count of number of times video rejoined after user seeked."
            quote: true
          - name: VPF
            description: "Video Playback Failure (VPF) occurs when a fatal error causes a video playback to fail. The field is a binary value and is set to 1 if the session started successfully but ended with a fatal error."
            quote: true
          - name: VPFErrorList
            description: "Video Playback Failure Error List is the list of errors (including custom errors) that caused the playback to fail."
            quote: true
          - name: ContentLength_ms
            description: "The length of the asset represented in milliseconds. Only applicable for VOD. For LIVE, the content length is not known, therefore the value is set to -1."
            quote: true
          - name: EndedStatus
            description: "The Status is an integer (0-5) showing the status of the session for that SSD: 0 = Not Ended; at the SSD issue time, the session is still active. 1 = Gracefully ended; the session ended with a session ended event. 2 = Expired due to lack of heartbeat update; we received no heartbeat update for 2 minutes. 3 = Expired due to long buffering; the session's lifetime buffering is longer than 30 minutes. 4 = Ended due to long pause; the session paused for a continuous period longer than 10 minutes. 5 = Ended due to continuous buffering; session is in buffering state for longer than 4 continuous minutes."
            quote: true
          - name: EndTime_unixtime
            description: "The time the session ended, indicated by the last hearbeat update from the session."
            quote: true
          - name: C3DeviceUA
            description: "Device User Agent automatically sent by the player. Value is used for Device Atlas lookup."
            quote: true
          - name: DaznUserAgent
            description: "Custom User Agent sent by the application. Value is used for Device Atlas lookup."
            quote: true
          - name: C3DeviceModel
            description: "Model of the device."
            quote: true
          - name: DvFw
            description: "Type of Player used - e.g. ExoPlayer Standard or ExoPlayer Magic Sauce"
            quote: true
          - name: ApplicationType
            description: "Application type"
            quote: true
          - name: ApplicationVersion
            description: "Version of the application. It represents some internal numbering of applications developed by DAZN."
            quote: true
          - name: AutoPlay
            description: "Indicated whether the session was started automatically or not."
            quote: true
          - name: C3DeRs
            description: "Legacy field - First Content Delivery Network (CDN) or DC tried by player."
            quote: true
          - name: C3Im
            description: "Legacy field - Precision."
            quote: true
          - name: C3PlayerName
            description: "Player used by the application for the session."
            quote: true
          - name: C3VideoIsLive
            description: "Indicates whether the session was Live or Video-On-Demand (VOD)."
            quote: true
          - name: ClosedCaptionLanguage
            description: "First language loaded by Closed Captions"
            quote: true
          - name: CommentatoryLanguage
            description: "Multitrack Audio Language at start of session"
            quote: true
          - name: Concurrency
            description: "Indicates that this session is the nth Multiview session that the user has started."
            quote: true
          - name: DeviceConnectionType
            description: "Mobile connection type. Value is reported only for mobile devices."
            quote: true
          - name: DRMType
            description: "Name of Streaming Protocol being used e.g. FairPlay, Playready."
            quote: true
          - name: Multivew
            description: "Indicates whether the session is a Multiview Session."
            quote: true
          - name: NativePlayerVersion
            description: "Version of the underlying player used."
            quote: true
          - name: StreamingProtocol
            description: "The streaming protocol used for the session e.g. DASH, ISM, HLS."
            quote: true
          - name: DvMrk
            description: "Device Marketing Name"
            quote: true
          - name: IsC3DeviceUADoubleDecoded
            description: "Legacy field - last non-null value dates back to 2019-11-19."
            quote: true
          - name: IsDaznUserAgentDoubleDecoded
            description: "Legacy field - last non-null value dates back to 2019-11-19."
            quote: true
          - name: DaznDeviceId
            description: "Identifies a device from where a stream originates. Free to View customers who won't have a ViewerID will be identified using their Device ID"
            quote: true
          - name: DaznSessionId
            description: "Unique identifier for a user session. This will be a key that can be used to correlate Conviva Data with events from Google Analytics"
            quote: true
          - name: FreeToView
            description: "Flag that denotes whether this asset was consumed as a Free to View piece of content. Although this is a boolean, the possible values are sent as strings, given that Conviva custom tags do not support boolean values."
            quote: true
          - name: curated_IpAddressType
            description: "Curated IP address family (e.g. IPv4 or IPv6), derived and curated by EDM by using the Snowflake PARSE_IP function on the raw IP address. This column will differentiate between the 2 types and we can expect the following data in this column: IPv4, IPv6, Unknown (when receiving dirty data), NULL when null value received. "
            quote: true
          - name: Asn
            description: "Autonomous System Number for the Internet Service Provider (ISP)"
            quote: true
          - name: FixtureId
            description: "The unique fixture UUID"
            quote: true
          - name: DAISessionStart
            description: "Tag sent by the player at the beginning of the playback session. Indicates if session has started as DAI or non-DAI. Optional."
            quote: true
          - name: entitlementSetId
            description: "Describes the name of Entitlement Set ID for PPV content."
            quote: true
