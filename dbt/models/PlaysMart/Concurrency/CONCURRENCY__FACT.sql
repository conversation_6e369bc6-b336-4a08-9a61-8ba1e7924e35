{{
config(
    materialized='incremental',
    incremental_strategy='delete+insert',
    unique_key='"playback_stream_date"',
    schema='FACT',
    database='PLAYBACK_STREAM__B2C__MART__' + snowflake_env(),
    snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_M_WH',
    tags=['presentation-playback-stream-mart']
)
}}

/*
    This purpose of this model is to generate a 5 minute timestamp and count the number of sessions and viewers active at that timestamp.
    By joining to the plays mart's SKEYs we allow analysis to include any of the fields from the following dims:
    - CONTENT
    - LOCATION
    - DEVICE
    - STREAM DETAILS

    Running this table should not prevent flows dependent on the plays mart to run.
*/

WITH playback_stream_fact AS (
    SELECT * FROM {{ ref_env('MART__PLAYBACK_STREAM__FACT') }}
    WHERE "playback_stream_date" >= '2021-01-01'
        AND "playback_duration_milliseconds" > 0
        -- incremental
        -- Because we have streams crossing midnight, we won't catch these until 2 days after the batch date.
        -- Therefore, on an incremental we're going to look back for two days worth of data.
        -- Note that data from yesterday will not be fully complete until tomorrow.
        AND DATE_TRUNC(DAY, "playback_stream_date") BETWEEN DATEADD(DAY, -1, TO_DATE('{{ var('batch_date') }}', 'YYYY-MM-DD')) AND '{{ var('batch_date') }}'
)

, date_list AS (
    SELECT DISTINCT "playback_stream_date" FROM playback_stream_fact
    )

, generator_cte AS (
    SELECT
        SEQ4() AS "n"
    FROM TABLE(GENERATOR(ROWCOUNT => 576))  -- two days of intervals
    ORDER BY 1
)

, time_interval AS (
    SELECT
        date_list.*
        ,generator_cte."n"
        ,DATEADD(MINUTE, ("n" * 5), date_list."playback_stream_date") AS "timestamp"
    FROM date_list
    LEFT JOIN generator_cte
    ORDER BY 1, 2
)

, concurrent_streams AS (
    SELECT
        COALESCE(time_interval."timestamp", TIME_SLICE(playback_stream_fact."playback_start_timestamp", 5, 'minutes')) AS "timestamp"
        -- Sessions that are shorter than 5 mins and run entirely between two 5 min intervals will be missed by join causing a NULL timestamp in the output.
        -- The time_slice returns the closest 5 min interval before the start of the session which we can use to fill in the null values.
        ,playback_stream_fact."playback_stream_date"
        ,playback_stream_fact."content_item__skey"
        ,playback_stream_fact."playback_stream_location__skey"
        ,playback_stream_fact."device_info__skey"
        ,playback_stream_fact."playback_stream_details__skey"
        ,COUNT(DISTINCT playback_stream_fact."dazn_session_id") AS "distinct_sessions_count"
        ,COUNT(DISTINCT playback_stream_fact."viewer_id") AS "distinct_viewers_count"
    FROM playback_stream_fact
    LEFT JOIN time_interval
        ON time_interval."timestamp" >= playback_stream_fact."playback_start_timestamp"
        AND time_interval."timestamp" < playback_stream_fact."playback_end_timestamp"
    GROUP BY 1, 2, 3, 4, 5, 6
)

SELECT * FROM concurrent_streams
