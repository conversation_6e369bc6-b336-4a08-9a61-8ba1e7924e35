version: 2

models:
  - name: CONCURRENCY__FACT
    description: "Fact table. Each record corresponds to a 5 minute timestamp and also references related dimensions."
    columns:
      - name: timestamp
        description: "Timestamp of a 5 minute interval."
        quote: true

      - name: playback_stream_date
        description: "The date when the playback stream was initiated."
        quote: true

      - name: content_item__skey
        description: "Foreign key, references primary (surrogate) key values of the Content Item dimension."
        quote: true

      - name: playback_stream_location__skey
        description: "Foreign key, references primary (surrogate) key values of the Playback Stream Location dimension."
        quote: true

      - name: device_info__skey
        description: "Foreign key, references primary (surrogate) key values of the Device Info dimension."
        quote: true

      - name: playback_stream_details__skey
        description: "Foreign key, references primary (surrogate) key values of the Playback Stream Origin dimension."
        quote: true

      - name: distinct_sessions_count
        description: "Count of distinct sessions within the 5 minute timestamp."
        quote: true

      - name: distinct_viewers_count
        description: "Count of distinct viewers within the 5 minute timestamp."
        quote: true
