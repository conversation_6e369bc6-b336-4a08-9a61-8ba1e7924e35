{{
    config(
        materialized='incremental',
        on_schema_change='append_new_columns',
        incremental_strategy='delete+insert',
        unique_key='"playback_stream_date"',
        database='PLAYBACK_STREAM__B2C__MART__' + snowflake_env(),
        schema='FACT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        alias='playback_stream__linear__fact',
        tags=['presentation-playback-linear-stream-mart']
    )
}}

WITH PLAYBACK_STREAM__FACT AS (
    SELECT * FROM {{ ref_env('MART__PLAYBACK_STREAM__FACT') }}
    where "playback_stream_date" between DATEADD('day', -7, TO_DATE('{{ var('batch_date') }}', 'YYYY-MM-DD'))  and TO_DATE('{{ var('batch_date') }}', 'YYYY-MM-DD')
)

,CONTENT_ITEM__DIM AS (
    SELECT content.* 
    FROM 
    {{ ref_env('DOMAIN__CONTENT_ITEM__DIM') }} content 
    join 
    MART_DIMENSIONS__B2C__DOMAIN__DEV.content.linear_channel_articles_view linear 
    on content."article_id" = linear."article_id"
)

,linear_content_dim AS (
    SELECT * FROM {{ ref('linear_content_dim') }}
)

,final as (
    select 
        "linear_article_skey"
        ,f."playback_start_timestamp" "str_start_timestamp"
        ,f."playback_end_timestamp" "str_end_timestamp"
        ,to_timestamp(CONVERT_TIMEZONE('UTC', "str_start_timestamp")) "str_start_time",
        to_timestamp(CONVERT_TIMEZONE('UTC', "str_end_timestamp")) "str_end_time"  ,
        to_timestamp(CONVERT_TIMEZONE('UTC',timestampadd('Milliseconds',nvl("startup_duration_milliseconds",0),timestampadd('Milliseconds',nvl("buffering_duration_milliseconds",0),timestampadd('Milliseconds',nvl("playback_duration_milliseconds",0),"str_start_time"))))) "str_new_end_time",
        "program_start_time" ,"program_end_time",
        f.META__DATA,
        "playback_stream__skey",
        "playback_stream_details__skey",
        "playback_stream_location__skey",
        "playback_stream_issue__skey",
        "device_info__skey",
        "application__skey",
        f."content_item__skey",
        "playback_stream_country__hash",
        f."sport_id__hash",
        "playback_stream_date",
        "playback_start_timestamp",
        "playback_end_timestamp",
        "playback_duration_milliseconds",
        "startup_duration_milliseconds",
        "buffering_duration_milliseconds",
        "video_restart_duration_milliseconds",
        "total_connection_induced_rebuffering_duration_milliseconds",
        "total_streaming_duration_milliseconds",
        "average_bitrate",
        "interrupts_count",
        "rejoins_count",
        "playback_entitlement_set_id",
        "dazn_device_id",
        "conviva_session_id",
        "user_agent",
        "viewer_id",
        "entitlements__skey",
        "customer_identity__skey",
        "dazn_session_id",
        "ip_address_hash",
        "user_status__skey",
        "dazn_subscription_name",
        "dazn_billing_account__skey",
        "dazn_subscription_daily_status__skey",
        "dazn_subscription_info__skey",
        "dazn_subscription_term__skey",
        "dazn_subscription_charge__skey",
        "dazn_subscription_source_system_name_derived__skey",
        "dazn_subscription_tracking_id__skey",
        "dazn_subscription_giftcode_campaign_name__skey",
        "last_dazn_subscription_name",
        "user_last_dazn_subscription__skey",
        "nfl_subscription_name",
        "nfl_billing_account__skey",
        "nfl_subscription_daily_status__skey",
        "nfl_subscription_info__skey",
        "nfl_subscription_term__skey",
        "nfl_subscription_charge__skey",
        "nfl_subscription_source_system_name_derived__skey",
        "nfl_subscription_tracking_id__skey",
        "nfl_subscription_giftcode_campaign_name__skey",
        "last_nfl_subscription_name",
        "user_last_nfl_subscription__skey",
        "is_free_to_view",
        "content_length"
        FROM
        PLAYBACK_STREAM__FACT f
        join CONTENT_ITEM__DIM 
            on f."content_item__skey" = CONTENT_ITEM__DIM."content_item__skey"
        left join linear_content_dim 
            on linear_content_dim."article_id" = CONTENT_ITEM__DIM."article_id" 
            and 
            (
                ("str_start_time" between linear_content_dim."program_start_time" AND  linear_content_dim."program_end_time") 
                OR  
                (linear_content_dim."program_start_time" BETWEEN "str_start_time" AND "str_new_end_time")
            )

)    

select 
     "linear_article_skey",
       case when "program_start_time" is null then "playback_start_timestamp" else "program_start_time" end "program_start_time",
       case when "program_end_time" is null then "playback_end_timestamp" else "program_end_time" end "program_end_time",
        META__DATA,
        "playback_stream__skey",
        "playback_stream_details__skey",
        "playback_stream_location__skey",
        "playback_stream_issue__skey",
        "device_info__skey",
        "application__skey",
        "content_item__skey",
        "playback_stream_country__hash",
        "sport_id__hash",
        "playback_stream_date",
        "playback_start_timestamp",
        "playback_end_timestamp",
         case when "program_start_time" is null then "total_streaming_duration_milliseconds" else 
            case
                when "str_start_time" >= "program_start_time" and "str_new_end_time"  <= "program_end_time" then DATEDIFF(millisecond,"str_start_time","str_new_end_time" )
                when "str_start_time" >= "program_start_time" and "str_new_end_time"  > "program_end_time" then DATEDIFF(millisecond,"str_start_time","program_end_time") 
                when "str_start_time" < "program_start_time" and "str_new_end_time"  <= "program_end_time" then DATEDIFF(millisecond,"program_start_time","str_new_end_time" )
                when "str_start_time" < "program_start_time" and "str_new_end_time"  > "program_end_time" then DATEDIFF(millisecond,"program_start_time","program_end_time" )
                else 0
            end 
        end "total_program_streaming_duration_milliseconds",
        "playback_duration_milliseconds",
        "startup_duration_milliseconds",
        "buffering_duration_milliseconds",
        "video_restart_duration_milliseconds",
        "total_connection_induced_rebuffering_duration_milliseconds",
        "total_streaming_duration_milliseconds",
        "average_bitrate",
        "interrupts_count",
        "rejoins_count",
        "playback_entitlement_set_id",
        "dazn_device_id",
        "conviva_session_id",
        "user_agent",
        "viewer_id",
        "entitlements__skey",
        "customer_identity__skey",
        "dazn_session_id",
        "ip_address_hash",
        "user_status__skey",
        "dazn_subscription_name",
        "dazn_billing_account__skey",
        "dazn_subscription_daily_status__skey",
        "dazn_subscription_info__skey",
        "dazn_subscription_term__skey",
        "dazn_subscription_charge__skey",
        "dazn_subscription_source_system_name_derived__skey",
        "dazn_subscription_tracking_id__skey",
        "dazn_subscription_giftcode_campaign_name__skey",
        "last_dazn_subscription_name",
        "user_last_dazn_subscription__skey",
        "nfl_subscription_name",
        "nfl_billing_account__skey",
        "nfl_subscription_daily_status__skey",
        "nfl_subscription_info__skey",
        "nfl_subscription_term__skey",
        "nfl_subscription_charge__skey",
        "nfl_subscription_source_system_name_derived__skey",
        "nfl_subscription_tracking_id__skey",
        "nfl_subscription_giftcode_campaign_name__skey",
        "last_nfl_subscription_name",
        "user_last_nfl_subscription__skey",
        "is_free_to_view",
        "content_length"
    from final
