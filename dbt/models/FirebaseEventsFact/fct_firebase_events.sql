{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"event_date"',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XL_WH',
        tags=['presentation-firebase-events-fact']
    )
}}

WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED_FIREBASE__FLAT_EVENT') }}
)

,renamed AS (
    SELECT
        CURRENT_TIMESTAMP AS "dbt_inserted_at"
        ,"firebase_primary_key"
        ,"app_info_id"
        ,"app_info_firebase_app_id"
        ,"app_info_install_source"
        ,"app_info_version"
        ,"device_category"
        ,"device_mobile_brand_name"
        ,"device_mobile_model_name"
        ,"device_mobile_marketing_name"
        ,"device_mobile_os_hardware_model"
        ,"device_operating_system"
        ,"device_operating_system_version"
        ,"device_vendor_id"
        ,"device_advertising_id"
        ,"device_language"
        ,"device_time_zone_offset_seconds"
        ,"device_is_limited_ad_tracking"
        ,"stream_id"
        ,"platform"
        ,"user_first_touch_timestamp"
        ,"user_id"
        ,"user_pseudo_id"
        ,"user_ltv_revenue"
        ,"user_ltv_currency"
        ,"traffic_source_name"
        ,"traffic_source_medium"
        ,"traffic_source_source"
        ,"geo_continent"
        ,"geo_country"
        ,"geo_region"
        ,"geo_city"
        ,"event_date"
        ,"event_timestamp"
        ,"event_timestamp__seconds" AS "event_timestamp_seconds"
        ,"event_previous_timestamp"
        ,"event_name"
        ,"event_value_in_usd"
        ,"event_bundle_sequence_id"
        ,"event_param_action_category"
        ,"event_param_action_label"
        ,"event_param_action_name"
        ,"event_param_action_origin"
        ,"age_restricted"
        ,"article_id"
        ,"asset_id"
        ,"event_param_bitrate"
        ,"event_param_button_title"
        ,"event_param_campaign"
        ,"campaign_info_source"
        ,"event_param_cdn"
        ,"event_param_channel"
        ,"event_param_competition_id"
        ,"event_param_competition_name"
        ,"competitor_id"
        ,"content_campaign"
        ,"event_param_customer_id"
        ,"event_param_data_cap_cellular_on"
        ,"event_param_data_cap_on"
        ,"event_param_data_cap_wifi_on"
        ,"event_param_device_id"
        ,"event_param_download_status"
        ,"event_param_engagement_time_msec"
        ,"event_param_error_code_cat"
        ,"event_param_error_code_response"
        ,"event_param_error_code_type"
        ,"event_param_error_http_code"
        ,"event_param_error_internal_code"
        ,"event_param_error_internal_msg"
        ,"event_param_error_misl_code"
        ,"event_param_error_misl_message"
        ,"event_param_error_user_message_key"
        ,"event_param_error_value"
        ,"event_param_eventid"
        ,"event_param_fa_event_action"
        ,"event_param_fa_event_desc"
        ,"event_param_fa_event_object"
        ,"event_param_factory_error"
        ,"event_param_failed_cdn"
        ,"favourite_id"
        ,"event_param_firebase_event_origin"
        ,"event_param_firebase_previous_class"
        ,"event_param_firebase_previous_id"
        ,"event_param_firebase_previous_screen"
        ,"event_param_firebase_screen"
        ,"event_param_firebase_screen_class"
        ,"event_param_firebase_screen_id"
        ,"ga_session_id_event_parameter"
        ,"event_param_interaction_type"
        ,"event_param_languagecode"
        ,"event_param_launch_origin"
        ,"event_param_medium"
        ,"event_param_min_bitrate"
        ,"event_param_notification_type"
        ,"event_param_currency"
        ,"event_param_offer_type"
        ,"event_param_offline_playback_position_in_millis"
        ,"event_param_page_index"
        ,"event_param_play_origin"
        ,"event_param_previous_app_version"
        ,"event_param_previous_first_open_count"
        ,"event_param_previous_os_version"
        ,"event_param_price"
        ,"event_param_product_id"
        ,"event_param_product_name"
        ,"event_param_rail_offset"
        ,"search_result_category"
        ,"event_param_screen_name"
        ,"search_item_selected"
        ,"search_term"
        ,"event_param_source"
        ,"event_param_sport_id"
        ,"event_param_sport_name"
        ,"event_param_update_with_analytics"
        ,"event_param_view_mode"
        ,"open_browse"
        ,"reminders_feature"
        ,"firebase_experiment_parameters"
        ,"share_page"
        ,"share_origin"
        ,"experiment_id"
        ,"experiment_key"
        ,"variation_id"
        ,"variation_key"
        ,"dazn_device_id"
        ,"article_name"
        ,"navigate_to"
        ,"tile_title"
        ,"rail_position_of_loaded"
        ,"rail_position_in_view"
        ,"rail_position_of_tile_start"
        ,"rail_name"
        ,"rail_title"
        ,"rail_length"
        ,"coming_up"
        ,"tile_position_of_loaded"
        ,"tile_position_in_view"
        ,"status"
        ,"dazn_session_id"
        ,"dazn_event_date_dt"
        ,"dazn_event_timestamp_ts"
        ,"dazn_event_previous_timestamp_ts"
        ,"event_param_article_id"
    FROM source
    WHERE {{ unixtime_build_trigger(var('build_mode'), var('rebuild_days'), 'event_timestamp_seconds') }}
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "firebase_primary_key" ORDER BY "event_timestamp" DESC NULLS LAST) = 1
)

SELECT * FROM renamed
