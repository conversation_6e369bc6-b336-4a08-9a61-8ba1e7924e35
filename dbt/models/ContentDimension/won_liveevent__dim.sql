{{
    config(
        materialized='table',
        database='MART_DIMENSIONS__B2C__DOMAIN__' + snowflake_env(),
        schema='CONTENT',
        tags=['presentation-content-dimension']
    )
}}

WITH liveevent AS (
    SELECT * FROM {{ ref('staging__dots__won_liveevent_current') }}
)

,region AS (
    SELECT * FROM {{ ref('staging__dots__won_region_current') }}
)

,liveproduct AS (
    SELECT * FROM {{ ref('staging__dots__won_liveproduct_current') }}
)

,liveright AS (
    SELECT * FROM {{ ref('staging__dots__won_liveright_current') }}
)

,contract AS (
    SELECT * FROM {{ ref('staging__dots__won_contract_current') }}
)

,rightsholder AS (
    SELECT * FROM {{ ref('staging__dots__won_rightsholder_current') }}
)

,closed_captions AS 
(
    SELECT "payload_id" "cc_payload_id",EDM_INSERT_DTTS "CC_EDM_INSERT_DTTS",
    ARRAY_UNIQUE_AGG(
        "input".VALUE:"languageCode"::STRING
    )   "live_event_cc_languages_code",
    ARRAY_UNIQUE_AGG(
        object_construct(
            'supplier_id',"input".VALUE:"ccSupplier":"id"::STRING,
            'supplier_name',"input".VALUE:"ccSupplier":"name"::STRING
    ))  "live_event_cc_supplier_details"
    FROM liveevent
    ,LATERAL FLATTEN( INPUT => "live_event_closed_captions",OUTER => TRUE) AS "input"
    GROUP BY 1,2
)

SELECT
    liveevent."live_event_id"
    ,liveevent."live_event_title"
    ,liveevent."dummy_fixture_id"
    ,COALESCE(liveevent."dummy_fixture_id", liveevent."fixture_id") AS "fixture_id"
    ,liveevent."region_id"
    ,region."region_name"
    -- "updating the region_name to be in sync with 'region_dimension' and 'TRANSIENT__PLAYS_2' tables"
    ,CASE
        WHEN region."region_name" = 'dach' THEN 'DACH'
        WHEN region."region_name" = 'gb' THEN 'UK and Ireland'
        WHEN region."region_name" = 'usa' THEN 'United States'
        WHEN region."region_name" = 'row' THEN 'Global'
        ELSE initcap(region."region_name")
    END AS "region_territory"
    ,region."region_ad_market"
    ,region."region_m2a_key"
    ,region."region_countries"
    ,REPLACE(REPLACE(region."region_name", 'usa', 'us'), 'row', 'moon') AS "region_outlet"
    ,liveevent."live_event_status"
    ,liveevent."live_event_start_timestamp"
    ,liveevent."live_event_end_timestamp"
    ,liveevent."live_event_platform"
    ,liveevent."live_event_type_name"
    ,liveevent."live_event_type"
    ,liveevent."live_event_broadcast_tier"
    ,liveevent."live_event_vob_key"
    ,liveevent."live_event_frame_rate"
    ,liveevent."live_event_workflow_type"
    ,liveevent."live_event_gallery_name"
    ,liveevent."live_event_gallery_id"
    ,liveevent."live_event_schedule_version"
    ,liveevent."live_event_is_co_exclusive"
    ,liveevent."live_event_is_obligated"
    ,liveevent."live_event_has_ftv_allowed"
    ,liveevent."live_event_has_ftv_catchup_allowed"
    ,liveevent."live_event_is_dci"
    ,liveevent."live_event_dci_group_name"
    ,liveevent."live_event_closed_caption_languages"
    ,liveevent."live_event_is_world_feed"
    ,liveevent."live_event_commentary_languages_code"
    ,liveevent."live_event_world_feed_languages_code"
    ,CASE
        WHEN liveevent."live_event_commentary_languages_code" IS NULL AND liveevent."live_event_is_world_feed" = true
        THEN liveevent."live_event_world_feed_languages_code"
        ELSE liveevent."live_event_commentary_languages_code"
     END AS "primary_commentary_language"
    ,liveevent."live_event_advertising_compliance_rules"
    ,liveevent."live_event_announced_start_timestamp"
    ,liveevent."live_event_sponsoring"
    ,liveevent."live_event_ht_filler"
    ,liveevent."live_event_regional_supplier"
    ,liveevent."live_encoding_id"
    ,liveevent."live_product_id"
    ,liveproduct."live_product_type"
    ,liveproduct."competition_id"
    ,liveproduct."live_product_ruleset_name"
    ,liveproduct."tournament_calendar_id"
    ,liveproduct."sport_id"
    ,liveproduct."sport_name"
    ,liveproduct."live_product_support_tier"
    ,liveproduct."live_product_order_of_play"
    ,liveproduct."live_product_expected_duration"
    ,liveproduct."live_product_contractual_compliance"
    ,liveevent."live_right_id"
    ,liveright."live_right_title"
    ,liveright."live_right_start_date"
    ,liveright."live_right_end_date"
    ,liveright."live_right_type"
    ,liveright."live_right_planned_fixtures"
    ,liveright."live_right_status"
    ,liveright."live_right_is_live"
    ,liveright."live_right_is_as_live"
    ,liveright."live_right_has_download_allowed"
    ,liveright."live_right_has_b2c_allowed"
    ,liveright."live_right_has_b2b_allowed"
    ,liveright."live_right_has_b2b_catchup_allowed"
    ,liveright."live_right_allowed_country_codes"
    ,liveright."live_right_disallowed_country_codes"
    ,liveright."live_right_non_exclusive_regions"
    ,CASE
        WHEN liveright."live_right_non_exclusive_regions" = '[]' THEN 'Exclusive'
        WHEN liveright."live_right_allowed_country_codes" = liveright."live_right_non_exclusive_regions" THEN 'Not Exclusive'
        WHEN liveright."live_right_allowed_country_codes_0" IN (liveright."live_right_non_exclusive_regions_0", liveright."live_right_non_exclusive_regions_1", liveright."live_right_non_exclusive_regions_2")
            AND (liveright."live_right_allowed_country_codes_1" IS NULL OR liveright."live_right_allowed_country_codes_1" IN (liveright."live_right_non_exclusive_regions_0", liveright."live_right_non_exclusive_regions_1", liveright."live_right_non_exclusive_regions_2"))
            AND (liveright."live_right_allowed_country_codes_2" IS NULL OR liveright."live_right_allowed_country_codes_2" IN (liveright."live_right_non_exclusive_regions_0", liveright."live_right_non_exclusive_regions_1", liveright."live_right_non_exclusive_regions_2"))
            THEN 'Not Exclusive'
        WHEN  liveright."live_right_non_exclusive_regions" IS NULL THEN 'Unknown'
        ELSE CONCAT('Not Exclusive in ', liveright."live_right_non_exclusive_regions")
    END "live_right_exclusivity_flag"
    ,liveright."live_right_allowed_audio_languages"
    ,liveright."contract_id"
    ,contract."contract_name"
    ,contract."contract_status"
    ,contract."contract_start_date"
    ,contract."contract_end_date"
    ,contract."rights_holder_id"
    ,rightsholder."rights_holder_name"
    -- Payload fields
    ,liveevent."payload" AS "liveevent_payload"
    ,region."payload" AS "region_payload"
    ,liveproduct."payload" AS "liveproduct_payload"
    ,liveright."payload" AS "liveright_payload"
    ,contract."payload" AS "contract_payload"
    ,rightsholder."payload" AS "rightsholder_payload"
    -- QA Fields
    ,region."region_id" IS NULL AND liveevent."region_id" IS NOT NULL AS "region_has_failed_join"
    ,liveproduct."live_product_id" IS NULL AND liveevent."live_product_id" IS NOT NULL AS "liveproduct_has_failed_join"
    ,liveright."live_right_id" IS NULL AND liveevent."live_right_id" IS NOT NULL AS "liveright_has_failed_join"
    ,contract."contract_id" IS NULL AND liveright."contract_id" IS NOT NULL AS "contract_has_failed_join"
    ,rightsholder."rights_holder_id" IS NULL AND contract."rights_holder_id" IS NOT NULL AS "rightsholder_has_failed_join"
    ,liveevent."live_event_regional_supplier_id"  as "live_event_regional_supplier_id" 
    ,closed_captions."live_event_cc_languages_code" AS "live_event_cc_languages_code"
    ,closed_captions."live_event_cc_supplier_details" AS "live_event_cc_supplier_details"

FROM liveevent
LEFT JOIN region
    ON liveevent."region_id" = region."region_id"
LEFT JOIN liveproduct
    ON liveevent."live_product_id" = liveproduct."live_product_id"
LEFT JOIN liveright
    ON liveevent."live_right_id" = liveright."live_right_id"
LEFT JOIN contract
    ON liveright."contract_id" = contract."contract_id"
LEFT JOIN rightsholder
    ON contract."rights_holder_id" = rightsholder."rights_holder_id"
LEFT JOIN closed_captions
    ON liveevent."payload_id" = closed_captions."cc_payload_id" and liveevent."EDM_INSERT_DTTS" = closed_captions."CC_EDM_INSERT_DTTS"
WHERE liveevent."payload_state" = 'ACTIVE'
    AND liveevent."fixture_id" IS NOT NULL
-- Deduping at the fixture-region level as this is the useful level of this data to join to any downstream datasets we use
-- Ordering by the live_event_id as this is effectively just a count of the amount of live events in WON and so the highest number is the most recently created and the one we should pick
QUALIFY ROW_NUMBER() OVER (PARTITION BY COALESCE(liveevent."dummy_fixture_id", liveevent."fixture_id"), liveevent."region_id" ORDER BY liveevent."live_event_id" DESC) = 1
