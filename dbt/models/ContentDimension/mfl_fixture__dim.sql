{{
    config(
        materialized='table',
        database='MART_DIMENSIONS__B2C__DOMAIN__' + snowflake_env(),
        schema='CONTENT',
        tags=['presentation-content-dimension'],
        post_hook=" GRANT SELECT ON TABLE {{ this }} TO MART_DIMENSIONS__B2C__DOMAIN__PROD_CONTENT_MFL_FIXTURE__DIM_TABLE_RO "
    )
}}

WITH fixture AS (
    SELECT * FROM {{ ref('staging__dots__mfl_fixture_current')}}
)

,competition AS (
    SELECT * FROM {{ ref('staging__dots__mfl_competition_current')}}
)

,contestant AS (
    SELECT * FROM {{ ref('staging__dots__mfl_contestant_current')}}
)

,country AS (
    SELECT * FROM {{ ref('staging__dots__mfl_country_current')}}
)

,ruleset AS (
    SELECT * FROM {{ ref('staging__dots__mfl_ruleset_current')}}
)

,sport AS (
    SELECT * FROM {{ ref('staging__dots__mfl_sport_current')}}
)

,stage AS (
    SELECT * FROM {{ ref('staging__dots__mfl_stage_current')}}
)

,tournament AS (
    SELECT * FROM {{ ref('staging__dots__mfl_tournamentcalendar_current')}}
)

,venue AS (
    SELECT * FROM {{ ref('staging__dots__mfl_venue_current')}}
)

,contestant_with_country AS (
    SELECT
        contestant.*
        ,country."country_name" AS "country_name"
    FROM contestant
    LEFT JOIN country
        USING ("country_id")
)

SELECT
     fixture."fixture_id"
    ,fixture."fixture_description"
    ,fixture."fixture_start_timestamp"
    ,fixture."fixture_status"
    ,fixture."sport_id"
    ,sport."sport_name"
    ,fixture."competition_id"
    ,competition."competition_name"
    ,competition."country_id" as "competition_country_id"
    ,competition_country."country_name" AS "competition_country_name"
    ,fixture."tournament_calendar_id"
    ,tournament."tournament_calendar_name"
    ,tournament."tournament_calendar_start_date"
    ,tournament."tournament_calendar_end_date"
    ,fixture."ruleset_id"
    ,ruleset."ruleset_name"
    ,fixture."stage_id"
    ,stage."stage_name"
    ,stage."stage_start_date"
    ,stage."stage_end_date"
    ,fixture."venue_id"
    ,venue."venue_name_short"
    ,venue."venue_name_long"
    ,fixture."payload":"correlation"::STRING AS "soccer_match_day"
    {% for contestant_number in [0,1,2,3,4,5] %}
    ,fixture."contestant_id_{{contestant_number}}" AS "contestant_{{contestant_number}}_id"
    ,contestant_{{contestant_number}}."contestant_name" AS "contestant_{{contestant_number}}_name"
    ,contestant_{{contestant_number}}."country_name" AS "contestant_{{contestant_number}}_country_name"
    {% endfor %}
    -- PAYLOAD fields
    ,fixture."payload" AS "fixture_payload"
    ,sport."payload" AS "sport_payload"
    ,competition."payload" AS "competition_payload"
    ,competition_country."payload" AS "competition_country_payload"
    ,tournament."payload" AS "tournament_payload"
    ,ruleset."payload" AS "ruleset_payload"
    ,stage."payload" AS "stage_payload"
    ,venue."payload" AS "venue_payload"
    -- QA Fields
    ,sport."sport_id" IS NULL AND fixture."sport_id" IS NOT NULL AS "sport_has_failed_join"
    ,competition."competition_id" IS NULL AND fixture."competition_id" IS NOT NULL AS "competition_has_failed_join"
    ,competition_country."country_id" IS NULL AND competition."country_id" IS NOT NULL AS "competition_country_has_failed_join"
    ,tournament."tournament_calendar_id" IS NULL AND fixture."tournament_calendar_id" IS NOT NULL AS "tournament_has_failed_join"
    ,ruleset."ruleset_id" IS NULL AND fixture."ruleset_id" IS NOT NULL AS "ruleset_has_failed_join"
    ,stage."stage_id" IS NULL AND fixture."stage_id" IS NOT NULL AS "stage_has_failed_join"
    ,venue."venue_id" IS NULL AND fixture."venue_id" IS NOT NULL AS "venue_has_failed_join"
    {% for contestant_number in [0,1,2,3,4,5] %}
    ,contestant_{{contestant_number}}."contestant_id" IS NULL AND fixture."contestant_id_{{contestant_number}}" IS NOT NULL AS "contestant_{{contestant_number}}_has_failed_join"
    {% endfor %}
FROM fixture
LEFT JOIN sport
    ON fixture."sport_id" = sport."sport_id"
LEFT JOIN competition
    ON fixture."competition_id" = competition."competition_id"
LEFT JOIN country competition_country
    ON competition_country."country_id" = competition."country_id"
LEFT JOIN tournament
    ON fixture."tournament_calendar_id" = tournament."tournament_calendar_id"
LEFT JOIN ruleset
    ON fixture."ruleset_id" = ruleset."ruleset_id"
LEFT JOIN stage
    ON fixture."stage_id" = stage."stage_id"
LEFT JOIN venue
    ON fixture."venue_id" = venue."venue_id"
{% for contestant_number in [0,1,2,3,4,5] %}
LEFT JOIN contestant_with_country contestant_{{contestant_number}}
    ON fixture."contestant_id_{{contestant_number}}" = contestant_{{contestant_number}}."contestant_id"
{% endfor %}
