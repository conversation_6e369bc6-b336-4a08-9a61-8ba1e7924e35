{{
    config(
        materialized='incremental',
        unique_key=['"article_id"','"program_date"'],
        incremental_strategy='delete+insert',
        database='MART_DIMENSIONS__B2C__DOMAIN__' + snowflake_env(),
        schema='CONTENT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        tags=['presentation-linear-dimension']
    )
}}

with source as (
    select 
    * 
    from {{ ref('staging__broadcast__linear_EPG_current') }} 
    {% if is_incremental() %}
        where "dwh_batch_date" >= (SELECT MAX("dwh_batch_date") FROM {{ this }})
        QUALIFY ROW_NUMBER() OVER(PARTITION BY "article_id","program_start_time" ORDER BY "schedule_date" desc ) = 1
    {% else %}
    QUALIFY ROW_NUMBER() OVER(PARTITION BY "article_id","program_start_time" ORDER BY "schedule_date" desc ) = 1
    {% endif %}
)

,config as (
    select * from MART_DIMENSIONS__B2C__DOMAIN__DEV.content.linear_channel_articles_view
)

select 
    CURRENT_TIMESTAMP AS "DBT_INSERT_DTTS"
    ,source."article_id" "article_id"
    ,"program_start_time"::date "program_date"
    ,"program_start_time"
    ,COALESCE(lead("program_start_time") over (partition by config."article_id" order by "program_start_time" asc),"program_end_time") "program_end_time"
    ,to_varchar("program_start_time") ||'-'|| source."article_id"  "linear_article_skey"
    ,"EPG_ID"
    ,"is_fast_channel"
    ,"is_partner_channel"
    ,"is_amagi"
    ,"dwh_batch_date"
    ,"channel_language"
    ,"competition_title"
    ,"competition_id"
    ,"channel_description"
    ,"channel_display_date"
    ,"channel_start_date"
    ,"channel_end_date"
    ,"entitlements"
    ,"is_play_for_free"
    ,"channel_label"
    ,"channel_title_localised"
    ,coalesce("linear_channel_name","Channel_display_name") "channel_display_name"
    --,"channel_title_english"
    ,"channel_sportid"
    ,"channel_sport_title"
    ,"program_description"
    ,"program_title"
    ,"program_episode_title"
    ,"program_event_year"
    ,"program_islive"
    ,"program_genre"
    ,"block"
    ,"country_set"
from source
left join config 
    on source."article_id" = config."article_id"
where "program_start_time" is not null