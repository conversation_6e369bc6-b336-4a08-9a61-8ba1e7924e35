version: 2
models:
  - name: won_vodevent__dim
    description: "Dimension table from WON data. Contains VOD event data at fixture & region level."
    columns:
      - name: vod_event_id
        description: "Unique id of this non-live event in WON i.e. External Reference"
        quote: true

      - name: vod_event_title
        description: "The title of the event"
        quote: true

      - name: vod_media_id
        description: "Reference ID to the associated WON media asset, created using the COALESCE of the two rightId and rightsId fields, needed as there was a change of the field name"
        quote: true

      - name: vod_product_id
        description: "Reference ID to the associated WON product "
        quote: true

      - name: region_id
        description: "ID that represents the DAZN region and MCS Product"
        quote: true

      - name: vod_right_id
        description: "Reference ID to the WON exploitation rights used to schedule this non-live event, created using the COALESCE of the two rightId and rightsId fields, needed as there was a change of the field name"
        quote: true

      - name: vod_event_status
        description: "The status of the regional transmission in WON"
        quote: true

      - name: vod_event_start_timestamp
        description: "The date/time that the VOD content will be available on DAZN, created as the combination of all fields that have been populated for this data point"
        quote: true

      - name: vod_event_end_timestamp
        description: "The UTC time the event end, created as the combination of all fields that have been populated for this data point"
        quote: true

      - name: vod_event_is_obligated
        description: "mustPlay field from transmission"
        quote: true

      - name: vod_event_has_ftv_allowed
        description: "Boolean value that indicates whether the non-live event can be offered as 'free to view"
        quote: true

      - name: vod_event_advertising
        description: "Represents the advertising field from Transmission"
        quote: true

      - name: vod_event_duration
        description: "Expected duration of the live event"
        quote: true

      - name: vod_event_schedule_version
        description: "Schedule version of Transmission from WON (Active, Alternative) - Alternative is used for pre-planning"
        quote: true

      - name: region_name
        description: "Name of the DAZN region"
        quote: true

      - name: region_territory
        description: "Name of DAZN region in sync with 'region_dimension' and 'TRANSIENT__PLAYS_2' tables"
        quote: true

      - name: region_default_timezone
        description: "The default timezone of the region"
        quote: true

      - name: region_ad_market
        description: "Region identifier uniquely used for advertising purposes"
        quote: true

      - name: region_organisation_id
        description: "ID that represents the related DCMS organisation"
        quote: true

      - name: region_outlet_key
        description: "The authentication key used for the relevant DAZN Feed outlet for given DAZN region"
        quote: true

      - name: region_m2a_key
        description: "Region identifier uniquely used by M2A"
        quote: true

      - name: region_countries
        description: "An array of countries associated with the region"
        quote: true

      - name: vod_media_label
        description: "Label used to identify the purpose of the media asset, created using the COALESCE of the two wonMediaLabel and label fields, needed as there was a change of the field name"
        quote: true

      - name: vod_media_old_file_name
        description: "Filename required for the produced media asset"
        quote: true

      - name: vod_media_team_assigned
        description: "The team assigned to product the media asset"
        quote: true

      - name: vod_media_has_reversion_required
        description: "Boolean value indicating whether the media asset needs to be re-versioned into another media asset with different audio language"
        quote: true

      - name: vod_media_has_closed_captions_required
        description: "Boolean value indicating if closed captions need to be added to produced media asset"
        quote: true

      - name: vod_media_audio_languages
        description: "Audio Languages used within the Media"
        quote: true

      - name: vod_media_audio_languages_value
        description: "The first value from the audioLanguages array representing the primary audio Languages used within the Media"
        quote: true

      - name: vod_product_title
        description: "Title used as reference to the original product title"
        quote: true

      - name: vod_product_type
        description: "The type of product"
        quote: true

      - name: fixture_id
        description: "ID that represents the fixture associated to the scheduled transmission"
        quote: true

      - name: vod_product_version
        description: "Version of the product"
        quote: true

      - name: competition
        description: "Array containing metadata relating to the competition of the product"
        quote: true

      - name: competition_id
        description: "ID of the related MFL competition"
        quote: true

      - name: competition_name
        description: "Name of the related MFL competition, created using the COALESCE of the two competition_name and competitionName fields, needed as there was a change of the field name"
        quote: true

      - name: vod_product_ruleset_name
        description: "Name of the related MFL ruleset"
        quote: true

      - name: vod_product_location
        description: "Location of the related MFL Competition"
        quote: true

      - name: contestant_ids
        description: "Array of IDs of the related MFL contestants"
        quote: true

      - name: sport_id
        description: "ID of the related MFL sport"
        quote: true

      - name: sport_name
        description: "Name of the related MFL sport"
        quote: true

      - name: vod_product_content_distinction_id
        description: "External reference of the relevant Content Distinction (aka Video Type), created using the COALESCE of the two contentDistinction_uuid and contentDistinctionReference fields, needed as there was a change of the field name"
        quote: true

      - name: vod_product_content_distinction_name
        description: "Name of the relevant Content Distinction (aka Video Type), created using the COALESCE of the two contentDistinction_name and contentDistinction fields, needed as there was a change of the field name"
        quote: true

      - name: vod_product_source_language
        description: "Audio language on the source video received by DAZN"
        quote: true

      - name: vod_product_has_closed_captions_available
        description: "Boolean value indicated if there are closed captions on the source video received by DAZN"
        quote: true

      - name: vod_product_closed_captions_language
        description: "Language of the closed captions provided on the source video received by DAZN"
        quote: true

      - name: vod_product_arrival_method
        description: "Method in which DAZN received the source video"
        quote: true

      - name: vod_product_synopsis
        description: "Short description of the non-live event"
        quote: true

      - name: vod_product_expected_duration
        description: "Expected duration of the live event"
        quote: true

      - name: vod_product_contractual_compliance
        description: "List of keys of prohibited ad types defined by rights compliance"
        quote: true

      - name: vod_right_title
        description: "Title of the linked product"
        quote: true

      - name: vod_right_start_date
        description: "Start date of the exploitation right"
        quote: true

      - name: vod_right_end_date
        description: "End date of the exploitation right"
        quote: true

      - name: vod_right_type
        description: "The type of the right E.g. OTT, ..."
        quote: true

      - name: vod_right_run_count
        description: "Number of runs (aka commitments) allowed for the given exploitation right"
        quote: true

      - name: vod_right_status
        description: "The status of the exploitation right "
        quote: true

      - name: tournament_calendar_id
        description: "ID of the related MFL tournament calendar"
        quote: true

      - name: contract_id
        description: "Unique id of this event in WON i.e. External Reference"
        quote: true

      - name: vod_right_has_ftv_allowed
        description: "Boolean value that indicates whether live events for the given right can be offered as 'free to view"
        quote: true

      - name: vod_right_has_download_allowed
        description: "Boolean value that indicates whether live events for the given right can be offered as 'downloadable"
        quote: true

      - name: vod_right_has_b2b_allowed
        description: "Boolean value indicating whether the rights are allowed for DAZN's B2B product"
        quote: true

      - name: vod_right_has_b2c_allowed
        description: "Boolean value indicating whether the rights are allowed for DAZN's B2C product"
        quote: true

      - name: vod_right_is_cleared_for_dazn_player
        description: "Boolean value that indicates if the content can be re-used on the DAZN Player product"
        quote: true

      - name: vod_right_is_cleared_for_social
        description: "Boolean value that indicates if the content can be re-used on social media"
        quote: true

      - name: vod_right_is_pre_recorded
        description: "Boolean value that indicates if the content has been pre-recorded"
        quote: true

      - name: vod_right_disallowed_country_codes
        description: "An array of countries blocked from consuming events for the given rights"
        quote: true

      - name: vod_right_allowed_audio_languages
        description: "An array of audio languages allowed to be applied against the event"
        quote: true

      - name: vod_right_allowed_country_codes
        description: "A list of countries allowed to consume events for the given rights"
        quote: true

      - name: vod_right_allowed_country_codes_0
        description: "The first value of the array in field allowedCountryCodes representing countries allowed to consume events for the given rights"
        quote: true

      - name: vod_right_allowed_country_codes_1
        description: "The second value of the array in field allowedCountryCodes representing countries allowed to consume events for the given rights"
        quote: true

      - name: vod_right_allowed_country_codes_2
        description: "The third value of the array in field allowedCountryCodes representing countries allowed to consume events for the given rights"
        quote: true

      - name: vod_right_non_exclusive_regions
        description: "A list of non exclusive regions for the given right"
        quote: true

      - name: vod_right_non_exclusive_regions_0
        description: "The first value of the array in field nonExclusiveRegions representing non exclusive regions for the given right"
        quote: true

      - name: vod_right_non_exclusive_regions_1
        description: "The second value of the array in field nonExclusiveRegions representing non exclusive regions for the given right"
        quote: true

      - name: vod_right_non_exclusive_regions_2
        description: "The third value of the array in field nonExclusiveRegions representing non exclusive regions for the given right"
        quote: true

      - name: contract_name
        description: "Name of the contract, created using the COALESCE of the two contractName and name fields, needed as there was a change of the field name"
        quote: true

      - name: contract_status
        description: "Status of the contract"
        quote: true

      - name: contract_start_date
        description: "Global start date of the contract"
        quote: true

      - name: contract_end_date
        description: "Global end date of the contract"
        quote: true

      - name: rights_holder_id
        description: "Reference ID of the contract's rights holder"
        quote: true

      - name: rights_holder_name
        description: "Name of the rights holder, created using the COALESCE of the two rightsHolderName and name fields, needed as there was a change of the field name"
        quote: true

      - name: vodevent_payload
        description: "Raw version of the full payload for the Live Event payload that can be used to explore exactly how the data has come through and see if any other new fields exist in the payload we have not brought through"
        quote: true

      - name: region_payload
        description: "Raw version of the full payload for the Live Event payload that can be used to explore exactly how the data has come through and see if any other new fields exist in the payload we have not brought through"
        quote: true

      - name: vodmedia_payload
        description: "Raw version of the full payload for the Live Event payload that can be used to explore exactly how the data has come through and see if any other new fields exist in the payload we have not brought through"
        quote: true

      - name: vodproduct_payload
        description: "Raw version of the full payload for the Live Event payload that can be used to explore exactly how the data has come through and see if any other new fields exist in the payload we have not brought through"
        quote: true

      - name: vodright_payload
        description: "Raw version of the full payload for the Live Event payload that can be used to explore exactly how the data has come through and see if any other new fields exist in the payload we have not brought through"
        quote: true

      - name: contract_payload
        description: "Raw version of the full payload for the Live Event payload that can be used to explore exactly how the data has come through and see if any other new fields exist in the payload we have not brought through"
        quote: true

      - name: rightsholder_payload
        description: "Raw version of the full payload for the Live Event payload that can be used to explore exactly how the data has come through and see if any other new fields exist in the payload we have not brought through"
        quote: true

      - name: region_has_failed_join
        description: "boolean flag indicating if the join condition failed"
        quote: true

      - name: vodmedia_has_failed_join
        description: "boolean flag indicating if the join condition failed"
        quote: true

      - name: vodproduct_has_failed_join
        description: "boolean flag indicating if the join condition failed"
        quote: true

      - name: vodright_has_failed_join
        description: "boolean flag indicating if the join condition failed"
        quote: true

      - name: contract_has_failed_join
        description: "boolean flag indicating if the join condition failed"
        quote: true

      - name: rightsholder_has_failed_join
        description: "boolean flag indicating if the join condition failed"
        quote: true

  - name: won_liveevent__dim
    description: "Dimension table from WON data. Contains live event data at fixture & region level."
    columns:
      - name: live_event_id
        description: "Unique id of this event in WON i.e. External Reference"
        quote: true

      - name: live_event_title
        description: "The title of the event"
        quote: true

      - name: dummy_fixture_id
        description: "ID that represents the dummy fixture if one is relevant for the scheduled transmission, created using the COALESCE of the two mflDaznDummyFixtureId and dummyFixtureUuid fields, needed as there was a change of the field name"
        quote: true

      - name: fixture_id
        description: "ID of the related MFL fixture directly associated to this live event, taking the dummy fixture ID if there is one"
        quote: true

      - name: region_id
        description: "ID that represents the DAZN region and MCS Product"
        quote: true

      - name: region_name
        description: "Name of the DAZN region"
        quote: true

      - name: region_territory
        description: "Name of DAZN region in sync with 'region_dimension' and 'TRANSIENT__PLAYS_2' tables"
        quote: true

      - name: region_ad_market
        description: "Region identifier uniquely used for advertising purposes"
        quote: true

      - name: region_m2a_key
        description: "Region identifier uniquely used by M2A"
        quote: true

      - name: region_countries
        description: "An array of countries associated with the region"
        quote: true

      - name: region_outlet
        description: "Name of the DAZN region modified to match outlet from conviva data."
        quote: true

      - name: live_event_status
        description: "The status of the regional transmission in WON"
        quote: true

      - name: live_event_start_timestamp
        description: "The UTC time of the Pre-KO of the event"
        quote: true

      - name: live_event_end_timestamp
        description: "The UTC time of the event end"
        quote: true

      - name: live_event_platform
        description: "Used to distinguish between scheduled events that are for the OTT platform and others"
        quote: true

      - name: live_event_type_name
        description: "Represents the TX Event Type used by Bookings for appropriate channel assignment E.g. TX50, PBB50, LTD50, OND59, TX59, DCI 50, DCI 59"
        quote: true

      - name: live_event_type
        description: "Used to distinguish a scheduled event as LIVE or ASLIVE (Delayed)"
        quote: true

      - name: live_event_broadcast_tier
        description: "The broadcast tier set against the event to indicate the level of production to applied by DAZN"
        quote: true

      - name: live_event_vob_key
        description: "Represents the voice over booth resources will be required for the event"
        quote: true

      - name: live_event_frame_rate
        description: "Indicates the frame rate in which the event will be delivered on the OTT product"
        quote: true

      - name: live_event_workflow_type
        description: "Represents the Alternative Workflow used by Planning to indicate a special scenario"
        quote: true

      - name: live_event_gallery_name
        description: "Indicates whether it is a gallery event requiring specific Gallery resources"
        quote: true

      - name: live_event_gallery_id
        description: "ID of the Gallery object"
        quote: true

      - name: live_event_schedule_version
        description: "Schedule version of Transmission from WON (Active, Alternative) - Alternative is used for pre-planning"
        quote: true

      - name: live_event_is_co_exclusive
        description: "Boolean represnting if DAZN shares exclusivity for the live event"
        quote: true

      - name: live_event_is_obligated
        description: "Surfaces whether transmission is obligated to be played"
        quote: true

      - name: live_event_has_ftv_allowed
        description: "Boolean value that indicates whether the live event can be offered as 'free to view"
        quote: true

      - name: live_event_has_ftv_catchup_allowed
        description: "Boolean value that indicates whether the catchup asset of the live event can be offered as 'free to view"
        quote: true

      - name: live_event_is_dci
        description: "Boolean value if event is dci or not"
        quote: true

      - name: live_event_dci_group_name
        description: "Represents the DCI group that the event forms"
        quote: true

      - name: live_event_closed_caption_languages
        description: "A map of one or more closed caption languages made available for this event keyed by ISO two-letter language codes"
        quote: true

      - name: live_event_is_world_feed
        description: "Indicates whether the World Feed commentary can be used or not"
        quote: true

      - name: live_event_commentary_languages_code
        description: "The first value from the commentaryLangs array representing the primary commentary language made available for this event"
        quote: true

      - name: live_event_world_feed_languages_code
        description: "The first value from the worldFeedLanguages array representing the primary world feed commentary language made available for this event"
        quote: true

      - name: primary_commentary_language
        description: "The primary commentary language derived for this event"
        quote: true

      - name: live_event_advertising_compliance_rules
        description: "Array representing the advertising compliance rules of the live event"
        quote: true

      - name: live_event_announced_start_timestamp
        description: "Announced time in UTC dropdown on transmission"
        quote: true

      - name: live_event_sponsoring
        description: "Represents the sponsoring field from Transmission"
        quote: true

      - name: live_event_ht_filler
        description: "Represents the HT Filler that needs to be planned for"
        quote: true

      - name: live_event_regional_supplier
        description: "Indicates that within a certain territory the content is actually being supplied by another rights holder"
        quote: true

      - name: live_encoding_id
        description: "Live encoding ID. unique per payload but not for allocation type. Used to map to live event table"
        quote: true

      - name: live_product_id
        description: "Reference to the  Live Product associated with this live event"
        quote: true

      - name: live_right_id
        description: "Reference ID to the WON exploitation rights used to schedule this event, created using the COALESCE of the two rightsId and rightId fields, needed as there was a change of the field name"
        quote: true

      - name: live_product_type
        description: "The type of product (E.g. Series, Episode, ...)"
        quote: true

      - name: competition_id
        description: "ID of the related MFL competition"
        quote: true

      - name: live_product_ruleset_name
        description: "Name of the related MFL ruleset"
        quote: true

      - name: tournament_calendar_id
        description: "ID of the related MFL tournament calendar"
        quote: true

      - name: sport_id
        description: "ID of the related MFL sport"
        quote: true

      - name: sport_name
        description: "Name of the related MFL sport"
        quote: true

      - name: live_product_support_tier
        description: "Support Tier from WON"
        quote: true

      - name: live_product_order_of_play
        description: "Order of Play from WON"
        quote: true

      - name: live_product_expected_duration
        description: "Expected duration of the live event"
        quote: true

      - name: live_product_contractual_compliance
        description: "List of keys of prohibited ad types defined by rights compliance"
        quote: true

      - name: live_right_title
        description: "Title of the linked product"
        quote: true

      - name: live_right_start_date
        description: "Start date of the exploitation right, created using the COALESCE of the two startDate and startDateFormula fields, needed as there was a change of the field name"
        quote: true

      - name: live_right_end_date
        description: "End date of the exploitation right, created using the COALESCE of the two endDate and endDateFormula fields, needed as there was a change of the field name"
        quote: true

      - name: live_right_type
        description: "The type of the right E.g. OTT, ..."
        quote: true

      - name: live_right_planned_fixtures
        description: "Number of runs (aka commitments) allowed for the given exploitation right"
        quote: true

      - name: live_right_status
        description: "The status of the exploitation right "
        quote: true

      - name: live_right_is_live
        description: "Boolean value which indicates if the exploitation right applies to LIVE events"
        quote: true

      - name: live_right_is_as_live
        description: "Boolean value which indicates if the exploitation right applies to ASLIVE (Delayed) events"
        quote: true

      - name: live_right_has_download_allowed
        description: "Boolean value that indicates whether live events for the given right can be offered as 'downloadable"
        quote: true

      - name: live_right_has_b2c_allowed
        description: "Boolean value indicating whether the rights are allowed for DAZN's B2C product"
        quote: true

      - name: live_right_has_b2b_allowed
        description: "Boolean value indicating whether the rights are allowed for DAZN's B2B product"
        quote: true

      - name: live_right_has_b2b_catchup_allowed
        description: "Boolean value indicating whether the rights are allowed on DAZN's B2B product as catchup content"
        quote: true

      - name: live_right_allowed_country_codes
        description: "A array of countries allowed to consume events for the given rights"
        quote: true

      - name: live_right_disallowed_country_codes
        description: "An array of countries blocked from consuming events for the given rights"
        quote: true

      - name: live_right_non_exclusive_regions
        description: "An array of non exclusive regions for the given right"
        quote: true

      - name: live_right_exclusivity_flag
        description: "Category defining content exclusivity for an event across regions"
        quote: true

      - name: live_right_allowed_audio_languages
        description: "An array of audio languages allowed to be applied against the event"
        quote: true

      - name: contract_id
        description: "Unique id of this event in WON i.e. External Reference"
        quote: true

      - name: contract_name
        description: "Name of the contract, created using the COALESCE of the two contractName and name fields, needed as there was a change of the field name"
        quote: true

      - name: contract_status
        description: "Status of the contract"
        quote: true

      - name: contract_start_date
        description: "Global start date of the contract"
        quote: true

      - name: contract_end_date
        description: "Global end date of the contract"
        quote: true

      - name: rights_holder_id
        description: "Reference ID of the contract's rights holder"
        quote: true

      - name: rights_holder_name
        description: "Name of the rights holder, created using the COALESCE of the two rightsHolderName and name fields, needed as there was a change of the field name"
        quote: true

      - name: liveevent_payload
        description: "Raw version of the full payload for the Live Event payload that can be used to explore exactly how the data has come through and see if any other new fields exist in the payload we have not brought through"
        quote: true

      - name: region_payload
        description: "Raw version of the full payload for the Live Event payload that can be used to explore exactly how the data has come through and see if any other new fields exist in the payload we have not brought through"
        quote: true

      - name: liveproduct_payload
        description: "Raw version of the full payload for the Live Event payload that can be used to explore exactly how the data has come through and see if any other new fields exist in the payload we have not brought through"
        quote: true

      - name: liveright_payload
        description: "Raw version of the full payload for the Live Event payload that can be used to explore exactly how the data has come through and see if any other new fields exist in the payload we have not brought through"
        quote: true

      - name: contract_payload
        description: "Raw version of the full payload for the Live Event payload that can be used to explore exactly how the data has come through and see if any other new fields exist in the payload we have not brought through"
        quote: true

      - name: rightsholder_payload
        description: "Raw version of the full payload for the Live Event payload that can be used to explore exactly how the data has come through and see if any other new fields exist in the payload we have not brought through"
        quote: true

      - name: region_has_failed_join
        description: "boolean flag indicating if the join condition failed"
        quote: true

      - name: liveproduct_has_failed_join
        description: "boolean flag indicating if the join condition failed"
        quote: true

      - name: liveright_has_failed_join
        description: "boolean flag indicating if the join condition failed"
        quote: true

      - name: contract_has_failed_join
        description: "boolean flag indicating if the join condition failed"
        quote: true

      - name: rightsholder_has_failed_join
        description: "boolean flag indicating if the join condition failed"
        quote: true

      - name: live_event_regional_supplier_id
        description: "Indicates that within a certain territory the content is actually being supplied by another rights holder - only ID"
        quote: true

      - name: live_event_cc_languages_code
        description: "A map of one or more closed caption languages made available for this event keyed by ISO two-letter language codes"
        quote: true

      - name: live_event_cc_supplier_details
        description: "Supplier id and name of the closed caption language providers"
        quote: true

  - name: mfl_fixture__dim
    description: "Dimension table from MFL data at fixture level."
    columns:
      - name: fixture_id
        description: "ID that represents the fixture associated to the scheduled transmission"
        quote: true

      - name: fixture_description
        description: "The description of the MFL Fixture"
        quote: true

      - name: fixture_start_timestamp
        description: "The timestamp of the start of the MFL Fixture, constructed using the combincation of the date and time fields"
        quote: true

      - name: fixture_status
        description: "The status of the MFL Fixture"
        quote: true

      - name: sport_id
        description: "ID of the related MFL sport"
        quote: true

      - name: sport_name
        description: "Name of the related MFL sport"
        quote: true

      - name: competition_id
        description: "ID of the related MFL competition"
        quote: true

      - name: competition_name
        description: "Name of the related MFL competition, created using the COALESCE of the two competition_name and competitionName fields, needed as there was a change of the field name"
        quote: true

      - name: competition_country_id
        description: "ID of the country in which the competition is held"
        quote: true

      - name: competition_country_name
        description: "Name of the country in which the competition is held"
        quote: true

      - name: tournament_calendar_id
        description: "ID of the related MFL tournament calendar"
        quote: true

      - name: tournament_calendar_name
        description: "The name of the Tournament Calendar"
        quote: true

      - name: tournament_calendar_start_date
        description: "The date of the start of the Tournament Calendar"
        quote: true

      - name: tournament_calendar_end_date
        description: "The date of the end of the Tournament Calendar"
        quote: true

      - name: ruleset_id
        description: "The UUID of the ruleset directly relating to the MFL Competition"
        quote: true

      - name: ruleset_name
        description: "The name of the ruleset in MFL E.g. Mens, Womens, Juniors, ..."
        quote: true

      - name: stage_id
        description: "The UUID of the Stage in MFL"
        quote: true

      - name: stage_name
        description: "The name of the Stage in MFL E.g. Final, Semi-final, Group, ..."
        quote: true

      - name: stage_start_date
        description: "The date of the start of the Stage"
        quote: true

      - name: stage_end_date
        description: "The date of the end of the Stage"
        quote: true

      - name: venue_id
        description: "The UUID of the Venue in MFL"
        quote: true

      - name: venue_name_short
        description: "The short name of the venue in MFL"
        quote: true

      - name: venue_name_long
        description: "The long name of the venue in MFL"
        quote: true

      - name: soccer_match_day
        description: "Refers to a single round of games where team participates, which might occur over the span of few days"
        quote: true

      - name: contestant_0_id
        description: "ID of the 1st contestant in the payload"
        quote: true

      - name: contestant_0_name
        description: "Name of the 1st contestant in the payload"
        quote: true

      - name: contestant_0_country_name
        description: "Country of the 1st contestant in the payload"
        quote: true

      - name: contestant_1_id
        description: "ID of the 2nd contestant in the payload"
        quote: true

      - name: contestant_1_name
        description: "Name of the 2nd contestant in the payload"
        quote: true

      - name: contestant_1_country_name
        description: "Country of the 2nd contestant in the payload"
        quote: true

      - name: contestant_2_id
        description: "ID of the 3rd contestant in the payload"
        quote: true

      - name: contestant_2_name
        description: "Name of the 3rd contestant in the payload"
        quote: true

      - name: contestant_2_country_name
        description: "Country of the 3rd contestant in the payload"
        quote: true

      - name: contestant_3_id
        description: "ID of the 4th contestant in the payload"
        quote: true

      - name: contestant_3_name
        description: "Name of the 4th contestant in the payload"
        quote: true

      - name: contestant_3_country_name
        description: "Country of the 4th contestant in the payload"
        quote: true

      - name: contestant_4_id
        description: "ID of the 5th contestant in the payload"
        quote: true

      - name: contestant_4_name
        description: "Name of the 5th contestant in the payload"
        quote: true

      - name: contestant_4_country_name
        description: "Country of the 5th contestant in the payload"
        quote: true

      - name: contestant_5_id
        description: "ID of the 6th contestant in the payload"
        quote: true

      - name: contestant_5_name
        description: "Name of the 6th contestant in the payload"
        quote: true

      - name: contestant_5_country_name
        description: "Country of the 6th contestant in the payload"
        quote: true

      - name: fixture_payload
        description: "payload of this specific mfl dataset, used for exploration"
        quote: true

      - name: sport_payload
        description: "payload of this specific mfl dataset, used for exploration"
        quote: true

      - name: competition_payload
        description: "payload of this specific mfl dataset, used for exploration"
        quote: true

      - name: competition_country_payload
        description: "payload of this specific mfl dataset, used for exploration"
        quote: true

      - name: tournament_payload
        description: "payload of this specific mfl dataset, used for exploration"
        quote: true

      - name: ruleset_payload
        description: "payload of this specific mfl dataset, used for exploration"
        quote: true

      - name: stage_payload
        description: "payload of this specific mfl dataset, used for exploration"
        quote: true

      - name: venue_payload
        description: "payload of this specific mfl dataset, used for exploration"
        quote: true

      - name: sport_has_failed_join
        description: "Boolean flag indicating if the join to this table failed"
        quote: true

      - name: competition_has_failed_join
        description: "Boolean flag indicating if the join to this table failed"
        quote: true

      - name: competition_country_has_failed_join
        description: "Boolean flag indicating if the join to this table failed"
        quote: true

      - name: tournament_has_failed_join
        description: "Boolean flag indicating if the join to this table failed"
        quote: true

      - name: ruleset_has_failed_join
        description: "Boolean flag indicating if the join to this table failed"
        quote: true

      - name: stage_has_failed_join
        description: "Boolean flag indicating if the join to this table failed"
        quote: true

      - name: venue_has_failed_join
        description: "Boolean flag indicating if the join to this table failed"
        quote: true

      - name: contestant_0_has_failed_join
        description: "Boolean flag indicating if the join to this table failed"
        quote: true

      - name: contestant_1_has_failed_join
        description: "Boolean flag indicating if the join to this table failed"
        quote: true

      - name: contestant_2_has_failed_join
        description: "Boolean flag indicating if the join to this table failed"
        quote: true

      - name: contestant_3_has_failed_join
        description: "Boolean flag indicating if the join to this table failed"
        quote: true

      - name: contestant_4_has_failed_join
        description: "Boolean flag indicating if the join to this table failed"
        quote: true

      - name: contestant_5_has_failed_join
        description: "Boolean flag indicating if the join to this table failed"
        quote: true

  - name: gmr_article__dim
    description: "GMR article dimension. From DOTS data source"
    columns:
      - name: DBT_INSERT_DTTS
        description: "Date/Timestamp the record was inserted by dbt to the curated layer"
        quote: true

      - name: EDM_INSERT_DTTS
        description: "Date/Timestamp the record was inserted by the ingest process into the open layer"
        quote: true

      - name: payload_id
        description: "A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message."
        quote: true

      - name: payload_type
        description: "A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers."
        quote: true

      - name: payload_state
        description: "An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?"
        quote: true

      - name: payload
        description: "An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer."
        quote: true

      - name: last_updated_timestamp
        description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
        quote: true

      - name: is_test
        description: "This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false."
        quote: true

      - name: envelope_schema_version
        description: "The version of the envelope schema. This version must be 2."
        quote: true

      - name: article_id
        description: "The ID of the article"
        quote: true

      - name: article_entity
        description: "TBC"
        quote: true

      - name: article_action
        description: "TBC"
        quote: true

      - name: article_updated_timestamp
        description: "The last time the article was updated (converted to a TIMESTAMP)"
        quote: true

      - name: article_title
        description: "The title of the article"
        quote: true

      - name: article_start_timestamp
        description: "The start time of the article (converted to a TIMESTAMP)"
        quote: true

      - name: article_end_timestamp
        description: "The end time of the article (converted to a TIMESTAMP)"
        quote: true

      - name: article_aggregated_start_timestamp
        description: "TBC"
        quote: true

      - name: article_publish_timestamp
        description: "TBC"
        quote: true

      - name: article_unpublish_timestamp
        description: "TBC"
        quote: true

      - name: article_territory
        description: "The territory of the article"
        quote: true

      - name: article_country
        description: "TBC"
        quote: true

      - name: article_country_title_localised
        description: "TBC"
        quote: true

      - name: article_description
        description: "The description of the article"
        quote: true

      - name: article_type
        description: "Array containing metadata about the type of the article"
        quote: true

      - name: article_type_id
        description: "The guid of the type of the article"
        quote: true

      - name: article_type_name
        description: "The name of the type of the article"
        quote: true

      - name: competition
        description: "An array containing metadata about the competition directly relating to the article, if any"
        quote: true

      - name: competition_id
        description: "The ID of the competition directly relating to the article, if any"
        quote: true

      - name: competition_title_localised
        description: "The localised title of the competition directly relating to the article, if any"
        quote: true

      - name: contestants
        description: "An array containing metadata about the contestants directly relating to the article, if any"
        quote: true

      - name: contestant_0_id
        description: "The ID of the first contestant in the contestants array, if any"
        quote: true

      - name: contestant_0_title_localised
        description: "The localised title of the first contestant in the contestants array, if any"
        quote: true

      - name: contestant_1_id
        description: "The ID of the second contestant in the contestants array, if any"
        quote: true

      - name: contestant_1_title_localised
        description: "The localised second of the first contestant in the contestants array, if any"
        quote: true

      - name: contestant_2_id
        description: "The ID of the third contestant in the contestants array, if any"
        quote: true

      - name: contestant_2_title_localised
        description: "The localised third of the first contestant in the contestants array, if any"
        quote: true

      - name: fixture
        description: "An array containing metadata about the fixture directly relating to the article, if any"
        quote: true

      - name: fixture_id
        description: "The ID of the fixture directly relating to the article, if any (will match to the MFL Fixture ID)"
        quote: true

      - name: fixture_start_timestamp
        description: "The unixtimestamp of the start of the fixture directly relating to the article, if any"
        quote: true

      - name: fixture_status
        description: "The status of the fixture directly relating to the article, if any"
        quote: true

      - name: fixture_title_localised
        description: "The localised title of the fixture directly relating to the article, if any"
        quote: true

      - name: sport
        description: "An array containing metadata about the sport directly relating to the article, if any"
        quote: true

      - name: sport_id
        description: "The ID of the sport directly relating to the article, if any (will match to the MFL Sport ID)"
        quote: true

      - name: sport_title_localised
        description: "The localised title of the sport directly relating to the article, if any"
        quote: true

      - name: ruleset
        description: "An array containing metadata about the ruleset directly relating to the article, if any"
        quote: true

      - name: ruleset_id
        description: "The ID of the ruleset directly relating to the article, if any (will match to the MFL Ruleset ID)"
        quote: true

      - name: ruleset_title_localised
        description: "The localised title of the ruleset directly relating to the article, if any"
        quote: true

      - name: tournament
        description: "An array containing metadata about the tournament directly relating to the article, if any"
        quote: true

      - name: tournament_id
        description: "The ID of the tournament directly relating to the article, if any (will match to the MFL Tournament Calendar ID)"
        quote: true

      - name: tournament_title_localised
        description: "The localised title of the tournament directly relating to the article, if any"
        quote: true

      - name: tournament_start_timestamp
        description: "The converted timestamp of the start of the tournament directly relating to the article, if any"
        quote: true

      - name: tournament_end_timestamp
        description: "The converted timestamp of the end of the tournament directly relating to the article, if any"
        quote: true

      - name: venue
        description: "An array containing metadata about the venue directly relating to the article, if any"
        quote: true

      - name: venue_id
        description: "The ID of the venue directly relating to the article, if any (will match to the MFL Venue ID)"
        quote: true

      - name: venue_title_localised
        description: "The localised title of the venue directly relating to the article, if any"
        quote: true

      - name: stage
        description: "An array containing metadata about the stage directly relating to the article, if any"
        quote: true

      - name: stage_id
        description: "The ID of the stage directly relating to the article, if any (will match to the MFL Stage ID)"
        quote: true

      - name: stage_title_localised
        description: "The localised title of the stage directly relating to the article, if any"
        quote: true

      - name: stage_start_timestamp
        description: "The timestamp of the start of the stage directly relating to the article, if any"
        quote: true

      - name: stage_end_timestamp
        description: "The timestamp of the start of the stage directly relating to the article, if any"
        quote: true

      - name: live_media_dazn_channel_id
        description: "TBC"
        quote: true

      - name: live_media_fixture
        description: "TBC"
        quote: true

      - name: live_media_live_stream_start_timestamp
        description: "TBC"
        quote: true

      - name: live_media_live_stream_end_timestamp
        description: "TBC"
        quote: true

      - name: live_media_outlet_id
        description: "TBC"
        quote: true

      - name: live_media_title
        description: "TBC"
        quote: true

      - name: live_media_tx_event_type_name
        description: "TBC"
        quote: true

      - name: live_media_type
        description: "TBC"
        quote: true

      - name: live_media_video_type
        description: "TBC"
        quote: true

      - name: product_value
        description: "TBC"
        quote: true

      - name: product_value_weight
        description: "TBC"
        quote: true

      - name: article_entitlement_ids
        description: "The entitlement IDs needed to watch the article"
        quote: true

      - name: article_language
        description: "TBC"
        quote: true

      - name: article_categories
        description: "TBC"
        quote: true

      - name: article_country_availability
        description: "An array of countries this article is available to watch in"
        quote: true

      - name: related_article_id
        description: "TBC"
        quote: true

      - name: article_is_age_restricted
        description: "Boolean, true if the article is age restricted"
        quote: true

      - name: article_is_hide_in_ui
        description: "Boolean, true if the article is hidden in the UI/platform"
        quote: true

      - name: article_images
        description: "An array containing metadata about the images on the article"
        quote: true

      - name: article_media_id
        description: "Article media id"
        quote: true

      - name: is_content_freemium
        description: "Boolean, true if the article is freemium"
        quote: true

      - name: is_content_b2b
        description: "Boolean, true if the article is b2b"
        quote: true

      - name: is_content_b2c
        description: "Boolean, true if the article is b2c"
        quote: true

      - name: is_content_linear
        description: "Boolean, true if the article is linear"
        quote: true

      - name: article_format
        description: "article formate wethere it's Short Highlights or Condensed or NA"
        quote: true

      - name: is_play4free
        description: "Boolean, true if the article is play4free"
        quote: true
