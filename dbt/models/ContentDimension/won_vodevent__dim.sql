{{
    config(
        materialized='table',
        database='MART_DIMENSIONS__B2C__DOMAIN__' + snowflake_env(),
        schema='CONTENT',
        tags=['presentation-content-dimension']
    )
}}

WITH vodevent AS (
    SELECT * FROM {{ ref('staging__dots__won_vodevent_current') }}
)

,region AS (
    SELECT * FROM {{ ref('staging__dots__won_region_current') }}
)

,vodproduct AS (
    SELECT * FROM {{ ref('staging__dots__won_vodproduct_current') }}
)

,vodright AS (
    SELECT * FROM {{ ref('staging__dots__won_vodright_current') }}
)

,vodmedia AS (
    SELECT * FROM {{ ref('staging__dots__won_vodmedia_current') }}
)

,contract AS (
    SELECT * FROM {{ ref('staging__dots__won_contract_current') }}
)

,rightsholder AS (
    SELECT * FROM {{ ref('staging__dots__won_rightsholder_current') }}
)

SELECT
    vodevent."vod_event_id"
    ,vodevent."vod_event_title"
    ,vodevent."vod_event_status"
    ,vodevent."vod_event_start_timestamp"
    ,vodevent."vod_event_end_timestamp"
    ,vodevent."vod_event_is_obligated"
    ,vodevent."vod_event_has_ftv_allowed"
    ,vodevent."vod_event_advertising"
    ,vodevent."vod_event_duration"
    ,vodevent."vod_event_schedule_version"
    ,vodevent."region_id"
    ,region."region_name"
    -- "updating the region_name to be in sync with 'region_dimension' and 'TRANSIENT__PLAYS_2' tables"
    ,CASE
        WHEN region."region_name" = 'dach' THEN 'DACH'
        WHEN region."region_name" = 'gb' THEN 'UK and Ireland'
        WHEN region."region_name" = 'usa' THEN 'United States'
        WHEN region."region_name" = 'row' THEN 'Global'
        ELSE initcap(region."region_name")
    END AS "region_territory"
    ,region."region_default_timezone"
    ,region."region_ad_market"
    ,region."region_organisation_id"
    ,region."region_outlet_key"
    ,region."region_m2a_key"
    ,region."region_countries"
    ,vodevent."vod_media_id"
    ,vodmedia."vod_media_label"
    ,vodmedia."vod_media_old_file_name"
    ,vodmedia."vod_media_team_assigned"
    ,vodmedia."vod_media_has_reversion_required"
    ,vodmedia."vod_media_has_closed_captions_required"
    ,vodmedia."vod_media_audio_languages"
    ,vodmedia."vod_media_audio_languages_value"
    ,vodevent."vod_product_id"
    ,vodproduct."vod_product_title"
    ,vodproduct."vod_product_type"
    ,vodproduct."fixture_id"
    ,vodproduct."vod_product_version"
    ,vodproduct."competition"
    ,vodproduct."competition_id"
    ,vodproduct."competition_name"
    ,vodproduct."vod_product_ruleset_name"
    ,vodproduct."vod_product_location"
    ,vodproduct."contestant_ids"
    ,vodproduct."sport_id"
    ,vodproduct."sport_name"
    ,vodproduct."vod_product_content_distinction_id"
    ,vodproduct."vod_product_content_distinction_name"
    ,vodproduct."vod_product_source_language"
    ,vodproduct."vod_product_has_closed_captions_available"
    ,vodproduct."vod_product_closed_captions_language"
    ,vodproduct."vod_product_arrival_method"
    ,vodproduct."vod_product_synopsis"
    ,vodproduct."vod_product_expected_duration"
    ,vodproduct."vod_product_contractual_compliance"
    ,vodevent."vod_right_id"
    ,vodright."vod_right_title"
    ,vodright."vod_right_start_date"
    ,vodright."vod_right_end_date"
    ,vodright."vod_right_type"
    ,vodright."vod_right_run_count"
    ,vodright."vod_right_status"
    ,vodright."tournament_calendar_id"
    ,vodright."vod_right_has_ftv_allowed"
    ,vodright."vod_right_has_download_allowed"
    ,vodright."vod_right_has_b2b_allowed"
    ,vodright."vod_right_has_b2c_allowed"
    ,vodright."vod_right_is_cleared_for_dazn_player"
    ,vodright."vod_right_is_cleared_for_social"
    ,vodright."vod_right_is_pre_recorded"
    ,vodright."vod_right_disallowed_country_codes"
    ,vodright."vod_right_allowed_audio_languages"
    ,vodright."vod_right_allowed_country_codes"
    ,vodright."vod_right_allowed_country_codes_0"
    ,vodright."vod_right_allowed_country_codes_1"
    ,vodright."vod_right_allowed_country_codes_2"
    ,vodright."vod_right_non_exclusive_regions"
    ,vodright."vod_right_non_exclusive_regions_0"
    ,vodright."vod_right_non_exclusive_regions_1"
    ,vodright."vod_right_non_exclusive_regions_2"
    ,vodright."contract_id"
    ,contract."contract_name"
    ,contract."contract_status"
    ,contract."contract_start_date"
    ,contract."contract_end_date"
    ,contract."rights_holder_id"
    ,rightsholder."rights_holder_name"
    -- Payload
    ,vodevent."payload" AS "vodevent_payload"
    ,region."payload" AS "region_payload"
    ,vodmedia."payload" AS "vodmedia_payload"
    ,vodproduct."payload" AS "vodproduct_payload"
    ,vodright."payload" AS "vodright_payload"
    ,contract."payload" AS "contract_payload"
    ,rightsholder."payload" AS "rightsholder_payload"
    -- IDs
    ,region."region_id" IS NULL AND vodevent."region_id" IS NOT NULL AS "region_has_failed_join"
    ,vodmedia."vod_media_id" IS NULL AND vodevent."vod_media_id" IS NOT NULL AS "vodmedia_has_failed_join"
    ,vodproduct."vod_product_id" IS NULL AND vodevent."vod_product_id" IS NOT NULL AS "vodproduct_has_failed_join"
    ,vodright."vod_right_id" IS NULL AND vodevent."vod_right_id" IS NOT NULL AS "vodright_has_failed_join"
    ,contract."contract_id" IS NULL AND vodright."contract_id" IS NOT NULL AS "contract_has_failed_join"
    ,rightsholder."rights_holder_id" IS NULL AND contract."rights_holder_id" IS NOT NULL AS "rightsholder_has_failed_join"
FROM vodevent
LEFT JOIN region
    ON vodevent."region_id" = region."region_id"
LEFT JOIN vodmedia
    ON vodevent."vod_media_id" = vodmedia."vod_media_id"
LEFT JOIN vodright
    ON vodevent."vod_right_id" = vodright."vod_right_id"
LEFT JOIN vodproduct
    ON vodevent."vod_product_id" = vodproduct."vod_product_id"
LEFT JOIN contract
    ON contract."contract_id" = vodright."contract_id"
LEFT JOIN rightsholder
    ON contract."rights_holder_id" = rightsholder."rights_holder_id"
WHERE vodevent."payload_state" = 'ACTIVE'
