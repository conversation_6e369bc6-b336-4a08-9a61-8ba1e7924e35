version: 2


models:
  - name: sky_addon_reconciliation
    description: >
      "This table shows all USER PARTNER IDs that are present in either ZUORA or in the daily file SKY provides.
      The file contains all IDs that were active on the day of the file generation (snapshot_ts).
      The table will collect information such as last seen date from either source and use it to categorise each ID in
      one of the following categories:
        - dazn delay: There's a potential delay in the DAZN source. This record might be correctly updated the next day
        - missing in dazn: The ID is missing in DAZN (Zuora)
        - sky delay: There's a potential delay in the SKY source. This record might be correctly updated the next day
        - missing in sky: The ID is missing in SKY
        - dazn needs to cancel: The ID disappeared from the SKY file, DAZN needs to cancel it
        - sky needs to cancel: The Addon is no longer active in DAZN (Zuora), SKY needs to cancel it
        - addon not active yet: When the addon was created but the start date is in the future. Could explain why the SKY side is NULL
        - aligned: The IDs are aligned between the two sources
        - error: Non of the above applies

      The severity is a numerical value assigned to each of these categories, in ascending order of importance. Useful
      to order this table by and surface important records at the top.
      "
    columns:
      - name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the model"
        quote: true

      - name: partner_user_id
        description: "The ID to identify the user between dazn and sky. From the rateplan field RatePlan.PartnerUserId__c"
        quote: true

      - name: billing_account_id
        description: "The Zuora Account ID coming from the Zuora Account entity E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: sky_activation
        description: "The date when this ID was activated in SKY"
        quote: true

      - name: sky_first_seen
        description: "The first date when this ID appeared in the SKY file"
        quote: true

      - name: sky_last_seen
        description: "The last date when this ID appeared in the SKY file"
        quote: true

      - name: subscription_add_on_effective_start_date
        description: "The date that this RatePlanCharge is effective from"
        quote: true

      - name: subscription_add_on_effective_end_date
        description: "The date that this RatePlanCharge is effective until, overwritten as 1 month after the from for OneTime AddOn charges"
        quote: true

      - name: rateplan_charge_charge_type
        description: "The charge type of this add on."
        quote: true

      - name: category
        description: "The category assigned to this ID. See the categories in the table description"
        quote: true

      - name: severity
        description: "The level of severity assigned to each of the categories above, useful to order by this table."
        quote: true
