{{
    config(
        materialized='table',
        transient=false,
        schema='PRESENTATION',
        tags=['presentation-sky-addon-reconciliation']
    )
}}

-- 2022-08-11: Important: all timestamp received from sky are in local time. not in UTC. We will ask them to change this to
-- UTC before change of timezones affect our pipelines.
-- The file received from SKY is also named with UTC. For example the file we receive at 22:00 on the 9th of august will
-- be called 20220810 on sky side because they are two hours ahead.

-- Note: the airflow batch date variable will be set to the previous day compared to when the task runs.
-- ie: if a task runs at 01:00 on 2022-08-10, the batch_date will be set to 2022-08-09

-- Because we receive the sky file after midnight, the snapshot timestamp will be set to the same-day-date
-- Therefore we need to allow for one extra day compared to our batch date.
-- ie: file lands at 00:30 on the 10th, task runs at 01:00 on the 10th, batch date is set to the 9th, file's snapshot
-- timestamp will be the 10th.
WITH sky_source AS (
    SELECT
        "sky_contract_id"
        ,DATE_TRUNC(DAY,"last_activated_at_timestamp") AS "sky_activation_date"
        ,DATE_TRUNC(DAY,"snapshot_timestamp") AS "sky_snapshot_date"
    FROM {{ source_env('LINEAR_ADD_ON_SUBSCRIBERS__DAILY', 'DAILY_ACTIVE_USER') }}
    WHERE "sky_snapshot_date" <= DATEADD(DAY,1,'{{ var('batch_date') }}')
)

-- The ZUORA pipeline will only collect data from the previous day. The vast majority of updated records should
-- have a timestamp equal to the batch date set by airflow.
, subscription_add_on__dim AS (
    SELECT * FROM {{ ref_env('subscription_add_on__dim') }}
    WHERE DATE_TRUNC(day,"rateplan_created_timestamp") <= '{{ var('batch_date') }}'
)

-- For each ID, activation TS we calculate the first and last seen snapshot timestamp.
, sky_last_seen AS (
    SELECT
        "sky_contract_id"
        ,"sky_activation_date"
        ,MIN("sky_snapshot_date") AS "sky_first_seen_date"
        ,MAX("sky_snapshot_date") AS "sky_last_seen_date"
    FROM sky_source
    GROUP BY 1,2
)

-- An ID might appear with multiple activation dates (if they buy the addon twice) The qualify ensures we only keep the latest one
, sky_deduped AS (
    SELECT * FROM sky_last_seen
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "sky_contract_id" ORDER BY "sky_activation_date" DESC) = 1
)

-- Similar to above, if we get multiple rows for the same ID we need to keep only one.
-- If there's an active addon, we'll take that one by selecting the addon where the batch date is between the start and end dates
, active_addons AS (
    SELECT * FROM subscription_add_on__dim
    WHERE '{{ var('batch_date') }}'::DATE BETWEEN "subscription_add_on_effective_start_date" AND "subscription_add_on_effective_end_date"
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "rateplan_partner_user_id" ORDER BY "subscription_add_on_effective_start_date" DESC) = 1
)

-- If there's no active addon, we'll take the latest one according to the addon's effective start date.
, remaining_addons AS (
    SELECT * FROM subscription_add_on__dim
    WHERE "rateplan_partner_user_id" NOT IN (select distinct "rateplan_partner_user_id" from active_addons)
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "rateplan_partner_user_id" ORDER BY "subscription_add_on_effective_start_date" DESC) = 1
)

-- we bring both sets together
, dazn_deduped AS (
    SELECT * FROM active_addons
    UNION
    SELECT * FROM remaining_addons
)

-- The step by step logic is defined below.
, logic AS (
    SELECT
        COALESCE(sky_deduped."sky_contract_id",dazn_deduped."rateplan_partner_user_id") AS "partner_user_id"
        ,dazn_deduped."billing_account_id"
        ,sky_deduped."sky_activation_date"
        ,sky_deduped."sky_first_seen_date"
        ,sky_deduped."sky_last_seen_date"
        ,dazn_deduped."subscription_add_on_effective_start_date"
        ,dazn_deduped."subscription_add_on_effective_end_date"
        ,dazn_deduped."rateplan_charge_charge_type"
        -- if the sky last seen date is before the batch date, we should assume that the addon is no longer active in SKY
        ,CASE WHEN sky_deduped."sky_last_seen_date" <= '{{ var('batch_date') }}' THEN 'expired' ELSE 'active' END AS "sky_status"
        -- if zuora's addon effective end date is before the batch date, we should assume that the addon is no longer active in DAZN
        ,CASE WHEN dazn_deduped."subscription_add_on_effective_end_date" <= '{{ var('batch_date') }}' THEN 'expired' ELSE 'active' END AS "dazn_status"
        -- using the information above we categorise each row.
        -- the high level logic is that we check for nulls on either side first, then we start looking at start/end dates to determine if the source are aligned or not
        ,CASE
            -- if there's no information in zuora, but the activation on sky side happened on the batch day or after, chances are that zuora will pick this addon up with tonight's run.
            WHEN dazn_deduped."rateplan_partner_user_id" IS NULL AND sky_deduped."sky_activation_date" >= '{{ var('batch_date') }}' THEN 'dazn delay'
            -- if there's still no information in zuora the day after.. we know there's an addon in SKY that is missing from DAZN.
            WHEN dazn_deduped."rateplan_partner_user_id" IS NULL THEN 'missing in dazn'
            -- If for some reason, the addon shows AS being created on the batch date in DAZN but it's missing in SKY, we could wait a day more to account for any delay on SKY side.
            WHEN sky_deduped."sky_contract_id" IS NULL AND dazn_deduped."subscription_add_on_effective_start_date" = '{{ var('batch_date') }}' THEN 'sky delay'
            -- If the start date is missing on the sky side and starting after the batch date, given it's not active we mark it as such regardless of sky's status
            WHEN sky_deduped."sky_contract_id" IS NULL AND dazn_deduped."subscription_add_on_effective_start_date" > '{{ var('batch_date') }}' THEN 'addon not active yet'
            -- If the ID exists on the sky side but the start date of the add on is in the future then it's likely to be an addon in pause. We'll mark it as such.
            -- Note, paused addons generate two RPCs linked to the same rateplan, we will only consider the latest-starting one when applying this logic, the original start date of the addon is therefore lost.
            WHEN sky_deduped."sky_contract_id" IS NOT NULL AND dazn_deduped."subscription_add_on_effective_start_date" > '{{ var('batch_date') }}' THEN 'user is paused'
            -- If we still don't have information on SKY side, then it's missing in sky
            WHEN sky_deduped."sky_contract_id" IS NULL THEN 'missing in sky'
            -- If the addon is no longer active in SKY but is still active in DAZN, DAZN needs to cancel
            WHEN "sky_status" = 'expired' AND "dazn_status" = 'active' THEN 'dazn needs to cancel'
            -- If the addon is no longer active in DAZN but is still active in SKY, SKY needs to cancel
            WHEN "sky_status" = 'active' AND "dazn_status" = 'expired' THEN 'sky needs to cancel'
            -- If the addons are both active or both expired THEN we're aligned with SKY
            WHEN "sky_status" = "dazn_status" THEN 'aligned'
            -- Anything else, shouldn't happen, is a potential bug and should be addressed
            ELSE 'error'
        END AS "category_temp"
        -- Depending on the category defined above we assign a severity ranking.
        ,CASE
            -- for these categories where an action is required the severity indicates how many days the addon has remained incorrectly active for (in either side)
            WHEN "category_temp" = 'dazn needs to cancel' THEN DATEDIFF(DAY,sky_deduped."sky_last_seen_date", '{{ var('batch_date') }}')
            -- Following those that we think dazn needs to cancel, the next highest severity is when we think that sky needs to cancel
            WHEN "category_temp" = 'sky needs to cancel' THEN 0
            -- Followed by when the ID is missing from either source
            WHEN "category_temp" IN ('missing in sky', 'missing in dazn') THEN -1
            -- Followed by when there's a suspect delay
            WHEN "category_temp" IN ('dazn delay', 'sky delay') THEN -2
            -- Follower by the addon not being active yet
            WHEN "category_temp" IN ('addon not active yet','user is paused') THEN -3
            -- Finally anything aligned is the lowest severity
            WHEN "category_temp" = 'aligned' THEN -4
        END AS "severity_temp"
        -- this next field checks if either source is out of sync
        ,CASE
            -- We're only keeping records with snapshot ts <= batch date + 1. Therefore if the maximum available snapshot ts is less than that, we have a problem
            WHEN (SELECT MAX("sky_snapshot_date") FROM sky_source) < DATEADD(DAY,1,'{{ var('batch_date') }}') THEN 'sky_out_of_sync'
            -- Similar here, if the dbt model updated before the batch date + 1 then we have a problem.
            WHEN (SELECT MAX("META__DBT_INSERT_DTTS") FROM subscription_add_on__dim) < DATEADD(DAY,1,'{{ var('batch_date') }}') THEN 'dazn_out_of_sync'
            ELSE 'synced'
        END AS "sync_status"
        ,IFF("sync_status" = 'synced', "category_temp", "sync_status") AS "category"
        ,IFF("sync_status" = 'synced', "severity_temp", NULL) AS "severity"
    FROM dazn_deduped
    FULL OUTER JOIN sky_deduped
        ON sky_deduped."sky_contract_id" = dazn_deduped."rateplan_partner_user_id"
)

SELECT
    "partner_user_id"
    ,"billing_account_id"
    ,"sky_activation_date"
    ,"sky_first_seen_date"
    ,"sky_last_seen_date"
    ,"subscription_add_on_effective_start_date"
    ,"subscription_add_on_effective_end_date"
    ,"rateplan_charge_charge_type"
    ,"category"
    ,"severity"
FROM logic
