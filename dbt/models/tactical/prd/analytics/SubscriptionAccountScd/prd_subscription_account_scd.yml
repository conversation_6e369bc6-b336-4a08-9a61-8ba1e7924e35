version: 2

models:
  - name: prd_subscription_account_scd
    description: "SCD table representing changes to only the Subscription, Type and Entitlement Set ID/Tier"
    columns:
      - name: zr_sub_account_id
        description: "The Zuora Account ID coming from the Zuora Account entity E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: zr_sub_crm_id
        description: "The Salesforce Account ID coming from Zuora Account entity E.g 0011o00001m6OyEAAU"
        quote: true

      - name: zr_sub_id
        description: "The ID of the Subscription, Subscription Names can have multiple IDs relating to different types of changes"
        quote: true

      - name: zr_sub_name
        description: "The Name of the Subscription E.g. A-S15283950"
        quote: true

      - name: zr_sub_source_system
        description: "The name of the partner managing the subscription externally"
        quote: true

      - name: zr_sub_giftcode
        description: "The giftcode applied to the subscription on creation, if any"
        quote: true

      - name: zr_sub_term_type
        description: "The term type of the subscription, can either be termed (contract with a fixed period of time) or evergreen (contract constantly renews and is ongoing with no fixed term"
        quote: true

      - name: zr_rpc_billing_period
        description: "The frequency the customer is billed, can either be Month or Subscription Term"
        quote: true

      - name: zr_subscription_type
        description: "A derived field from the term type and the billing period to categorise the subscription type, can be Monthly, Annual or Instalment"
        quote: true

      - name: zr_subscription_tier
        description: "The Entitilement Set Id of the Rateplan, which is filtered to the associated RatePlanCharge's Flat Fee Pricing Model and Recurring Type, so producing the Tier for the Subscription"
        quote: true

      - name: zr_sub_mrr
        description: "The monthly recurring revenue, which is the amount the customer pays for their subscription ignoring Discounts and PPV, adjusting the subscription length down to one month and rounded to two decimal places"
        quote: true

      - name: effective_from_ts
        description: "The datetime this data point is valid from (inclusive)"
        quote: true

      - name: effective_until_ts
        description: "The datetime this data point is valid until (exlusive)"
        quote: true

      - name: zr_sub_type_quarantine_flag
        description: "A flag detailing if we have found something dodgy with this record and therefore should be excluded from all downstream analysis, right now this is defined as having no CRM ID"
        quote: true

      - name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the flow"
        quote: true
