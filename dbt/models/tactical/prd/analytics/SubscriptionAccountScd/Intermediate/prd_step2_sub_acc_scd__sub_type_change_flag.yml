version: 2

models:
  - name: prd_step2_sub_acc_scd__sub_type_change_flag
    description: "Step to identify the rows where relevant fields have changed so we can filter down the table down to only changes that are relevant to the Subscription Type or Tier"
    columns:
      - name: subscription_id
        description: "The ID of the Subscription, Subscription Names can have multiple IDs relating to different types of changes"
        quote: true

      - name: subscription_name
        description: "The Name of the Subscription E.g. A-S15283950"
        quote: true

      - name: subscription_version
        description: "The version of the Subscription ID"
        quote: true

      - name: subscription_name_original_created_timestamp
        description: "The timestamp of the creation of the original/first Subscription ID for this Subscription Name"
        quote: true

      - name: subscription_id_created_timestamp
        description: "The timestamp this Subscription ID was created"
        quote: true

      - name: subscription_id_updated_timestamp
        description: "The last time the Subscription ID was updated in Zuora"
        quote: true

      - name: billing_account_id
        description: "The Zuora Account ID coming from the Zuora Account entity E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: crm_account_id
        description: "The Zuora CRM ID which matches to the Salesforce Account ID E.g. 0011o00001m6OyEAAU"
        quote: true

      - name: crm_account_id_ifnull_billing_account_id
        description: "A combination of CRM Account Id and Billing Account ID, for when CRM Account Id is missing, which sometimes happens at the very start of a Sub, or some weird other Zuora errors"
        quote: true

      - name: subscription_source_system_name
        description: "The name of the partner managing the subscription externally"
        quote: true

      - name: subscription_sign_up_giftcode
        description: "The giftcode applied to the subscription on sign-up, if any"
        quote: true

      - name: subscription_term_type
        description: "The term type of the subscription, can either be termed (contract with a fixed period of time) or evergreen (contract constantly renews and is ongoing with no fixed term"
        quote: true

      - name: subscription_billing_period
        description: "The frequency the customer is billed, can either be Month or Subscription Term"
        quote: true

      - name: subscription_type
        description: "A derived field from the term type and the billing period, or external giftcodes, to categorise the subscription type, can be Monthly, Annual, Instalment or Externally Billed"
        quote: true

      - name: subscription_tier
        description: "The Entitilement Set Id of the Rateplan, which is filtered to the associated RatePlanCharge's Flat Fee Pricing Model and Recurring Type, so producing the Tier for the Subscription"
        quote: true

      - name: subscription_monthly_recurring_revenue
        description: "The monthly recurring revenue, which is the amount the customer pays for their subscription ignoring Discounts and PPV, adjusting the subscription length down to one month and rounded to two decimal places"
        quote: true

      - name: record_valid_from_timestamp_original
        description: "The datetime this data point is valid from (inclusive), renamed to make the next step easier to identify"
        quote: true

      - name: record_valid_until_timestamp_original
        description: "The datetime this data point is valid until (exclusive), renamed to make the next step easier to identify"
        quote: true

      - name: max__subscription_effective_until
        description: "The maximum record_valid_until_timestamp for the Account/Subscription, used in next step to know when the Subscription ends"
        quote: true

      - name: dimension_concat
        description: "A concatenation of all data points we are monitoring to see if they change"
        quote: true

      - name: dimension_concat_lag
        description: "The lag of the dimension_concat to be able to see if any data points have changed"
        quote: true

      - name: change_flag
        description: "Boolean flag for if this row marks a change in the data points we are monitoring, or if it's the last row for that Account/Subscription"
        quote: true
