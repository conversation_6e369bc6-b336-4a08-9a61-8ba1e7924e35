{{
    config(
        materialized='table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH source AS (
    SELECT * FROM {{ ref('prd_step1_subscription_account__scd') }}
)

SELECT
    "subscription_id"
    ,"subscription_name"
    ,"subscription_version"
    ,"subscription_name_original_created_timestamp"
    ,"subscription_id_created_timestamp"
    ,"subscription_id_updated_timestamp"
    ,"billing_account_id"
    ,"crm_account_id"
    ,"crm_account_id_ifnull_billing_account_id"
    ,"subscription_source_system_name"
    ,"subscription_sign_up_giftcode"
    ,"subscription_term_type"
    ,"subscription_billing_period"
    ,"subscription_type"
    ,"subscription_tier"
    ,"subscription_monthly_recurring_revenue"

    -- Renaming these so that the alias for next steps work better
    ,"record_valid_from_timestamp" AS "record_valid_from_timestamp_original"
    ,"record_valid_until_timestamp" AS "record_valid_until_timestamp_original"

    ,MAX("record_valid_until_timestamp_original") OVER (PARTITION BY "crm_account_id_ifnull_billing_account_id", "subscription_name") AS "max__record_valid_until_timestamp"
    ,CONCAT(
        IFNULL("subscription_name",'Empty')
        ,IFNULL("subscription_type",'Empty')
        ,IFNULL("subscription_tier",'Empty')
        ,IFNULL("subscription_monthly_recurring_revenue",0)
    ) AS "dimension_concat"
    ,LAG("dimension_concat", 1) OVER (PARTITION BY "crm_account_id_ifnull_billing_account_id" ORDER BY "record_valid_from_timestamp_original", "record_valid_until_timestamp_original") AS "dimension_concat_lag"
    ,CASE
        WHEN "dimension_concat_lag" IS NULL THEN TRUE
        WHEN "dimension_concat_lag" != "dimension_concat" THEN TRUE
        ELSE FALSE
    END AS "change_flag"
FROM source
