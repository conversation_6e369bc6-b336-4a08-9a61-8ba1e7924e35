{{
    config(
        materialized='table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH subscription_name_scd AS (
    SELECT * FROM {{ ref('subscription_name__scd') }}
)

-- filtering out overlapping subs that are not the most recently active ones and so cause problems
-- e.g. billing_account_id = '8a12903f7e9a4fcb017ebba396b8044d' OR '2c92a00c781c675401783cd84fc81f46'
,overlap_filter AS (
    SELECT
        "subscription_id"
        ,"subscription_name"
        ,"subscription_version"
        ,"subscription_name_original_created_timestamp"
        ,"subscription_id_created_timestamp"
        ,"subscription_id_updated_timestamp"
        ,"billing_account_id"
        ,"crm_account_id"
        ,IFNULL("crm_account_id", "billing_account_id") AS "crm_account_id_ifnull_billing_account_id"
        ,"subscription_source_system_name"
        ,"subscription_sign_up_giftcode"
        ,"subscription_term_type"
        ,"subscription_billing_period"
        ,"subscription_type"
        ,"subscription_tier"
        ,"subscription_monthly_recurring_revenue"
        ,"record_valid_from_timestamp"
        ,"record_valid_until_timestamp"
    FROM subscription_name_scd
    QUALIFY CASE
                -- When the row's before Sub Orig Created Timestamp is greater than the current row's Sub Orig Created Timestamp
                WHEN LAG("subscription_name_original_created_timestamp") OVER (PARTITION BY "crm_account_id_ifnull_billing_account_id" ORDER BY "record_valid_from_timestamp", "subscription_name_original_created_timestamp") > "subscription_name_original_created_timestamp"
                    -- Then filter it out as it's an overlapping Sub and we always want the most recently effective sub to carry through
                    THEN FALSE
                ELSE TRUE
            END
)

SELECT
    "subscription_id"
    ,"subscription_name"
    ,"subscription_version"
    ,"subscription_name_original_created_timestamp"
    ,"subscription_id_created_timestamp"
    ,"subscription_id_updated_timestamp"
    ,"billing_account_id"
    ,"crm_account_id"
    ,"crm_account_id_ifnull_billing_account_id"
    ,"subscription_source_system_name"
    ,"subscription_sign_up_giftcode"
    ,"subscription_term_type"
    ,"subscription_billing_period"
    ,"subscription_type"
    ,"subscription_tier"
    ,"subscription_monthly_recurring_revenue"

    -- Keeping old versions of these valids just for debugging
    ,"record_valid_from_timestamp" AS "record_valid_from_timestamp_old"
    ,"record_valid_until_timestamp" AS "record_valid_until_timestamp_old"

    ,"record_valid_from_timestamp" AS "record_valid_from_timestamp"
    -- Need to 'correct' for weird overlapping subscriptions by looking for overlaps and using the next effective from to match it exactly
    -- e.g. A-S13d49f29b40e4e77b54bef2594bcfd96, Acc ID 2c92a0076390d4590163a87d93b73325 => simple overlap
    -- e.g. A-S30304553, Acc ID 2c92a0076390d4590163a03d277548d1 => Complicated multiple overlaps
    ,CASE
        WHEN LEAD("record_valid_from_timestamp") OVER (PARTITION BY "crm_account_id_ifnull_billing_account_id" ORDER BY "record_valid_from_timestamp", "subscription_name_original_created_timestamp") > "record_valid_until_timestamp" THEN "record_valid_until_timestamp"
        ELSE IFNULL(LEAD("record_valid_from_timestamp") OVER (PARTITION BY "crm_account_id_ifnull_billing_account_id" ORDER BY "record_valid_from_timestamp", "subscription_name_original_created_timestamp"), "record_valid_until_timestamp")
    END AS "record_valid_until_timestamp"
FROM overlap_filter
