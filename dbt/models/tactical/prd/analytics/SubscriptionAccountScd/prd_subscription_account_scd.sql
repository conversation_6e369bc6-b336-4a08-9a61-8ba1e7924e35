{{
    config(
        alias = 'subscription_account_scd',
        materialized='table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH source AS (
    SELECT * FROM {{ ref('prd_step2_sub_acc_scd__sub_type_change_flag') }}
)

SELECT
    "billing_account_id" AS "zr_sub_account_id"
    ,"crm_account_id" AS "zr_sub_crm_id"
    ,"subscription_id" AS "zr_sub_id"
    ,"subscription_name" AS "zr_sub_name"
    ,NULL AS "zr_sub_status"
    ,"subscription_source_system_name" AS "zr_sub_source_system"
    ,"subscription_sign_up_giftcode" AS "zr_sub_giftcode"
    ,"subscription_term_type" AS "zr_sub_term_type"
    ,"subscription_billing_period" AS "zr_rpc_billing_period"
    ,"subscription_type" AS "zr_subscription_type"
    ,"subscription_tier" AS "zr_subscription_tier"
    ,"subscription_monthly_recurring_revenue" AS "zr_sub_mrr"

    ,"record_valid_from_timestamp_original" AS "effective_from_ts"
    ,CASE
        -- If it's the last row for the Sub Name, then use the Max Sub Effective Until, to make sure we haven't filtered out the last row in the change flag
        WHEN LEAD("record_valid_from_timestamp_original") OVER (PARTITION BY "crm_account_id_ifnull_billing_account_id", "subscription_name" ORDER BY "record_valid_from_timestamp_original", "record_valid_until_timestamp_original") IS NULL THEN "max__record_valid_until_timestamp"
        -- If not, then just use the next row's effective from
        ELSE IFNULL(LEAD("record_valid_from_timestamp_original") OVER (PARTITION BY "crm_account_id_ifnull_billing_account_id" ORDER BY "record_valid_from_timestamp_original", "record_valid_until_timestamp_original"), "record_valid_until_timestamp_original")
    END AS "effective_until_ts"

    ,"crm_account_id" IS NULL AS "zr_sub_type_quarantine_flag"
    ,CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
FROM source
WHERE
    "change_flag"
QUALIFY "effective_from_ts" != "effective_until_ts"
