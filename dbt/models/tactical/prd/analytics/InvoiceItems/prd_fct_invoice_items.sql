-- Define the table materialization
{{
    config(
        alias = 'fct_invoice_items',
        tags=['presentation-invoice-items']
    )
}}

WITH invoice_items AS (
    SELECT * FROM {{ ref('fct_invoice_items') }}
)

,ppv_purchases AS (
    SELECT * FROM {{ ref('fct_ppv_purchases') }}
)

,subscription_id_dim AS (
    SELECT * FROM {{ ref('subscription_id__dim') }}
)

,final AS (
    SELECT
        invoice_items."invoice_item_id"
        ,invoice_items."invoice_id"
        ,invoice_items."invoice_item_service_start_date"
        ,invoice_items."invoice_item_service_end_date"
        ,invoice_items."invoice_item_created_date"
        ,invoice_items."invoice_item_charge_amount"
        ,invoice_items."invoice_item_tax_amount"
        ,invoice_items."subscription_id"
        ,invoice_items."rateplan_charge_id"
        ,invoice_items."rateplan_id"
        ,invoice_items."rateplan_charge_charge_type"
        ,invoice_items."rateplan_charge_name"
        ,invoice_items."billing_account_id"
        ,invoice_items."subscription_name"
        ,invoice_items."billing_account_currency_code"
        ,invoice_items."fx_full_date"
        ,invoice_items."currency_from"
        ,invoice_items."currency_to"
        ,invoice_items."exchange_rate"
        ,invoice_items."usd_charge_amount"
        ,invoice_items."usd_tax_amount"
        ,invoice_items."invoice_item_product_type"
        ,ppv_purchases."ppv_source" AS "ppv_source"
        ,ppv_purchases."purchase_type" AS "ppv_purchase_type"
        ,subscription_id_dim."subscription_type"
        ,invoice_items."META__DBT_INSERT_DTTS"
    FROM invoice_items
    LEFT JOIN ppv_purchases ON invoice_items."rateplan_id" = ppv_purchases."rateplan_id"
    LEFT JOIN subscription_id_dim ON invoice_items."subscription_id" = subscription_id_dim."subscription_id"
)

SELECT * FROM final
