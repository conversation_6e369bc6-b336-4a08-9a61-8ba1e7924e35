{{
    config(
        materialized='table_case_sensitive',
        quoting={
            'database': true,
            'schema': true,
            'identifier': true
        },
        alias='content_dimension',
        tags=['presentation-content-dimension']
    )
}}

WITH old_content_dimension AS (
    SELECT * FROM {{ source('ANALYTICS', 'OLD_CONTENT_DIMENSION') }}
)

,gmr_article AS (
    SELECT * FROM {{ ref('gmr_article__dim') }}
)

,mfl_fixture AS (
    SELECT * FROM {{ ref('mfl_fixture__dim') }}
)

,won_liveevent AS (
    SELECT * FROM {{ ref('won_liveevent__dim') }}
)

,freemium_payload AS (
    SELECT 
        "fixture_id"
        ,"region_outlet"
        ,TO_VARCHAR(GET(t.value,'name')) AS "freemium_name"
    FROM {{ ref('won_liveevent__dim') }},
    TABLE(FLATTEN("liveevent_payload" : "options")) AS t
    WHERE t.value : "name" IN ('Freemium','Freemium - France Excluded')
)

,ppv_payload AS (
    SELECT
        "fixture_id"
    FROM {{ ref('won_liveevent__dim') }},
    TABLE(FLATTEN("liveevent_payload":"options"))  AS t
    WHERE  t.value : "name" :: VARCHAR = 'PPV'
    GROUP BY 1 
)

,article_market AS (
    SELECT 
        A.UUID
        ,A.EDM_INSERT_DTTS
        ,ORG.NAME
        ,CASE
            WHEN LOWER(SPLIT_PART(ORG.NAME,' ',2))='us' THEN 'usa' 
            WHEN LOWER(SPLIT_PART(ORG.NAME,' ',2))='germany'  THEN 'dach'
            ELSE LOWER(SPLIT_PART(ORG.NAME,' ',2)) 
        END AS "article_market_name" 
    FROM EDM_PROD.STAGING_PTVDB_PTVDO.EDITORIAL_ARTICLES A
    INNER JOIN EDM_PROD.STAGING_PTVDB_PTVDO.ORGANISATIONS ORG 
        ON A.ORGN_CLUB_ID=ORG.ORGN_ID 
        AND ORGN_TYPE='SITE'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY A.UUID ORDER BY A.EDM_INSERT_DTTS DESC)=1
)

,new_content_dimension AS (
    SELECT
        gmr_article."article_id" AS "article_id"
        ,gmr_article."payload" AS "article"
        ,CASE
            WHEN gmr_article."article_territory" = 'jp' THEN 'japan'
            WHEN gmr_article."article_territory" = 'it' THEN 'italy'
            WHEN gmr_article."article_territory" = 'es' THEN 'spain'
            WHEN gmr_article."article_territory" = 'ca' THEN 'canada'
            WHEN gmr_article."article_territory" = 'row' THEN 'moon'
            WHEN gmr_article."article_territory" = 'latin' THEN 'brazil'
            ELSE gmr_article."article_territory"
            END AS "outlet"
        ,gmr_article."article_language" AS "article_language"
        ,gmr_article."article_type_id"  AS "str_article_type_id"
        ,COALESCE(mfl_fixture."competition_id", gmr_article."competition_id") AS "competition_id"
        ,COALESCE(mfl_fixture."sport_id", gmr_article."sport_id") AS "sport_id"
        ,NULL AS "livestream_id"
        ,NULL AS "article_link_translation_language"
        ,COALESCE(mfl_fixture."fixture_description", gmr_article."fixture_title_localised", gmr_article."article_title") AS "english_article_title"
        ,COALESCE(gmr_article."article_title", mfl_fixture."fixture_description", gmr_article."fixture_title_localised") AS "local_article_title"
        ,COALESCE(gmr_article."product_value_weight"::STRING, 'Unknown') AS "article_quality"
        ,NULL AS "article_promotion"
        ,NULL AS "shoulder_content_or_live"
        ,NULL AS "special_event_rail"
        ,NULL AS "embargoed"
        ,NULL AS "linear_channel"
        ,CASE
            WHEN gmr_article."article_type_name" = 'Shoulder' THEN 'Shoulder Content'
            ELSE REPLACE(gmr_article."article_type_name", 'Live - ', '')
        END AS "article_type"
        ,CASE
            WHEN won_liveevent."live_right_has_download_allowed" = 'true' THEN 'Yes'
            WHEN won_liveevent."live_right_has_download_allowed" = 'false' THEN 'No'
            ELSE won_liveevent."live_right_has_download_allowed"
        END AS "downloadable_content"
        ,CASE
            WHEN gmr_article."article_is_age_restricted" = 'true' THEN 'Yes'
            WHEN gmr_article."article_is_age_restricted" = 'false' THEN 'No'
            ELSE gmr_article."article_is_age_restricted"
        END AS "age_restricted"
        ,NULL AS "short_highlights"
        ,CASE
            WHEN won_liveevent."live_event_has_ftv_allowed" = 'true' THEN 'Yes'
            WHEN won_liveevent."live_event_has_ftv_allowed"= 'false' THEN 'No'
            ELSE won_liveevent."live_event_has_ftv_allowed"
        END AS "free_to_view"
        ,NULL AS "tile_flag"
        ,COALESCE(mfl_fixture."contestant_0_id", gmr_article."contestant_0_id") AS "home_contestant_id"
        ,COALESCE(mfl_fixture."contestant_1_id", gmr_article."contestant_1_id") AS "away_contestant_id"
        ,mfl_fixture."competition_country_name" AS "competition_country"
        ,COALESCE(mfl_fixture."stage_name", gmr_article."stage_title_localised") AS "stage_name"
        ,COALESCE(mfl_fixture."contestant_0_name", gmr_article."contestant_0_title_localised") AS "home_contestant_name"
        ,mfl_fixture."contestant_0_country_name" AS "home_contestant_country"
        ,mfl_fixture."contestant_1_country_name" AS "away_contestant_country"
        ,COALESCE(mfl_fixture."contestant_1_name", gmr_article."contestant_1_title_localised") AS "away_contestant_name"
        ,COALESCE(gmr_article."fixture_id", 'No Linked Fixture') AS "fixture_id"
        ,'gmr/won/mfl' AS "content_source"
        ,COALESCE(mfl_fixture."ruleset_name", gmr_article."ruleset_title_localised") AS "ruleset_name"
        ,COALESCE(mfl_fixture."tournament_calendar_name", gmr_article."tournament_title_localised") AS "tournament_calendar_name"
        ,COALESCE(mfl_fixture."tournament_calendar_start_date", gmr_article."tournament_start_timestamp", '1900-01-01') AS "tournament_calendar_start_date"
        ,COALESCE(mfl_fixture."tournament_calendar_end_date", gmr_article."tournament_end_timestamp", '1900-01-01') AS "tournament_calendar_end_date"
        ,COALESCE(mfl_fixture."stage_start_date", gmr_article."stage_start_timestamp", '1900-01-01') AS "stage_start_date"
        ,COALESCE(mfl_fixture."stage_end_date", gmr_article."stage_end_timestamp", '1900-01-01') AS "stage_end_date"
        ,COALESCE(mfl_fixture."venue_name_short", gmr_article."venue_title_localised") AS "venue_short_name"
        ,COALESCE(mfl_fixture."venue_name_long", gmr_article."venue_title_localised") AS "venue_long_name"
        ,mfl_fixture."competition_country_name" AS "venue_country"
        ,COALESCE(mfl_fixture."fixture_start_timestamp", gmr_article."fixture_start_timestamp", '1900-01-01') AS "fixture_date"
        ,COALESCE(mfl_fixture."fixture_description", gmr_article."fixture_title_localised") AS "fixture_name"
        ,'1900-01-01 00:00:00' AS "effective_from"
        ,'9999-12-31 23:59:59' AS "effective_until"
        ,CURRENT_TIMESTAMP() AS "edm_inserted_at"
        ,COALESCE(mfl_fixture."competition_name", gmr_article."competition_title_localised") AS "competition_name"
        ,COALESCE(mfl_fixture."sport_name", gmr_article."sport_title_localised") AS "sport_name"
        ,CASE
            WHEN won_liveevent."live_right_has_b2b_allowed" = 'true' THEN 'Yes'
            WHEN won_liveevent."live_right_has_b2b_allowed"= 'false' THEN 'No'
            ELSE won_liveevent."live_right_has_b2b_allowed"
        END AS "b2b_flag"
        ,NULL AS "str_advertised_timestamp"
        ,won_liveevent."rights_holder_name" AS "rightsholder_name"
        ,NULL AS "won_external_reference"
        ,won_liveevent."live_event_start_timestamp" AS "won_tile_start_plan"
        ,won_liveevent."live_event_end_timestamp" AS "won_tile_end_plan"
        ,won_liveevent."live_event_status" AS "won_event_status"
        ,won_liveevent."live_event_vob_key" AS "won_vob_key"
        ,won_liveevent."live_event_gallery_name" AS "won_gallery_name"
        ,won_liveevent."live_event_is_dci" AS "won_dci"
        ,won_liveevent."live_event_broadcast_tier" AS "won_broadcast_tier"
        ,won_liveevent."live_product_support_tier" AS "won_support_tier"
        ,won_liveevent."live_event_workflow_type" AS "won_alternative_workflow"
        ,won_liveevent."primary_commentary_language" AS "won_commentary_language"
        ,won_liveevent."live_right_exclusivity_flag" AS "won_exclusivity_flag"
        ,won_liveevent."contract_name" AS "won_contract_name"
        ,won_liveevent."contract_start_date" AS "won_contract_start"
        ,won_liveevent."contract_end_date" AS "won_contract_end"
        ,won_liveevent."live_event_type" AS "won_content_distinction"
        ,NULL AS "won_advertising"
    FROM gmr_article
    LEFT JOIN mfl_fixture
        ON gmr_article."fixture_id" = mfl_fixture."fixture_id"
    LEFT JOIN won_liveevent
        ON gmr_article."fixture_id" = won_liveevent."fixture_id"
        AND "outlet" = won_liveevent."region_outlet"
)

,total_content_dimension AS (
    SELECT * FROM old_content_dimension
    UNION ALL
    SELECT * FROM new_content_dimension
    WHERE "article_id" NOT IN (SELECT "article_id" FROM old_content_dimension)
)

,final AS (
    SELECT 
        total_content_dimension.*
        ,won_liveevent."live_event_type_name" AS "tx_event_type"
        ,COALESCE(freemium_payload."freemium_name",FALSE)  AS "info_freemium" 
        ,CASE 
            WHEN ppv_payload."fixture_id" IS NOT NULL
                THEN TRUE
            ELSE FALSE
        END "is_ppv"
        ,article_market."article_market_name"
    FROM total_content_dimension
    LEFT JOIN freemium_payload
        ON total_content_dimension."fixture_id" = freemium_payload."fixture_id"
        AND "outlet" = freemium_payload."region_outlet"
    LEFT JOIN ppv_payload
        ON total_content_dimension."fixture_id" = ppv_payload."fixture_id"
    LEFT JOIN won_liveevent
        ON total_content_dimension."fixture_id" = won_liveevent."fixture_id"
        AND "outlet" = won_liveevent."region_outlet"
    LEFT JOIN article_market 
        ON total_content_dimension."article_id"= article_market.UUID
)

SELECT * FROM final
