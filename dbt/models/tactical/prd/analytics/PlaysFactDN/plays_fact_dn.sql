{{
    config(
        materialized='incremental_case_sensitive',
        incremental_strategy='delete+insert',
        unique_key='"str_full_date"',
        quoting={
            'database': true,
            'schema': true,
            'identifier': true
        },
        alias='plays_fact_dn',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        tags=['presentation-playback-stream-mart']
    )
}}

WITH transient__plays_2 AS (
    SELECT * FROM {{ ref_env('TRANSIENT__PLAYS_2') }}
)

, content AS (
    SELECT * FROM {{ ref_env('MART__CONTENT_ITEM__DIM') }}
)

, cust_identity AS (
    SELECT * FROM {{ ref_env('customer_identity_dim_current') }}
    QUALIFY ROW_NUMBER() OVER(PARTITION BY "viewer_id" ORDER BY "effective_from" DESC) = 1
)

, date_dim AS (
    SELECT * FROM {{ ref_env('dim_date') }}
)

, subs_filtered AS (
    SELECT * FROM {{ ref_env('daily_subscriptions_filtered') }}
)

, region_dimension AS (
    SELECT * FROM {{ ref_env('region_dimension') }}
)

, sub_source_mapping AS (
    SELECT * FROM {{ ref_env('subscription_source_system_name_mapping_dim') }}
)

, subs_data AS (
    SELECT
        cust_identity."crm_account_id"
        , cust_identity."billing_account_id"
        , cust_identity."viewer_id"
        , subs_filtered."subscription_name"
        , subs_filtered."subscription_start_date"
        , CASE WHEN subs_filtered."has_active_free_trial" = TRUE THEN 'Free Trial'
            WHEN subs_filtered."is_paused" = TRUE THEN 'Paused'
            ELSE 'Active Paid' END AS "cust_account_status"
        , subs_filtered."subscription_country"
        , subs_filtered."subscription_source_system_name_derived"
        , subs_filtered."subscription_type"
        , subs_filtered."subscription_territory"
        , COALESCE(sub_source_mapping."partner_name", subs_filtered."subscription_source_system_name_derived") AS "cust_customer_type_f"
        , CASE
            WHEN subs_filtered."billing_account_is_batch_50" = TRUE THEN 'Test Customer'
            ELSE 'Customer' END AS "cust_test_customer_flag"
        , subs_filtered."batch_date"
        , subs_filtered."subscription_product_group"
    FROM subs_filtered
    LEFT JOIN cust_identity
        ON cust_identity."billing_account_id" = subs_filtered."billing_account_id"
    LEFT JOIN sub_source_mapping
        ON subs_filtered."subscription_source_system_name_derived__skey" = sub_source_mapping."subscription_source_system_name_derived__skey"
    WHERE subs_filtered."batch_date" >= DATEADD(DAY, -2 , '{{ var('batch_date') }}')
)

SELECT
    DATE_TRUNC('day', transient__plays_2."start_time") AS "str_edm_batch_date"
    , CURRENT_TIMESTAMP() AS "str_inserted_at"
    , transient__plays_2."viewer_id" AS "str_viewer_id"
    , transient__plays_2."state" AS "str_state"
    , transient__plays_2."city" AS "str_city"
    , transient__plays_2."asn" AS "str_asn"
    , transient__plays_2."isp_name" AS "str_isp_name"
    , transient__plays_2."playing_time" AS "str_playing_time"
    , transient__plays_2."buffering_time" AS "str_buffering_time"
    , transient__plays_2."interrupts" AS "str_interrupts"
    , transient__plays_2."avg_bitrate" AS "str_avg_bitrate"
    , transient__plays_2."startup_error" AS "str_startup_error"
    , transient__plays_2."ip_address_hash" AS "str_ip_address_hash"
    , transient__plays_2."conviva_session_id" AS "str_conviva_session_id"
    , transient__plays_2."stream_url" AS "str_stream_url"
    , transient__plays_2."startup_error_list" AS "str_startup_error_list"
    , transient__plays_2."percent_complete" AS "str_percent_complete"
    , transient__plays_2."connection_induced_rebuffering_time" AS "str_connection_induced_rebuffering_time_raw"
    , transient__plays_2."video_restart_time" AS "str_video_restart_time"
    , transient__plays_2."rejoined_count" AS "str_rejoined_count"
    , transient__plays_2."vpf" AS "str_vpf"
    , transient__plays_2."vpf_error_list" AS "str_vpf_error_list"
    , transient__plays_2."content_length" AS "str_content_length"
    , transient__plays_2."player_name" AS "str_player_name"
    , transient__plays_2."concurrency" AS "str_concurrency"
    , transient__plays_2."drm_type" AS "str_drm_type"
    , transient__plays_2."multiview" AS "str_multiview"
    , transient__plays_2."streaming_protocol" AS "str_streaming_protocol"
    , transient__plays_2."dazn_device_id" AS "str_dazn_device_id"
    , transient__plays_2."dazn_session_id" AS "str_dazn_session_id"
    , transient__plays_2."free_to_view" AS "str_free_to_view"
    , transient__plays_2."country" AS "str_country"
    , transient__plays_2."start_time" AS "str_start_time"
    , transient__plays_2."end_time" AS "str_end_time"
    , transient__plays_2."total_streaming_time" AS "str_total_streaming_time"
    , NULL AS "str_length_of_membership"
    , transient__plays_2."closed_caption_language" AS "str_closed_caption_language"
    , transient__plays_2."commentary_language" AS "str_commentary_language"
    , transient__plays_2."native_player_version" AS "str_native_player_version"
    , transient__plays_2."origin_server" AS "str_origin_server"
    , transient__plays_2."automatic_or_manual_play" AS "str_automatic_or_manual_play"
    , transient__plays_2."engaged_play" AS "str_engaged_play"
    , transient__plays_2."pbb_flag" AS "str_pbb_flag"
    , transient__plays_2."ond_flag" AS "str_ond_flag"
    , transient__plays_2."mob_manifest_flag" AS "str_mob_manifest_flag"
    , transient__plays_2."user_agent" AS "str_user_agent"
    , transient__plays_2."full_date" AS "str_full_date"
    , content."article_id" AS "str_article_id"
    , transient__plays_2."live_or_on_demand" AS "str_live_or_on_demand"
    , transient__plays_2."connection_type" AS "str_connection_type"
    , transient__plays_2."application_version" AS "str_application_version"
    , transient__plays_2."application_major_version" AS "str_application_major_version"
    , transient__plays_2."application_minor_version" AS "str_application_minor_version"
    , transient__plays_2."application_patch_version" AS "str_application_patch_version"
    , transient__plays_2."application_type" AS "str_application_type"
    , transient__plays_2."application_category" AS "str_application_category"
    , transient__plays_2."genuine_attempt_flag" AS "str_genuine_attempt_flag"
    , transient__plays_2."ebvs_flag" AS "str_ebvs_flag"
    , 1 AS "str_play_flag"
    , UUID_STRING() AS "str_dss_uuid"
    , transient__plays_2."cdn_name" AS "str_cdn_name"
    , transient__plays_2."device_vendor" AS "str_device_vendor"
    , transient__plays_2."device_model" AS "str_device_model"
    , transient__plays_2."device_marketing_name" AS "str_device_marketing_name"
    , transient__plays_2."device_height" AS "str_device_height"
    , transient__plays_2."device_width" AS "str_device_width"
    , transient__plays_2."device_diagonal_screen_size" AS "str_device_diagonal_screen_size"
    , transient__plays_2."device_release_year" AS "str_device_release_year"
    , transient__plays_2."device_is_touchscreen" AS "str_device_is_touchscreen"
    , transient__plays_2."os_name" AS "str_os_name"
    , transient__plays_2."os_version" AS "str_os_version"
    , transient__plays_2."os_major_version" AS "str_os_major_version"
    , transient__plays_2."os_minor_version" AS "str_os_minor_version"
    , transient__plays_2."browser_name" AS "str_browser_name"
    , transient__plays_2."browser_rendering_engine" AS "str_browser_rendering_engine"
    , transient__plays_2."browser_version" AS "str_browser_version"
    , transient__plays_2."browser_major_version" AS "str_browser_major_version"
    , transient__plays_2."browser_minor_version" AS "str_browser_minor_version"
    , transient__plays_2."browser_patch_version" AS "str_browser_patch_version"
    , transient__plays_2."os_patch_version" AS "str_os_patch_version"
    , HASH(transient__plays_2."user_agent") AS "str_user_agent_key"
    , transient__plays_2."device_manufacturer" AS "str_device_manufacturer"
    , transient__plays_2."device_platform" AS "str_device_platform"
    , transient__plays_2."device_full_description_lr" AS "str_device_full_description_lr"
    , NULL AS "str_tv_flag"
    , date_dim."week_end_date" AS "str_week_end_date"
    , date_dim."week_of_year" AS "str_week_number"
    , date_dim."quarter_of_year" AS "str_quarter"
    , date_dim."month_start_date" AS "str_month"
    , date_dim."year_number" AS "str_year"
    , cust_identity."crm_account_id" AS "cust_account_id"
    , NULL AS "cust_portability_status"
    , transient__plays_2."viewer_id" AS "cust_viewer_id"
    , COALESCE(dazn_subs."subscription_name", nfl_subs."subscription_name") AS "cust_subscription_name"
    , COALESCE(dazn_subs."subscription_start_date", nfl_subs."subscription_start_date") AS "cust_subscription_term_start_date"
    , COALESCE(dazn_subs."cust_account_status", nfl_subs."cust_account_status") AS "cust_account_status"
    , COALESCE(dazn_subs."subscription_country", nfl_subs."subscription_country") AS "cust_country"
    , COALESCE(dazn_subs."subscription_source_system_name_derived", nfl_subs."subscription_source_system_name_derived") AS "cust_customer_type_raw"
    , COALESCE(dazn_subs."subscription_type", nfl_subs."subscription_type") AS "cust_subscription_type"
    , COALESCE(dazn_subs."subscription_territory", nfl_subs."subscription_territory") AS "cust_territory"
    , region_dimension."region" AS "cust_region"
    , COALESCE(dazn_subs."cust_customer_type_f", nfl_subs."cust_customer_type_f") AS "cust_customer_type_f"
    , COALESCE(dazn_subs."cust_test_customer_flag", nfl_subs."cust_test_customer_flag") AS "cust_test_customer_flag"
    , content."outlet" AS "str_outlet"
    , content."article_language_code" AS "str_article_language"
    , content."competition_id" AS "str_competition_id"
    , content."sport_id" AS "str_sport_id"
    , content."livestream_id" AS "str_livestream_id"
    , content."article_title_english" AS "str_english_article_title"
    , content."article_title_local" AS "str_local_article_title"
    , content."article_quality" AS "str_article_quality"
    , content."article_promotion" AS "str_article_promotion"
    , content."shoulder_content_or_live" AS "str_shoulder_content_or_live"
    , content."has_special_event_rail" AS "str_special_event_rail"
    , content."is_embargoed" AS "str_embargoed"
    , content."is_linear_channel" AS "str_linear_channel"
    , content."article_type" AS "str_article_type"
    , content."is_allowed_download" AS "str_downloadable_content"
    , content."is_age_restricted" AS "str_age_restricted"
    , content."is_short_highlights" AS "str_short_highlights"
    , content."is_allowed_free_to_view" AS "str_free_to_view_article"
    , content."has_tile_text" AS "str_tile_flag"
    , content."competition_country" AS "str_competition_country"
    , content."stage_name" AS "str_stage_name"
    , content."home_contestant_name" AS "str_home_contestant_name"
    , content."home_contestant_country" AS "str_home_contestant_country"
    , content."away_contestant_country" AS "str_away_contestant_country"
    , content."away_contestant_name" AS "str_away_contestant_name"
    , content."fixture_id" AS "str_fixture_id"
    , content."content_item_origin" AS "str_content_source"
    , content."ruleset_name" AS "str_ruleset_name"
    , content."tournament_calendar_name" AS "str_tournament_calendar_name"
    , content."tournament_calendar_start_date" AS "str_tournament_calendar_start_date"
    , content."tournament_calendar_end_date" AS "str_tournament_calendar_end_date"
    , content."stage_start_date" AS "str_stage_start_date"
    , content."stage_end_date" AS "str_stage_end_date"
    , content."venue_short_name" AS "str_venue_short_name"
    , content."venue_long_name" AS "str_venue_long_name"
    , content."venue_country" AS "str_venue_country"
    , content."fixture_start_timestamp" AS "str_fixture_date"
    , content."fixture_name" AS "str_fixture_name"
    , content."record_valid_from_timestamp" AS "str_content_effective_from"
    , NULL AS "str_article_edm_inserted_at"
    , content."competition_name" AS "str_competition_name"
    , content."sport_name" AS "str_sport_name"
    , transient__plays_2."territory" AS "str_territory"
    , transient__plays_2."region" AS "str_region"
    , transient__plays_2."device_full_description" AS "str_device_full_description"
    , IFNULL("str_article_type",'') || '-' || IFNULL("str_fixture_name", '') || '-' || IFNULL(TO_CHAR("str_fixture_date",'YYYY-MM-DDTHH24:MI:SS'),'') AS "str_fixture_name_concat"
    , CASE
        WHEN "str_competition_country" || '-' || "str_competition_name" = 'Germany-Bundesliga' THEN "str_competition_country" || '-' || "str_competition_name" || ' (' || "str_sport_name" || ')'
        WHEN "str_competition_country" || '-' || "str_competition_name" = 'Spain-Copa del Rey' THEN "str_competition_country" || '-' || "str_competition_name" || ' (' || "str_sport_name" || ')'
        WHEN "str_competition_country" || '-' || "str_competition_name" = 'Europe-WC Qualification Europe' THEN "str_competition_country" || '-' || "str_competition_name" || ' (' || "str_sport_name" || ')'
        ELSE IFNULL("str_competition_country",'') || '-' || "str_competition_name"
    END AS "str_competition_name_concat"
    , IFNULL("str_article_type", '') || '-' || IFNULL("str_fixture_name",'') || '-' || IFNULL(TO_CHAR("str_fixture_date",'YYYY-MM-DDTHH24:MI:SS'), '') AS "str_article_concat"
    , CURRENT_TIMESTAMP() AS "dss_processed_at"
    , transient__plays_2."hd_flag" AS "str_hd_flag"
    , transient__plays_2."hd_capable_flag" AS "str_hd_capable_flag"
    , MD5(IFNULL("cust_account_id", '') || IFNULL("str_ip_address_hash",'') || IFNULL("str_fixture_id", '') || IFNULL("str_article_quality", '') || IFNULL("str_user_agent", '') || IFNULL("str_connection_type",'')) AS "str_customer_streaming_session_id"
    , NULL AS "str_latitude"
    , NULL AS "str_longitude"
    , transient__plays_2."sd_flag" AS "str_sd_flag"
    , transient__plays_2."device_hardware_type" AS "str_device_hardware_type"
    , transient__plays_2."device_category" AS "str_device_category"
    , transient__plays_2."startup_error_type" AS "str_startup_error_type"
    , transient__plays_2."startup_time" AS "str_startup_time"
    , transient__plays_2."connection_induced_rebuffering_time_capped" AS "str_connection_induced_rebuffering_time"
    , transient__plays_2."error_code" AS "error_code"
    , 1 AS "str_valid_for_analysis"
    , transient__plays_2."curated_ip_address_type" AS "str_curated_ip_address_type"
    , NULL AS "cust_sf_account_created_date"
    , transient__plays_2."business_type" AS "cust_business_type"
    , content."rightsholder_name" AS "str_rightsholder_name"
    , content."advertised_timestamp" AS "str_advertised_timestamp"
    , content."is_allowed_b2b" AS "str_available_on_b2b"
    , content."won_external_id" AS "won_external_reference"
    , content."tile_planned_start_timestamp" AS "won_tile_start_plan"
    , content."tile_planned_end_timestamp" AS "won_tile_end_plan"
    , content."transmission_status" AS "won_event_status"
    , content."voiceover_booth_key" AS "won_vob_key"
    , content."gallery_resource_name" AS "won_gallery_name"
    , content."is_allowed_dci" AS "won_dci"
    , content."broadcast_tier" AS "won_broadcast_tier"
    , content."support_tier" AS "won_support_tier"
    , content."alternative_workflow" AS "won_alternative_workflow"
    , content."commentary_language_code" AS "won_commentary_language"
    , content."is_exclusive" AS "won_exclusivity_flag"
    , content."contract_name" AS "won_contract_name"
    , content."contract_start_date" AS "won_contract_start"
    , content."contract_end_date" AS "won_contract_end"
    , content."content_distinction" AS "won_content_distinction"
    , content."advertising_asset_label" AS "won_advertising"
    , content."home_contestant_id" AS "str_home_contestant_id"
    , content."away_contestant_id" AS "str_away_contestant_id"
    , transient__plays_2."is_dai_session" AS "str_dai_session_start"
    , transient__plays_2."playback_entitlement_set_id" AS "str_entitlement_set_id"
    , transient__plays_2."ad_manager_name" AS "ad_manager_name"
FROM transient__plays_2
LEFT JOIN content ON transient__plays_2."article_id" = content."article_id"
LEFT JOIN cust_identity ON transient__plays_2."viewer_id" = cust_identity."viewer_id"
LEFT JOIN date_dim ON transient__plays_2."full_date"::DATE = date_dim."date_day"
LEFT JOIN region_dimension ON transient__plays_2."country" = region_dimension."join_key"
LEFT JOIN subs_data AS dazn_subs
    ON cust_identity."billing_account_id" = dazn_subs."billing_account_id"
    AND dazn_subs."batch_date" = transient__plays_2."full_date"
    AND dazn_subs."subscription_product_group" = 'DAZN'
LEFT JOIN subs_data AS nfl_subs
    ON cust_identity."billing_account_id" = nfl_subs."billing_account_id"
    AND nfl_subs."batch_date" = transient__plays_2."full_date"
    AND nfl_subs."subscription_product_group" = 'NFL'
