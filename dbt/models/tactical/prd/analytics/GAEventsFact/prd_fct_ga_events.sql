{{
    config(
        materialized='incremental_case_sensitive',
        incremental_strategy='delete+insert',
        unique_key='"full_date"',
        on_schema_change='append_new_columns',
        quoting={
            'database': true,
            'schema': true,
            'identifier': true
        },
        alias='events_fact',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        tags=['presentation-ga-events-fact']
    )
}}

WITH source AS (
    SELECT * FROM {{ ref('fct_ga_events') }}
)

,renamed AS (
    SELECT
        "edm_inserted_at" AS "edm_inserted_at"
        ,"visit_number" AS "visit_number"
        ,"session_id" AS "session_id"
        ,"visit_start_timestamp" AS "timestamp"
        ,"full_date" AS "full_date"
        ,"full_visitor_id" AS "full_visitor_id"
        ,"client_id" AS "client_id"
        ,"total_events" AS "total_events"
        ,"device_category" AS "device_category"
        ,"geo_country" AS "geo_country"
        ,"application_id" AS "application_id"
        ,"app_version" AS "appVersion"
        ,"hit_number" AS "hit_number"
        ,"hit_time_ms" AS "time"
        ,"event_action" AS "event_action"
        ,"event_category" AS "event_category"
        ,"event_label" AS "event_label"
        ,"hostname" AS "hostname"
        ,"page_path" AS "page_path"
        ,"page_title" AS "page_title"
        ,"experiment_id" AS "experiment_id"
        ,"customer_id" AS "customer_id"
        ,"user_agent" AS "user_agent"
        ,"application_type" AS "application_type"
        ,"application_version" AS "application_version"
        ,"vwo_test_variant_name" AS "VWOTestVariantName_scd41"
        ,"application_environment" AS "application_environment"
        ,"client_id_scd" AS "clientId_scd"
        ,"click_ref" AS "clickref"
        ,"launch_origin" AS "launch_origin"
        ,"vwo_test_campaign_name" AS "VWOTestCampaignName_scd67"
        ,"vwo_test_variant_id" AS "VWO_test_variant_id"
        ,"publisher_id" AS "publisher_id"
        ,"publisher_name" AS "publisher_name"
        ,"vwo_test_campaign_id" AS "VWOTestCampaignId_scd72"
        ,"open_browse" AS "open_browse"
        ,"test_variant" AS "test_variant"
        ,"test_variant_open_browse" AS "test_variant_open_browse"
        ,"test_variant_rail_management" AS "testVariantRailManagement_scd147"
        ,"competition_id" AS "competitionId"
        ,"competition_name" AS "competition_name"
        ,"content_title_sport_name" AS "content_title_sport_name"
        ,"navigation_tile_article_id" AS "navigation_tile_article_Id"
        ,"navigation_tile_navigate_to" AS "navigation_tile_navigate_to"
        ,"navigation_tile_title" AS "navigation_tile_title"
        ,"rail_current_position" AS "rail_current_position"
        ,"rail_length" AS "rail_length"
        ,"rail_name" AS "rail_name"
        ,"rail_starting_position" AS "rail_starting_position"
        ,"rail_number_in_view" AS "rail_number_in_view"
        ,"search_number_of_results" AS "search_number_of_results"
        ,"search_result_category" AS "search_result_category"
        ,"search_selected_result_position" AS "search_selected_result_position"
        ,"search_term" AS "search_term"
        ,"rail_number_of_loaded" AS "rail_number_of_loaded"
        ,"article_type" AS "article_type"
        ,"player_action" AS "player_action"
        ,"play_origin" AS "play_origin"
        ,"payment_billing_country" AS "payment_billing_country"
        ,"payment_billing_period" AS "payment_billing_period"
        ,"payment_state" AS "payment_state"
        ,"payment_subscription_type" AS "payment_subscription_type"
        ,"password_reset_state" AS "password_reset_state"
        ,"content_tile_position_in_view" AS "content_tile_position_in_view"
        ,"content_tile_position_of_loaded" AS "content_tile_position_of_loaded"
        ,"navigation_tile_position_in_view" AS "navigation_tile_position_in_view"
        ,"navigation_tile_position_of_loaded" AS "navigation_tile_position_of_loaded"
        ,"content_tile_article_name" AS "content_tile_article_name"
        ,"content_tile_coming_up_label" AS "content_tile_coming_up_label"
        ,"perform_id_ansii" AS "perform_id_ansii"
        ,"perform_id" AS "perform_id"
        ,"ga_page_category" AS "pageCategory_hcd83"
        ,"payment_method" AS "payment_method"
        ,"page_load_delta" AS "page_load_delta"
        ,"competitor_id" AS "competitorid"
        ,"fixture_id" AS "fixtureid"
        ,"rail_title" AS "rail_title"
        ,"dazn_device_id" AS "dazn_device_id"
        ,"article_id" AS "articleId"
        ,"ga_unique_session_id" AS "unique_session_id"
        ,"hit_time" AS "hit_time"
        ,"hit_id" AS "hit_id"
        ,"device_key" AS "device_key"
        ,"page_url" AS "page_url"
        ,"page_category" AS "page_category"
        ,"full_date_ts" AS "full_date_ts"
        ,"session_start_ts" AS "session_start_ts"
        ,"dbt_inserted_at" AS "dss_inserted_at"
        ,"channel_grouping" AS "channel_grouping"
        ,"fixture_name" AS "fixture_name"
        ,"competitor_name" AS "competitor_name"
        ,"content_country" AS "content_country"
        ,"content_tile_label_id" AS "content_tile_label_id"
        ,"giftcode_action" AS "giftcode_action"
        ,"giftcode_status" AS "giftcode_status"
        ,"giftcode_type" AS "giftcode_type"
        ,"giftcode_id" AS "giftcode_id"
        ,"giftcode_extra_free_months" AS "giftcode_extra_free_months"
        ,"giftcode_promo_id" AS "giftcode_promo_id"
        ,"giftcode_campaign_id" AS "giftcode_campaig_id"
        ,"giftcode_extra_payment_method" AS "giftcode_extra_payment_method"
        ,"giftcode_eu_content_portability" AS "giftcode_eu_content_portability"
        ,"payment_action" AS "payment_action"
        ,"payment_additional_payment_method" AS "payment_additional_payment_method"
        ,"payment_auth_country" AS "payment_auth_country"
        ,"payment_auth_provider" AS "payment_auth_provider"
        ,"payment_auth_response" AS "payment_auth_response"
        ,"adblock_status" AS "adblock_status"
        ,"subscription_action" AS "subscription_action"
        ,"subscription_action_status" AS "subscription_action_status"
        ,"subscription_billing_period" AS "subscription_billing_period"
        ,"subscription_subscription_type" AS "subscription_subscription_type"
        ,"subscription_restart_date" AS "subscription_restart_date"
        ,"edit_item" AS "edit_item"
        ,"edit_action_status" AS "edit_action_status"
        ,"edit_value" AS "edit_value"
        ,"edit_previous_value" AS "edit_previous_value"
        ,"edit_field" AS "edit_field"
        ,"itm_source" AS "itm_source"
        ,"itm_medium" AS "itm_medium"
        ,"itm_campaign" AS "itm_campaign"
        ,"live_edge" AS "live_edge"
        ,"pulse_alert_position" AS "pulse_alert_position"
        ,"player_playback_initiation" AS "player_playback_initiation"
        ,"player_playback_source" AS "player_playback_source"
        ,"gtm_container_version" AS "gtm_container_version"
        ,"ga_primary_key" AS "ga_primary_key"
    FROM source
    WHERE ({{ unixtime_build_trigger(var('build_mode'), var('rebuild_days'), 'timestamp') }})
)

SELECT * FROM renamed
