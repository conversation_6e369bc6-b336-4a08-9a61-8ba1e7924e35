version: 2

models:
  - name: prd_dim__entitlement_sets_scd
    description: "Entitlement Sets SCD showing the entitlement ids included in sets at any given point in time"
    columns:
      - name: entitlement_sets_scd_primary_key
        description: "MD<PERSON> hashed key of the entitlement_set_id and updatedTimestamp (aka effective_from), used in the merge statement as a unique identifier in the entitlement sets scd"
        quote: true

      - name: entitlement_set_id
        description: Id string of the Entitlement Set
        quote: true

      - name: effective_from
        description: Timestamp that the entitlements are effective from.
        quote: true

      - name: effective_until
        description: Timestamp that the entitlements are effective until.
        quote: true

      - name: message_id
        description: Unique id of the sns message that updated the SCD
        quote: true

      - name: message_timestamp
        description: Timestamp of when the message appeared in the open layer
        quote: true

      - name: entitlement_ids
        description: Array of entitlement ids that the set has access to.
        quote: true

      - name: event_start_timestamp
        description: The date on which the event related to the entitlement will take place
        quote: true

  - name: prd_dim__user_entitlement_scd
    description: "User Entitlement SCD - represents what Tiers or PPV entitlements that users have access to, their current status, and when/if it expires"
    columns:
      - name: dazn_user_id
        description: "DAZN User ID used to identify users across a number of tables incl. Zuora and Salesforce"
        quote: true

      - name: entitlement_set_id
        description: "Id of the entitlement set, as a human readable string. It is also used in the purchase flow, with this field stored in Zuora's RatePlan custom field."
        quote: true

      - name: effective_from
        description: "UTC timestamp indicating when the entitlement set was assigned to the user or when the user purchased the entitlement set."
        quote: true

      - name: effective_until
        description: "UTC timestamp indicating when the entitlement set expired or was cancelled or the tier was changed."
        quote: true

      - name: set_expired_type
        description: "The event which caused the set to expire, either expiration for PPV, deleted, or updated for tiers."
        quote: true

      - name: product_type
        description: "The type of the entitlement set, either ppv or tier"
        quote: true

      - name: initial_expiration_timestamp
        description: "For PPVs, a UTC timestamp derived from the value of TTL (time to live), indicating when the user entitlement becomes obsolete i.e. the user will no longer have the entitlement after this date. This will blank for Tier Purchases."
        quote: true

      - name: entitlement_change_type
        description: "The type of event being recorded in relation to the user's ownership of the entitlement set i.e. create or updated or migration."
        quote: true

      - name: crm_account_id
        description: "The CRM Id of the user, from Salesforce. This field may be null for the first 24 hours of the user's account being created."
        quote: true

      - name: billing_account_id
        description: "The billing Id of the user, from Zuora. This field may be null for the first 24 hours or more of the user's account being created."
        quote: true

      - name: subscription_name
        description: "The subscription name of the user, from Zuora. This field may be null for the first 24 hours or more of the user's account being created."
        quote: true

      - name: previous_entitlement_set_id
        description: "For update messages, shows the previous entitlement set value being updated"
        quote: true

      - name: source
        description: "The origin of modification. Can be as purchase (will have rate plan) or SF, if changes were executed directly from Salesforce API"
        quote: true

      - name: user_entitlement_scd_pkey
        description: "primary key of the table - a hash of the dazn_user_id, entitlement_set_id and entitlement_set_effective_from"
        quote: true

  - name: prd_dim__article_entitlement_id_scd
    description: "Entitlements SCD in order to track the entitlement strings that are associated with an article id at a certain time point"
    columns:
      - name: article_id
        description: A unique identifier for each article that requires an entitlement.
        quote: true

      - name: entitlement_ids
        description: An array containing the entitlement ids where at least one is needed to view an article id.
        quote: true

      - name: effective_from
        description: Timestamp that the entitlements are effective from.
        quote: true

      - name: effective_until
        description: Timestamp that the entitlements are effective until.
        quote: true

      - name: article_entitlement_skey
        description: "surrogate key for the table, a hash of the article_id and effective_from fields"
        quote: true
