version: 2

models:
  - name: prd_fct_ppv_purchases
    description: "PPV Purchases Tracking - Each record in this table representes a PPV rateplan added to a subscription. The records are augmented with the user IDs,  the tracking ID for partnerships, bundle vs standalone and direct vs 3pp. THis table doesn't account for refund or revocations of PPV entitlements."
    columns:
      - name: purchase_date
        description: "Rateplan created datetime - this reflects the purchase date"
        quote: true

      - name: rateplan_id
        description: "Id of the rateplan"
        quote: true

      - name: rateplan_name
        description: "Name of the rateplan"
        quote: true

      - name: tracking_id
        description: "Front End Partnerships Tracking ID usually for use in commercial partnership tracking"
        quote: true

      - name: entitlement_set_id
        description: "Entitlement Set ID"
        quote: true

      - name: subscription_id
        description: "Zuora Subscription ID"
        quote: true

      - name: subscription_name
        description: "Zuora Subscription Name"
        quote: true

      - name: billing_account__id
        description: "Zuora Account ID"
        quote: true

      - name: crm_account__id
        description: "CRM/Customer ID"
        quote: true

      - name: charge_type
        description: "Charge Type - for PPV should always be OneTime"
        quote: true

      - name: ppv_source
        description: "Third Party Payment Provider Name e.g. Apple, Google"
        quote: true

      - name: purchase_type
        description: "Bundle Type - Whether the customer purchased the PPV with a DAZN subscription or as standalone"
        quote: true
