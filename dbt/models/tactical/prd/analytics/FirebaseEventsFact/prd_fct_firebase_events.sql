{{
    config(
        materialized='incremental_case_sensitive',
        incremental_strategy='delete+insert',
        unique_key='"event_date"',
        on_schema_change='append_new_columns',
        quoting={
            'database': true,
            'schema': true,
            'identifier': true
        },
        alias='firebase_events_fact',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        tags=['presentation-firebase-events-fact'],
        persist_docs={
            'relation': false,
            'columns': false
        },
    )
}}

WITH source AS (
    SELECT * FROM {{ ref('fct_firebase_events') }}
)

,renamed AS (
    SELECT
        "app_info_id" AS "app_info_id"
        ,"app_info_firebase_app_id" AS "app_info_firebase_app_id"
        ,"app_info_install_source" AS "app_info_install_source"
        ,"app_info_version" AS "app_info_version"
        ,"device_category" AS "device_category"
        ,"device_mobile_brand_name" AS "device_mobile_brand_name"
        ,"device_mobile_model_name" AS "device_mobile_model_name"
        ,"device_mobile_marketing_name" AS "device_mobile_marketing_name"
        ,"device_mobile_os_hardware_model" AS "device_mobile_os_hardware_model"
        ,"device_operating_system" AS "device_operating_system"
        ,"device_operating_system_version" AS "device_operating_system_version"
        ,"device_vendor_id" AS "device_vendor_id"
        ,"device_advertising_id" AS "device_advertising_id"
        ,"device_language" AS "device_language"
        ,"device_time_zone_offset_seconds" AS "device_time_zone_offset_seconds"
        ,"device_is_limited_ad_tracking" AS "device_is_limited_ad_tracking"
        ,"stream_id" AS "stream_id"
        ,"platform" AS "platform"
        ,"user_first_touch_timestamp" AS "user_first_touch_timestamp"
        ,"user_id" AS "user_id"
        ,"user_pseudo_id" AS "user_pseudo_id"
        ,"user_ltv_revenue" AS "user_ltv_revenue"
        ,"user_ltv_currency" AS "user_ltv_currency"
        ,"traffic_source_name" AS "traffic_source_name"
        ,"traffic_source_medium" AS "traffic_source_medium"
        ,"traffic_source_source" AS "traffic_source_source"
        ,"geo_continent" AS "geo_continent"
        ,"geo_country" AS "geo_country"
        ,"geo_region" AS "geo_region"
        ,"geo_city" AS "geo_city"
        ,"event_date" AS "event_date"
        ,"event_timestamp" AS "event_timestamp"
        ,"event_previous_timestamp" AS "event_previous_timestamp"
        ,"event_name" AS "event_name"
        ,"event_value_in_usd" AS "event_value_in_usd"
        ,"event_bundle_sequence_id" AS "event_bundle_sequence_id"
        ,"event_param_action_category" AS "event_param_action_category"
        ,"event_param_action_label" AS "event_param_action_label"
        ,"event_param_action_name" AS "event_param_action_name"
        ,"event_param_action_origin" AS "event_param_action_origin"
        ,"age_restricted" AS "age_restricted"
        ,"event_param_article_id" AS "event_param_article_id"
        ,"event_param_bitrate" AS "event_param_bitrate"
        ,"event_param_button_title" AS "event_param_button_title"
        ,"event_param_campaign" AS "event_param_campaign"
        ,"campaign_info_source" AS "campaign_info_source"
        ,"event_param_cdn" AS "event_param_cdn"
        ,"event_param_channel" AS "event_param_channel"
        ,"event_param_competition_id" AS "event_param_competition_id"
        ,"event_param_competition_name" AS "event_param_competition_name"
        ,"competitor_id" AS "competitor_id"
        ,"content_campaign" AS "content_campaign"
        ,"event_param_customer_id" AS "event_param_customer_id"
        ,"event_param_data_cap_cellular_on" AS "event_param_data_cap_cellular_on"
        ,"event_param_data_cap_on" AS "event_param_data_cap_on"
        ,"event_param_data_cap_wifi_on" AS "event_param_data_cap_wifi_on"
        ,"event_param_device_id" AS "event_param_device_id"
        ,"event_param_download_status" AS "event_param_download_status"
        ,"event_param_engagement_time_msec" AS "event_param_engagement_time_msec"
        ,"event_param_error_code_cat" AS "event_param_error_code_cat"
        ,"event_param_error_code_response" AS "event_param_error_code_response"
        ,"event_param_error_code_type" AS "event_param_error_code_type"
        ,"event_param_error_http_code" AS "event_param_error_http_code"
        ,"event_param_error_internal_code" AS "event_param_error_internal_code"
        ,"event_param_error_internal_msg" AS "event_param_error_internal_msg"
        ,"event_param_error_misl_code" AS "event_param_error_misl_code"
        ,"event_param_error_misl_message" AS "event_param_error_misl_message"
        ,"event_param_error_user_message_key" AS "event_param_error_user_message_key"
        ,"event_param_error_value" AS "event_param_error_value"
        ,"event_param_eventid" AS "event_param_eventid"
        ,"event_param_fa_event_action" AS "event_param_fa_event_action"
        ,"event_param_fa_event_desc" AS "event_param_fa_event_desc"
        ,"event_param_fa_event_object" AS "event_param_fa_event_object"
        ,"event_param_factory_error" AS "event_param_factory_error"
        ,"event_param_failed_cdn" AS "event_param_failed_cdn"
        ,"favourite_id" AS "favourite_id"
        ,"event_param_firebase_event_origin" AS "event_param_firebase_event_origin"
        ,"event_param_firebase_previous_class" AS "event_param_firebase_previous_class"
        ,"event_param_firebase_previous_id" AS "event_param_firebase_previous_id"
        ,"event_param_firebase_previous_screen" AS "event_param_firebase_previous_screen"
        ,"event_param_firebase_screen" AS "event_param_firebase_screen"
        ,"event_param_firebase_screen_class" AS "event_param_firebase_screen_class"
        ,"event_param_firebase_screen_id" AS "event_param_firebase_screen_id"
        ,"ga_session_id_event_parameter" AS "ga_session_id_event_parameter"
        ,"event_param_interaction_type" AS "event_param_interaction_type"
        ,"event_param_languagecode" AS "event_param_languagecode"
        ,"event_param_launch_origin" AS "event_param_launch_origin"
        ,"event_param_medium" AS "event_param_medium"
        ,"event_param_min_bitrate" AS "event_param_min_bitrate"
        ,"event_param_notification_type" AS "event_param_notification_type"
        ,"event_param_currency" AS "event_param_currency"
        ,"event_param_offer_type" AS "event_param_offer_type"
        ,"event_param_offline_playback_position_in_millis" AS "event_param_offline_playback_position_in_millis"
        ,"event_param_page_index" AS "event_param_page_index"
        ,"event_param_play_origin" AS "event_param_play_origin"
        ,"event_param_previous_app_version" AS "event_param_previous_app_version"
        ,"event_param_previous_first_open_count" AS "event_param_previous_first_open_count"
        ,"event_param_previous_os_version" AS "event_param_previous_os_version"
        ,"event_param_price" AS "event_param_price"
        ,"event_param_product_id" AS "event_param_product_id"
        ,"event_param_product_name" AS "event_param_product_name"
        ,"event_param_rail_offset" AS "event_param_rail_offset"
        ,"event_param_screen_name" AS "event_param_screen_name"
        ,"search_item_selected" AS "search_item_selected"
        ,"search_term" AS "search_term"
        ,"event_param_source" AS "event_param_source"
        ,"event_param_sport_id" AS "event_param_sport_id"
        ,"event_param_sport_name" AS "event_param_sport_name"
        ,"event_param_update_with_analytics" AS "event_param_update_with_analytics"
        ,"event_param_view_mode" AS "event_param_view_mode"
        ,"open_browse" AS "open_browse"
        ,"reminders_feature" AS "reminders_feature"
        ,"firebase_experiment_parameters" AS "firebase_experiment_parameters"
        ,"dazn_event_date_dt" AS "dazn_event_date_dt"
        ,"dazn_event_timestamp_ts" AS "dazn_event_timestamp_ts"
        ,"dazn_event_previous_timestamp_ts" AS "dazn_event_previous_timestamp_ts"
        ,"dbt_inserted_at" AS "dss_inserted_at"
        ,"experiment_id" AS "experiment_id"
        ,"experiment_key" AS "experiment_key"
        ,"variation_id" AS "variation_id"
        ,"variation_key" AS "variation_key"
        ,"dazn_device_id" AS "dazn_device_id"
        ,"search_result_category" AS "search_result_category"
        ,"share_origin" AS "share_origin"
        ,"share_page" AS "share_page"
        ,"rail_title" AS "rail_title"
        ,"rail_name" AS "rail_name"
        ,"rail_length" AS "rail_length"
        ,"rail_position_of_loaded" AS "rail_position_of_loaded"
        ,"rail_position_in_view" AS "rail_position_in_view"
        ,"rail_position_of_tile_start" AS "rail_position_of_tile_start"
        ,"tile_title" AS "tile_title"
        ,"tile_position_of_loaded" AS "tile_position_of_loaded"
        ,"tile_position_in_view" AS "tile_position_in_view"
        ,"coming_up" AS "coming_up"
        ,"navigate_to" AS "navigate_to"
        ,"article_name" AS "article_name"
        ,"status" AS "status"
        ,"dazn_session_id" AS "dazn_session_id"
        ,"firebase_primary_key" AS "firebase_primary_key"
    FROM source
    WHERE {{ unixtime_build_trigger(var('build_mode'), var('rebuild_days'), 'event_timestamp_seconds') }}
)

SELECT * FROM renamed
