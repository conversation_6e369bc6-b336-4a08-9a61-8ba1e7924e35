{{
     config(
         materialized='incremental',
         incremental_strategy='delete+insert',
         unique_key=['"dazn_user_id"','"subscription_product_group"','"product_status_effective_from_pk"'],
         schema='PRESENTATION',
         snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH'
     )
 }}

 WITH usr AS 
         (SELECT * 
         from   OPEN__B2C__SEGMENT__PROD.SEGMENT_CONNECTOR_USER_EVENTS_PROD.USERS
         )
 ,zuora_account_current AS 
     (
         SELECT 
          COALESCE("dazn_user_id","crm_account_id") AS "dazn_user_id",
          "crm_account_id",
         "billing_account_id",
         "billing_account_status",
         "billing_account_last_updated_timestamp",
         "billing_account_created_timestamp"
         FROM  {{ ref('staging__zuora__account_current')}}
     )
 ,salesforce_account_current AS 
     (
         SELECT * FROM {{ ref('staging__salesforce__account_current')}}
     )
 ,user_account_created AS 
     (
         SELECT * FROM {{ ref('user_account_created') }}
     )
     {% if is_incremental() %}
 ,scd_existing AS 
     (
 	    SELECT * FROM {{ this }}
         WHERE "product_status_effective_until"::date = '9999-12-31' 
     )
 ,max_date As 
 (
     (SELECT coalesce(MAX("product_status_effective_from"),'1900-01-01') As maxtime FROM SCD_EXISTING)
 )
     {% endif %}
     ,user_txn AS 
     (
         SELECT * FROM {{ ref('subscription_status_event_txn') }}
     )
 , user_details_final AS (
 	with acct_created_date AS (
 	 SELECT "dazn_user_id","viewer_id",min("user_created_date") "user_created_date" FROM (
 	 SELECT
 		usr.DAZN_ID as "dazn_user_id",usr.VIEWER_ID "viewer_id",
 		least_ignore_nulls(acct_create_date,S."crm_account_created_timestamp"::date,A."billing_account_created_timestamp"::date,CREATED_TIMESTAMP::date,RECEIVED_AT::DATE) "user_created_date"
 		FROM usr
 		LEFT JOIN (SELECT
 		COALESCE(cust.DAZN_ID,cust2.DAZN_ID) "dazn_user_id","billing_account_created_timestamp" from
 		zuora_account_current A
 		LEFT JOIN usr cust ON A."dazn_user_id" = cust.DAZN_ID
 		LEFT JOIN usr cust2 ON A."dazn_user_id" = cust2.DAZN_ID
 		) A
 		on usr.DAZN_ID= A."dazn_user_id"
 		LEFT JOIN salesforce_account_current S
 		on usr.SALESFORCE_ID= S."crm_account_id"
 		LEFT JOIN (select "dazn_user_id", min("user_account_created_timestamp" )::date acct_create_date 
 		from user_account_created group by 1) acct
 		on acct."dazn_user_id"=usr.DAZN_ID
 		GROUP BY ALL
 		) GROUP BY ALL
 	)
 	,user_details AS (
 		SELECT DAZN_ID "dazn_user_id","crm_account_id", --SALESFORCE_ID "crm_account_id",
        "billing_account_id",USER_TYPE "user_type",SOURCE_TYPE "source_type"
 		FROM usr 
 		LEFT JOIN (
 		SELECT "dazn_user_id" ,"billing_account_id","crm_account_id"
 		FROM zuora_account_current A 
 		WHERE "billing_account_status" = 'Active' AND "billing_account_id" IS NOT null
 		QUALIFY row_number() OVER (PARTITION BY "dazn_user_id" ORDER BY "billing_account_last_updated_timestamp" desc)=1
 		)  A 
 		ON DAZN_ID ="dazn_user_id" 
 	)
 	select 
 	user_details."dazn_user_id","crm_account_id","billing_account_id", "user_type","source_type","viewer_id","user_created_date"
 	FROM 
 	user_details 
 	LEFT JOIN acct_created_date ON user_details."dazn_user_id" = acct_created_date."dazn_user_id"
 )
 ,

 alldataset as
 (
 select scd_inc."dazn_user_id",
 			UPPER(scd_inc."subscription_product_group") as "subscription_product_group",
 			scd_inc."subscription_product_status",
 			scd_inc."effective_start_date" "product_status_effective_from", 
             scd_inc."subscription_event_timestamp",
             scd_inc."subscription_status_event_type"
 		--	scd_existing."product_status_effective_from"  "product_status_effective_until" 
 from user_txn SCD_INC
 {% if is_incremental() %}
 inner join max_date on 1=1
   {% endif %} 
             WHERE "event_start_date"< CURRENT_DATE   
             {% if is_incremental() %}
             AND "subscription_status_event_type"  != 'SEP'
             AND "event_start_date" >= max_date.maxtime
             {% endif %} 
 {% if is_incremental() %}
 union all 
 select scd_existing."dazn_user_id",
 			UPPER(scd_existing."subscription_product_group") as "subscription_product_group",
 			scd_existing."subscription_product_status",
 			scd_existing."product_status_effective_from", 
             scd_existing."subscription_event_timestamp",
             scd_existing."subscription_status_event_type"
 		--	scd_existing."product_status_effective_from"  "product_status_effective_until" 
 from scd_existing 
 {% endif %}
 )


 ,remove_dups AS (
 SELECT * FROM (
 		SELECT a.*,iff("subscription_product_status" = lag("subscription_product_status") 
 			OVER ( PARTITION BY "dazn_user_id","subscription_product_group" order BY "product_status_effective_from","subscription_event_timestamp"),TRUE,FALSE) ignore_flag
 		fROM 
         (
             SELECT 
             * from
             alldataset
             --QUALIFY ROW_NUMBER() OVER (PARTITION BY "dazn_user_id","subscription_product_group","product_status_effective_from" ORDER BY "subscription_event_timestamp" DESC)=1
         ) a
 	)
 	WHERE ignore_flag = FALSE 
 )

 ,SCD_LOGIC AS (	
 		SELECT 
 			remove_dups."dazn_user_id",
             remove_dups."subscription_product_group",
             remove_dups."subscription_product_status",
 			remove_dups."product_status_effective_from",

 			CASE 

 				WHEN remove_dups."subscription_product_status" =  'Partial' THEN 
                 LEAD(remove_dups."product_status_effective_from", 1, '9999-12-31') OVER 
 				(PARTITION BY remove_dups."dazn_user_id" ORDER BY remove_dups."product_status_effective_from"::date,
                 COALESCE(remove_dups."subscription_event_timestamp",'1900-01-01')) 					
 				ELSE 
                 LEAD(remove_dups."product_status_effective_from", 1, '9999-12-31') OVER 
 			(PARTITION BY remove_dups."dazn_user_id",remove_dups."subscription_product_group" 
             ORDER BY remove_dups."product_status_effective_from"::date,COALESCE(remove_dups."subscription_event_timestamp",'1900-01-01') asc) 
 		     END "product_status_effective_until",
               remove_dups."subscription_event_timestamp",
               remove_dups."subscription_status_event_type"
 		FROM remove_dups
 )
 SELECT 
         final_insert."dazn_user_id",
         final_insert."subscription_product_group",
         final_insert."subscription_product_status",
         final_insert."product_status_effective_from",
         final_insert."product_status_effective_until",
         final_insert."subscription_event_timestamp",
         "crm_account_id",
         "billing_account_id",
         "user_type",
         "source_type",
         "viewer_id",
         least("user_created_date","product_status_effective_from") "user_created_date",
         case when final_insert."product_status_effective_from" =final_insert."product_status_effective_until" 
            then dateadd(millisecond,-1,final_insert."product_status_effective_from") 
            else final_insert."product_status_effective_from" end "product_status_effective_from_pk"
          ,"subscription_status_event_type"
          ,CURRENT_TIMESTAMP() AS META__DBT_INSERT_DTTS
 FROM 
     scd_logic final_insert
     LEFT JOIN 
     user_details_final user_details ON final_insert."dazn_user_id" = user_details."dazn_user_id"