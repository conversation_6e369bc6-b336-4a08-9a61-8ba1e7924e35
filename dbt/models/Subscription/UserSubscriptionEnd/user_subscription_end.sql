{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"subscription_name"',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_S_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH cust AS (
    SELECT 
    "dazn_user_id" AS "dazn_user_id"
    ,"salesforce_id" AS "crm_account_id"
    FROM  {{ ref('staging__segment__user_events_users') }}
)

,sub_name AS (
    SELECT * FROM {{ ref('staging__zuora__subscription_name_current') }}
    {% if is_incremental() %}
    WHERE
    "subscription_id_updated_timestamp"::DATE >= (SELECT MAX("subscription_id_updated_timestamp"::DATE) FROM {{ this }})
    {% endif %} 
QUALIFY ROW_NUMBER() OVER (PARTITION BY "subscription_name" ORDER BY "subscription_name_original_created_timestamp" DESC) =1
)

,acct AS (
    SELECT 
    COALESCE("dazn_user_id","crm_account_id") as "dazn_user_id",
    "billing_account_id" 
    FROM {{ ref('staging__zuora__account_current')}}
)

,source AS (SELECT
acct_cust."dev_dazn_user_id" AS "dazn_user_id",
COALESCE(sub_name."subscription_product_group",'DAZN') AS "product_group",
sub_name."subscription_name" AS "subscription_name",
sub_name."subscription_id_updated_timestamp" AS "subscription_id_updated_timestamp",
MIN(sub_name."subscription_start_date") AS "subscription_start_date",
MAX(case when sub_name."subscription_cancelled_date" IS NOT NULL THEN sub_name."subscription_cancelled_date"
ELSE sub_name."subscription_end_date"  END) AS "subscription_end_date"
FROM sub_name
INNER JOIN (SELECT
COALESCE(cust1."dazn_user_id",cust2."dazn_user_id") AS "dev_dazn_user_id",
acct."billing_account_id" 
FROM acct
LEFT JOIN cust cust1 
ON acct."dazn_user_id" = cust1."dazn_user_id"
LEFT JOIN cust cust2 
ON acct."dazn_user_id" = cust2."crm_account_id"
) acct_cust
ON acct_cust."billing_account_id" = sub_name."billing_account_id"
GROUP BY ALL
)

SELECT 
CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS",
"dazn_user_id",
"product_group",
"subscription_name",
"subscription_id_updated_timestamp",
"subscription_start_date",
"subscription_end_date"
from source
