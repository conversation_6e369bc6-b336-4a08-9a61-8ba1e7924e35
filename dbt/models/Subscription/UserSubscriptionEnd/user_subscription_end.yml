version: 2

models:
  - name: user_subscription_end
    description: ""
    columns:
      - name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the model"
        quote: true

      - name: dazn_user_id
        description: ""
        quote: true

      - name: product_group
        description: ""
        quote: true

      - name: subscription_name
        description: ""
        quote: true
        tests:
          - unique

      - name: subscription_id_updated_timestamp
        description: ""
        quote: true

      - name: subscription_start_date
        description: ""
        quote: true

      - name: subscription_end_date
        description: ""
        quote: true

      - name: event_start_date
        description: ""
        quote: true
