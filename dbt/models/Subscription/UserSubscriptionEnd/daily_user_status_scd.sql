{{
     config(
         materialized='incremental',
         incremental_strategy='delete+insert',
         unique_key=['"dazn_user_id"'],
         schema='PRESENTATION',
         snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
         tags=['presentation-subscription-domain']
     )
 }}

WITH usr AS (
    SELECT DAZN_ID "dazn_user_id",SALESFORCE_ID "crm_account_id",CREATED_TIMESTAMP,RECEIVED_AT,VIEWER_ID,USER_TYPE,SOURCE_TYPE
     from   OPEN__B2C__SEGMENT__PROD.SEGMENT_CONNECTOR_USER_EVENTS_PROD.USERS
)

,zuora_account_current AS (
    SELECT 
    COALESCE("dazn_user_id","crm_account_id") AS "dazn_user_id",
    "crm_account_id",
    "billing_account_id",
    "billing_account_status",
    "billing_account_last_updated_timestamp",
    "billing_account_created_timestamp"
    FROM  {{ ref_env('staging__zuora__account_current')}}
)

,salesforce_account_current AS (
    SELECT * FROM {{ ref_env('staging__salesforce__account_current')}}
)

,user_account_created AS (
    SELECT * FROM {{ ref_env('user_account_created') }}
)

,user_details_final AS (
    with acct_created_date AS (
        SELECT 
        "dazn_user_id",
        "viewer_id",
        min("user_created_date") "user_created_date" 
        FROM (
             SELECT
            usr."dazn_user_id" as "dazn_user_id",usr.VIEWER_ID "viewer_id",
            least_ignore_nulls(acct_create_date,S."crm_account_created_timestamp"::date,A."billing_account_created_timestamp"::date,CREATED_TIMESTAMP::date,RECEIVED_AT::DATE) "user_created_date"
            FROM usr
            LEFT JOIN 
            (
                SELECT
                COALESCE(cust."dazn_user_id",cust2."crm_account_id") "dazn_user_id","billing_account_created_timestamp" from
                zuora_account_current A
                LEFT JOIN usr cust ON A."dazn_user_id" = cust."dazn_user_id"
                LEFT JOIN usr cust2 ON A."dazn_user_id" = cust2."crm_account_id"
            ) A
            on usr."dazn_user_id"= A."dazn_user_id"
            LEFT JOIN salesforce_account_current S
            on usr."crm_account_id"= S."crm_account_id"
            LEFT JOIN (select "dazn_user_id", min("user_account_created_timestamp" )::date acct_create_date 
            from user_account_created group by 1) acct
            on acct."dazn_user_id"=usr."dazn_user_id"
            GROUP BY ALL
        ) GROUP BY ALL
    )
    ,user_details AS (
        SELECT 
            usr."dazn_user_id",
            A."crm_account_id",
            "billing_account_id",
            USER_TYPE "user_type",
            SOURCE_TYPE "source_type"
        FROM usr 
        LEFT JOIN (
        SELECT "dazn_user_id" ,"billing_account_id","crm_account_id"
        FROM zuora_account_current A 
        WHERE "billing_account_status" = 'Active' AND "billing_account_id" IS NOT null
        QUALIFY row_number() OVER (PARTITION BY "dazn_user_id" ORDER BY "billing_account_last_updated_timestamp" desc)=1
        )  A 
        ON usr."dazn_user_id" =A."dazn_user_id" 
    )
select 
    user_details."dazn_user_id",
    "crm_account_id",
    "billing_account_id", 
    "user_type",
    "source_type",
    "viewer_id",
    "user_created_date"
    FROM 
    user_details 
    LEFT JOIN acct_created_date ON user_details."dazn_user_id" = acct_created_date."dazn_user_id"
 )

{% if is_incremental() %}
    ,max_date As (
     SELECT coalesce(MAX("user_status_effective_from"),'1900-01-01') As maxtime FROM  {{ this }}
    )
    ,inc_users as (
        select "dazn_user_id" "inc_dazn_user_id" from {{ ref_env('subscription_product_status_scd') }}
        inner join max_date on 1=1 
        where "product_status_effective_from"::date >=  maxtime
        group by 1
    )
{% endif %}

,user_txn AS (
    SELECT s.* FROM {{ ref_env('subscription_product_status_scd') }} s
    {% if is_incremental() %}
        inner join inc_users on "dazn_user_id" = "inc_dazn_user_id"
    {% endif %} 
    WHERE  "dazn_user_id" is not null
    AND "product_status_effective_from"::date  < CURRENT_DATE   
    --and "viewer_id" in ('8129630478b6','8b08a734be6d','Ext-13286650')
)

,alldataset as(
    select 
    "dazn_user_id"
    ,CASE WHEN "subscription_status_event_type"='SEP' THEN "product_status_effective_from"::date ELSE "product_status_effective_from" END "effective_start_date"
    ,CASE WHEN "subscription_product_status" = 'Partial' THEN dateadd(s,-1,"product_status_effective_from") ELSE COALESCE("subscription_event_timestamp","product_status_effective_from") end "subscription_event_timestamp"
    ,"subscription_product_status",
    split_part(MAX
    (case when "subscription_product_group"='DAZN' then "product_status_effective_until" ||'*' || "subscription_product_status" END) over (partition by "dazn_user_id" 
    order by "product_status_effective_from" ),'*',2) DAZN_STATUS ,
    split_part(Max( 
    case when "subscription_product_group"='NFL' then "product_status_effective_until" ||'*' || "subscription_product_status" END) over (partition by "dazn_user_id" 
    order by "product_status_effective_from" ),'*',2) NFL_STATUS ,
    split_part(Max( 
    case when "subscription_product_group"='LIGASEGUNDA' then "product_status_effective_until" ||'*' || "subscription_product_status" END) over (partition by "dazn_user_id" 
    order by "product_status_effective_from" ),'*',2) LIGASEGUNDA_STATUS,
    split_part(Max( 
    case when "subscription_product_group"='PGA' then "product_status_effective_until" ||'*' || "subscription_product_status" END) over (partition by "dazn_user_id" 
    order by "product_status_effective_from" ),'*',2) PGA_STATUS,
    split_part(Max( 
    case when "subscription_product_group"='RALLYTV' then "product_status_effective_until" ||'*' || "subscription_product_status" END) over (partition by "dazn_user_id" 
    order by "product_status_effective_from" ),'*',2) RALLYTV_STATUS
    ,split_part(Max( 
    case when "subscription_product_group"='FIBA' then "product_status_effective_until" ||'*' || "subscription_product_status" END) over (partition by "dazn_user_id" 
    order by "product_status_effective_from" ),'*',2) FIBA_STATUS
    ,split_part(Max( 
    case when "subscription_product_group"='TENNISTV' then "product_status_effective_until" ||'*' || "subscription_product_status" END) over (partition by "dazn_user_id" 
    order by "product_status_effective_from" ),'*',2) TENNISTV_STATUS
    ,split_part(Max( 
    case when "subscription_product_group"='NATIONALLEAGUETV' then "product_status_effective_until" ||'*' || "subscription_product_status" END) over (partition by "dazn_user_id" 
    order by "product_status_effective_from" ),'*',2) NATIONALLEAGUETV_STATUS

     ,split_part(Max( 
    case when "subscription_product_group"='ELF' then "product_status_effective_until" ||'*' || "subscription_product_status" END) over (partition by "dazn_user_id" 
    order by "product_status_effective_from" ),'*',2) ELF_STATUS



    ,"subscription_product_group",
    "subscription_status_event_type"
    from user_txn
 )

 ,user_status_derivation as (
    SELECT 
    "dazn_user_id",
    "effective_start_date",
    "subscription_event_timestamp",
    case 
        when DAZN_STATUS = 'ActivePaid' or NFL_STATUS = 'ActivePaid' or FIBA_STATUS = 'ActivePaid' or LIGASEGUNDA_STATUS = 'ActivePaid'  
            or PGA_STATUS = 'ActivePaid' or RALLYTV_STATUS = 'ActivePaid'  or TENNISTV_STATUS = 'ActivePaid'  or NATIONALLEAGUETV_STATUS = 'ActivePaid'  or ELF_STATUS= 'ActivePaid'
        then 'ActivePaid'
        when DAZN_STATUS = 'FreeTrial' or NFL_STATUS = 'FreeTrial' or FIBA_STATUS = 'FreeTrial' or LIGASEGUNDA_STATUS = 'FreeTrial'  
            or PGA_STATUS = 'FreeTrial' or RALLYTV_STATUS = 'FreeTrial'  or TENNISTV_STATUS = 'FreeTrial'    or NATIONALLEAGUETV_STATUS = 'FreeTrial' or ELF_STATUS= 'FreeTrial'
        then 'FreeTrial'
        when DAZN_STATUS = 'ActiveGrace' or NFL_STATUS = 'ActiveGrace' or FIBA_STATUS = 'ActiveGrace' or LIGASEGUNDA_STATUS = 'ActiveGrace'  
            or PGA_STATUS = 'ActiveGrace' or RALLYTV_STATUS = 'ActiveGrace'  or TENNISTV_STATUS = 'ActiveGrace'  or NATIONALLEAGUETV_STATUS = 'ActiveGrace'  or ELF_STATUS= 'ActiveGrace' 
        then 'ActiveGrace'
        when DAZN_STATUS = 'Paused' or NFL_STATUS = 'Paused' or FIBA_STATUS = 'Paused' or LIGASEGUNDA_STATUS = 'Paused'  
            or PGA_STATUS = 'Paused' or RALLYTV_STATUS = 'Paused'  or TENNISTV_STATUS = 'Paused'   or NATIONALLEAGUETV_STATUS = 'Paused' or ELF_STATUS= 'Paused'
        then 'Paused'
        when DAZN_STATUS = 'Frozen' or NFL_STATUS = 'Frozen' or FIBA_STATUS = 'Frozen' or LIGASEGUNDA_STATUS = 'Frozen'  
            or PGA_STATUS = 'Frozen' or RALLYTV_STATUS = 'Frozen'  or TENNISTV_STATUS = 'Frozen'  or NATIONALLEAGUETV_STATUS = 'Frozen'  or ELF_STATUS= 'Frozen'
        then 'Frozen'
        else 'Partial'     end as "user_status",

         LTRIM( case when DAZN_STATUS is not null then ',DAZN'||':'||DAZN_STATUS else '' end ||
        case when NFL_STATUS is not null then ',NFL'||':'||NFL_STATUS else '' end ||
        case when FIBA_STATUS is not null then ',FIBA'||':'||FIBA_STATUS else '' end ||
       case when LIGASEGUNDA_STATUS is not null then ',LIGASEGUNDA'||':'||LIGASEGUNDA_STATUS else '' end ||
      case when PGA_STATUS is not null then ',PGA'||':'||PGA_STATUS  else ' ' end ||
       case when RALLYTV_STATUS is not null then ',RALLYTV'||':'||RALLYTV_STATUS else '' end ||
       case when TENNISTV_STATUS is not null then ',TENNISTV'||':'||TENNISTV_STATUS else '' end ||
      case when NATIONALLEAGUETV_STATUS is not null then ',NATIONALLEAGUETV'||':'||NATIONALLEAGUETV_STATUS else '' end ||
     case when  ELF_STATUS is not null then ',ELF'||':'||ELF_STATUS else '' end,',' ) as  "all_product_status",


  LTRIM(case when DAZN_STATUS not in ('Frozen','Partial') then ',DAZN' else '' end ||
  case when NFL_STATUS not in ('Frozen','Partial') then ',NFL'  else '' end  ||
 case when FIBA_STATUS not in ('Frozen','Partial') then ',FIBA'  else ''  end ||
 case when LIGASEGUNDA_STATUS not in ('Frozen','Partial') then ',LIGASEGUNDA'  else ''  end ||
 case when PGA_STATUS not in ('Frozen','Partial') then ',PGA'  else '' end ||
 case when RALLYTV_STATUS not in ('Frozen','Partial') then ',RALLYTV' else ''  end ||
 case when TENNISTV_STATUS  not in ('Frozen','Partial') then ',TENNISTV' else '' end ||
 case when NATIONALLEAGUETV_STATUS not in ('Frozen','Partial') then ',NATIONALLEAGUETV' else ''  end ||
 case when  ELF_STATUS not in ('Frozen','Partial') then ',ELF' else ''  end, ',') as  "paid_products"

 
    from alldataset
 )




 , dedup as (
    select 
    "dazn_user_id"
    ,"user_status"
    ,"effective_start_date"
    ,"subscription_event_timestamp"
    ,TRIM("all_product_status")  as "all_product_status"
    ,TRIM("paid_products") as "paid_products"
    from (
        select 
        *,
        iff("user_status" = lag("user_status") OVER ( PARTITION BY "dazn_user_id" order BY "effective_start_date","subscription_event_timestamp")
            and "all_product_status" = lag("all_product_status") OVER ( PARTITION BY "dazn_user_id" order BY "effective_start_date","subscription_event_timestamp")
            and "paid_products" = lag("paid_products") OVER ( PARTITION BY "dazn_user_id" order BY "effective_start_date","subscription_event_timestamp")
            ,TRUE,FALSE
        ) ignore_flag
        from 
        user_status_derivation
    )
    where ignore_flag=FALSE
 )

 , final as (
    select 
    "dazn_user_id",
    "user_status", 
    "effective_start_date" "user_status_effective_from",
    LEAD("effective_start_date", 1, '9999-12-31') OVER 
            (PARTITION BY "dazn_user_id"
             ORDER BY "effective_start_date",COALESCE("subscription_event_timestamp",'1900-01-01')) 
             as "user_status_effective_until" 
    ,"all_product_status"
    ,"paid_products"
    FROM dedup
 )

select final.*,
         "crm_account_id",
         "billing_account_id",
         "user_type",
         "source_type",
         "viewer_id",
         least("user_created_date","user_status_effective_from") "user_created_date"

from 
final
LEFT JOIN 
user_details_final user_details ON final."dazn_user_id" = user_details."dazn_user_id"
