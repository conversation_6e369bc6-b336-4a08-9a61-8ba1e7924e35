{{
     config(
         materialized='incremental',
         incremental_strategy='delete+insert',
         unique_key=['"dazn_user_id"'],
         schema='PRESENTATION',
         snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH'
     )
 }}

WITH usr AS (
    SELECT DAZN_ID "dazn_user_id",SALESFORCE_ID "crm_account_id",CREATED_TIMESTAMP,RECEIVED_AT,VIEWER_ID,USER_TYPE,SOURCE_TYPE
     from   OPEN__B2C__SEGMENT__PROD.SEGMENT_CONNECTOR_USER_EVENTS_PROD.USERS
)

,zuora_account_current AS (
    SELECT 
    COALESCE("dazn_user_id","crm_account_id") AS "dazn_user_id",
    "crm_account_id",
    "billing_account_id",
    "billing_account_status",
    "billing_account_last_updated_timestamp",
    "billing_account_created_timestamp"
    FROM  {{ ref('staging__zuora__account_current')}}
)

,salesforce_account_current AS (
    SELECT * FROM {{ ref('staging__salesforce__account_current')}}
)

,user_account_created AS (
    SELECT * FROM {{ ref('user_account_created') }}
)

,user_details_final AS (
 	with acct_created_date AS (
 	    SELECT 
        "dazn_user_id",
        "viewer_id",
        min("user_created_date") "user_created_date" 
        FROM (
 	         SELECT
            usr."dazn_user_id" as "dazn_user_id",usr.VIEWER_ID "viewer_id",
            least_ignore_nulls(acct_create_date,S."crm_account_created_timestamp"::date,A."billing_account_created_timestamp"::date,CREATED_TIMESTAMP::date,RECEIVED_AT::DATE) "user_created_date"
            FROM usr
            LEFT JOIN 
            (
                SELECT
                COALESCE(cust."dazn_user_id",cust2."crm_account_id") "dazn_user_id","billing_account_created_timestamp" from
                zuora_account_current A
                LEFT JOIN usr cust ON A."dazn_user_id" = cust."dazn_user_id"
                LEFT JOIN usr cust2 ON A."dazn_user_id" = cust2."crm_account_id"
 		    ) A
 		    on usr."dazn_user_id"= A."dazn_user_id"
            LEFT JOIN salesforce_account_current S
            on usr."crm_account_id"= S."crm_account_id"
            LEFT JOIN (select "dazn_user_id", min("user_account_created_timestamp" )::date acct_create_date 
            from user_account_created group by 1) acct
            on acct."dazn_user_id"=usr."dazn_user_id"
            GROUP BY ALL
 		) GROUP BY ALL
 	)
    ,user_details AS (
        SELECT 
            usr."dazn_user_id",
            A."crm_account_id",
            "billing_account_id",
            USER_TYPE "user_type",
            SOURCE_TYPE "source_type"
        FROM usr 
        LEFT JOIN (
        SELECT "dazn_user_id" ,"billing_account_id","crm_account_id"
        FROM zuora_account_current A 
        WHERE "billing_account_status" = 'Active' AND "billing_account_id" IS NOT null
        QUALIFY row_number() OVER (PARTITION BY "dazn_user_id" ORDER BY "billing_account_last_updated_timestamp" desc)=1
        )  A 
        ON usr."dazn_user_id" =A."dazn_user_id" 
    )
select 
 	user_details."dazn_user_id",
    "crm_account_id",
    "billing_account_id", 
    "user_type",
    "source_type",
    "viewer_id",
    "user_created_date"
 	FROM 
 	user_details 
 	LEFT JOIN acct_created_date ON user_details."dazn_user_id" = acct_created_date."dazn_user_id"
 )

{% if is_incremental() %}
    ,max_date As (
     SELECT coalesce(MAX("user_status_effective_from"),'1900-01-01') As maxtime FROM  {{ this }}
    )
    ,inc_users as (
        select "dazn_user_id" "inc_dazn_user_id" from {{ ref('subscription_status_event_txn') }}
        inner join max_date on 1=1 
        where "effective_start_date" >=  maxtime and "subscription_status_event_type"!='SEP'
        group by 1
    )
{% endif %}

,user_txn AS (
    SELECT s.* FROM {{ ref('subscription_status_event_txn') }} s
    {% if is_incremental() %}
        inner join inc_users on "dazn_user_id" = "inc_dazn_user_id"
    {% endif %} 
    WHERE "event_start_date"< CURRENT_DATE   
)

,alldataset as(
    select 
    "dazn_user_id"
    ,CASE WHEN "subscription_status_event_type"='SEP' THEN "effective_start_date"::date ELSE "effective_start_date" END "effective_start_date"
    ,CASE WHEN "subscription_product_status" = 'Partial' THEN dateadd(s,-1,"effective_start_date") ELSE COALESCE("subscription_event_timestamp","effective_start_date") end "subscription_event_timestamp"
    ,"subscription_product_status",
    split_part(
        max(case 
                when "subscription_product_group"='DAZN' then "effective_start_date"|| COALESCE("subscription_event_timestamp","effective_start_date") ||'*' || "subscription_product_status" 
            END) 
        over (partition by "dazn_user_id" order by "effective_start_date",COALESCE("subscription_event_timestamp","effective_start_date")),'*',2
    ) DAZN_STATUS ,
    split_part(Max( 
    case when "subscription_product_group"='NFL' then "effective_start_date" || COALESCE("subscription_event_timestamp","effective_start_date") ||'*' || "subscription_product_status" END) over (partition by "dazn_user_id" 
    order by "effective_start_date",COALESCE("subscription_event_timestamp","effective_start_date")),'*',2) NFL_STATUS ,
    split_part(Max( 
    case when "subscription_product_group"='LIGASEGUNDA' then "effective_start_date" || COALESCE("subscription_event_timestamp","effective_start_date") ||'*' || "subscription_product_status" END)over (partition by "dazn_user_id" 
    order by "effective_start_date",COALESCE("subscription_event_timestamp","effective_start_date")),'*',2) LIGASEGUNDA_STATUS,
    split_part(Max( 
    case when "subscription_product_group"='PGA' then "effective_start_date" || COALESCE("subscription_event_timestamp","effective_start_date") ||'*' || "subscription_product_status" END)over (partition by "dazn_user_id" 
    order by "effective_start_date",COALESCE("subscription_event_timestamp","effective_start_date")),'*',2) PGA_STATUS,
    split_part(Max( 
    case when "subscription_product_group"='RALLYTV' then "effective_start_date" || COALESCE("subscription_event_timestamp","effective_start_date") ||'*' || "subscription_product_status" END)over (partition by "dazn_user_id" 
    order by "effective_start_date",COALESCE("subscription_event_timestamp","effective_start_date")),'*',2) RALLYTV_STATUS
    ,split_part(Max( 
    case when "subscription_product_group"='FIBA' then "effective_start_date" || COALESCE("subscription_event_timestamp","effective_start_date") ||'*' || "subscription_product_status" END)over (partition by "dazn_user_id" 
    order by "effective_start_date",COALESCE("subscription_event_timestamp","effective_start_date")),'*',2) FIBA_STATUS
    ,split_part(Max( 
    case when "subscription_product_group"='TENNISTV' then "effective_start_date" || COALESCE("subscription_event_timestamp","effective_start_date") ||'*' || "subscription_product_status" END)over (partition by "dazn_user_id" 
    order by "effective_start_date",COALESCE("subscription_event_timestamp","effective_start_date")),'*',2) TENNISTV_STATUS
    ,"subscription_product_group",
    "subscription_status_event_type"
    from user_txn
 )

 ,user_status_derivation as (
    SELECT 
    "dazn_user_id",
    "effective_start_date",
    "subscription_event_timestamp",
    case 
        when DAZN_STATUS = 'ActivePaid' or NFL_STATUS = 'ActivePaid' or FIBA_STATUS = 'ActivePaid' or LIGASEGUNDA_STATUS = 'ActivePaid'  
            or PGA_STATUS = 'ActivePaid' or RALLYTV_STATUS = 'ActivePaid'  or TENNISTV_STATUS = 'ActivePaid'   
        then 'ActivePaid'
        when DAZN_STATUS = 'FreeTrial' or NFL_STATUS = 'FreeTrial' or FIBA_STATUS = 'FreeTrial' or LIGASEGUNDA_STATUS = 'FreeTrial'  
            or PGA_STATUS = 'FreeTrial' or RALLYTV_STATUS = 'FreeTrial'  or TENNISTV_STATUS = 'FreeTrial'   
        then 'FreeTrial'
        when DAZN_STATUS = 'ActiveGrace' or NFL_STATUS = 'ActiveGrace' or FIBA_STATUS = 'ActiveGrace' or LIGASEGUNDA_STATUS = 'ActiveGrace'  
            or PGA_STATUS = 'ActiveGrace' or RALLYTV_STATUS = 'ActiveGrace'  or TENNISTV_STATUS = 'ActiveGrace'   
        then 'ActiveGrace'
        when DAZN_STATUS = 'Paused' or NFL_STATUS = 'Paused' or FIBA_STATUS = 'Paused' or LIGASEGUNDA_STATUS = 'Paused'  
            or PGA_STATUS = 'Paused' or RALLYTV_STATUS = 'Paused'  or TENNISTV_STATUS = 'Paused'   
        then 'Paused'
        when DAZN_STATUS = 'Frozen' or NFL_STATUS = 'Frozen' or FIBA_STATUS = 'Frozen' or LIGASEGUNDA_STATUS = 'Frozen'  
            or PGA_STATUS = 'Frozen' or RALLYTV_STATUS = 'Frozen'  or TENNISTV_STATUS = 'Frozen'   
        then 'Frozen'
        else 'Partial' 
    end as "user_status"
    from alldataset
 )

 , dedup as (
    select 
    "dazn_user_id"
    ,"user_status"
    ,"effective_start_date"
    ,"subscription_event_timestamp"
    from (
        select 
        *,
        iff("user_status" = lag("user_status") 
            OVER ( PARTITION BY "dazn_user_id" order BY "effective_start_date","subscription_event_timestamp")
            ,TRUE,FALSE
        ) ignore_flag
        from 
        user_status_derivation
    )
    where ignore_flag=FALSE
 )

 , final as (
    select 
    "dazn_user_id",
    "user_status", 
    "effective_start_date" "user_status_effective_from",
	LEAD("effective_start_date", 1, '9999-12-31') OVER 
 			(PARTITION BY "dazn_user_id"
             ORDER BY "effective_start_date",COALESCE("subscription_event_timestamp",'1900-01-01')) 
             as "user_status_effective_until" 
    FROM dedup
 )

select final.*,
         "crm_account_id",
         "billing_account_id",
         "user_type",
         "source_type",
         "viewer_id",
         least("user_created_date","user_status_effective_from") "user_created_date",
from 
final
LEFT JOIN 
user_details_final user_details ON final."dazn_user_id" = user_details."dazn_user_id"