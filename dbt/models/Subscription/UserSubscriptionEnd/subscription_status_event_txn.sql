{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key=['"dazn_user_id"','"subscription_product_group"','"effective_start_date"'],
        cluster_by=['"event_start_date"'],
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        tags=['presentation-subscription-domain']
    )
}}

    with cust AS (
        SELECT DAZN_ID "dazn_user_id",SALESFORCE_ID "crm_account_id",CREATED_TIMESTAMP,RECEIVED_AT
        from  OPEN__B2C__SEGMENT__PROD.SEGMENT_CONNECTOR_USER_EVENTS_PROD.USERS
    )
    ,entitlement as (
        Select * from TRANSFORMATION_PROD.CURATED.CURATED__USER_ENTITLEMENT_LOG
        Qualify row_number() over (partition by "USER_ID" order by "EVENT_TIMESTAMP" desc)=1
    )
    ,rateplancharge as (
        Select *  FROM {{ ref('staging__zuora__rateplancharge_current')}}
    )
    ,rateplan as (
        Select * from  {{ ref('staging__zuora__rateplan_current')}}
    )
    ,subid as (
        Select * from {{ ref('staging__zuora__subscription_id_current') }} 
    )
    ,subname as (
        select * from {{ ref('staging__zuora__subscription_name_current') }} 
    )
    ,account as (
        select 
        COALESCE("dazn_user_id","crm_account_id") AS "dazn_user_id",
        "billing_account_id",
         "billing_account_status",
         "billing_account_last_updated_timestamp",
         "billing_account_created_timestamp"
        from {{ ref('staging__zuora__account_current')}} 
    )
    ,amendment as (
        select * from {{ ref('staging__zuora__amendment')}}
    )
    ,subend as (
        select * from {{ ref('user_subscription_end')}}
    )
    ,payment as (
        select * from {{ ref('staging__zuora__payment_current')}}
    )
    ,invoice as (
        select * from {{ ref('staging__zuora__invoice_current')}}
    )
    ,invoicepayment as (
        select * from {{ ref('staging__zuora__invoice_payment_current')}} 
    )
    ,invoiceitem as (
        select * from {{ ref('staging__zuora__invoice_item_current')}} 
    )
    ,salesforce as (
        select * from {{ ref('staging__salesforce__account_current')}} 
    )
    ,acctcreated as (
        select * from {{ ref('user_account_created') }} 
    )
    ,productstatuslog as (
        SELECT * FROM TRANSFORMATION_PROD.CURATED.CURATED__SEPSERVICE_USER_PRODUCT_STATUS_LOG
    )
    

    SELECT
        CURRENT_TIMESTAMP() AS "META__DBT_INSERT_DTTS",
        "dazn_user_id",
       UPPER("subscription_product_group") as "subscription_product_group",
        "effective_start_date",
        "subscription_product_status",
        "subscription_event_timestamp",
        "subscription_status_event_type",
        "effective_start_date"::date "event_start_date"
        FROM (
        select
                A."dazn_user_id",
                COALESCE(S."subscription_product_group",'DAZN') "subscription_product_group",
                CASE
                                WHEN  RC."rateplan_charge_effective_start_date" > S."subscription_name_original_created_timestamp"
                                then RC."rateplan_charge_effective_start_date" Else S."subscription_name_original_created_timestamp" :: date
                END "effective_start_date",
                case
                                when s."subscription_number_of_free_trial_periods" >0 and
                                RC."rateplan_charge_effective_start_date" :: date =S."subscription_name_original_created_timestamp" ::date
                                then 'FreeTrial'
                                ELSE 'ActivePaid'
                END "subscription_product_status",
                CASE WHEN  RC."rateplan_charge_effective_start_date" > S."subscription_name_original_created_timestamp"
                then RC."rateplan_charge_effective_start_date" Else S."subscription_name_original_created_timestamp" END "subscription_event_timestamp",
                'RateplanEvent' AS "subscription_status_event_type"
                from rateplancharge RC
                INNER JOIN rateplan RP
                ON RC."rateplan_id" =RP."rateplan_id"
                INNER JOIN subid S
                ON S."subscription_id" =RP."subscription_id"
                INNER JOIN subname SN
                ON SN."subscription_name" =S."subscription_name"
                INNER JOIN
                (SELECT
                COALESCE(cust."dazn_user_id",cust2."dazn_user_id") "dazn_user_id",a."billing_account_id" from
                account A
                LEFT JOIN cust cust ON a."dazn_user_id" = cust."dazn_user_id"
                LEFT JOIN cust cust2 ON a."dazn_user_id" = cust2."crm_account_id"
                ) a
                ON A."billing_account_id"=S."billing_account_id"
                {% if is_incremental() %}
            INNER JOIN  (SELECT 
            COALESCE(MAX("event_start_date"),'1900-01-01') AS "event_start_date" from {{this}}
            WHERE "subscription_status_event_type" = 'RateplanEvent'
            AND "event_start_date" < CURRENT_DATE()) AS rp_incr
            on "effective_start_date" >= "event_start_date"
        {% endif %}
                        WHERE "dazn_user_id" is not NULL
                        AND "effective_start_date" <= SN."subscription_end_date"
        group by ALL
        UNION ALL
select
                COALESCE(cust."dazn_user_id",cust2."dazn_user_id") "dazn_user_id",
                COALESCE(S."subscription_product_group",'DAZN') "subscription_product_group",
                --am."amendment_contract_effective_date" "effective_start_date",
                case when "amendment_type" ='ResumeSubscription' then am."amendment_resume_date" 
                else am."amendment_contract_effective_date" end as "effective_start_date",
                case when "amendment_type" ='SuspendSubscription' then'Paused'  ELSE 'ActivePaid' end "subscription_product_status",
                AM."amendment_created_timestamp" "subscription_event_timestamp",
                'SubscriptionPauseEvent' AS "subscription_status_event_type"
                from subid SI
                INNER JOIN subname S ON S."subscription_name"=SI."subscription_name"
                INNER JOIN amendment AM
                On SI."subscription_id"=AM."subscription_id"
                AND  Am."amendment_type" in ('SuspendSubscription','ResumeSubscription','Renewal')
                INNER JOIN account A
                LEFT JOIN cust cust ON A."dazn_user_id" = cust."dazn_user_id"
                LEFT JOIN cust cust2 ON A."dazn_user_id" = cust2."crm_account_id"
                ON A."billing_account_id"=S."billing_account_id"
                {% if is_incremental() %}
    INNER JOIN  (SELECT 
    COALESCE(MAX("event_start_date"),'1900-01-01') AS "event_start_date" from {{this}}
    WHERE "subscription_status_event_type" = 'SubscriptionPauseEvent'
    AND "event_start_date" < CURRENT_DATE()) AS pause 
    --on "effective_start_date" >= "event_start_date"
    on "subscription_event_timestamp"::date >= "event_start_date"
 {% endif %}
                where  A."dazn_user_id" is not NULL
                --AND  am."amendment_contract_effective_date" < S."subscription_end_date"::date
                AND "effective_start_date" < S."subscription_end_date"::date
group by ALL
UNION ALL
SELECT
                hardcancel."dazn_user_id",
                hardcancel.PRODUCTGROUP "subscription_product_group",
                hardcancel.START_DATE "effective_start_date",
                hardcancel.Product_status "subscription_product_status",
                "subscription_event_timestamp",
                'SubscriptionCancelEvent' AS "subscription_status_event_type"
                FROM (
                                select
                                COALESCE(cust."dazn_user_id",cust2."dazn_user_id") "dazn_user_id",
                                COALESCE("subscription_product_group",'DAZN') PRODUCTGROUP,
                                am."amendment_contract_effective_date" START_DATE,AM."amendment_created_timestamp",
                                GREATEST_ignore_nulls (AM."amendment_created_timestamp",DATEADD(min,1, am."amendment_contract_effective_date"))  "subscription_event_timestamp",
                                'Frozen' Product_status, "subscription_name"
                                from subname S
                                INNER JOIN amendment AM
                                On S."subscription_previous_subscription_id"=AM."subscription_id"
                                AND  Am."amendment_type"='Cancellation'
                                INNER JOIN account A
                                LEFT JOIN cust cust ON A."dazn_user_id" = cust."dazn_user_id"
                                LEFT JOIN cust cust2 ON A."dazn_user_id" = cust2."crm_account_id"
                                ON A."billing_account_id"=S."billing_account_id"
                                and A."dazn_user_id" is not NULL AND  "subscription_end_date"::date <= am."amendment_contract_effective_date"
                                group by ALL
                ) hardcancel LEFT JOIN subend sub_end
                                ON hardcancel."dazn_user_id" = sub_end."dazn_user_id" AND
                                hardcancel."PRODUCTGROUP" = sub_end."product_group" and
                                hardcancel."subscription_name" <> sub_end."subscription_name"
                                AND  hardcancel.START_DATE >= sub_end."subscription_start_date" and  hardcancel.START_DATE  < COALESCE(sub_end."subscription_end_date",'9999-12-31')
                {% if is_incremental() %}
    INNER JOIN  (SELECT 
    COALESCE(MAX("event_start_date"),'1900-01-01') AS "event_start_date" from {{this}}
    WHERE "subscription_status_event_type" = 'SubscriptionCancelEvent'
    AND "event_start_date" < CURRENT_DATE()) AS cancel 
    --on "effective_start_date" >= "event_start_date"
    on "amendment_created_timestamp"::date >= "event_start_date"
 {% endif %}
 WHERE  sub_end."dazn_user_id" IS NULL
union ALl
                SELECT
                                hardcancel."dazn_user_id",
                                hardcancel.PRODUCTGROUP "subscription_product_group",
                                hardcancel.START_DATE "effective_start_date",
                                hardcancel.Product_status "subscription_product_status",
                                dateADD(min,1,hardcancel.START_DATE) "subscription_event_timestamp",
                                'SubscriptionEndEvent' AS "subscription_status_event_type"
                                FROM (
                                                select
                                                COALESCE(cust."dazn_user_id",cust2."dazn_user_id") "dazn_user_id",
                                                COALESCE("subscription_product_group",'DAZN') PRODUCTGROUP,
                                                "subscription_end_date" START_DATE,
                                                --"subscription_id_updated_timestamp",
                                                'Frozen' Product_status, "subscription_name"
                                                from subname S
                                                INNER JOIN account A
                                                LEFT JOIN cust cust ON A."dazn_user_id" = cust."dazn_user_id"
                                                LEFT JOIN cust cust2 ON A."dazn_user_id" = cust2."crm_account_id"
                                                ON A."billing_account_id"=S."billing_account_id"
                                                WHERE
                                                "subscription_end_date" <= CURRENT_DATE()
                                                and A."dazn_user_id" is not NULL
                                ) hardcancel LEFT JOIN subend sub_end
                                                ON hardcancel."dazn_user_id" = sub_end."dazn_user_id" AND
                                                hardcancel."PRODUCTGROUP" = sub_end."product_group" and
                                                hardcancel."subscription_name" <> sub_end."subscription_name"                         
                                                AND  hardcancel.START_DATE >= sub_end."subscription_start_date"
                                                and  hardcancel.START_DATE  < COALESCE(sub_end."subscription_end_date",'9999-12-31')
                                                {% if is_incremental() %}
    INNER JOIN  (SELECT 
    COALESCE(MAX("event_start_date"),'1900-01-01') AS "event_start_date" from {{this}}
    WHERE "subscription_status_event_type" = 'SubscriptionEndEvent'
    AND "event_start_date" < CURRENT_DATE()) AS end_event 
    on "effective_start_date" >= "event_start_date"
 {% endif %}
                                WHERE  sub_end."dazn_user_id" IS NULL
UNION ALL
SELECT
                                grace."dazn_user_id",
                                grace.PRODUCTGROUP "subscription_product_group",
                                grace.START_DATE "effective_start_date",
                                grace.Product_status "subscription_product_status",
                                "payment_created_timestamp" "subscription_event_timestamp",
                                'PaymentEvent' AS "subscription_status_event_type"
                                FROM (
                                                select
                                                "dazn_user_id",
                                                COALESCE("subscription_product_group",'DAZN') PRODUCTGROUP,
                                                COALESCE("invoice_due_date","invoice_date") START_DATE,
                                                "payment_created_timestamp",
                                                case when "payment_status" ='Error' then 'ActiveGrace' else 'ActivePaid' end  Product_status,"subscription_name"
                                                from payment P
                                                INNER JOIN invoicepayment IP ON
                                                P."payment_id"=IP."payment_id"
                                                AND "payment_status" in ('Error','Processed')
                                                INNER JOIN invoice I ON
                                                I."invoice_id"=IP."invoice_id"
                                                INNER JOIN invoiceitem II ON
                                                II."invoice_id"=I."invoice_id"
                                                INNER JOIN rateplancharge rc
                                                on ii."rateplan_charge_id" = rc."rateplan_charge_id"
                                                inner join rateplan r
                                                on rc."rateplan_id"=r."rateplan_id"
                                                INNER JOIN account A
                                                ON A."billing_account_id"=I."billing_account_id"
                                                INNER JOIN subname S
                                                on ii."subscription_number"=S."subscription_name"
                                                and COALESCE("invoice_due_date","invoice_date")<"subscription_end_date"
                                                where r."rateplan_product_type" is null OR
                                                  upper(r."rateplan_product_type") not in ('PPV','ADDON') 
                                                QUALIFY ROW_NUMBER() OVER (PARTITION BY "dazn_user_id",
                                                PRODUCTGROUP,
                                                START_DATE ORDER BY "payment_created_timestamp" desc)=1
                                ) grace
                                LEFT JOIN subend sub_end
                                                ON grace."dazn_user_id" = sub_end."dazn_user_id" AND
                                                grace."PRODUCTGROUP" = sub_end."product_group" and
                                                grace."subscription_name" <> sub_end."subscription_name"
                                                AND  grace.START_DATE BETWEEN sub_end."subscription_start_date" AND COALESCE(sub_end."subscription_end_date",'9999-12-31')
                               {% if is_incremental() %}
    INNER JOIN  (SELECT 
    COALESCE(MAX("event_start_date"),'1900-01-01') AS "event_start_date" from {{this}}
    WHERE "subscription_status_event_type" = 'PaymentEvent'
    AND "event_start_date" < CURRENT_DATE()) AS pay_event 
    on "subscription_event_timestamp" >= "event_start_date" 
 {% endif %}
  WHERE  sub_end."dazn_user_id" IS NULL
                group by all
union all
                select
                COALESCE(cust."dazn_user_id",cust2."dazn_user_id")  as "dazn_user_id",
                'DAZN' as "subscription_product_group",
                "EVENT_TIMESTAMP"::date "effective_start_date",
                case when "EVENT_NAME" ilike '%REMOVE%' THEN 'Frozen' ELSE 'ActivePaid' End "subscription_product_status",
                "EVENT_TIMESTAMP" "subscription_event_timestamp",
                'DocomoEvent' AS "subscription_status_event_type"
                from entitlement E
                LEFT JOIN cust cust ON E."USER_ID" = cust."dazn_user_id"
                LEFT JOIN cust cust2 ON E."USER_ID" = cust2."crm_account_id"

                {% if is_incremental() %}
                        INNER JOIN  (SELECT 
    COALESCE(MAX("event_start_date"),'1900-01-01') AS "event_start_date" from {{this}}
    WHERE "subscription_status_event_type" = 'DocomoEvent'
    AND "event_start_date" < CURRENT_DATE()) AS docomo
    on "DWH_INSERT_TIMESTAMP" >= "event_start_date"
 {% endif %}
                 where "SOURCE"='Docomo'  
                group by ALL
union all
select "dazn_user_id","subscription_product_group",min("effective_start_date") as "effective_start_date",
"subscription_product_status","subscription_event_timestamp","subscription_status_event_type" from (

SELECT
                U."dazn_user_id" as "dazn_user_id",
               
                'DAZN' as "subscription_product_group",
                GREATEST_ignore_nulls(acct_create_date,S."crm_account_created_timestamp"::date,A."billing_account_created_timestamp"::date,CREATED_TIMESTAMP::date,RECEIVED_AT::date) "effective_start_date",
                'Partial' As "subscription_product_status",
                NULL "subscription_event_timestamp",
                'UserCreateEvent' AS "subscription_status_event_type"
                from cust U
                LEFT JOIN (SELECT
                COALESCE(cust."dazn_user_id",cust2."dazn_user_id") "dazn_user_id","billing_account_created_timestamp" from
                account  A
                LEFT JOIN cust cust ON a."dazn_user_id" = cust."dazn_user_id"
                LEFT JOIN cust cust2 ON a."dazn_user_id" = cust2."crm_account_id"
                ) A
                on U."dazn_user_id"= A."dazn_user_id"
                LEFT JOIN salesforce S
                on U."crm_account_id"= S."crm_account_id"
                LEFT JOIN (select "dazn_user_id", min("user_account_created_timestamp" )::date acct_create_date from acctcreated group by 1) acct
                on acct."dazn_user_id"=U."dazn_user_id"

                {% if is_incremental() %}
    INNER JOIN  (SELECT 
    COALESCE(MAX("event_start_date"),'1900-01-01') AS "event_start_date" from {{this}}
    WHERE "subscription_status_event_type" = 'UserCreateEvent'
    AND "event_start_date" < CURRENT_DATE()) AS user_create_event
    on "effective_start_date" >= "event_start_date"
    LEFT JOIN (
        SELECT "dazn_user_id" FROM {{this}}
        WHERE "subscription_status_event_type" = 'UserCreateEvent'
        GROUP BY 1
    ) AS id_event
    ON U."dazn_user_id"=id_event."dazn_user_id"
    WHERE id_event."dazn_user_id" IS NULL
 {% endif %}
group by ALL
) GROUP BY ALL
UNION ALL       
                SELECT
                USER_ID "dazn_user_id" ,
                PRODUCT_GROUP "subscription_product_group" ,
                EVENT_TIMESTAMP "effective_start_date" ,
                PRODUCT_STATUS "subscription_product_status" ,
                EVENT_TIMESTAMP  "subscription_event_timestamp" ,
                'SEP' "subscription_status_event_type"
                FROM productstatuslog  ---change tocurated view

                {% if is_incremental() %}
    INNER JOIN  (SELECT 
    COALESCE(MAX("event_start_date"),'1900-01-01') AS "event_start_date" from {{this}}
    WHERE "subscription_status_event_type" = 'SEP'
    AND "event_start_date" < CURRENT_DATE()) AS sep_event
    on "DWH_BATCH_DATE" >= "event_start_date"
 {% endif %}
         ) ALL_EVENTS