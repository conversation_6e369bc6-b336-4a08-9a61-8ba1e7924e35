{{
config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key=['"dazn_user_id"','"entitlement_set_id"','"record_valid_from_timestamp"'],
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_S_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH user_event_users AS(
     SELECT "dazn_user_id"
     FROM {{ref('staging__segment__user_events_users')}}
)

,user_entitlements_log AS(
    SELECT * FROM TRANSFORMATION_PROD.CURATED.CURATED__USER_ENTITLEMENT_LOG
)

{% if is_incremental() %}
    ,max_date As (
    SELECT coalesce(MAX("record_valid_from_timestamp")::date,'1900-01-01') AS maxtime FROM {{ this }}
    )
{% endif %}

,base AS (
    SELECT 
       user_entitlements_log. *
        ,REGEXP_REPLACE(user_entitlements_log."USER_ID",'''|,','') AS "USERID"
        ,iff(user_entitlements_log."EVENT_NAME" = LEAD(user_entitlements_log."EVENT_NAME") OVER (PARTITION BY "USERID",user_entitlements_log."ENTITLEMENTSETID" ORDER BY user_entitlements_log."EVENT_TIMESTAMP"),TRUE,FALSE) AS "ignore_flag"
    FROM user_entitlements_log 
{% if is_incremental() %}
    INNER JOIN max_date on 1=1
{% endif %} 
    WHERE TRUE
    AND user_entitlements_log."EVENT_TIMESTAMP" IS NOT NULL
{% if is_incremental() %}
    AND "EVENT_TIMESTAMP"::DATE >=maxtime
    AND "EVENT_TIMESTAMP"::DATE < CURRENT_DATE()
{% endif %} 
)

,ent AS (
    SELECT 
        base."USERID" AS "USER_ID"
        ,base."EVENT_TIMESTAMP" AS "START_TIME"
        ,decode(base."EVENT_NAME",'INSERT',1,'MODIFY',1,'REMOVE',-1,0) AS "weightage"
        ,lag(base."EVENT_TIMESTAMP") OVER (PARTITION BY base."USER_ID",base."ENTITLEMENTSETID" ORDER BY base."EVENT_TIMESTAMP" DESC,"weightage" desc) AS "END_TIME"
        ,base."SUBSCRIPTION_NAME"
        ,base."EVENT_NAME"
        ,base."ENTITLEMENTSETID"
        ,base."OLD_IMAGE_PAYLOAD":"productType":"S" AS "OLD_PRODUCT_TYPE"
        ,COALESCE(base."PRODUCT_TYPE","OLD_PRODUCT_TYPE") AS "PRODUCT_TYPE"
        ,base."SOURCE"
    FROM base
    WHERE TRUE 
    AND "ignore_flag" =FALSE 
)

,last_event AS(
    SELECT 
        *
        ,sum(ent."weightage") OVER (PARTITION BY ent."USER_ID",ent."ENTITLEMENTSETID") AS "weightage_sum"
        ,FIRST_VALUE(ent."EVENT_NAME") OVER (PARTITION BY ent. "USER_ID",ent."ENTITLEMENTSETID" ORDER BY ent."START_TIME" DESC ROWS BETWEEN UNBOUNDED PRECEDING AND 
         CURRENT ROW ) AS "last_event"
    FROM ent  
    WHERE EXISTS (SELECT 1 FROM user_event_users WHERE user_event_users."dazn_user_id" = ent."USER_ID")
)

,final AS (
    SELECT 
        last_event."USER_ID"
        ,last_event."START_TIME"
        ,CASE 
           WHEN last_event."last_event" = 'REMOVE' AND last_event."END_TIME" IS NULL THEN last_event."START_TIME"
           WHEN last_event."END_TIME" IS NULL THEN '9999-12-31' 
           ELSE last_event."END_TIME" END AS "END_TIME"
        ,CASE 
           WHEN last_event."SOURCE" = 'Docomo' THEN 'Docomo' ELSE last_event."SUBSCRIPTION_NAME" END AS "SUBSCRIPTION_NAME"
        ,last_event."EVENT_NAME"
        ,last_event."PRODUCT_TYPE"
        ,last_event."ENTITLEMENTSETID" 
        ,last_event."SOURCE"
    FROM last_event 
)

SELECT 
    final."USER_ID" AS "dazn_user_id"
    ,final."START_TIME" AS "record_valid_from_timestamp"
    ,final."END_TIME" AS "record_valid_until_timestamp"
    ,final."SUBSCRIPTION_NAME" AS "subscription_name"
    ,final."EVENT_NAME" AS "event_name"
    ,final."PRODUCT_TYPE" AS "product_type"
    ,final."ENTITLEMENTSETID" AS "entitlement_set_id"
    ,final."SOURCE" AS "source"
    ,current_timestamp() AS "META__DBT_INSERT_DTTS" 
FROM final 
WHERE TRUE
