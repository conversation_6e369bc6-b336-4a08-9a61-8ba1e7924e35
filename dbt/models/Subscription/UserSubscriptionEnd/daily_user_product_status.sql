{{
    config(
        materialized='view',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_S_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH dim_date AS (SELECT "date_day" FROM {{ ref('dim_date') }}
    WHERE "date_day" between '2016-01-01' and current_date() ) 

,prd_stats AS (SELECT * FROM {{ ref('subscription_product_status_scd') }} )

,dazn_users AS  (SELECT "dazn_user_id","source_type","user_type","user_created_date" , "viewer_id","billing_account_id","crm_account_id",
  dim_date."date_day" as "batch_date",
  FROM prd_stats
  INNER JOIN dim_date on 1=1 
  WHERE "subscription_product_status" = 'Partial' 
  AND  dim_date."date_day" >= "product_status_effective_from"::date)

select  
   dazn_users."batch_date" as "batch_date",
   dazn_users."dazn_user_id" as "dazn_user_id",
   dazn_users."source_type" as "source_type",
   dazn_users."user_type" as "user_type",
   dazn_users."user_created_date" as "user_created_date",
   dazn_users."viewer_id" as "viewer_id",
   dazn_users."billing_account_id" as "billing_account_id",
   dazn_users."crm_account_id" as "crm_account_id",

  case when dazn_users."user_type" = 'Docomo' or usr_dazn."subscription_status_event_type" ilike '%Docomo%' then TRUE else FALSE end as "docomo_flg",
coalesce( usr_dazn."subscription_product_status" ,'Partial') as "product_status_dazn" ,
coalesce( usr_nfl."subscription_product_status" ,'Partial') as "product_status_nfl" ,
coalesce( usr_fiba."subscription_product_status" ,'Partial') as "product_status_fiba" ,
coalesce( usr_ligasegunda."subscription_product_status" ,'Partial') as "product_status_ligasegunda" ,
coalesce( usr_pga."subscription_product_status" ,'Partial') as "product_status_pga" ,
coalesce( usr_rallytv."subscription_product_status" ,'Partial') as "product_status_rallytv" ,
coalesce( usr_tennistv."subscription_product_status" ,'Partial') as "product_status_tennistv" ,

case when usr_dazn."subscription_product_status" = 'ActivePaid' or usr_nfl."subscription_product_status" = 'ActivePaid' 
or usr_fiba."subscription_product_status" = 'ActivePaid' or usr_ligasegunda."subscription_product_status" = 'ActivePaid'  
or usr_pga."subscription_product_status" = 'ActivePaid' or usr_rallytv."subscription_product_status" = 'ActivePaid'  
or usr_tennistv."subscription_product_status" = 'ActivePaid'   then 'ActivePaid'
when usr_dazn."subscription_product_status" = 'FreeTrial' or usr_nfl."subscription_product_status" = 'FreeTrial' 
or usr_fiba."subscription_product_status" = 'FreeTrial' or usr_ligasegunda."subscription_product_status" = 'FreeTrial'  
or usr_pga."subscription_product_status" = 'FreeTrial' or usr_rallytv."subscription_product_status" = 'FreeTrial'  
or usr_tennistv."subscription_product_status" = 'FreeTrial'   then 'FreeTrial'
when usr_dazn."subscription_product_status" = 'ActiveGrace' or usr_nfl."subscription_product_status" = 'ActiveGrace' 
or usr_fiba."subscription_product_status" = 'ActiveGrace' or usr_ligasegunda."subscription_product_status" = 'ActiveGrace'  
or usr_pga."subscription_product_status" = 'ActiveGrace' or usr_rallytv."subscription_product_status" = 'ActiveGrace'  
or usr_tennistv."subscription_product_status" = 'ActiveGrace'   then 'ActiveGrace'
when usr_dazn."subscription_product_status" = 'Paused' or usr_nfl."subscription_product_status" = 'Paused' 
or usr_fiba."subscription_product_status" = 'Paused' or usr_ligasegunda."subscription_product_status" = 'Paused'  
or usr_pga."subscription_product_status" = 'Paused' or usr_rallytv."subscription_product_status" = 'Paused'  
or usr_tennistv."subscription_product_status" = 'Paused'   then 'Paused'
when usr_dazn."subscription_product_status" = 'Frozen' or usr_nfl."subscription_product_status" = 'Frozen' 
or usr_fiba."subscription_product_status" = 'Frozen' or usr_ligasegunda."subscription_product_status" = 'Frozen'  
or usr_pga."subscription_product_status" = 'Frozen' or usr_rallytv."subscription_product_status" = 'Frozen'  
or usr_tennistv."subscription_product_status" = 'Frozen'   then 'Frozen'
else 'Partial' end as "user_status"
from dazn_users

LEFT JOIN prd_stats usr_dazn
on dazn_users."dazn_user_id" = usr_dazn."dazn_user_id"
and dazn_users."batch_date" >= usr_dazn."product_status_effective_from"::date
and dazn_users."batch_date" < usr_dazn."product_status_effective_until"::date
and usr_dazn."subscription_product_group" = 'DAZN'

LEFT JOIN prd_stats usr_nfl
on dazn_users."dazn_user_id" = usr_nfl."dazn_user_id"
and dazn_users."batch_date" >= usr_nfl."product_status_effective_from"::date
and dazn_users."batch_date" < usr_nfl."product_status_effective_until"::date
and usr_nfl."subscription_product_group" = 'NFL'

LEFT JOIN prd_stats usr_fiba
on dazn_users."dazn_user_id" = usr_fiba."dazn_user_id"
and dazn_users."batch_date" >= usr_fiba."product_status_effective_from"::date
and dazn_users."batch_date" < usr_fiba."product_status_effective_until"::date
and usr_fiba."subscription_product_group" = 'FIBA'

LEFT JOIN prd_stats usr_ligasegunda
on dazn_users."dazn_user_id" = usr_ligasegunda."dazn_user_id"
and dazn_users."batch_date" >= usr_ligasegunda."product_status_effective_from"::date
and dazn_users."batch_date" < usr_ligasegunda."product_status_effective_until"::date
and usr_ligasegunda."subscription_product_group" = 'LIGASEGUNDA'

LEFT JOIN prd_stats usr_pga
on dazn_users."dazn_user_id" = usr_pga."dazn_user_id"
and dazn_users."batch_date" >= usr_pga."product_status_effective_from"::date
and dazn_users."batch_date" < usr_pga."product_status_effective_until"::date
and usr_pga."subscription_product_group"  = 'PGA'

LEFT JOIN prd_stats usr_rallytv
on dazn_users."dazn_user_id" = usr_rallytv."dazn_user_id"
and dazn_users."batch_date" >= usr_rallytv."product_status_effective_from"::date
and dazn_users."batch_date" < usr_rallytv."product_status_effective_until"::date
and usr_rallytv."subscription_product_group" = 'RALLYTV'

LEFT JOIN prd_stats usr_tennistv
on dazn_users."dazn_user_id" = usr_tennistv."dazn_user_id"
and dazn_users."batch_date" >= usr_tennistv."product_status_effective_from"::date
and dazn_users."batch_date" < usr_tennistv."product_status_effective_until"::date
and usr_tennistv."subscription_product_group" = 'TENNISTV'
