{{
    config(
        materialized='table',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_S_WH',
        tags=['presentation-subscription-domain']
    )
}}

{% set subscription_mart_variables = subscription_mart_variables() %}

WITH load_test_users AS (
    SELECT * FROM {{ ref_env('load_test_users') }}
)

,billing_account_id_test_user AS (
    SELECT "billing_account_id","billing_account_is_batch_50"
    FROM {{ ref('billing_account_id_test__dim') }}
)

,subscription_source AS (
    SELECT *
    FROM {{ ref('subscription_id__dim') }}
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "dazn_user_id" ORDER BY "subscription_id_updated_timestamp" desc) = 1
)

,acct AS (SELECT * FROM {{ ref('staging__zuora__account_current') }})

,sub_test AS (
    SELECT tst."billing_account_id",
    MAX(tst."billing_account_is_batch_50") AS "nonbilled_user" 
    FROM subscription_source 
    INNER JOIN billing_account_id_test_user tst USING ("billing_account_id")
    WHERE "subscription_source_system_name" NOT IN ({{ in_clause_from_list(subscription_mart_variables.test_campaign_ids) }}) 
    AND COALESCE(CONCAT("subscription_direct_carrier_billing_carrier_name",' DCB'),"subscription_source_system_name", 'Direct') != 'Commercial Premises'
    AND  "subscription_status" NOT IN ('Draft','Suspended')
    GROUP BY 1
)

,final AS 
(SELECT 
CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
,subscription_source."crm_account_id" AS "crm_account_id"
,subscription_source."dazn_user_id" AS "dazn_user_id"
,subscription_source."billing_account_id" AS "billing_account_id"
,subscription_source."billing_account_currency_code" AS "billing_account_currency_code"
,acct."billing_account_last_updated_timestamp" AS "billing_account_last_updated_timestamp"   
,acct."billing_account_batch_name" AS "billing_account_batch_name" 
,sub_test."nonbilled_user"  AS "nonbilled_user"
FROM subscription_source 
LEFT JOIN acct 
ON acct."billing_account_id" = subscription_source."billing_account_id" 
LEFT JOIN sub_test
ON sub_test."billing_account_id" = subscription_source."billing_account_id" 
UNION ALL
SELECT 
CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
,NULL AS "crm_account_id"
,load_test_users."dazn_id" AS "dazn_user_id"
,NULL AS "billing_account_id"
,NULL AS "billing_account_currency_code"
,NULL AS "billing_account_last_updated_timestamp"   
,'LoadTestUser' AS "billing_account_batch_name" 
,TRUE AS "nonbilled_user"
FROM load_test_users
)

SELECT * FROM final
QUALIFY ROW_NUMBER() OVER (PARTITION BY "dazn_user_id" ORDER BY "billing_account_last_updated_timestamp" NULLS LAST) = 1
