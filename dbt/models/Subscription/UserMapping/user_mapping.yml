version: 2

models:
  - name: user_mapping
    description: ""
    columns:
      - name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the model"
        quote: true

      - name: billing_account_id
        description: ""
        quote: true
        tests:
          - unique

      - name: dazn_user_id
        description: "DAZN user ID which is used to link purchases to user entitlements"
        quote: true

      - name: crm_account_id
        description: ""
        quote: true

      - name: billing_account_batch_name
        description: ""
        quote: true

      - name: billing_account_last_updated_timestamp
        description: ""
        quote: true

      - name: billing_account_currency_code
        description: ""
        quote: true

      - name: nonbilled_user
        description: "check if the user is a test_user or not "
        quote: true
