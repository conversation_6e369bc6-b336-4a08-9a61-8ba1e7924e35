{{
    config(
        materialized='incremental',
        unique_key='"batch_date"',
        incremental_strategy='delete+insert',
        database='USERS_DAILY_COUNT__B2C__MART__' + snowflake_env(),
        schema='FACT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        cluster_by=['"batch_date"'],
        tags=['presentation-subscription-domain']
    )
}}


WITH daily_users AS (
    SELECT * FROM {{ ref('daily_users') }}
    {% if is_incremental() %}
    -- If incremental, then run for only the last day
    WHERE "batch_date" BETWEEN DATEADD('day', -1, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
    {% endif %}
)

SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,"batch_date"
    ,"batch_week_end_date"
    ,"user_status__skey"
    ,"user_marketing_preferences__skey"
    {% for source in ["dazn", "nfl"] %}
    ,"has_{{source}}_subscription"
    ,"{{source}}_billing_account__skey"
    ,"{{source}}_subscription_daily_status__skey"
    ,"{{source}}_subscription_info__skey"
    ,"{{source}}_subscription_term__skey"
    ,"{{source}}_subscription_charge__skey"
    ,"{{source}}_subscription_source_system_name_derived__skey"
    ,"{{source}}_subscription_tracking_id__skey"
    ,"{{source}}_subscription_sign_up_campaign_id__skey"
    ,"{{source}}_subscription_giftcode_campaign_name__skey"
    ,"user_last_{{source}}_subscription__skey"
    {% endfor %}
    ,COUNT("dazn_user_id") AS "user_count"
FROM daily_users
GROUP BY ALL
