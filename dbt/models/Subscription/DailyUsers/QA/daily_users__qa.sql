{{
    config(
        materialized='incremental',
        unique_key='"batch_date"',
        incremental_strategy='delete+insert',
        schema='METADATA',
        tags=['presentation-subscription-domain']
    )
}}

WITH daily_users AS (
    SELECT * FROM {{ ref('daily_users') }}
    WHERE
        -- If it's more than 14 days ago, then only calculate weekly to save processing
        (("batch_date" < DATEADD('day', -14, CURRENT_DATE) AND "batch_date" = DATE_TRUNC('week', "batch_date"))
        OR
        -- Within 14 days we should look daily (incrementally)
        ("batch_date" >= DATEADD('day', -14, CURRENT_DATE)))
        {% if is_incremental() %}
        AND
        -- Going back 7 days to make sure the full rebuilds of the dimensions haven't somehow missed previous skeys
        "batch_date" BETWEEN DATEADD('day', -7, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
        {% endif %}
)

,date_dim AS (
    SELECT * FROM {{ ref('daily_counts__date__dim') }}
)

,user_stat_dim AS (
    SELECT * FROM {{ ref('user_status__dim') }}
)

,user_pref_dim AS (
    SELECT * FROM {{ ref('user_marketing_preferences__dim') }}
)

,user_last_dazn_sub_dim AS (
    SELECT * FROM {{ ref('user_last_dazn_subscription__dim') }}
)

,user_last_nfl_sub_dim AS (
    SELECT * FROM {{ ref('user_last_nfl_subscription__dim') }}
)

SELECT
    daily_users."batch_date"
    ,date_dim."is_week_end"
    ,date_dim."is_month_end"
    ,date_dim."is_year_end"

    -- Duplicate check
    ,COUNT(daily_users."dazn_user_id") AS "user_count"
    ,COUNT(DISTINCT daily_users."dazn_user_id") AS "distinct_user_count"
    ,"user_count" - "distinct_user_count" AS "duplicate_user_count"

    -- NULL skey joins
    ,COUNT_IF(user_stat_dim."user_status__skey" IS NULL) AS "user_status__skey__null_count"
    ,COUNT_IF(user_pref_dim."user_marketing_preferences__skey" IS NULL) AS "user_marketing_preferences__skey__null_count"
    ,COUNT_IF(user_last_dazn_sub_dim."user_last_dazn_subscription__skey" IS NULL) AS "user_last_dazn_subscription__skey__null_count"
    ,COUNT_IF(user_last_nfl_sub_dim."user_last_nfl_subscription__skey" IS NULL) AS "user_last_nfl_subscription__skey__null_count"

    -- NULL key fields
    ,COUNT_IF(user_stat_dim."home_country_code" IS NULL) AS "home_country_code__null_count"
    ,COUNT_IF(user_stat_dim."is_nfl_authenticated_email" IS NULL AND user_stat_dim."nfl_user" = 'NEW') AS "is_nfl_authenticated_email__null_count"
    ,COUNT_IF(user_pref_dim."has_allowed_dazn_marketing_emails" IS NULL) AS "has_allowed_dazn_marketing_emails__null_count"
    ,COUNT_IF(user_pref_dim."has_allowed_nfl_marketing_emails" IS NULL) AS "has_allowed_nfl_marketing_emails__null_count"

    -- Unexpected value counts
    -- Just monitoring the subs levels to make sure nothing drops off (or jumps up) a cliff
    ,COUNT_IF(daily_users."has_dazn_subscription" = TRUE) AS "has_dazn_subscription__count"
    ,COUNT_IF(daily_users."has_nfl_subscription" = TRUE) AS "has_nfl_subscription__count"
    ,COUNT_IF(user_pref_dim."has_allowed_dazn_marketing_emails" = TRUE) AS "has_allowed_dazn_marketing_emails__true_count"
    ,COUNT_IF(user_pref_dim."has_allowed_nfl_marketing_emails" = TRUE) AS "has_allowed_nfl_marketing_emails__true_count"

    -- Last updated
    ,MAX(daily_users."META__DBT_INSERT_DTTS") AS "max_dbt_insert_dtts"

FROM daily_users
LEFT JOIN date_dim USING ("batch_date")
LEFT JOIN user_stat_dim USING ("user_status__skey")
LEFT JOIN user_pref_dim USING ("user_marketing_preferences__skey")
LEFT JOIN user_last_dazn_sub_dim USING ("user_last_dazn_subscription__skey")
LEFT JOIN user_last_nfl_sub_dim USING ("user_last_nfl_subscription__skey")
GROUP BY 1,2,3,4
