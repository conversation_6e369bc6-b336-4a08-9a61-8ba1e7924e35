{{
    config(
        materialized='view',
        database='USERS_DAILY_COUNT__B2C__MART__' + snowflake_env(),
        schema='INTERMEDIATE',
        tags=['presentation-subscription-domain']
    )
}}

{% set sources = ["dazn", "nfl"] %}

WITH daily_users AS (
    SELECT * FROM {{ ref('daily_users') }}
)

SELECT
    "META__DBT_INSERT_DTTS"
    ,"batch_date"
    ,"batch_week_end_date"
    ,"dazn_user_id"
    ,"viewer_id"
    ,"crm_account_id"
    ,"billing_account_id"
    ,"partner_id"
    ,"user_status__skey"
    ,"user_marketing_preferences__skey"
    {% for source in sources %}
    ,"has_{{source}}_subscription"
    ,"{{source}}_subscription_name"
    ,"{{source}}_billing_account__skey"
    ,"{{source}}_subscription_daily_status__skey"
    ,"{{source}}_subscription_info__skey"
    ,"{{source}}_subscription_term__skey"
    ,"{{source}}_subscription_charge__skey"
    ,"{{source}}_subscription_source_system_name_derived__skey"
    ,"{{source}}_subscription_tracking_id__skey"
    ,"{{source}}_subscription_sign_up_campaign_id__skey"
    ,"{{source}}_subscription_giftcode_campaign_name__skey"
    ,"last_{{source}}_subscription_name"
    ,"user_last_{{source}}_subscription__skey"
    {% endfor %}
FROM daily_users
