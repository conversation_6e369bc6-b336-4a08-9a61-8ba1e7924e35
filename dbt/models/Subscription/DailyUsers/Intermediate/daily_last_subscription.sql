{{
    config(
        materialized='incremental',
        unique_key='"batch_date"',
        incremental_strategy='delete+insert',
        schema='TRANSIENT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        cluster_by=['"batch_date"'],
        tags=['presentation-subscription-domain']
    )
}}


WITH dim_date AS (
    SELECT * FROM {{ ref('dim_date') }}
    WHERE
        -- Always filter for 2023-04-01 onwards as minimum full rebuild date
        "date_day" BETWEEN '2023-04-01' AND DATEADD('day', -1, CURRENT_DATE)
        {% if is_incremental() %}
        AND
        -- If incremental, then run for only the last day
        "date_day" BETWEEN DATEADD('day', -1, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
        {% endif %}
)

,sub_name_scd AS (
    SELECT * FROM {{ ref('subscription_name__scd') }}
)

SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,dim_date."date_day" AS "batch_date"
    ,sub_name_scd."billing_account_id"
    ,sub_name_scd."subscription_product_group"
    ,sub_name_scd."subscription_name"
    ,sub_name_scd."subscription_start_date"
    -- If the latest valid subscription (at the time of the batch_date) has a valid date in the past (before the batch_date), then we should use the record_valid_until_timestamp
    -- If the subscription is currently acive (at the time of the batch_date), then we need to take the planned end date, that will be in the future, taking the record_valid_until_timestamp may just give a date of the next change of subscription
    ,IFF(sub_name_scd."record_valid_until_timestamp"::DATE <= dim_date."date_day", sub_name_scd."record_valid_until_timestamp"::DATE, sub_name_scd."subscription_planned_end_date") AS "subscription_end_date"
    ,sub_name_scd."subscription_tier"
    ,sub_name_scd."subscription_type"
FROM dim_date
-- Bringing in all active subscriptions before that batch_date
LEFT JOIN sub_name_scd
    ON dim_date."date_day" >= sub_name_scd."record_valid_from_timestamp"
-- Pull back only the most recently active Subscription for that Billing Account per Product Group
QUALIFY ROW_NUMBER() OVER (PARTITION BY dim_date."date_day", sub_name_scd."billing_account_id", sub_name_scd."subscription_product_group" ORDER BY sub_name_scd."record_valid_from_timestamp" DESC, sub_name_scd."subscription_planned_end_date" DESC, sub_name_scd."subscription_start_date" DESC) = 1
