{{
    config(
        materialized='table',
        schema='TRANSIENT',
        tags=['presentation-subscription-domain']
    )
}}

WITH user_events_scd AS (
    SELECT * FROM {{ ref('staging__segment__user_events_identifies_scd') }}
)

-- For now we will just cte this, but we will create a static patch table for this when segment settles down and we trust it more
,salesforce_created_patch AS (
    SELECT
        "dazn_user_id"
        ,"crm_account_created_timestamp"
    FROM {{ ref('staging__salesforce__account_current') }}
)

SELECT
    user_events_scd."dazn_user_id"
    ,CASE
        WHEN MIN(salesforce_created_patch."crm_account_created_timestamp") < MIN(user_events_scd."timestamp") THEN MIN(salesforce_created_patch."crm_account_created_timestamp")
        ELSE MIN(user_events_scd."timestamp")
    END AS "user_account_created_timestamp"
FROM user_events_scd
LEFT JOIN salesforce_created_patch USING ("dazn_user_id")
GROUP BY 1
