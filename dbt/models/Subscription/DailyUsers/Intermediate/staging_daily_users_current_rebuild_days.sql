{{
    config(
        materialized='table',
        schema='TRANSIENT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        cluster_by=['"batch_date"'],
        tags=['presentation-subscription-domain']
    )
}}

{% set sources = ["dazn", "nfl"] %}
{% set daily_users_rebuild_days = 2 %}
{% set daily_users_product_groups = ["dazn", "nfl", "fiba", "LigaSegunda", "pga", "RallyTV", "TennisTV"] %}

WITH dim_date AS (
    SELECT * FROM {{ ref_env('dim_date') }}
    WHERE TRUE
        AND (
            "date_day" BETWEEN DATEADD('day', -{{daily_users_rebuild_days}}, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
            {% if is_incremental() %}
            -- Always filter for 2023-04-01 onwards as minimum full rebuild date
            OR "date_day" BETWEEN '2023-04-01' AND DATEADD('day', -1, CURRENT_DATE)
            {% endif %}
        )
)

,usr AS (SELECT * FROM {{ ref_env('subscription_product_status_scd') }})

,user_product_stats AS(
    SELECT
        usr."dazn_user_id"
        ,dim_date."date_day" AS "batch_date"
        ,MIN("user_created_date") AS "user_account_created_date"
        {% for product_group in daily_users_product_groups %}
            ,COALESCE(
                MAX(
                    CASE
                        WHEN "subscription_product_group" = '{{ product_group.upper() }}' THEN "subscription_product_status"
                    END
                    )
                ,'Partial'
                ) AS "product_status_{{ product_group }}"
        {% endfor %}
        --,split_part(min ("dv_prod_status"),'*',2) AS "user_status"
    FROM dim_date
    INNER JOIN usr
        ON dim_date."date_day" >= "product_status_effective_from"::DATE
            AND dim_date."date_day" < "product_status_effective_until"::DATE
    --left join dev_prod_status
    --on dev_prod_status."Product_status" = usr."subscription_product_status"
    GROUP BY 1, 2
)

,user_status_scd AS ( SELECT * FROM {{ ref_env('daily_user_status_scd') }} )

,usr_stats AS (
    SELECT *
       ,dim_date."date_day" AS "batch_date"
    FROM user_status_scd
    INNER JOIN dim_date
        ON dim_date."date_day" >= "user_status_effective_from"::DATE
            AND dim_date."date_day" < "user_status_effective_until"::DATE
)

,staging__segment__user_events_users AS ( SELECT * FROM {{ ref_env('staging__segment__user_events_users') }} )

,users AS (
    SELECT "dazn_user_id"
        ,MAX("viewer_id") AS "viewer_id"
        ,MAX("nfl_user") AS "nfl_user"
        ,MAX("source_type") AS "source_type"
        ,MAX("home_country_code") AS "home_country_code"
    FROM staging__segment__user_events_users
    GROUP BY 1
)

,user_events_scd AS ( SELECT * FROM {{ ref_env('staging__segment__user_events_identifies_scd_v1') }} )

,staging__segment__user_events_identifies_home_country_scd_v1 AS ( SELECT * FROM {{ ref_env('staging__segment__user_events_identifies_home_country_scd_v1') }} )

,user_events_home_scd AS (
    SELECT "dazn_user_id"
        ,"original_timestamp"
        ,TRIM(UPPER("home_country_code")) AS "home_country_code"
        ,"record_valid_from_timestamp"
        ,"record_valid_until_timestamp"
    FROM staging__segment__user_events_identifies_home_country_scd_v1
)

,user_pref_scd AS ( SELECT * FROM {{ ref_env('staging__segment__user_preferences_user_preferences__scd') }} )

,user_account_created AS ( SELECT * FROM {{ ref_env('user_account_created') }} )

,cust_id_dim AS ( SELECT * FROM {{ ref_env('customer_identity_dim_current') }} )

,daily_subs_filtered AS ( SELECT * FROM {{ ref_env('daily_subscriptions_filtered') }} )

,subscription_mart_info__dim AS ( SELECT * FROM {{ ref_env('subscription_mart_info__dim') }} )

,daily_last_subscription AS ( SELECT * FROM {{ ref_env('daily_last_subscription') }} )

{% for source in sources %}
,daily_{{source}}_subs AS (
    SELECT
        daily_subs_filtered.*
        ,COALESCE(usr."dazn_user_id",crm."dazn_user_id") AS "derived_dazn_user_id"
    FROM daily_subs_filtered
    LEFT JOIN subscription_mart_info__dim
        USING ("subscription_info__skey")
    LEFT JOIN cust_id_dim usr
        ON daily_subs_filtered."dazn_user_id" = usr."dazn_user_id"
    LEFT JOIN cust_id_dim crm
        ON daily_subs_filtered."dazn_user_id" = crm."crm_account_id"
    WHERE TRUE
        AND "subscription_product_group" = '{{source.upper()}}'
        AND (
            "batch_date" BETWEEN DATEADD('day', -{{daily_users_rebuild_days}}, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
            {% if is_incremental() %}
            -- Always filter for 2023-04-01 onwards as minimum full rebuild date
            OR "batch_date" BETWEEN '2023-04-01' AND DATEADD('day', -1, CURRENT_DATE)
            {% endif %}
        )
    QUALIFY TRUE
        AND ROW_NUMBER() OVER ( PARTITION BY daily_subs_filtered."dazn_user_id", "batch_date" ORDER BY "subscription_planned_end_date" DESC, "subscription_start_date" DESC ) = 1
)

,daily_last_{{source}}_subs AS (
    SELECT * FROM daily_last_subscription
    WHERE TRUE
        AND "subscription_product_group" = '{{source.upper()}}'
        AND (
            "batch_date" BETWEEN DATEADD('day', -{{daily_users_rebuild_days}}, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
            {% if is_incremental() %}
            -- Always filter for 2023-04-01 onwards as minimum full rebuild date
            OR "batch_date" BETWEEN '2023-04-01' AND DATEADD('day', -1, CURRENT_DATE)
            {% endif %}
        )
    QUALIFY TRUE
        AND ROW_NUMBER() OVER ( PARTITION BY "billing_account_id", "batch_date" ORDER BY "subscription_end_date" DESC, "subscription_start_date" DESC ) = 1
)
{% endfor %}

SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,dim_date."date_day" AS "batch_date"
    ,dim_date."week_end_date" AS "batch_week_end_date"
    ,users."dazn_user_id"
    ,users."viewer_id"
    ,users."source_type"
    ,users."nfl_user"
    -- As Segment doesn't seem to provide the IDs correctly much, best to try and pull from the cust_id_dim instead
    ,cust_id_dim."crm_account_id"
    ,cust_id_dim."billing_account_id"
    ,cust_id_dim."partner_id"
    --,user_events_scd."home_country_code"
    ,COALESCE(
        COALESCE(
            user_events_home_scd."home_country_code"
            ,IFF(user_events_scd."user_type" = 'Docomo', 'JP', UPPER(user_events_home_scd."home_country_code"))
            )
            ,users."home_country_code"
        ) AS "home_country_code"
    ,user_events_scd."language_locale_key"
    ,user_events_scd."is_validated_dazn_email"
    ,user_events_scd."is_nfl_authenticated_email"
    ,user_pref_scd."allow_marketing_emails" AS "has_allowed_dazn_marketing_emails"
    ,user_pref_scd."allow_nfl_marketing_emails" AS "has_allowed_nfl_marketing_emails"
    ,user_pref_scd."preferences_opted_out_from_personalisation"
    ,user_pref_scd."preferences_opted_in_thirty_days_cancellation"
    ,user_pref_scd."preferences_multi_track_audio_language"
    ,user_pref_scd."allow_daznbet_marketing_emails"
    ,user_product_stats."user_account_created_date"::DATE AS "user_account_created_date"
    ,LAST_DAY(user_product_stats."user_account_created_date", 'week') AS "user_account_created_week"
    {% for source in sources %}
    ,daily_{{source}}_subs."subscription_name" IS NOT NULL AS "has_{{source}}_subscription"
    ,daily_{{source}}_subs."subscription_name" AS "{{source}}_subscription_name"
    ,daily_{{source}}_subs."billing_account__skey" AS "{{source}}_billing_account__skey"
    ,daily_{{source}}_subs."subscription_daily_status__skey" AS "{{source}}_subscription_daily_status__skey"
    ,daily_{{source}}_subs."subscription_info__skey" AS "{{source}}_subscription_info__skey"
    ,daily_{{source}}_subs."subscription_term__skey" AS "{{source}}_subscription_term__skey"
    ,daily_{{source}}_subs."subscription_charge__skey" AS "{{source}}_subscription_charge__skey"
    ,daily_{{source}}_subs."subscription_source_system_name_derived__skey" AS "{{source}}_subscription_source_system_name_derived__skey"
    ,daily_{{source}}_subs."subscription_tracking_id__skey" AS "{{source}}_subscription_tracking_id__skey"
    ,daily_{{source}}_subs."subscription_sign_up_campaign_id__skey" AS "{{source}}_subscription_sign_up_campaign_id__skey"
    ,daily_{{source}}_subs."subscription_giftcode_campaign_name__skey" AS "{{source}}_subscription_giftcode_campaign_name__skey"
    ,daily_last_{{source}}_subs."subscription_name" AS "last_{{source}}_subscription_name"
    ,daily_last_{{source}}_subs."subscription_end_date" AS "last_{{source}}_subscription_end_date"
    ,CASE
        WHEN daily_last_{{source}}_subs."subscription_end_date" >= '9999-01-01' THEN '9999-12-31'
        ELSE LAST_DAY(daily_last_{{source}}_subs."subscription_end_date", 'week')
    END AS "last_{{source}}_subscription_end_week"
    ,daily_last_{{source}}_subs."subscription_type" AS "last_{{source}}_subscription_type"
    ,daily_last_{{source}}_subs."subscription_tier" AS "last_{{source}}_subscription_tier"
    {% endfor %}
    ,HASH(
        "user_account_created_week"
        --,user_events_home_scd."home_country_code"
        ,COALESCE(
            COALESCE(
                user_events_home_scd."home_country_code"
                ,IFF(user_events_scd."user_type" = 'Docomo', 'JP', UPPER(user_events_home_scd."home_country_code"))
                )
                ,users."home_country_code"
            )
        ,user_product_stats."product_status_dazn"
        ,user_product_stats."product_status_nfl"
        ,user_events_scd."is_validated_dazn_email"
        ,user_events_scd."is_nfl_authenticated_email"
        ,users."nfl_user"
        ,users."source_type"
    ) AS "user_status__skey"
    ,HASH(
        user_pref_scd."allow_marketing_emails"
        ,user_pref_scd."allow_nfl_marketing_emails"
        ,user_pref_scd."preferences_opted_out_from_personalisation"
        ,user_pref_scd."preferences_opted_in_thirty_days_cancellation"
        ,user_pref_scd."preferences_multi_track_audio_language"
        ,user_pref_scd."allow_daznbet_marketing_emails"
        ,user_events_scd."language_locale_key"
    ) AS "user_marketing_preferences__skey"
    {% for source in sources %}
    ,HASH(
        daily_last_{{source}}_subs."subscription_end_date"
        ,daily_last_{{source}}_subs."subscription_type"
        ,daily_last_{{source}}_subs."subscription_tier"
    ) AS "user_last_{{source}}_subscription__skey"
    {% endfor %}
    {% for product_group in daily_users_product_groups %}
    ,user_product_stats."product_status_{{ product_group }}"
    {% endfor %}
    ,usr_stats."user_status"
    ,CONCAT(dim_date."date_day" || '-' || users."dazn_user_id") AS "comp_key"
FROM dim_date
-- All users that are currently valid in the User Service at the end of the batch_date
CROSS JOIN users

INNER JOIN  user_product_stats
    ON users."dazn_user_id" = user_product_stats."dazn_user_id"
        AND dim_date."date_day" = user_product_stats."batch_date"

INNER JOIN usr_stats
    ON usr_stats."dazn_user_id" = users."dazn_user_id"
        AND dim_date."date_day" =  usr_stats."batch_date"

LEFT JOIN user_events_scd
    ON users."dazn_user_id" = user_events_scd."dazn_user_id"
        AND dim_date."date_day" >= user_events_scd."record_valid_from_timestamp"::DATE
        AND dim_date."date_day" < user_events_scd."record_valid_until_timestamp"::DATE

LEFT JOIN user_events_home_scd
    ON user_events_scd."dazn_user_id" = user_events_home_scd."dazn_user_id"
        AND dim_date."date_day" >= user_events_home_scd."record_valid_from_timestamp"::DATE
        AND dim_date."date_day" < user_events_home_scd."record_valid_until_timestamp"::DATE

-- Bring in the User Preferences
LEFT JOIN user_pref_scd
    ON users."dazn_user_id" = user_pref_scd."dazn_user_id"
        AND dim_date."date_day" >= user_pref_scd."record_valid_from_timestamp"::DATE
        AND dim_date."date_day" < user_pref_scd."record_valid_until_timestamp"::DATE

LEFT JOIN user_account_created
    ON users."dazn_user_id" = user_account_created."dazn_user_id"
        AND user_account_created."user_account_created_timestamp"::DATE <= dim_date."date_day"

-- Need to join in the Customer Identity Dim to get the correct unique billing_account_id based off the dazn_user_id
LEFT JOIN cust_id_dim
    ON user_events_scd."dazn_user_id" = cust_id_dim."dazn_user_id"
    --AND dim_date."date_day" >= cust_id_dim."effective_from"::DATE
    --AND dim_date."date_day" < cust_id_dim."effective_until"::DATE

{% for source in sources %}
-- Bringing in all active DAZN subscriptions on that batch_date
LEFT JOIN daily_{{source}}_subs
    -- Using crm_account_id if the billing_account_id is NULL, like for Docomo, which replicates how we do it in the Docomo Dim, as well
    --ON IFNULL(cust_id_dim."billing_account_id", cust_id_dim."crm_account_id") = daily_{{source}}_subs."billing_account_id"
    ON cust_id_dim."dazn_user_id" = daily_{{source}}_subs."derived_dazn_user_id"
        AND dim_date."date_day" = daily_{{source}}_subs."batch_date"
-- Bringing in the last active sub for the account on that batch_date from an intermediate model
LEFT JOIN daily_last_{{source}}_subs
    -- Using crm_account_id if the billing_account_id is NULL, like for Docomo, which replicates how we do it in the Docomo Dim, as well
    ON IFNULL(cust_id_dim."billing_account_id", cust_id_dim."crm_account_id") = daily_last_{{source}}_subs."billing_account_id"
        AND dim_date."date_day" = daily_last_{{source}}_subs."batch_date"
{% endfor %}
