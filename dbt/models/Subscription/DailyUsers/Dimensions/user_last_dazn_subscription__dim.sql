{{
    config(
        materialized='incremental',
        incremental_strategy='merge',
        unique_key='"user_last_dazn_subscription__skey"',
        database='USERS_DAILY_COUNT__B2C__MART__' + snowflake_env(),
        schema='USER',
        tags=['presentation-subscription-domain']
    )
}}

WITH daily_users AS (
    SELECT * FROM {{ ref('daily_users') }}
)

SELECT DISTINCT
    "user_last_dazn_subscription__skey"
    ,"last_dazn_subscription_end_week"
    ,"last_dazn_subscription_type"
    ,"last_dazn_subscription_tier"
FROM daily_users
{% if is_incremental() %}
WHERE
    "batch_date" BETWEEN DATEADD('day', -1, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
    AND
    "user_last_dazn_subscription__skey" NOT IN (SELECT "user_last_dazn_subscription__skey" FROM {{ this }})
{% endif %}
