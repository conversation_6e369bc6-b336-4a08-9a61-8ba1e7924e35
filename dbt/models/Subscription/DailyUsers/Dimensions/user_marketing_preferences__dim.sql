{{
    config(
        materialized='incremental',
        incremental_strategy='merge',
        unique_key='"user_marketing_preferences__skey"',
        database='USERS_DAILY_COUNT__B2C__MART__' + snowflake_env(),
        schema='USER',
        tags=['presentation-subscription-domain']
    )
}}

WITH daily_users AS (
    SELECT * FROM {{ ref('daily_users') }}
)

SELECT DISTINCT
    "user_marketing_preferences__skey"
    ,"has_allowed_dazn_marketing_emails"
    ,"has_allowed_nfl_marketing_emails"
    ,"preferences_opted_out_from_personalisation"
    ,"preferences_opted_in_thirty_days_cancellation"
    ,"preferences_multi_track_audio_language"
    ,"allow_daznbet_marketing_emails"
    ,"language_locale_key"
FROM daily_users
{% if is_incremental() %}
WHERE
    "batch_date" BETWEEN DATEADD('day', -1, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
    AND
    "user_marketing_preferences__skey" NOT IN (SELECT "user_marketing_preferences__skey" FROM {{ this }})
{% endif %}
