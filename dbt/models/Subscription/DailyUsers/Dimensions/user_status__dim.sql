{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"user_status__skey"',
        database='USERS_DAILY_COUNT__B2C__MART__' + snowflake_env(),
        schema='USER',
        tags=['presentation-subscription-domain']
    )
}}

WITH daily_users AS (
    SELECT * FROM {{ ref('daily_users') }}
)

SELECT DISTINCT
    daily_users."user_status__skey"
    ,"user_account_created_week"
    ,"home_country_code"
    ,"product_status_dazn"
    ,"product_status_nfl"
    ,"is_validated_dazn_email"
    ,"is_nfl_authenticated_email"
    ,"nfl_user"
    ,"source_type"
FROM daily_users 
{% if is_incremental() %}
LEFT JOIN (SELECT "user_status__skey" FROM {{ this }} GROUP BY 1) inc
on daily_users."user_status__skey" = inc."user_status__skey"
WHERE
    "batch_date" BETWEEN DATEADD('day', -1, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
AND inc."user_status__skey" IS NULL
{% endif %}
