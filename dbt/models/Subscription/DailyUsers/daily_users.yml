version: 2

models:
  - name: daily_last_subscription
    description: "Table containing a daily list of all customers that have ever had a Subscription at DAZN referencing their most reecnt Subscription before the batch_date"
    columns:
      - name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the model"
        quote: true

      - name: batch_date
        description: "The date of the spine for the calculation of the last subscription before that date"
        quote: true

      - name: billing_account_id
        description: "The Zuora Account ID coming from the Zuora Account entity E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: subscription_product_group
        description: "Can be either 'NFL' whereas it is an NFL subscription, or 'DAZN' otherwise."
        quote: true

      - name: subscription_name
        description: "The Subscription Name that is the most recently active for the Billing Account and Product Group from before the batch_date"
        quote: true

      - name: subscription_start_date
        description: "The Subscription Start Date of the Subscription that is the most recently active for the Billing Account and Product Group from before the batch_date"
        quote: true

      - name: subscription_end_date
        description: "The Subscription End Date of the Subscription that is the most recently active for the Billing Account and Product Group from before the batch_date"
        quote: true

      - name: subscription_tier
        description: "The Subscription Tier of the Subscription that is the most recently active for the Billing Account and Product Group from before the batch_date"
        quote: true

      - name: subscription_type
        description: "The Subscription Type of the Subscription that is the most recently active for the Billing Account and Product Group from before the batch_date"
        quote: true

  - name: user_account_created
    description: "Model to calculate the very first time the user has created their account"
    columns:
      - name: dazn_user_id
        description: "The DAZN User Id of the Account E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: user_account_created_timestamp
        description: "The minimum timestamp we see the users being created in our systems, derived from Salesforce before migration to Segment"
        quote: true

  - name: staging_daily_users_current_rebuild_days
    description: "Table containing a daily list of all Users ever seen at DAZN as of the batch_date, enriched with various details about their current and most recent subscriptions, if any"
    columns:
      - name: comp_key
        description: " "
        quote: true
        tests:
          - unique

  - name: daily_users
    description: "Table containing a daily list of all Users ever seen at DAZN as of the batch_date, enriched with various details about their current and most recent subscriptions, if any"
    columns:
      - &META__DBT_INSERT_DTTS
        name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the model"
        quote: true

      - &batch_date
        name: batch_date
        description: "The date of the spine for daily users"
        quote: true

      - &batch_week_end_date
        name: batch_week_end_date
        description: "The week end date of the spine for daily users"
        quote: true

      - &dazn_user_id
        name: dazn_user_id
        description: "The DAZN User Id of the user, E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - &viewer_id
        name: viewer_id
        description: "The Viewer ID of the user, E.g. 3cc19ed76c25"
        quote: true

      - &crm_account_id
        name: crm_account_id
        description: "The CRM Account ID if the user, E.g. 0011o00001pVfx9AAC"
        quote: true

      - &billing_account_id
        name: billing_account_id
        description: "The Zuora Account ID coming from the Zuora Account entity E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - &partner_id
        name: partner_id
        description: "The ID coming from external Partners (like Docomo or NFL)."
        quote: true

      - &home_country_code
        name: home_country_code
        description: "The two-letter Country Code of the user. E.g. GB, IT, DE, ..."
        quote: true

      - &language_locale_key
        name: language_locale_key
        description: "The language locale of the user. E.g. en_US, pt_BR, de, it, ..."
        quote: true

      - &is_validated_dazn_email
        name: is_validated_dazn_email
        description: "Flag for if the user has verified their email on the DAZN platform"
        quote: true

      - &nfl_user
        name: nfl_user
        description: "The type of NFL user (E.g. New/Existing), NULL if Existing"
        quote: true

      - &is_nfl_authenticated_email
        name: is_nfl_authenticated_email
        description: "Flag for if the user has authenticated their email on the NFL platform"
        quote: true

      - &product_status_dazn
        name: product_status_dazn
        description: "The product status for the user on the DAZN platform"
        quote: true

      - &product_status_nfl
        name: product_status_nfl
        description: "The product status for the user on the NFL platform"
        quote: true

      - &source_type
        name: source_type
        description: "The source type of the user (E.g. NFL)"
        quote: true

      - &has_allowed_dazn_marketing_emails
        name: has_allowed_dazn_marketing_emails
        description: "Flag for if the user has allowed Dazn marketing emails. Will shortly be deprecated for the User Preferences events."
        quote: true

      - &has_allowed_nfl_marketing_emails
        name: has_allowed_nfl_marketing_emails
        description: "Flag for if the Canadian NFL users have allowed marketing emails. Will shortly be deprecated for the User Preferences events."
        quote: true

      - &preferences_opted_out_from_personalisation
        name: preferences_opted_out_from_personalisation
        description: "TBC"
        quote: true

      - &preferences_opted_in_thirty_days_cancellation
        name: preferences_opted_in_thirty_days_cancellation
        description: "TBC"
        quote: true

      - &preferences_multi_track_audio_language
        name: preferences_multi_track_audio_language
        description: "TBC"
        quote: true

      - &allow_daznbet_marketing_emails
        name: allow_daznbet_marketing_emails
        description: "TBC"
        quote: true

      - &user_account_created_date
        name: user_account_created_date
        description: "The minimum date we see the users being created in our systems, derived from Salesforce before migration to Segment"
        quote: true

      - &user_account_created_week
        name: user_account_created_week
        description: "The minimum week end date we see the users being created in our systems, derived from Salesforce before migration to Segment"
        quote: true

      - &has_dazn_subscription
        name: has_dazn_subscription
        description: "Flag for if the User has an active DAZN Subscription on the batch_date"
        quote: true

      - &dazn_subscription_name
        name: dazn_subscription_name
        description: "The Subscription Name of the active DAZN Subscription on the batch_date"
        quote: true

      - &dazn_billing_account__skey
        name: dazn_billing_account__skey
        description: "The billing_account__skey of the Subscription active DAZN Subscription on the batch_date"
        quote: true

      - &dazn_subscription_daily_status__skey
        name: dazn_subscription_daily_status__skey
        description: "The subscription_daily_status__skey of the Subscription active DAZN Subscription on the batch_date"
        quote: true

      - &dazn_subscription_info__skey
        name: dazn_subscription_info__skey
        description: "The subscription_info__skey of the Subscription active DAZN Subscription on the batch_date"
        quote: true

      - &dazn_subscription_term__skey
        name: dazn_subscription_term__skey
        description: "The subscription_term__skey of the Subscription active DAZN Subscription on the batch_date"
        quote: true

      - &dazn_subscription_charge__skey
        name: dazn_subscription_charge__skey
        description: "The subscription_charge__skey of the Subscription active DAZN Subscription on the batch_date"
        quote: true

      - &dazn_subscription_source_system_name_derived__skey
        name: dazn_subscription_source_system_name_derived__skey
        description: "The subscription_source_system_name_derived__skey of the Subscription active DAZN Subscription on the batch_date"
        quote: true

      - &dazn_subscription_tracking_id__skey
        name: dazn_subscription_tracking_id__skey
        description: "The subscription_tracking_id__skey of the Subscription active DAZN Subscription on the batch_date"
        quote: true

      - &dazn_subscription_sign_up_campaign_id__skey
        name: dazn_subscription_sign_up_campaign_id__skey
        description: "The subscription_sign_up_campaign_id__skey of the Subscription active DAZN Subscription on the batch_date"
        quote: true

      - &dazn_subscription_giftcode_campaign_name__skey
        name: dazn_subscription_giftcode_campaign_name__skey
        description: "The subscription_giftcode_campaign_name__skey of the Subscription active DAZN Subscription on the batch_date"
        quote: true

      - &last_dazn_subscription_name
        name: last_dazn_subscription_name
        description: "The Subscription Name of the last DAZN Subscription to be active before the batch_date"
        quote: true

      - &last_dazn_subscription_end_date
        name: last_dazn_subscription_end_date
        description: "The Subscription End Date of the last DAZN Subscription to be active before the batch_date"
        quote: true

      - &last_dazn_subscription_end_week
        name: last_dazn_subscription_end_week
        description: "The Subscription End Week of the last DAZN Subscription to be active before the batch_date"
        quote: true

      - &last_dazn_subscription_type
        name: last_dazn_subscription_type
        description: "The Subscription Type of the last DAZN Subscription to be active before the batch_date"
        quote: true

      - &last_dazn_subscription_tier
        name: last_dazn_subscription_tier
        description: "The Subscription Tier of the last DAZN Subscription to be active before the batch_date"
        quote: true

      - &has_nfl_subscription
        name: has_nfl_subscription
        description: "Flag for if the User has an active NFL Subscription on the batch_date"
        quote: true

      - &nfl_subscription_name
        name: nfl_subscription_name
        description: "The Subscription Name of the active NFL Subscription on the batch_date"
        quote: true

      - &nfl_billing_account__skey
        name: nfl_billing_account__skey
        description: "The billing_account__skey of the Subscription active NFL Subscription on the batch_date"
        quote: true

      - &nfl_subscription_daily_status__skey
        name: nfl_subscription_daily_status__skey
        description: "The subscription_daily_status__skey of the Subscription active NFL Subscription on the batch_date"
        quote: true

      - &nfl_subscription_info__skey
        name: nfl_subscription_info__skey
        description: "The subscription_info__skey of the Subscription active NFL Subscription on the batch_date"
        quote: true

      - &nfl_subscription_term__skey
        name: nfl_subscription_term__skey
        description: "The subscription_term__skey of the Subscription active NFL Subscription on the batch_date"
        quote: true

      - &nfl_subscription_charge__skey
        name: nfl_subscription_charge__skey
        description: "The subscription_charge__skey of the Subscription active NFL Subscription on the batch_date"
        quote: true

      - &nfl_subscription_source_system_name_derived__skey
        name: nfl_subscription_source_system_name_derived__skey
        description: "The subscription_source_system_name_derived__skey of the Subscription active NFL Subscription on the batch_date"
        quote: true

      - &nfl_subscription_tracking_id__skey
        name: nfl_subscription_tracking_id__skey
        description: "The subscription_tracking_id__skey of the Subscription active NFL Subscription on the batch_date"
        quote: true

      - &nfl_subscription_sign_up_campaign_id__skey
        name: nfl_subscription_sign_up_campaign_id__skey
        description: "The subscription_sign_up_campaign_id__skey of the Subscription active NFL Subscription on the batch_date"
        quote: true

      - &nfl_subscription_giftcode_campaign_name__skey
        name: nfl_subscription_giftcode_campaign_name__skey
        description: "The subscription_giftcode_campaign_name__skey of the Subscription active NFL Subscription on the batch_date"
        quote: true

      - &last_nfl_subscription_name
        name: last_nfl_subscription_name
        description: "The Subscription Name of the last NFL Subscription to be active before the batch_date"
        quote: true

      - &last_nfl_subscription_end_date
        name: last_nfl_subscription_end_date
        description: "The Subscription End Date of the last NFL Subscription to be active before the batch_date"
        quote: true

      - &last_nfl_subscription_end_week
        name: last_nfl_subscription_end_week
        description: "The Subscription End Week of the last NFL Subscription to be active before the batch_date"
        quote: true

      - &last_nfl_subscription_type
        name: last_nfl_subscription_type
        description: "The Subscription Type of the last NFL Subscription to be active before the batch_date"
        quote: true

      - &last_nfl_subscription_tier
        name: last_nfl_subscription_tier
        description: "The Subscription Tier of the last NFL Subscription to be active before the batch_date"
        quote: true

      - &user_status__skey
        name: user_status__skey
        description: "The surrogate key (skey) used to join on the user_status__dim"
        quote: true

      - &user_marketing_preferences__skey
        name: user_marketing_preferences__skey
        description: "The surrogate key (skey) used to join on the user_marketing_preferences__dim"
        quote: true

      - &user_last_dazn_subscription__skey
        name: user_last_dazn_subscription__skey
        description: "The surrogate key (skey) used to join on the user_last_dazn_subscription__dim"
        quote: true

      - &user_last_nfl_subscription__skey
        name: user_last_nfl_subscription__skey
        description: "The surrogate key (skey) used to join on the user_last_nfl_subscription__dim"
        quote: true

      - &product_status_fiba
        name: product_status_fiba
        description: " "
        quote: true

      - &product_status_LigaSegunda
        name: product_status_LigaSegunda
        description: " "
        quote: true

      - &product_status_pga
        name: product_status_pga
        description: " "
        quote: true

      - &product_status_RallyTV
        name: product_status_RallyTV
        description: " "
        quote: true

      - &user_status
        name: user_status
        description: " "
        quote: true

      - name: comp_key
        description: " "
        quote: true

  - name: daily_users__intermediate
    description: "Table containing a daily list of all Users ever seen at DAZN as of the batch_date, enriched with various details about their current and most recent subscriptions, if any"
    columns:
      - *META__DBT_INSERT_DTTS

      - *batch_date

      - *batch_week_end_date

      - *dazn_user_id

      - *viewer_id

      - *crm_account_id

      - *billing_account_id

      - *partner_id

      - *user_status__skey

      - *user_marketing_preferences__skey

      - *has_dazn_subscription

      - *dazn_subscription_name

      - *dazn_billing_account__skey

      - *dazn_subscription_daily_status__skey

      - *dazn_subscription_info__skey

      - *dazn_subscription_term__skey

      - *dazn_subscription_charge__skey

      - *dazn_subscription_source_system_name_derived__skey

      - *dazn_subscription_tracking_id__skey

      - *dazn_subscription_sign_up_campaign_id__skey

      - *dazn_subscription_giftcode_campaign_name__skey

      - *last_dazn_subscription_name

      - *user_last_dazn_subscription__skey

      - *has_nfl_subscription

      - *nfl_subscription_name

      - *nfl_billing_account__skey

      - *nfl_subscription_daily_status__skey

      - *nfl_subscription_info__skey

      - *nfl_subscription_term__skey

      - *nfl_subscription_charge__skey

      - *nfl_subscription_source_system_name_derived__skey

      - *nfl_subscription_tracking_id__skey

      - *nfl_subscription_sign_up_campaign_id__skey

      - *nfl_subscription_giftcode_campaign_name__skey

      - *last_nfl_subscription_name

      - *user_last_nfl_subscription__skey

  - name: user_status__dim
    description: "Dimension table surfacing information on user status fields, like country_code and nfl_user status"
    columns:
      - name: user_status__skey
        description: "The surrogate key (skey) used to join on the user_status__dim"
        quote: true
        tests:
          - unique:
              config:
                severity: error

      - *user_account_created_week

      - *home_country_code

      - *product_status_dazn

      - *product_status_nfl

      - *is_validated_dazn_email

      - *is_nfl_authenticated_email

      - *nfl_user

      - *source_type

  - name: user_marketing_preferences__dim
    description: "Dimension table surfacing information on users marketing preferences"
    columns:
      - name: user_marketing_preferences__skey
        description: "The surrogate key (skey) used to join on the *user_marketing_preferences__dim"
        quote: true
        tests:
          - unique:
              config:
                severity: error

      - *has_allowed_dazn_marketing_emails

      - *has_allowed_nfl_marketing_emails

      - *preferences_opted_out_from_personalisation

      - *preferences_opted_in_thirty_days_cancellation

      - *preferences_multi_track_audio_language

      - *allow_daznbet_marketing_emails

      - *language_locale_key

  - name: user_last_dazn_subscription__dim
    description: "Dimension table surfacing information on users last DAZN subscription"
    columns:
      - name: user_last_dazn_subscription__skey
        description: "The surrogate key (skey) used to join on the *user_last_dazn_subscription__dim"
        quote: true
        tests:
          - unique:
              config:
                severity: error

      - *last_dazn_subscription_end_week

      - *last_dazn_subscription_type

      - *last_dazn_subscription_tier

  - name: user_last_nfl_subscription__dim
    description: "Dimension table surfacing information on users last NFL subscription"
    columns:
      - name: user_last_nfl_subscription__skey
        description: "The surrogate key (skey) used to join on the *user_last_nfl_subscription__dim"
        quote: true
        tests:
          - unique:
              config:
                severity: error

      - *last_nfl_subscription_end_week

      - *last_nfl_subscription_type

      - *last_nfl_subscription_tier

  - name: daily_users__billing_account__dim
    description: "Dimension table surfacing information on billing account ex: country, territory.."
    columns:
      - name: billing_account__skey
        description: "The surrogate key (skey) used to join on the billing_account__dim"
        quote: true

      - name: subscription_country
        description: "The country of the subscription, originating from the Zuora contact. E.g. Spain, Germany, Japan, United States, United Kingdom, ..."
        quote: true

      - name: subscription_territory
        description: "The territory of the subscription, pulled in by a join to region_dimension. E.g. Spain, DACH, Japan, United States, UK and Ireland, ..."
        quote: true

      - name: billing_account_currency_code
        description: "The currency code for the Zuora Account, E.g. EUR, USD, ..."
        quote: true

      - name: billing_account_is_batch_50
        description: "Boolean flag, true if the account has ever been on Batch50, which implies it is a test account"
        quote: true

      - name: min_billing_account_subscription_name_created_week
        description: "The week of the very first date that the subscription was first created"
        quote: true

      - name: billing_account_has_advanced_payment_manager
        description: "A flag for if subscriptions on the same account have different payment methods"
        quote: true

      - name: user_account_created_week
        description: "The minimum week_end_date we see the users being created in our systems, derived from Salesforce before migration to Segment"
        quote: true

      - name: country__skey
        description: "The skey for the COUNTRY__DIM that comes directly from the seed of region_dimension for country-territory mappings, this skey is actually just the exact value of the subscription_country"
        quote: true

  - name: daily_users__subscription_charge__dim
    description: "Dimension table surfacing information on the subscription charge like term type, billing period..."
    columns:
      - name: subscription_charge__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_charge__dim"
        quote: true

      - name: subscription_type
        description: "A derived field from the term type and the billing period, or external giftcodes, to categorise the subscription type, can be Monthly, Annual, Instalment or Externally Billed"
        quote: true

      - name: subscription_monthly_recurring_revenue
        description: "The monthly recurring revenue, which is the amount the customer pays for their subscription ignoring Discounts and PPV, adjusting the subscription length down to one month and rounded to two decimal places"
        quote: true

      - name: next_subscription_monthly_recurring_revenue
        description: "The next effective monthly recurring revenue, that could be different from the current monthly recurring revenue and will be effective in the future"
        quote: true

      - name: subscription_bill_cycle_day
        description: "The billing cycle day of the month that the customer is charged"
        quote: true

      - name: subscription_monthly_recurring_revenue_daily_rank
        description: "The rank of the MRR, high to low, on the current batch_date across the dimensions of territory, currency, sub_type and tier"
        quote: true

  - name: daily_users__subscription_daily_status__dim
    description: "Dimension table surfacing information on the subscription daily status like pause, addon, discount.."
    columns:
      - name: subscription_daily_status__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_daily_status__dim"
        quote: true

      - name: is_paused
        description: "Boolean flag, true if the subscription is paused on the exact date_day of the spine"
        quote: true

      - name: has_active_post_sign_up_discount
        description: "Boolean flag, true if the subscription has an active post-sign-up discount on the exact date_day of the spine"
        quote: true

      - name: has_active_free_trial
        description: "Boolean flag, true if the subscription has an active free trial on the exact date_day of the spine"
        quote: true

      - name: has_active_introductory_discount
        description: "Boolean flag, true if the subscription has an active introductory discount on the exact date_day of the spine"
        quote: true

      - name: discounted_monthly_recurring_revenue
        description: "The MRR with discounts applied: MRR * active_discount_percentage"
        quote: true

      - name: daily_revenue_estimate
        description: "The discounted MRR brought down to the daily level: Disc MRR / days_in_month"
        quote: true

  - name: daily_users__subscription_info__dim
    description: "Dimension table surfacing information on the subscription like tier, trip number.."
    columns:
      - name: subscription_info__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_info__dim"
        quote: true

      - name: is_soft_cancelled
        description: "The status of the subscription is cancelled and it won't renew on the current end date"
        quote: true

      - name: subscription_tier
        description: "The Entitlement Set Id of the Rateplan, which is filtered to the associated RatePlanCharge's Flat Fee Pricing Model and Recurring Type, so producing the Tier for the Subscription"
        quote: true

      - name: billing_account_trip_number
        description: "The count of previous subscriptions from this billing account including this subscription for all product groups"
        quote: true

      - name: is_resubscription
        description: "Identifies if this is a resubscription for this product group or not"
        quote: true

      - name: subscription_product_group
        description: "Can be either 'NFL' whereas it is an NFL subscription, or 'DAZN' otherwise."
        quote: true

  - name: daily_users__subscription_term__dim
    description: "Dimension table surfacing information on subscription term like subscription start week and end week"
    columns:
      - name: subscription_term__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_term__dim"
        quote: true

      - name: subscription_start_week
        description: "The week end date the subscription starts, including any free trial"
        quote: true

      - name: subscription_end_week
        description: "The latest week end date Zuora has seen this subscription end, taking into account retrospective cancellation (when the cancel date is before the subscription is updated)"
        quote: true

  - name: daily_users__date__dim
    description: "Date Dimension, used for any information around dates. This is not the official DAZN date dimension, but one created by a dbt macro used for reports"
    columns:
      - name: date_day
        description: ""
        quote: true

      - name: prior_date_day
        description: ""
        quote: true

      - name: next_date_day
        description: ""
        quote: true

      - name: prior_year_date_day
        description: ""
        quote: true

      - name: prior_year_over_year_date_day
        description: ""
        quote: true

      - name: day_of_week
        description: ""
        quote: true

      - name: day_of_week_name
        description: ""
        quote: true

      - name: day_of_week_name_short
        description: ""
        quote: true

      - name: day_of_month
        description: ""
        quote: true

      - name: day_of_year
        description: ""
        quote: true

      - name: week_start_date
        description: ""
        quote: true

      - name: week_end_date
        description: ""
        quote: true

      - name: prior_year_week_start_date
        description: ""
        quote: true

      - name: prior_year_week_end_date
        description: ""
        quote: true

      - name: week_of_year
        description: ""
        quote: true

      - name: prior_year_week_of_year
        description: ""
        quote: true

      - name: month_of_year
        description: ""
        quote: true

      - name: month_name
        description: ""
        quote: true

      - name: month_name_short
        description: ""
        quote: true

      - name: month_start_date
        description: ""
        quote: true

      - name: month_end_date
        description: ""
        quote: true

      - name: prior_year_month_start_date
        description: ""
        quote: true

      - name: prior_year_month_end_date
        description: ""
        quote: true

      - name: quarter_of_year
        description: ""
        quote: true

      - name: quarter_start_date
        description: ""
        quote: true

      - name: quarter_end_date
        description: ""
        quote: true

      - name: year_number
        description: ""
        quote: true

      - name: year_start_date
        description: ""
        quote: true

      - name: year_end_date
        description: ""
        quote: true

      - name: is_week_end
        description: ""
        quote: true

      - name: is_month_end
        description: ""
        quote: true

      - name: is_year_end
        description: ""
        quote: true

      - name: batch_date
        description: ""
        quote: true

  - name: daily_users__subscription_campaign_mapping__dim
    description: "Dimension used for mapping the subscription campaigns (sign-up and post sign-up) to their partner and relevant details"
    columns:
      - name: subscription_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: subscription_sign_up_campaign_id__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: first_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: last_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: campaign_type
        description: "The type of subscription campaign, E.g. Direct, Indirect, Agents, ..."
        quote: true

      - name: campaign_detailed_type
        description: "The detailed type of subscription campaign, E.g. Direct, Operators, Retail, ..."
        quote: true

      - name: campaign_partner_name
        description: "The partner associated with the subscrpition campaign, E.g. TIM, Orange, Telefonica, ..."
        quote: true

  - name: daily_users__subscription_source_system_name_mapping__dim
    description: "Dimension used for mapping the subscription source system names to their partner and relevant details"
    columns:
      - name: subscription_source_system_name_derived__skey
        description: "The surrogate key (skey) used to join on the sub_source_system dim"
        quote: true

      - name: partner_type
        description: "The type of partner, E.g. Direct, Indirect, Agents, ..."
        quote: true

      - name: partner_detailed_type
        description: "The detailed type of partner, E.g. Direct, Operators, Retail, ..."
        quote: true

      - name: partner_name
        description: "The cleaned up version of the source system name, that we will call partner, E.g. TIM, Orange, Telefonica, ..."
        quote: true

  - name: daily_users__subscription_tracking_id_mapping__dim
    description: "Dimension used for mapping the subscription tracking IDs to their partner and relevant details"
    columns:
      - name: subscription_tracking_id__skey
        description: "The surrogate key (skey) used to join on the tracking_id dim"
        quote: true

      - name: tracking_id_type
        description: "The type of subscription tracking ID, E.g. Direct, Indirect, Agents, ..."
        quote: true

      - name: tracking_id_detailed_type
        description: "The detailed type of subscription tracking ID, E.g. Direct, Operators, Retail, ..."
        quote: true

      - name: tracking_id_partner_name
        description: "The partner associated with the subscrpition tracking ID, E.g. TIM, Orange, Telefonica, ..."
        quote: true

      - name: tracking_id_campaign_name
        description: "The campaign name of the subscription Tracking ID"
        quote: true

  - name: daily_users__country__dim
    description: "Dimension containing a mapping of countries to territories"
    columns:
      - name: country__skey
        description: "The surrogate key (skey) used to join on the country__dim"
        quote: true

      - name: country
        description: "The long form country name for this country mapped from the join_key"
        quote: true

      - name: territory
        description: "The DAZN territory mapping for this corresponding country mapped from the join_key"
        quote: true

      - name: region
        description: "The DAZN region mapping for this corresponding country mapped from the join_key"
        quote: true

      - name: country_code
        description: "The two-letter country code for this country mapped form the join_key"
        quote: true

      - name: join_key
        description: "A field containing both long form country names and short two-letter country codes that you can use to be the only field used to join to other datasest and not cause duplication"
        quote: true

      - name: has_nfl_gpi
        description: "1 or 0 Flag for if this country is a country where NFL GPI is available"
        quote: true

  - name: daily_users__fact
    description: "A fact table containing a daily count of the total amount of users at DAZN grouped by skeys that join to dimensions to describe the user on the batch_date like marketing preferences, current and most recent subscription info"
    columns:
      - *META__DBT_INSERT_DTTS

      - *batch_date

      - *batch_week_end_date

      - *user_status__skey

      - *user_marketing_preferences__skey

      - *has_dazn_subscription

      - *dazn_billing_account__skey

      - *dazn_subscription_daily_status__skey

      - *dazn_subscription_info__skey

      - *dazn_subscription_term__skey

      - *dazn_subscription_charge__skey

      - *dazn_subscription_source_system_name_derived__skey

      - *dazn_subscription_tracking_id__skey

      - *dazn_subscription_sign_up_campaign_id__skey

      - *dazn_subscription_giftcode_campaign_name__skey

      - *user_last_dazn_subscription__skey

      - *has_nfl_subscription

      - *nfl_billing_account__skey

      - *nfl_subscription_daily_status__skey

      - *nfl_subscription_info__skey

      - *nfl_subscription_term__skey

      - *nfl_subscription_charge__skey

      - *nfl_subscription_source_system_name_derived__skey

      - *nfl_subscription_tracking_id__skey

      - *nfl_subscription_sign_up_campaign_id__skey

      - *nfl_subscription_giftcode_campaign_name__skey

      - *user_last_nfl_subscription__skey

      - name: user_count
        description: "Count of users (dazn_user_id values) grouped by skeys and batch date"
        quote: true

  - name: daily_users__qa
    description: "A table containing a daily summary of various QA/error check relating to the Daily Users dataset"
    columns:
      - *batch_date

      - name: is_week_end
        description: "Flag for if the batch_date is the end of a week"
        quote: true

      - name: is_month_end
        description: "Flag for if the batch_date is the end of a month"
        quote: true

      - name: is_year_end
        description: "Flag for if the batch_date is the end of a year"
        quote: true

      - name: user_count
        description: "The count of the total amount of users on the batch_date"
        quote: true

      - name: distinct_user_count
        description: "The count of the total amount of distinct users on the batch_date"
        quote: true

      - name: duplicate_user_count
        description: "The count of the total amount of duplicate users on the batch_date"
        quote: true

      - name: user_status__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the user_status__skey"
        quote: true

      - name: user_marketing_preferences__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the user_marketing_preferences__skey"
        quote: true

      - name: user_last_dazn_subscription__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the user_last_dazn_subscription__skey"
        quote: true

      - name: user_last_nfl_subscription__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the user_last_nfl_subscription__skey"
        quote: true

      - name: home_country_code__null_count
        description: "The count of the total amount of subscriptions that have a NULL home_country_code"
        quote: true

      - name: is_nfl_authenticated_email__null_count
        description: "The count of the total amount of subscriptions that have a NULL is_nfl_authenticated_email when the nfl_status is NEW"
        quote: true

      - name: has_allowed_dazn_marketing_emails__null_count
        description: "The count of the total amount of subscriptions that have a NULL has_allowed_dazn_marketing_emails"
        quote: true

      - name: has_allowed_nfl_marketing_emails__null_count
        description: "The count of the total amount of subscriptions that have a NULL has_allowed_nfl_marketing_emails"
        quote: true

      - name: has_dazn_subscription__count
        description: "The count of the amount of users that have active DAZN subscriptions on the batch_date"
        quote: true

      - name: has_nfl_subscription__count
        description: "The count of the amount of users that have active NFL subscriptions on the batch_date"
        quote: true

      - name: has_allowed_dazn_marketing_emails__true_count
        description: "The count of the amount of users that have allowed DAZN Marketing Emails"
        quote: true

      - name: has_allowed_nfl_marketing_emails__true_count
        description: "The count of the amount of users that have allowed NFL Marketing Emails"
        quote: true

      - name: max_dbt_insert_dtts
        description: "The maximum datetime records have been inserted into this table for the batch_date"
        quote: true
