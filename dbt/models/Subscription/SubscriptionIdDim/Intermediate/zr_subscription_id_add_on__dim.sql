{{
    config(
        materialized='table',
        transient=true,
        schema='TRANSIENT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_S_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH subscription_id_current AS (
    SELECT * FROM {{ ref('staging__zuora__subscription_id_current') }}
   -- WHERE FALSE
)

,rateplan_current AS (
    SELECT * FROM {{ ref('staging__zuora__rateplan_current') }}
)

,rateplancharge AS (
    SELECT * FROM {{ ref('staging__zuora__rateplancharge') }}
)

,dmp_addon_staging AS (
    SELECT * FROM {{ ref('staging__dmp_subscription_add_on') }}
)

,zr_name_current AS (
    SELECT * FROM {{ ref('staging__zuora__subscription_name_current') }}

)

,dmp_orders AS (
    SELECT * FROM {{ ref('staging__zuora_ev_legacy_orders') }}

)

,dmp_addon AS (
    SELECT 
        dmp_orders."subscription_name"
        ,dmp_addon_staging.*
        ,zr_name_current."subscription_id"
    FROM dmp_addon_staging 
    INNER JOIN dmp_orders ON "ord_prod_id"="linked_billing_product_id"
    INNER JOIN zr_name_current ON dmp_orders."subscription_name"=zr_name_current."subscription_name"

)

,rateplan_subs as (
    SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,HASH(
        subscription_id_current."subscription_id"
        ,IFNULL("service_provider_user_id",rateplan_current."rateplan_partner_user_id") 
        ,IFNULL("validity_start_date",rateplancharge."rateplan_charge_effective_start_date")
    ) AS "subscription_id_add_on__skey"
    ,subscription_id_current."subscription_id" 
    ,subscription_id_current."subscription_name"
    ,subscription_id_current."subscription_id_created_timestamp"
    ,subscription_id_current."subscription_start_date"
    ,subscription_id_current."subscription_end_date"
    ,subscription_id_current."billing_account_id"
    ,rateplan_current."rateplan_id"
    ,rateplan_current."rateplan_name"
    ,rateplan_current."rateplan_created_timestamp"
    ,rateplan_current."entitlement_set_id"
    ,rateplan_current."rateplan_source_system_name"
    ,rateplan_current."rateplan_tracking_id"
    ,CASE  
        WHEN "validity_start_date" IS NOT NULL 
            THEN dmp_addon."service_provider"
            ELSE rateplan_current."rateplan_partner_id" 
     END AS "rateplan_partner_id"
    ,rateplan_current."rateplan_partner_user_id"
    ,rateplan_current."rateplan_next_invoice_date"
    ,CASE 
        WHEN "catalog_product_id" ILIKE '%CONCURRENCY%' 
            THEN 'concurrency' 
            ELSE rateplan_current."rateplan_addon_type" 
     END AS "subscription_add_on_type"
    ,rateplancharge."rateplan_charge_id"
    ,rateplancharge."rateplan_charge_name"
    ,rateplancharge."rateplan_charge_charge_type"
    ,LEAST_IGNORE_NULLS("validity_start_date" ,rateplancharge."rateplan_charge_effective_start_date" ) AS "subscription_add_on_effective_start_date"
    ,GREATEST_IGNORE_NULLS (CASE WHEN dmp_addon."message_type" IN ('BILLING_PRODUCT_CANCELATION','BILLING_PRODUCT_RENEWAL') THEN "validity_end_date"::DATE 
    ELSE NULL END,CASE
    -- When it's a NFL AddOn, and has the 1 day Effectiveness
        WHEN rateplancharge."rateplan_charge_charge_type" = 'OneTime' 
            AND DATEDIFF('day', rateplancharge."rateplan_charge_effective_start_date", rateplancharge."rateplan_charge_effective_end_date") = 1
            AND rateplan_current."rateplan_name" ILIKE 'NFL%' 
        -- Then use the subscription_end_date
            THEN subscription_id_current."subscription_end_date"
    -- When it's a PAC/3PP AddOn, and has the 1 day Effectiveness
        WHEN rateplancharge."rateplan_charge_charge_type" = 'OneTime' 
            AND DATEDIFF('day', rateplancharge."rateplan_charge_effective_start_date", rateplancharge."rateplan_charge_effective_end_date") = 1
            -- Then use the least of the NextInvoiceDate (plus active grace period) or the SubEndDate (if it's cancelled before the NextInvoiceDate)
            THEN LEAST(subscription_id_current."subscription_end_date", DATEADD('day', 12, rateplan_current."rateplan_next_invoice_date"))
    -- Else (when it's a direct sub, or 0 day 3PP/PAC) use the RPC Eff End, as that correctly accounts for pauses and cancels
            ELSE rateplancharge."rateplan_charge_effective_end_date"
     END) AS "subscription_add_on_effective_end_date"
    ,"event_timestamp" AS "event_timestamp"
FROM subscription_id_current 
LEFT JOIN rateplan_current 
    ON rateplan_current."subscription_id" = subscription_id_current."subscription_id"
LEFT JOIN rateplancharge 
    ON rateplancharge."rateplan_id" = rateplan_current."rateplan_id"
LEFT JOIN dmp_addon 
   ON dmp_addon."subscription_id"=rateplan_current."subscription_id" 
   AND UPPER(dmp_addon."service_provider")=UPPER(rateplan_current."rateplan_partner_id")
   AND UPPER(dmp_addon."entitlement_set_id")=UPPER(rateplan_current."entitlement_set_id")
     -- THIS CONDITION TO BE REVISTED. 
WHERE
    (rateplan_current."rateplan_product_type" = 'addon' OR LOWER(dmp_addon."product_type")='addon')
    -- Filtering for only AddOns that are effective at the time the SubId is created
    -- This filters out future resumes that aren't live yet, or past periods that have been cancelled
   -- AND
   -- subscription_id_current."subscription_id_created_timestamp" >= "subscription_add_on_effective_start_date"
    AND
    subscription_id_current."subscription_id_created_timestamp" < "subscription_add_on_effective_end_date"
)

,dmp_purchase as (
SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,HASH(
        subscription_id_current."subscription_id"
        ,dmp_addon."service_provider_user_id"
        ,dmp_addon."validity_start_date"
    ) AS "subscription_id_add_on__skey"
    ,subscription_id_current."subscription_id"
    ,subscription_id_current."subscription_name"
    ,subscription_id_current."subscription_id_created_timestamp"
    ,subscription_id_current."subscription_start_date"
    ,subscription_id_current."subscription_end_date"
    ,subscription_id_current."billing_account_id"  
    ,dmp_addon."billing_product_id" AS "rateplan_id"
    ,dmp_addon."catalog_product_id" AS "rateplan_name" 
    ,IFNULL(dmp_addon."purchase_date",dmp_addon."event_timestamp") AS "rateplan_created_timestamp"
    ,dmp_addon."entitlement_set_id" AS "entitlement_set_id"
    ,dmp_addon."source_system_derived" AS "rateplan_source_system_name"
    ,dmp_addon."tracking_id_derived" AS "rateplan_tracking_id"
    ,IFNULL(dmp_addon."service_provider",'Unknown') AS "rateplan_partner_id"
    ,dmp_addon."service_provider_user_id" AS "rateplan_partner_user_id"
    ,dmp_addon."next_billing_date" AS "rateplan_next_invoice_date"
    ,CASE 
        WHEN "rateplan_name" ILIKE '%CONCURRENCY%' 
            THEN 'concurrency' ELSE NULL 
        END AS "subscription_add_on_type" 
    ,dmp_addon."billing_product_id" AS "rateplan_charge_id"
    ,dmp_addon."catalog_product_id" AS "rateplan_charge_name"
    ,dmp_addon."billing_charge_type" AS "rateplan_charge_charge_type"
    ,dmp_addon."validity_start_date"::DATE AS "subscription_add_on_effective_start_date"
    ,dmp_addon."validity_end_date"::DATE AS "subscription_add_on_effective_end_date"
    ,dmp_addon."event_timestamp" AS "event_timestamp"
    FROM subscription_id_current
    LEFT JOIN dmp_addon ON dmp_addon."subscription_id"=subscription_id_current."subscription_id"
    LEFT JOIN rateplan_subs ON dmp_addon."subscription_id"=rateplan_subs."subscription_id"
    WHERE (rateplan_subs."subscription_id" IS NULL OR dmp_addon."entitlement_set_id"<> rateplan_subs."entitlement_set_id")
    AND dmp_addon."validity_start_date"::DATE<dmp_addon."validity_end_date"::DATE
)

,final AS (
SELECT * FROM rateplan_subs
UNION ALL 
SELECT * FROM dmp_purchase
)
SELECT * FROM final
