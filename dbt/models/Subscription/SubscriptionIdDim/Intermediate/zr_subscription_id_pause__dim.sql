{{
    config(
        materialized='table',
        unique_key='"subscription_id"',
        schema='TRANSIENT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_S_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH subscription_id_current AS (
    SELECT * FROM {{ ref('staging__zuora__subscription_id_current') }}
    WHERE "subscription_product_group" != 'NFL'
)

,rateplan_staging AS (
    SELECT * FROM {{ ref('staging__zuora__rateplan_current') }}
)

,rateplancharge_staging AS (
    SELECT * FROM {{ ref('staging__zuora__rateplancharge_current') }}
)

,dmp_pause AS (
SELECT * FROM {{ ref('staging__dmp_subscription_pause') }}
)


,sub_name_current AS (
    SELECT * FROM {{ ref('staging__zuora__subscription_name_current') }}

)

,rpc_gaps AS (
    SELECT
        subscription_id_current."subscription_id"
        ,subscription_id_current."subscription_name"
        ,subscription_id_current."subscription_id_created_timestamp"
        ,subscription_id_current."billing_account_id"
        ,rateplancharge_staging."rateplan_charge_effective_start_date"
        ,rateplancharge_staging."rateplan_charge_effective_end_date"
        ,LEAD(rateplancharge_staging."rateplan_charge_effective_start_date") OVER (PARTITION BY subscription_id_current."subscription_id" ORDER BY rateplancharge_staging."rateplan_charge_effective_start_date", rateplancharge_staging."rateplan_charge_effective_end_date") AS "lead_rateplan_charge_effective_start_date"
    FROM subscription_id_current
    LEFT JOIN rateplan_staging
        ON rateplan_staging."subscription_id" = subscription_id_current."subscription_id"
    LEFT JOIN rateplancharge_staging
        ON rateplancharge_staging."rateplan_id" = rateplan_staging."rateplan_id"
    WHERE
        -- There are some (very few) Subscriptions that don't yet have the required Rateplan Charges, mainly due to latency
        -- So to at least make sure these IDs are present in the dataset we look for NULLs as well
        (rateplancharge_staging."rateplan_charge_charge_model" = 'Flat Fee Pricing' OR rateplancharge_staging."rateplan_charge_charge_model" IS NULL)
        AND
        (rateplancharge_staging."rateplan_charge_charge_type" = 'Recurring' OR rateplancharge_staging."rateplan_charge_charge_type" IS NULL)
        AND
        (rateplan_staging."rateplan_product_type" != 'addon' OR rateplan_staging."rateplan_product_type" IS NULL)
        AND
        -- Exclude RPCs that have the exact same effective start/end dates, as these come up with cancels before/during pauses and mess with the logic e.g. A-Sa206591ad210f17c936d15fdf2af3900
        rateplancharge_staging."rateplan_charge_effective_start_date" != rateplancharge_staging."rateplan_charge_effective_end_date"
    -- Filter for only gaps in RPC Effective Dates, which means a pause
    QUALIFY "lead_rateplan_charge_effective_start_date" > rateplancharge_staging."rateplan_charge_effective_end_date"
)

,zu_sub AS (
    SELECT
        "subscription_id"
        ,"subscription_name"
        ,"subscription_id_created_timestamp"
        ,"billing_account_id"
        ,"rateplan_charge_effective_end_date" AS "subscription_pause_start_date"
        ,"lead_rateplan_charge_effective_start_date" AS "subscription_pause_end_date"
        ,DATEDIFF('week', "subscription_pause_start_date", "subscription_pause_end_date") AS "pause_duration_weeks"
    FROM rpc_gaps
-- Filter for the most recent pause to account for multiple past pauses that get repeated in the RPC effectives
QUALIFY ROW_NUMBER() OVER (PARTITION BY "subscription_id" ORDER BY "rateplan_charge_effective_start_date" DESC, "rateplan_charge_effective_end_date" DESC) = 1
)

,ev_sub AS (
    SELECT
        sub_name_current."subscription_id"
        ,sub_name_current."subscription_name"
        ,sub_name_current."subscription_id_created_timestamp"
        ,sub_name_current."billing_account_id"
        ,"pause_start_date"::DATE AS "subscription_pause_start_date"
        ,"pause_end_date"::DATE AS "subscription_pause_end_date"
        ,DATEDIFF('week', "subscription_pause_start_date", "subscription_pause_end_date") AS "pause_duration_weeks"
    FROM dmp_pause
    INNER JOIN sub_name_current ON "legacy_subscription_name"=sub_name_current."subscription_name"
)

,final AS (
SELECT * FROM zu_sub
UNION ALL
SELECT * FROM ev_sub
)

SELECT * FROM final
-- Filter for the most recent pause to account for multiple past pauses that get repeated in the RPC effectives
QUALIFY ROW_NUMBER() OVER (PARTITION BY "subscription_id" ORDER BY "subscription_pause_start_date" DESC, "subscription_pause_end_date" DESC) = 1
