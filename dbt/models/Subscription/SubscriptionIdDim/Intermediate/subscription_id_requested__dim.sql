{{
    config(
        materialized='table',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_2XL_WH'
    )
}}

-- Removing the tag to make sure the model is not getting updated.
-- tags=['presentation-subscription-domain']

WITH subscription_staging AS (
    SELECT * FROM {{ ref('staging__zuora__subscription_id_current') }}
)

,rateplan_staging AS (
    SELECT * FROM {{ ref('staging__zuora__rateplan_current') }}
)

,rateplancharge_staging AS (
    SELECT * FROM {{ ref('staging__zuora__rateplancharge') }}
)

SELECT
    subscription_staging."subscription_id"
    ,subscription_staging."subscription_name"
    ,subscription_staging."subscription_name_original_created_timestamp"
    ,subscription_staging."subscription_id_created_timestamp"
    ,subscription_staging."subscription_id_updated_timestamp"
    ,subscription_staging."billing_account_id"
    ,subscription_staging."subscription_source_system_name"
    ,subscription_staging."subscription_sign_up_giftcode"
    ,subscription_staging."subscription_term_type"
    ,subscription_staging."subscription_is_auto_renew"
    ,rateplan_staging."rateplan_id"
    ,rateplan_staging."rateplan_updated_timestamp"
    ,rateplan_staging."rateplan_name"
    -- With the right filtering of only Recurring Flat Fee Pricing RatePlan this entitlement_set_id is actually just the tier
    -- but do note that with other filters we get other entitlements, like ppv
    ,rateplan_staging."entitlement_set_id" AS "subscription_tier"
    ,rateplancharge_staging."rateplan_charge_id"
    ,rateplancharge_staging."rateplan_charge_name"
    ,rateplancharge_staging."rateplan_charge_effective_start_date"
    ,rateplancharge_staging."rateplan_charge_effective_end_date"
    ,rateplancharge_staging."rateplan_charge_billing_period"
    ,rateplancharge_staging."rateplan_charge_bill_cycle_day"
    ,rateplancharge_staging."rateplan_charge_up_to_periods"
    ,rateplancharge_staging."rateplan_charge_charged_through_date"
    ,CASE
        -- If it's a Partner Acces Code (PAC) subscription then the billing info is controlled externally and we have no way of knowing how what price they are actually paying
        -- So we NULL out the MRR when we identify these by the presence of a Source System and if they have a valid giftcode applied
        WHEN subscription_staging."subscription_source_system_name" IS NOT NULL AND subscription_staging."subscription_sign_up_giftcode" IS NOT NULL THEN NULL
        ELSE rateplancharge_staging."rateplan_charge_monthly_recurring_revenue"
    END AS "rateplan_charge_monthly_recurring_revenue"
    ,CASE
        -- If it's a Partner Acces Code (PAC) subscription then the billing info is controlled externally and we have no way of knowing how what price they are actually paying
        -- So we NULL out the MRR when we identify these by the presence of a Source System and if they have a valid giftcode applied
        WHEN subscription_staging."subscription_source_system_name" IS NOT NULL AND subscription_staging."subscription_sign_up_giftcode" IS NOT NULL THEN NULL
        ELSE LAG(rateplancharge_staging."rateplan_charge_monthly_recurring_revenue") OVER (PARTITION BY subscription_staging."subscription_id" ORDER BY rateplancharge_staging."rateplan_charge_effective_end_date", rateplancharge_staging."rateplan_charge_effective_start_date", rateplancharge_staging."rateplan_charge_version"::INT)
    END AS "previous_rateplan_charge_monthly_recurring_revenue"
    ,CASE
        -- If it's a Partner Acces Code (PAC) subscription then the billing info is controlled externally and we have no way of knowing how they are actually billed
        -- We can identify these by the presence of a Source System and if they have a valid giftcode applied
        WHEN subscription_staging."subscription_source_system_name" IS NOT NULL AND subscription_staging."subscription_sign_up_giftcode" IS NOT NULL AND subscription_staging."subscription_sign_up_giftcode" != 'null' THEN 'Externally Billed'
        -- We use the following 4 lines to catch any weirdly configured RatePlanCharges and to make sure we classify them right
        WHEN rateplancharge_staging."rateplan_charge_name" ILIKE '%month%' THEN 'Monthly'
        WHEN rateplancharge_staging."rateplan_charge_name" ILIKE '%annual%' THEN 'Annual'
        WHEN rateplancharge_staging."rateplan_charge_name" ILIKE ANY ('%instal%', '%ppi%') 
            THEN 
                CASE
                    WHEN
                        subscription_staging."subscription_product_group" = 'NFL' AND coalesce(rateplancharge_staging."rateplan_charge_up_to_periods",12) != 12
                    THEN 'Season Instalment'
                    ELSE 'Instalment'
                END
        WHEN rateplancharge_staging."rateplan_charge_name" ILIKE '%week%' THEN 'Weekly'
        -- But we still need the following lines to catch any RatePlanCharges that we cannot easily catch from the Name, and as we should not rely on a free-text field like the Name
        WHEN subscription_staging."subscription_term_type" = 'EVERGREEN' AND rateplancharge_staging."rateplan_charge_billing_period" = 'Month' THEN 'Monthly'
        WHEN subscription_staging."subscription_term_type" = 'TERMED' AND rateplancharge_staging."rateplan_charge_billing_period" IN ('Subscription Term', 'Annual') THEN 'Annual'
        WHEN subscription_staging."subscription_term_type" = 'TERMED' AND rateplancharge_staging."rateplan_charge_billing_period" = 'Month' 
            THEN 
                CASE
                    WHEN
                        subscription_staging."subscription_product_group" = 'NFL' AND coalesce(rateplancharge_staging."rateplan_charge_up_to_periods",12) != 12
                    THEN 'Season Instalment'
                    ELSE 'Instalment'
                END
        WHEN subscription_staging."subscription_term_type" = 'TERMED' AND rateplancharge_staging."rateplan_charge_billing_period" = 'Week' THEN 'Weekly'
        WHEN subscription_staging."subscription_term_type" = 'EVERGREEN' AND
             rateplancharge_staging."rateplan_charge_charge_type" = 'OneTime' AND
             rateplancharge_staging."rateplan_charge_billing_period" IS NULL AND
             rateplancharge_staging."rateplan_charge_name" ilike '%ONE TIME NFL Season Pro Pass%' THEN 'Annual'
        WHEN subscription_staging."subscription_term_type" = 'TERMED' AND rateplancharge_staging."rateplan_charge_name" ilike '%Half Season%' THEN 'Season Upfront'
        ELSE 'Unknown'
    END AS "subscription_type"
    ,CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
FROM subscription_staging
LEFT JOIN rateplan_staging ON rateplan_staging."subscription_id" = subscription_staging."subscription_id"
LEFT JOIN rateplancharge_staging ON rateplancharge_staging."rateplan_id" = rateplan_staging."rateplan_id"
WHERE 1=1
AND (
    -- There are some (very few) Subscriptions that don't yet have the required Rateplan Charges, mainly due to latency
    -- So to at least make sure these IDs are present in the dataset we look for NULLs as well
    (rateplancharge_staging."rateplan_charge_charge_model" = 'Flat Fee Pricing' OR rateplancharge_staging."rateplan_charge_charge_model" IS NULL)
    AND
    (rateplancharge_staging."rateplan_charge_charge_type" = 'Recurring' OR rateplancharge_staging."rateplan_charge_charge_type" IS NULL)
    AND
    (rateplan_staging."rateplan_product_type" != 'addon' OR rateplan_staging."rateplan_product_type" IS NULL)
    OR
        (
            --  Adding the below condition to allow the OneTime subscription Ids in to the data model
            (subscription_staging."subscription_term_type" = 'EVERGREEN' AND
                rateplancharge_staging."rateplan_charge_charge_type" = 'OneTime' AND
                    rateplancharge_staging."rateplan_charge_billing_period" IS NULL AND
                        rateplancharge_staging."rateplan_charge_name" ilike '%ONE TIME NFL Season Pro Pass%')
                )
    )
AND
    -- There are often some weird RatePlanCharges that are not actualy effective for any amount of time
    -- This can really mess up with this model, mostly in the MRR LAG as that is often the one picked if we don't filter these out
    rateplancharge_staging."rateplan_charge_effective_start_date" != rateplancharge_staging."rateplan_charge_effective_end_date"
-- We select the latest ending RatePlanCharge for each Subscription ID as this will be the one that the user has most recently requested
-- There are very infrequently some duplicates of Effective Start/End, and so we put a final catch for these with the Version ORDER BY
QUALIFY ROW_NUMBER() OVER (PARTITION BY subscription_staging."subscription_id" ORDER BY rateplancharge_staging."rateplan_charge_effective_end_date" DESC, rateplancharge_staging."rateplan_charge_effective_start_date" DESC, rateplancharge_staging."rateplan_charge_version"::INT DESC) = 1
