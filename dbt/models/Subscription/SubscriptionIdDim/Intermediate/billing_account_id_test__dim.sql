{{
    config(
        materialized='table',
        schema='PRESENTATION'
    )
}}

-- Removing the tag to make sure the model is not getting updated.
-- tags=['presentation-subscription-domain']

WITH account_source AS (
    SELECT * FROM {{ ref('staging__zuora__account') }}
)

SELECT
    "billing_account_id"
    ,MAX(CASE WHEN "billing_account_batch_name" = 'Batch50' THEN TRUE ELSE FALSE END) AS "billing_account_is_batch_50"
FROM account_source
GROUP BY 1
