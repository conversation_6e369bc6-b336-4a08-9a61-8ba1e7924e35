{{
    config(
        materialized='table',
        schema='TRANSIENT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_2XL_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH event_discount AS (
    SELECT  * FROM {{ ref('staging__dmp_subscription_promotion') }}
    WHERE "event_status"='SUCCEEDED' AND "is_active"
)


,sub_core AS (
    SELECT * FROM {{ ref('staging__dmp_subscription_core') }}
)

-- calculating some window functions over the whole of the rate plan charges (that we can't group by)
,windows AS (
    SELECT 
         sub_core."subscription_id"
        ,sub_core."subscription_name"
        ,sub_core."subscription_name_original_created_timestamp"
        ,sub_core."subscription_id_created_timestamp"
        ,sub_core."billing_account_id"
        ,IFF(sub_core."subscription_start_date" < "start_date", "coupon_code",NULL)  "rateplan_charge_post_sign_up_giftcode"
        ,FIRST_VALUE("rateplan_charge_post_sign_up_giftcode" IGNORE NULLS) OVER (PARTITION BY sub_core."subscription_id" ORDER BY "start_date") AS "first_rateplan_charge_post_sign_up_giftcode"
        ,LAST_VALUE("rateplan_charge_post_sign_up_giftcode" IGNORE NULLS) OVER (PARTITION BY sub_core."subscription_id" ORDER BY "start_date") AS "last_rateplan_charge_post_sign_up_giftcode"
        , IFF(sub_core."subscription_start_date" < "start_date","coupon_campaign", NULL) "rateplan_charge_post_sign_up_giftcode_campaign_name"
        ,CASE 
            WHEN event_discount."type" IN ('PERCENTAGE_OFF','PERCENTAGE') 
                THEN event_discount."promotion_percentage" 
                ELSE NULL 
        END  AS "rateplan_charge_tier_discount_percentage"
        ,FIRST_VALUE("rateplan_charge_post_sign_up_giftcode_campaign_name" IGNORE NULLS) OVER (PARTITION BY sub_core."subscription_id" ORDER BY "start_date") AS "first_rateplan_charge_post_sign_up_giftcode_campaign_name"
        ,LAST_VALUE("rateplan_charge_post_sign_up_giftcode_campaign_name" IGNORE NULLS) OVER (PARTITION BY sub_core."subscription_id" ORDER BY "start_date") AS "last_rateplan_charge_post_sign_up_giftcode_campaign_name"
        ,"start_date" as "rateplan_charge_effective_start_date"
        ,"end_date" as "rateplan_charge_effective_end_date"
        ,"channel" "rateplan_context"
        ,FIRST_VALUE("rateplan_context" IGNORE NULLS) OVER (PARTITION BY sub_core."subscription_id" ORDER BY "start_date") AS "first_rateplan_charge_post_sign_up_context"
        ,LAST_VALUE("rateplan_context" IGNORE NULLS) OVER (PARTITION BY sub_core."subscription_id" ORDER BY "start_date") AS "last_rateplan_charge_post_sign_up_context"
        , IFF("subscription_version"=1,"coupon_campaign",NULL) AS "subscription_sign_up_campaign_id"
        ,IFF(sub_core."subscription_start_date" ="start_date", TRUE, FALSE) AS "is_introductory_discount"
        --,PROMO_PAYLOAD
       -- ,event_discount.event_timestamp AS "event_timestamp"
        ,OBJECT_CONSTRUCT(
            'is_introductory_discount', "is_introductory_discount"
            ,'subscription_sign_up_campaign_id', "subscription_sign_up_campaign_id"
            ,'rateplan_charge_post_sign_up_giftcode', "rateplan_charge_post_sign_up_giftcode"
            ,'rateplan_charge_post_sign_up_giftcode_campaign_name', "rateplan_charge_post_sign_up_giftcode_campaign_name"
            ,'rateplan_charge_tier_discount_percentage',"rateplan_charge_tier_discount_percentage"
            ,'rateplan_charge_effective_start_date', "rateplan_charge_effective_start_date"
            ,'rateplan_charge_effective_end_date', "rateplan_charge_effective_end_date"
            ,'rateplan_context', "rateplan_context"
        ) AS "rateplan_charge_post_sign_up_object"
    FROM event_discount
    INNER JOIN sub_core
    ON  event_discount."billing_product_id" = sub_core."subscription_id"
    WHERE
      -- post-sign-up giftcodes only come through in Discount RPCs
        event_discount."type" IN ('PERCENTAGE_OFF','PERCENTAGE') 
        AND
        -- we only need to see RPCs that have post-sign-up discounts
        "rateplan_charge_tier_discount_percentage" IS NOT NULL
        AND
        -- we only need to see RPCs that have post-sign-up giftcodes that are relevant to this SubId so are effective on or after the SubId was created
        "rateplan_charge_effective_end_date" >= sub_core."subscription_id_created_timestamp"::DATE 
)

-- grouping the SubId to then add some additional summary data points and aggregations and ability to collect into ARRAYs
SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,"subscription_id"
    ,"subscription_name"
    ,"subscription_name_original_created_timestamp"
    ,"subscription_id_created_timestamp"
    ,"billing_account_id"
    ,"first_rateplan_charge_post_sign_up_giftcode"
    ,"last_rateplan_charge_post_sign_up_giftcode"
    ,"first_rateplan_charge_post_sign_up_giftcode_campaign_name"
    ,"last_rateplan_charge_post_sign_up_giftcode_campaign_name"
    ,"first_rateplan_charge_post_sign_up_context"
    ,"last_rateplan_charge_post_sign_up_context"
    ,"subscription_sign_up_campaign_id"
   -- ,"event_timestamp"
    ,MAX("is_introductory_discount") AS "is_introductory_discount"
    ,MIN("rateplan_charge_tier_discount_percentage") as "rateplan_charge_tier_min_discount_percentage"
    ,MAX("rateplan_charge_tier_discount_percentage") as "rateplan_charge_tier_max_discount_percentage"
    ,COUNT(DISTINCT "rateplan_charge_tier_discount_percentage") AS "rateplan_charge_tier_discount_distinct_count"
    ,COUNT(DISTINCT "rateplan_charge_post_sign_up_giftcode") AS "rateplan_charge_post_sign_up_giftcode_distinct_count"
    ,ARRAY_AGG(DISTINCT "rateplan_charge_post_sign_up_giftcode") AS "rateplan_charge_post_sign_up_giftcode_list"
    ,COUNT(DISTINCT "rateplan_charge_post_sign_up_giftcode_campaign_name") AS "rateplan_charge_post_sign_up_giftcode_campaign_name_distinct_count"
    ,ARRAY_AGG(DISTINCT "rateplan_charge_post_sign_up_giftcode_campaign_name") AS "rateplan_charge_post_sign_up_giftcode_campaign_name_list"
    ,MIN("rateplan_charge_effective_start_date") AS "rateplan_charge_effective_start_date"
    ,MAX("rateplan_charge_effective_end_date") AS "rateplan_charge_effective_end_date"
    ,DATEDIFF(DAY,MIN("rateplan_charge_effective_start_date"),MAX("rateplan_charge_effective_end_date")) AS "discount_duration_days"
    ,DATEDIFF(WEEK,MIN("rateplan_charge_effective_start_date"),MAX("rateplan_charge_effective_end_date")) AS "discount_duration_weeks"
    ,ARRAY_AGG("rateplan_charge_post_sign_up_object") WITHIN GROUP (ORDER BY "rateplan_charge_effective_start_date", "rateplan_charge_effective_end_date") AS "discount_data"
FROM windows
GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13
