{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"subscription_start_date"',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_2XL_WH',
        tags=['presentation-subscription-domain']
    )
}}

{% set subscription_mart_variables = subscription_mart_variables() %}

WITH sub_name_current AS (
    SELECT * FROM {{ ref('staging__zuora__subscription_name_current') }}
    {% if is_incremental() %}
    WHERE "subscription_name_original_created_timestamp"::DATE BETWEEN DATEADD('day', -{{ subscription_mart_variables.rebuild_days }}, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
    {% endif %} 
)

,zuora_account_current AS (
    SELECT * FROM {{ ref('staging__zuora__account_current') }}
)

,docomo_dim AS (
    SELECT * FROM {{ ref('subscription_id_docomo__dim') }}
    {% if is_incremental() %}
    WHERE "subscription_start_date" BETWEEN DATEADD('day', -{{ subscription_mart_variables.rebuild_days }}, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
    {% endif %} 
)

,cust_id_dim AS (
    SELECT * FROM {{ ref('customer_identity_dim_current') }}
)

,playback_fact AS (
    -- Using new dbt sources and not the refs to avoid circular references with the plays join to users
    SELECT * FROM {{ source('FACT', 'PLAYBACK_STREAM__FACT') }}
)

,content_dim AS (
    -- Using new dbt sources and not the refs to avoid circular references with the plays join to users
    SELECT * FROM {{ source('CONTENT', 'CONTENT_ITEM__DIM') }}
)

,entitlements_dim AS (
    -- Using new dbt sources and not the refs to avoid circular references with the plays join to users
    SELECT * FROM {{ source('ENTITLEMENTS', 'ENTITLEMENTS__DIM') }}

)

-------ev code
,ev_sub_name_current AS (
    SELECT 
    dmp.* 
    FROM {{ ref('staging__dmp_subscription_core') }} AS dmp
    LEFT JOIN {{ ref('staging__zuora__subscription_name_current') }} sub_name_current using("subscription_name") 
    WHERE dmp."subscription_status" != 'Expired' AND sub_name_current."subscription_name" IS NULL

  {% if is_incremental() %}
    AND dmp."subscription_name_original_created_timestamp"::DATE BETWEEN DATEADD('day', -{{ subscription_mart_variables.rebuild_days }}, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
    {% endif %} 
      QUALIFY ROW_NUMBER() OVER (PARTITION BY "subscription_name" ORDER BY "subscription_id_updated_timestamp" DESC--, "subscription_id_created_timestamp" DESC, "subscription_version"::INT DESC
      ) 
      = 1
)

-- Adding the logic to get Viewer ID using both CRM ID and DAZN ID 
,all_subs_ids AS (
    SELECT
        sub_name_current."billing_account_id"
        ,zuora_account_current."crm_account_id"
        ,"dazn_user_id"
    FROM sub_name_current
    LEFT JOIN zuora_account_current
    USING ("billing_account_id")
	UNION all  
    SELECT DISTINCT
        docomo_dim."billing_account_id"
        ,docomo_dim."crm_account_id"
        ,docomo_dim."dazn_user_id"
    FROM docomo_dim

-------ev code

    UNION ALL 
        SELECT
         ev_sub_name_current."billing_account_id"
        ,zuora_account_current."crm_account_id"
        ,ev_sub_name_current."dazn_user_id"
    FROM ev_sub_name_current
    LEFT JOIN zuora_account_current
    USING ("billing_account_id")
)

,dazn_id AS (
	SELECT 
		all_subs."billing_account_id",
		all_subs."crm_account_id",
		all_subs."dazn_user_id",
		cust_id_dim."viewer_id"
    FROM all_subs_ids all_subs
    LEFT JOIN TRANSFORMATION_PROD.PRESENTATION.customer_identity_dim_current cust_id_dim 
    ON all_subs."dazn_user_id"=cust_id_dim."dazn_user_id"
)

,crm_id AS (
	SELECT 
		all_subs."billing_account_id",
		all_subs."crm_account_id",
		all_subs."dazn_user_id",
		cust_id_dim."viewer_id"
    FROM all_subs_ids all_subs
    LEFT JOIN TRANSFORMATION_PROD.PRESENTATION.customer_identity_dim_current cust_id_dim 
    ON all_subs."crm_account_id"=cust_id_dim."dazn_user_id"
)

,dcm_crm_id AS (
	SELECT 
		all_subs."billing_account_id",
		all_subs."crm_account_id",
		all_subs."dazn_user_id",
		cust_id_dim."viewer_id"
    FROM all_subs_ids all_subs
    LEFT JOIN TRANSFORMATION_PROD.PRESENTATION.customer_identity_dim_current cust_id_dim 
    ON all_subs."crm_account_id"=cust_id_dim."crm_account_id"
)
-- Joining Docomo and Zuora Staging together
,all_subs AS (
		SELECT 
			"billing_account_id",
			"crm_account_id",
			"dazn_user_id",
			"viewer_id"
		FROM (
                SELECT * FROM dazn_id 
                UNION ALL
                SELECT * FROM crm_id
                UNION ALL
                SELECT * FROM dcm_crm_id
			)
		QUALIFY ROW_number() OVER (PARTITION BY "billing_account_id" ORDER BY "viewer_id")=1 
)
-- Getting all acquisitions from the last 7 days (as we look at 7 day attribution, so the attribution can change for up to 7 days)
-- and joining with the cust id dim to get viewer_id for the plays join
,acqs AS (
    SELECT
        sub_name_current."billing_account_id"
        ,"viewer_id"
        ,sub_name_current."subscription_name"
        ,sub_name_current."subscription_name_original_created_timestamp"::DATE AS "subscription_start_date"
        ,sub_name_current."subscription_country"
    FROM sub_name_current 
    JOIN all_subs ON sub_name_current."billing_account_id" = all_subs."billing_account_id"
	UNION ALL
    SELECT DISTINCT
        docomo_dim."billing_account_id"
        ,"viewer_id"
        ,docomo_dim."subscription_name"
        ,docomo_dim."subscription_start_date"
        ,docomo_dim."subscription_country"
    FROM docomo_dim  
    JOIN all_subs ON docomo_dim."billing_account_id" =all_subs."billing_account_id"

--ev code
    union all

        SELECT
        ev_sub_name_current."billing_account_id"
        ,"viewer_id"
        ,ev_sub_name_current."subscription_name"
        ,ev_sub_name_current."subscription_name_original_created_timestamp"::DATE AS "subscription_start_date"
        ,ev_sub_name_current."subscription_country"
    FROM ev_sub_name_current 
    JOIN all_subs ON ev_sub_name_current."billing_account_id" =all_subs."billing_account_id"
 )

,plays_prep AS (
    SELECT
        playback_fact."viewer_id"
        ,playback_fact."playback_stream_date"
        ,playback_fact."playback_duration_milliseconds"
        ,content_dim."fixture_id"
        ,content_dim."fixture_name"
        ,content_dim."fixture_start_timestamp"::DATE "fixture_start_date"
        ,content_dim."competition_id"
        ,content_dim."competition_name"
        ,content_dim."sport_id"
        ,content_dim."sport_name"
        ,content_dim."ruleset_name"
        ,content_dim."article_type"
        ,content_dim."article_quality"
        ,entitlements_dim."required_entitlement_set_ids"::STRING AS "required_entitlement_set_ids"
    FROM playback_fact
    -- Using INNER JOIN as there are some pesky ads appearing in conviva playback, and so this will exclude them
    INNER JOIN content_dim USING ("content_item__skey")
    LEFT JOIN entitlements_dim USING ("entitlements__skey")
    WHERE "playback_stream_date" BETWEEN DATEADD('day', -{{ subscription_mart_variables.rebuild_days }}-1, '{{ var('batch_date') }}') AND DATEADD('day', {{ subscription_mart_variables.rebuild_days }}, '{{ var('batch_date') }}')
)

-- Group the content of the first 7 days up to fixture_id level, allowing windowed sumation of playback at the competition level as well
,attr AS (
    SELECT
        acqs."viewer_id"
        ,acqs."billing_account_id"
        ,acqs."subscription_name"
        ,acqs."subscription_start_date"
        ,acqs."subscription_country"
        ,plays_prep."fixture_id"
        ,plays_prep."competition_id"
        ,plays_prep."sport_id"
        ,CASE WHEN acqs."subscription_country" = 'United States' AND plays_prep."sport_id" = '2x2oqzx60orpoeugkd754ga17' THEN TRUE ELSE FALSE END AS "is_us_sport_name_boxing"
        ,MIN(plays_prep."fixture_name") AS "fixture_name"
        ,MIN(plays_prep."fixture_start_date") "fixture_start_date"
        ,MIN(plays_prep."competition_name") AS "competition_name"
        ,MIN(plays_prep."sport_name") AS "sport_name"
        ,MIN(plays_prep."ruleset_name") AS "ruleset_name"
        ,COUNT_IF(plays_prep."required_entitlement_set_ids" ILIKE '%vs%') > 0 AS "has_ppv_required_entitlement"
        ,MIN(plays_prep."playback_stream_date") AS "first_stream_date"
        ,SUM(plays_prep."playback_duration_milliseconds") AS "total_playback_duration_milliseconds"
        ,SUM(CASE WHEN plays_prep."article_type" = 'Live' THEN plays_prep."playback_duration_milliseconds" ELSE 0 END) AS "live_playback_duration_milliseconds"
        ,SUM(CASE WHEN plays_prep."article_quality" = '5' THEN plays_prep."playback_duration_milliseconds" ELSE 0 END) AS "article_quality_5_playback_duration_milliseconds"
        ,SUM("total_playback_duration_milliseconds") OVER (PARTITION BY acqs."subscription_name", plays_prep."competition_id", plays_prep."sport_id") AS "competition_total_playback_duration_milliseconds"
    FROM acqs
    LEFT JOIN plays_prep
        ON acqs."viewer_id" = plays_prep."viewer_id"
        -- Joining on one day before just in case there are any sync issues
        AND plays_prep."playback_stream_date" BETWEEN DATEADD('day', -1, acqs."subscription_start_date") AND DATEADD('day', {{ subscription_mart_variables.rebuild_days }}, acqs."subscription_start_date")
    GROUP BY 1,2,3,4,5,6,7,8,9
)

SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,"viewer_id"
    ,"billing_account_id"
    ,"subscription_name"
    ,"subscription_start_date"
    ,"subscription_country"
    -- Need to differentiate NULLs from no attribution from real NULLs (that sometimes come through mainly comps and sports)
    -- but you can't put strings in all field types ,so some of those we keep as NULLs or 0s
    ,IFF("first_stream_date" IS NULL, '<no streams found>', "fixture_id") AS "fixture_id"
    ,IFF("first_stream_date" IS NULL, '<no streams found>', "fixture_name") AS "fixture_name"
    ,"fixture_start_date"
    ,IFF("first_stream_date" IS NULL, '<no streams found>', "competition_id") AS "competition_id"
    ,IFF("first_stream_date" IS NULL, '<no streams found>', "competition_name") AS "competition_name"
    ,IFF("first_stream_date" IS NULL, '<no streams found>', "sport_id") AS "sport_id"
    ,IFF("first_stream_date" IS NULL, '<no streams found>', "sport_name") AS "sport_name"
    ,IFF("first_stream_date" IS NULL, '<no streams found>', "ruleset_name") AS "ruleset_name"
    ,"has_ppv_required_entitlement"
    ,"first_stream_date"
    ,"total_playback_duration_milliseconds"
    ,"live_playback_duration_milliseconds"
    ,"article_quality_5_playback_duration_milliseconds"
    ,"competition_total_playback_duration_milliseconds"
    ,IFF(DATEDIFF('day', "subscription_start_date", CURRENT_DATE) >= {{ subscription_mart_variables.rebuild_days }}, TRUE, FALSE)  AS "is_final_content_attribution"
FROM attr
-- Choosing the highest ranked fixture for the Sub Name based on the order by priorities
QUALIFY ROW_NUMBER() OVER (PARTITION BY "subscription_name" ORDER BY
                                                                    -- Prioritising boxing highest just in US
                                                                    "is_us_sport_name_boxing" DESC
                                                                    -- Then looking at the top competition streamed (so including all Previews, highlights, catchups for that comp)
                                                                    ,"competition_total_playback_duration_milliseconds" DESC
                                                                    -- Then out of that top competition prioritising the fixtures that have been streamed live
                                                                    ,"live_playback_duration_milliseconds" DESC
                                                                    -- Then after live, prioritising Major Events (Article Quality = 5)
                                                                    ,"article_quality_5_playback_duration_milliseconds" DESC
                                                                    -- Then after ME, just prioritising the most watched fixture_id
                                                                    ,"total_playback_duration_milliseconds" DESC
                                                                    ) = 1
