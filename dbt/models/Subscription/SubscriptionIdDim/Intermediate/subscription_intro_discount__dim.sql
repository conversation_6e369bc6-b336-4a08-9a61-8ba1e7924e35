{{
    config(
        materialized='table',
        schema='PRESENTATION',
        tags=['presentation-subscription-domain']
    )
}}

WITH zuora_source AS (
    SELECT * FROM {{ ref('zr_subscription_intro_discount__dim') }}
)

,dmp_source AS (
    SELECT * FROM {{ ref('dmp_subscription_intro_discount__dim') }}
)

SELECT * FROM zuora_source
UNION ALL
SELECT
    "subscription_id"
    ,"subscription_name"
    ,"billing_account_id"
    ,"subscription_id_created_timestamp"
    ,"subscription_intro_discount_discount_percentage"
    ,"subscription_intro_discount_effective_from_date"
    ,"subscription_intro_discount_effective_until_date"
    ,"subscription_intro_discount_discount_duration_days"
    ,"subscription_intro_discount_discount_duration_months"
FROM dmp_source
