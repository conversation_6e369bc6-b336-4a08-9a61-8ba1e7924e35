{{
    config(
        materialized='table',
        unique_key='"subscription_id"',
        schema='TRANSIENT',
        tags=['presentation-subscription-domain']
    )
}}

WITH dmp_pause AS (
    SELECT * FROM {{ ref('staging__dmp_subscription_pause') }}
)

,sub_core AS (
    SELECT * FROM {{ ref('staging__dmp_subscription_core') }}
)

SELECT 
     dmp_pause."billing_product_id" AS "subscription_id"
    ,sub_core."subscription_name"
    ,sub_core."subscription_id_created_timestamp"
    ,sub_core."billing_account_id"
    --,"subscription_start_date"
    ,dmp_pause."pause_start_date"::DATE  AS "subscription_pause_start_date"
    ,dmp_pause."pause_end_date"::DATE  AS "subscription_pause_end_date"
    ,DATEDIFF('week', "subscription_pause_start_date", "subscription_pause_end_date") AS "pause_duration_weeks"
    ,dmp_pause."event_timestamp" AS "subscription_pause_request_timestamp"
    ,dmp_pause."validity_end_date"::DATE AS "subscription_validity_end_date"
    ,dmp_pause."next_billing_date"::DATE AS "subscription_rateplan_charge_charged_through_date"
FROM dmp_pause
INNER JOIN sub_core
ON dmp_pause."billing_product_id"=sub_core."subscription_id"
