{{
    config(
        materialized='table',
        transient=true,
        schema='TRANSIENT',
        tags=['presentation-subscription-domain']
    )
}}

WITH zuora_source AS (
    SELECT * FROM {{ ref('zr_subscription_id_add_on__dim') }}
)

,dmp_source AS (
    SELECT * FROM {{ ref('dmp_subscription_id_add_on__dim') }}
)

SELECT * FROM zuora_source
UNION ALL
SELECT 
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,"subscription_id_add_on__skey"
    ,"subscription_id"
    ,"subscription_name"
    ,"subscription_id_created_timestamp"
    ,"subscription_start_date"
    ,"subscription_end_date"
    ,"billing_account_id"
    ,"rateplan_id"
    ,"rateplan_name"
    ,"rateplan_created_timestamp"
    ,"entitlement_set_id"
    ,"rateplan_source_system_name"
    ,"rateplan_tracking_id"
    ,"rateplan_partner_id"
    ,"rateplan_partner_user_id"
    ,"rateplan_next_invoice_date"
    ,"subscription_add_on_type"
    ,"rateplan_charge_id"
    ,"rateplan_charge_name"
    ,"rateplan_charge_charge_type"
    ,"subscription_add_on_effective_start_date"
    ,"subscription_add_on_effective_end_date"
    ,"event_timestamp"
FROM dmp_source
