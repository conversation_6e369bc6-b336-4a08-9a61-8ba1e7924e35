version: 2

sources:
  - name: ENTITLEMENTS
    database: PLAYBACK_STREAM__B2C__MART__PROD
    quoting:
      identifier: true
    tables:
      - name: ENTITLEMENTS__DIM
        description: "Conformed dimension. Each record represents the Entitlements information for an article id, this includes the available, required and used entitlement set ids"
        columns:
          - name: META__DATA
            description: "Surrogate key which uniquely identifies a table row."
            quote: true

          - name: entitlements__skey
            description: "Surrogate key which uniquely identifies a table row."
            quote: true

          - name: article_id
            description: "Source identifier for the article that corresponds to the content item."
            quote: true

          - name: available_entitlement_set_ids
            description: "The available entitlement set ids for a particular viewer"
            quote: true

          - name: required_entitlement_set_ids
            description: "The required entitlement set ids to view this content"
            quote: true

          - name: used_entitlement_set_ids
            description: "The intersection of available and the required entitlement set ids"
            quote: true

          - name: entitlement_ids
            description: "The entitlement set ids for this content from the article entitlement scd"
            quote: true
