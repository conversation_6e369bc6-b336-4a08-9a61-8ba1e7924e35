version: 2

sources:
  - name: CONTENT
    database: PLAYBACK_STREAM__B2C__MART__PROD
    quoting:
      identifier: true
    tables:
      - name: CONTENT_ITEM__DIM
        description: "Conformed dimension. Each record represents the latest version of a content item that was made available for consumption / playback on the DAZN platform. There may be multiple records associated with a content item if one or more attributes have changed value over time, but only a single record may represent the current version of a content item."
        columns:
          - name: META__DATA
            description: "Variant column containing JSON structured meta data on the record, such as a timestamp of when the record was inserted"
            quote: true

          - name: content_item__skey
            description: "Surrogate key which uniquely identifies a content item."
            quote: true

          - name: record_valid_from_timestamp
            description: "Timestamp indicating when the record (representing a version of the content item) came into effect."
            quote: true

          - name: record_valid_to_timestamp
            description: "Timestamp indicating when the record (representing a version of the content item) expired. This value points to a date in the future if the record is current."
            quote: true

          - name: content_item_origin
            description: "The source of the content item record e.g. PCMS, backfill."
            quote: true

          - name: content_distinction
            description: "A more granular categorisation for the content type."
            quote: true

          - name: won_external_id
            description: "Identifier for the content item within the What's On external system."
            quote: true

          - name: advertised_timestamp
            description: "Advertised timestamp (local timezone) for the start of the content item, as shown on the Coming Up tile."
            quote: true

          - name: tile_planned_start_timestamp
            description: "The planned time for when the content item tile will become available on the platform."
            quote: true

          - name: tile_planned_end_timestamp
            description: "The planned time for when the content item tile will be taken down from the platform."
            quote: true

          - name: outlet
            description: "Name of the region to which the content is being delivered."
            quote: true

          - name: article_id
            description: "Source identifier for the article that corresponds to the content item."
            quote: true

          - name: article_type
            description: "Categorisation of the article based on the content format e.g. Short Highlights, Catch Up, Film."
            quote: true

          - name: article_type_id
            description: "Source identifier for the article type."
            quote: true

          - name: article_language_code
            description: "Code corresponding to the local language in which the article is made available."
            quote: true

          - name: article_link_translation_language_code
            description: "Code corresponding to the translation language of the linked article, if applicable."
            quote: true

          - name: commentary_language_code
            description: "Code of the primary language of the commentary available for the content item."
            quote: true

          - name: article_title_english
            description: "The title of the article in English."
            quote: true

          - name: article_title_local
            description: "The title of the article in the local language, corresponding to the 'article_langage_code' column value."
            quote: true

          - name: article_quality
            description: "Indicates the production and broadcast levels of article."
            quote: true

          - name: article_promotion
            description: "Specifies the type of promotion applied to the article, if any e.g. Editor's Picks, Standout."
            quote: true

          - name: fixture_id
            description: "Source identifier for the fixture associated with the content item."
            quote: true

          - name: fixture_name
            description: "The name or description of the fixture associated with the content item."
            quote: true

          - name: fixture_start_timestamp
            description: "Timestamp indicating the when the fixture began."
            quote: true

          - name: venue_short_name
            description: "Short name of the venue where the fixture took place."
            quote: true

          - name: venue_long_name
            description: "Long name of the venue where the fixture took place."
            quote: true

          - name: venue_country
            description: "Name of the country where the fixture venue is located."
            quote: true

          - name: competition_id
            description: "Source identifier for the competition associated with the fixture."
            quote: true

          - name: competition_name
            description: "Name of the competition associated with the fixture."
            quote: true

          - name: competition_country
            description: "Name of the country where the competition is located."
            quote: true

          - name: tournament_calendar_name
            description: "Name of tournament calendar associated with the competition, which specifies the season."
            quote: true

          - name: tournament_calendar_start_date
            description: "Date indicating when the tournament calendar begins."
            quote: true

          - name: tournament_calendar_end_date
            description: "Date indicating when the tournament calendar ends."
            quote: true

          - name: stage_name
            description: "Name of the stage associated with the fixture."
            quote: true

          - name: stage_start_date
            description: "Date indicating when the stage begins."
            quote: true

          - name: stage_end_date
            description: "Date indicating when the stage ends."
            quote: true

          - name: sport_id__hash
            description: "Hash of the sport_id column value."
            quote: true

          - name: sport_id
            description: "Source identifier for the sport corresponding to the fixture."
            quote: true

          - name: sport_name
            description: "Name of the sport corresponding to the fixture."
            quote: true

          - name: ruleset_name
            description: "Name of the ruleset corresponding to the sport associated with the fixture."
            quote: true

          - name: home_contestant_id
            description: "Source identifier for the home contestant participating in the fixture."
            quote: true

          - name: home_contestant_name
            description: "Name of the home contestant participating in the fixture."
            quote: true

          - name: home_contestant_country
            description: "Name of the home contestant's country."
            quote: true

          - name: away_contestant_id
            description: "Source identifier for the guest contestant participating in the fixture."
            quote: true

          - name: away_contestant_name
            description: "Name of the guest contestant participating in the fixture."
            quote: true

          - name: away_contestant_country
            description: "Name of the guest contestant's country."
            quote: true

          - name: livestream_id
            description: "Source identifier for the livestream via which the content was delivered, if applicable."
            quote: true

          - name: shoulder_content_or_live
            description: "Indicates whether the content item is made available as shoulder content or live."
            quote: true

          - name: is_embargoed
            description: "Indicates whether the content item can only be made available as preview or catch-up and not as Live."
            quote: true

          - name: is_age_restricted
            description: "Indicates whether an age restriction rating is associated with the content item."
            quote: true

          - name: is_exclusive
            description: "Indicates whether the content item is exclusive to a region."
            quote: true

          - name: is_allowed_dci
            description: "Indicates whether the content item may be subject ads insertion."
            quote: true

          - name: is_allowed_b2b
            description: "Indicates whether the content item may be offered as part of DAZN's B2B product."
            quote: true

          - name: is_allowed_download
            description: "Indicates whether the content item may be offered as downloadable."
            quote: true

          - name: is_allowed_free_to_view
            description: "Indicates whether the content item can be offered as free to view."
            quote: true

          - name: is_linear_channel
            description: "Indicates whether the content was delivered via linear channel."
            quote: true

          - name: is_short_highlights
            description: "Indicates whether the content item is available as short highlights."
            quote: true

          - name: has_tile_text
            description: "Indicates whether the article tile contains text."
            quote: true

          - name: has_special_event_rail
            description: "Indicates whether the article is featured on a special event rail."
            quote: true

          - name: transmission_status
            description: "The status of the content transmission e.g. Scheduled release."
            quote: true

          - name: voiceover_booth_key
            description: "Key corresponding to the voice-over booth resources required for the event."
            quote: true

          - name: gallery_resource_name
            description: "Name of the gallery resource required for the event, where applicable."
            quote: true

          - name: broadcast_tier
            description: "The broadcast tier set against the content item to indicate the level of production applied by DAZN."
            quote: true

          - name: support_tier
            description: "Value indicative of the priority assigned to the content item by technical support."
            quote: true

          - name: alternative_workflow
            description: "Indicates a special scenario that is associated with the content item."
            quote: true

          - name: advertising_asset_label
            description: "The label of the advertising asset that is played out during the content item."
            quote: true

          - name: rightsholder_name
            description: "Name of the content item rights holder."
            quote: true

          - name: contract_name
            description: "Name of the parent contract with a given rights holder."
            quote: true

          - name: contract_start_date
            description: "Global start date of the contract."
            quote: true

          - name: contract_end_date
            description: "Global end date of the contract."
            quote: true
