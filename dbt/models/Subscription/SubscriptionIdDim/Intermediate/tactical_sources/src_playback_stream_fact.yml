version: 2

sources:
  - name: FACT
    database: PLAYBACK_STREAM__B2C__MART__PROD
    quoting:
      identifier: true
    tables:
      - name: PLAYBACK_STREAM__FACT
        description: "Fact table. Each record corresponds to a playback stream and provides quantitative information as well as references to related dimensions."
        columns:
          - name: META__DATA
            description: "Variant column containing JSON structured meta data on the record, such as a timestamp of when the record was inserted"
            quote: true

          - name: playback_stream__skey
            description: "Surrogate key which uniquely identifies a table row."
            quote: true

          - name: playback_stream_details__skey
            description: "Foreign key, references primary (surrogate) key values of the Playback Stream Origin dimension."
            quote: true

          - name: playback_stream_location__skey
            description: "Foreign key, references primary (surrogate) key values of the Playback Stream Location dimension."
            quote: true

          - name: playback_stream_issue__skey
            description: "Foreign key, references primary (surrogate) key values of the Playback Stream Issue dimension."
            quote: true

          - name: device_info__skey
            description: "Foreign key, references primary (surrogate) key values of the Device Info dimension."
            quote: true

          - name: application__skey
            description: "Foreign key, references primary (surrogate) key values of the Application dimension."
            quote: true

          - name: content_item__skey
            description: "Foreign key, references primary (surrogate) key values of the Content Item dimension."
            quote: true

          - name: playback_stream_country__hash
            description: "Hashed value of the name of the country country corresponding to the IP address from which the stream originates."
            quote: true

          - name: sport_id__hash
            description: "Hashed value of the name of the sport that corresponds to the content item being delivered via the playback stream."
            quote: true

          - name: playback_stream_date
            description: "The date when the playback stream was initiated."
            quote: true

          - name: playback_start_timestamp
            description: "Timestamp indicating when the playback stream started."
            quote: true

          - name: playback_end_timestamp
            description: "Timestamp indicating when the playback stream ended."
            quote: true

          - name: playback_duration_milliseconds
            description: "The amount of time that content was played during a stream, measured in milliseconds. Excludes buffering time."
            quote: true

          - name: startup_duration_milliseconds
            description: "The amount of time taken for the video to display at startup, measured in milliseconds."
            quote: true

          - name: buffering_duration_milliseconds
            description: "The amount of time in a stream spent buffering not related to customer interaction e.g. resuming or seeking to a point in time, measured in milliseconds."
            quote: true

          - name: video_restart_duration_milliseconds
            description: "The amount of time spent restarting video playback during a stream, measured in milliseconds."
            quote: true

          - name: raw_connection_induced_rebuffering_duration_milliseconds
            description: "The amount of time spent in connection induced re-buffering that is not due to seek or video start, measured in milliseconds."
            quote: true

          - name: total_connection_induced_rebuffering_duration_milliseconds
            description: "Total amount of time in a stream spent in connection induced re-buffering, measured in milliseconds. Includes re-buffering time due to seek and video start."
            quote: true

          - name: total_streaming_duration_milliseconds
            description: "The total duration of the stream, including playback and buffering, measured in milliseconds."
            quote: true

          - name: average_bitrate
            description: "Video bit rate averaged over the total duration of the stream."
            quote: true

          - name: interrupts_count
            description: "The number of times playback was interrupted for re-buffering. This excludes instances when playback is paused and resumed by the customer, if no buffering occurs as a result."
            quote: true

          - name: rejoins_count
            description: "The number of times a customer rejoined the stream."
            quote: true

          - name: entitlement_set_id
            description: "Source identifier of the entitlement set associated with the content played during the stream."
            quote: true

          - name: dazn_device_id
            description: "Internal identifier for client devices used to stream content."
            quote: true

          - name: conviva_session_id
            description: "Source-defined identifier (Conviva) for the playback session associated with the stream."
            quote: true

          - name: user_agent
            description: "The user agent string - contains information about the software and hardware running on the device that is accessing the playback stream."
            quote: true

          - name: viewer_id
            description: "Viewer ID from conviva, this can be used to join on customer history scd"
            quote: true
