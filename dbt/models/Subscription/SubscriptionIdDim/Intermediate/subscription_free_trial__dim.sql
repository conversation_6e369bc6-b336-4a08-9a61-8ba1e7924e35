{{
    config(
        materialized='table',
        unique_key='"subscription_id"',
        schema='PRESENTATION'
    )
}}

-- Removing the tag to make sure the model is not getting updated.
-- tags=['presentation-subscription-domain']

WITH subscription_id_current AS (
    SELECT * FROM {{ ref('staging__zuora__subscription_id_current') }}
)

SELECT
    "subscription_id"
    ,"subscription_name"
    ,"billing_account_id"
    ,"subscription_free_trial_periods_type"
    ,"subscription_number_of_free_trial_periods"
    ,"subscription_name_original_created_timestamp"
    ,CASE
        -- Regardless of the PeriodType, if the Periods are 0, then there is no Free Trial start or end
        WHEN "subscription_number_of_free_trial_periods" = 0 OR "subscription_number_of_free_trial_periods" IS NULL THEN NULL
        -- If not, and there are Free Trial periods, then the start of the free trial will always be the Sub Name Original Created Date, ie the date it was first created in Zuora
        ELSE "subscription_name_original_created_timestamp"::DATE
    END AS "subscription_free_trial_start_date"
    ,CASE
        -- Regardless of the PeriodType, if the Periods are 0, then there is no Free Trial start or end
        WHEN "subscription_number_of_free_trial_periods" = 0 OR "subscription_number_of_free_trial_periods" IS NULL THEN NULL
        -- If the Periods Type is months or NULL (NULL as the field is a new field and before it was released we only had months FTs)
        -- Then we should add the multiple of months from the NumFreeTrialPeriod field to the Sub Orig Created Date, which we have proved matches to how the product invoices work
        WHEN LOWER("subscription_free_trial_periods_type") = 'months' OR "subscription_free_trial_periods_type" IS NULL THEN DATEADD('month', IFNULL("subscription_number_of_free_trial_periods", 0), "subscription_name_original_created_timestamp"::DATE)
        -- If it's an NFL days Free Trial that has a Subscription Start Date of 2023-08-01, then the Subscrition Start Date is not actually right
        -- So we need to add the number of free trial days to the Sub Orig Created Date
        WHEN "subscription_product_group" = 'NFL' AND LOWER("subscription_free_trial_periods_type") = 'days' AND "subscription_start_date" = '2023-08-01' THEN DATEADD('day', IFNULL("subscription_number_of_free_trial_periods", 0), "subscription_name_original_created_timestamp"::DATE)
        -- If the Period Type is days, then we should use the date of the Sub Start Date field
        WHEN LOWER("subscription_free_trial_periods_type") = 'days' THEN "subscription_start_date"
    END AS "subscription_free_trial_end_date"
    ,CASE
        -- Regardless of the PeriodType, if the Periods are 0, then there is no Free Trial start or end
        -- This is needed a separate condition to the next one as the FreeTrialPeriodsType field is still sometimes NULL for recently created Subs, but only when the Period Num is 0
        WHEN "subscription_number_of_free_trial_periods" = 0 OR "subscription_number_of_free_trial_periods" IS NULL THEN 0
        -- If the Period Type is months, or it is null and we can safely assume it's monthly as per some Discovery
        -- Then we should add the multiple of months from the NumFreeTrialPeriod field to the Sub Start Date, which we have proved matches to how the product invoices work
        WHEN LOWER("subscription_free_trial_periods_type") = 'months' OR "subscription_free_trial_periods_type" IS NULL THEN DATEDIFF('day', "subscription_name_original_created_timestamp", DATEADD('month', IFNULL("subscription_number_of_free_trial_periods", 0), "subscription_name_original_created_timestamp"))
        -- If the Period Type is days, then we should use the date of the Sub Start Date field
        WHEN LOWER("subscription_free_trial_periods_type") = 'days' THEN "subscription_number_of_free_trial_periods"
    END::INT AS "subscription_free_trial_length_days_advertised"
    ,CASE
        -- Regardless of the PeriodType, if the Periods are 0, then there is no Free Trial, so dates would be NULL and DATEDIFF below does not work
        WHEN "subscription_number_of_free_trial_periods" = 0 OR "subscription_number_of_free_trial_periods" IS NULL THEN 0
        -- If there is a FT, then simply difference between start and end dates gives the length
        ELSE DATEDIFF('day', "subscription_free_trial_start_date", "subscription_free_trial_end_date")
    END::INT AS "subscription_free_trial_length_days_actual"
    ,CASE
        -- Regardless of the PeriodType, if the Periods are 0, then there is no Free Trial start or end
        -- This is needed a separate condition to the next one as the FreeTrialPeriodsType field is still sometimes NULL for recently created Subs, but only when the Period Num is 0
        WHEN "subscription_number_of_free_trial_periods" = 0 OR "subscription_number_of_free_trial_periods" IS NULL THEN 0
        -- If the Period Type is months, or it is null and we can safely assume it's monthly as per some Discovery
        -- Then we should add the multiple of months from the NumFreeTrialPeriod field to the Sub Start Date, which we have proved matches to how the product invoices work
        WHEN LOWER("subscription_free_trial_periods_type") = 'months' OR "subscription_free_trial_periods_type" IS NULL THEN DATEDIFF('month', "subscription_name_original_created_timestamp", DATEADD('month', IFNULL("subscription_number_of_free_trial_periods", 0), "subscription_name_original_created_timestamp"))
        -- If the Period Type is days, then we should use the date of the Sub Start Date field
        WHEN LOWER("subscription_free_trial_periods_type") = 'days' THEN 0
    END::INT AS "subscription_free_trial_length_months_advertised"
    ,CASE
        -- Regardless of the PeriodType, if the Periods are 0, then there is no Free Trial, so dates would be NULL and DATEDIFF below does not work
        WHEN "subscription_number_of_free_trial_periods" = 0 OR "subscription_number_of_free_trial_periods" IS NULL THEN 0
        -- If there is a FT, then simply difference between start and end dates gives the length
        ELSE DATEDIFF('month', "subscription_free_trial_start_date", "subscription_free_trial_end_date")
    END::INT AS "subscription_free_trial_length_months_actual"
    ,CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
FROM subscription_id_current
-- Every subscription has a first Version, and we can therefore use this with the Curated ID Current table to make sure this table is unique
WHERE "subscription_version" = 1
