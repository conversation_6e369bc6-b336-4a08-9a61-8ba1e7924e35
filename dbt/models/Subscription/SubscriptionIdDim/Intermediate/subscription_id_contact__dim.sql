{{
    config(
        materialized='table',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_M_WH'
    )
}}

-- Removing the tag to make sure the model is not getting updated.
-- tags=['presentation-subscription-domain']

WITH subscription_source AS (
    SELECT * FROM {{ ref('staging__zuora__subscription_id_current') }}
)

,contact_staging AS (
    SELECT * FROM {{ ref('staging__zuora__contact') }}
)

,contact_scd AS (
    SELECT
        "contact_country"
        ,"contact_state"
        ,"contact_created_timestamp"
        ,"contact_updated_timestamp"
        ,"billing_account_id"
        ,ROW_NUMBER() OVER (PARTITION BY "billing_account_id" ORDER BY "contact_updated_timestamp") AS "billing_account_id__row_number"
        ,IFF("billing_account_id__row_number" = 1, "contact_created_timestamp", "contact_updated_timestamp") AS "record_valid_from_timestamp"
        ,LEAD("contact_updated_timestamp", 1, '9999-12-31') OVER (PARTITION BY "billing_account_id" ORDER BY "contact_updated_timestamp") AS "record_valid_until_timestamp"
    FROM contact_staging
)

SELECT
    subscription_source."subscription_id"
    ,subscription_source."billing_account_id"
    ,subscription_source."subscription_id_created_timestamp"
    ,subscription_source."subscription_id_updated_timestamp"
    ,COALESCE(contact_scd."contact_country",subscription_source."subscription_country") AS "contact_country"
    ,contact_scd."contact_state"
FROM subscription_source
LEFT JOIN contact_scd
    ON subscription_source."billing_account_id" = contact_scd."billing_account_id"
    AND contact_scd."record_valid_from_timestamp" <= subscription_source."subscription_id_updated_timestamp"
    AND contact_scd."record_valid_until_timestamp" > subscription_source."subscription_id_updated_timestamp"
