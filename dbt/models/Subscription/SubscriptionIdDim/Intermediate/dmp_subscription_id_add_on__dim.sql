{{
    config(
        materialized='table',
        transient=true,
        schema='TRANSIENT',
        tags=['presentation-subscription-domain']
    )
}}

WITH event_addon AS (
    SELECT * FROM {{ ref('staging__dmp_subscription_add_on') }}
)

,sub_core AS (
    SELECT * FROM {{ ref('staging__dmp_subscription_core') }}
)

SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,HASH(
        sub_core."subscription_id"
        ,event_addon."service_provider_user_id"
        ,event_addon."validity_start_date"
    ) AS "subscription_id_add_on__skey"
    ,sub_core."subscription_id"
    ,sub_core."subscription_name"
    ,sub_core."subscription_id_created_timestamp"
    ,sub_core."subscription_start_date"
    ,sub_core."subscription_end_date"
    ,sub_core."billing_account_id"  
    ,event_addon."billing_product_id" AS "rateplan_id"
    ,event_addon."catalog_product_id" AS "rateplan_name" 
    ,IFNULL(event_addon."purchase_date",event_addon."event_timestamp") AS "rateplan_created_timestamp"
    ,event_addon."entitlement_set_id" AS "entitlement_set_id"
    ,event_addon."source_system_derived" AS "rateplan_source_system_name"
    ,event_addon."tracking_id_derived" AS "rateplan_tracking_id"
    ,IFNULL(event_addon."service_provider",'Unknown') AS "rateplan_partner_id"
    ,event_addon."service_provider_user_id" AS "rateplan_partner_user_id"
    ,event_addon."next_billing_date" AS "rateplan_next_invoice_date"
    ,CASE 
        WHEN "rateplan_name" ILIKE '%CONCURRENCY%' 
            THEN 'concurrency' ELSE NULL 
        END AS "subscription_add_on_type" 
    ,event_addon."billing_product_id" AS "rateplan_charge_id"
    ,event_addon."catalog_product_id" AS "rateplan_charge_name"
    ,event_addon."billing_charge_type" AS "rateplan_charge_charge_type"
    ,event_addon."validity_start_date"::DATE AS "subscription_add_on_effective_start_date"
    ,event_addon."validity_end_date"::DATE AS "subscription_add_on_effective_end_date"
    ,event_addon."event_timestamp" AS "event_timestamp"
    ,CASE 
        WHEN event_addon."message_type"='BILLING_PRODUCT_CANCELATION' 
            THEN event_addon."event_timestamp"
            ELSE NULL 
    END AS "add_on_cancelled_date"
    ,CASE 
        WHEN event_addon."message_type"='BILLING_PRODUCT_CANCELATION' 
            THEN event_addon."cancel_reason"
            ELSE NULL 
    END AS "add_on_cancelled_reason"
FROM event_addon
INNER JOIN sub_core
ON event_addon."linked_billing_product_id"=sub_core."subscription_id"
--LEFT JOIN sub_cancelled ON sub_core."subscription_id"=sub_cancelled.LINKED_BILLING_PRODUCT_ID
WHERE "subscription_add_on_effective_start_date"<"subscription_add_on_effective_end_date"
