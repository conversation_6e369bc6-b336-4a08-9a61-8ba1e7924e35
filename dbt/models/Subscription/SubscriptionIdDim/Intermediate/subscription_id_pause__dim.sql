{{
    config(
        materialized='table',
        unique_key='"subscription_id"',
        schema='TRANSIENT',
        tags=['presentation-subscription-domain']
    )
}}

WITH zuora_source AS (
SELECT * FROM {{ ref('zr_subscription_id_pause__dim') }}
)

,dmp_source AS (
    SELECT * FROM {{ ref('dmp_subscription_id_pause__dim') }}
)

SELECT * FROM zuora_source
UNION ALL
SELECT 
    "subscription_id"
    ,"subscription_name"
    ,"subscription_id_created_timestamp"
    ,"billing_account_id"
    ,"subscription_pause_start_date"
    ,"subscription_pause_end_date"
    ,"pause_duration_weeks"
FROM dmp_source
