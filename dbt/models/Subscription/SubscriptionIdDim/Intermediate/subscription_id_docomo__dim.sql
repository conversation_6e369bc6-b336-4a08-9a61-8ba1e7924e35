{{
    config(
        materialized='table',
        schema='TRANSIENT',
        tags=['presentation-subscription-domain']
    )
}}

WITH sf_source AS (
    SELECT * FROM {{ ref('staging__salesforce__account') }}
    WHERE TRUE
    -- this condition is to handle the sf decommision cut-off: anything before 31st Mar 2024 has to be considered from SF
    AND "crm_last_modified_timestamp"::date <= '2024-03-31'
)

,identifies_event_source AS (
    SELECT * FROM {{ ref('staging__segment__user_events_identifies') }}
    WHERE TRUE
    -- this condition is to handle the sf decommision cut-off: anything after 31st Mar 2024 has to be considered from Segments
    AND "original_timestamp"::date > '2024-03-31'
)

,account_create AS (
    SELECT * FROM {{ ref('user_account_created')}}
)

,docomo_test_fact AS (
    SELECT * FROM {{ source('ANALYTICS', 'docomo_test_users_fact') }}
)

,sf_identifies_status_changes AS (
    SELECT
        "crm_account_id"
        -- IN very few circumstances the dazn_user_id is NULL, so we will make it the crm_account_id in those instances as that is often the dazn_user_id anyway in some legacy accounts
        ,IFNULL("dazn_user_id", "crm_account_id") AS "dazn_user_id"
        ,"crm_account_created_timestamp"
        ,"crm_last_modified_timestamp"
        ,REPLACE("crm_account_status",' ','') AS "crm_account_status"
        ,"crm_account_type"
        ,"is_test_user"
        ,"partner_id"
    FROM sf_source
    WHERE "crm_account_type" = 'Docomo'
    UNION ALL
    SELECT
        COALESCE(sf_source."crm_account_id", CONCAT('IAM-',identifies_event_source."dazn_user_id")) AS "crm_account_id"
        ,identifies_event_source."dazn_user_id" AS "dazn_user_id"
        ,account_create."user_account_created_timestamp" AS "crm_account_created_timestamp"
        ,identifies_event_source."original_timestamp" AS "crm_last_modified_timestamp"
        ,COALESCE(identifies_event_source."product_status_dazn", identifies_event_source."subscription_status") AS "crm_account_status"
        ,identifies_event_source."user_type" AS "crm_account_type"
        ,COALESCE(sf_source."is_test_user", {{ docomo_test_email_domain() }}) AS "is_test_user"
        ,COALESCE(sf_source."partner_id", identifies_event_source."partner_id") AS "partner_id"
    FROM identifies_event_source
    LEFT JOIN account_create USING ("dazn_user_id")
    LEFT JOIN sf_source USING ("dazn_user_id")
    WHERE identifies_event_source."user_type" = 'Docomo'
)

,status_changes AS (
    SELECT
        "crm_account_id"
        ,"dazn_user_id"
        ,"crm_account_created_timestamp"
        ,"crm_last_modified_timestamp"
        ,"crm_account_status"
        -- Transforming the crm_account_status field to match with Zuora Statuses and to be used for versioning
        ,CASE
            WHEN "crm_account_status" IN ('Blocked', 'Expired', 'ExpiredMarketing', 'Frozen', 'Partial') THEN 'Cancelled'
            WHEN "crm_account_status" IN ('ActiveGrace', 'ActivePaid') THEN 'Active'
            ELSE "crm_account_status"
        END AS "account_status"
        ,LAG("crm_account_status") OVER (PARTITION BY "crm_account_id" ORDER BY "crm_last_modified_timestamp") AS "lag_crm_account_status"
        ,"crm_account_type"
        ,"is_test_user"
        ,"partner_id"
    FROM sf_identifies_status_changes
    QUALIFY "crm_account_status" != LAG("crm_account_status", 1, 'Start') OVER (PARTITION BY "crm_account_id" ORDER BY "crm_last_modified_timestamp")
)

SELECT
    -- Subscription ID should be a unique ID to represent a copmletely new state of a subscription, we can do this by counting the rows of the status change for the account
    -- We append a D- prefix to this field so that it's obvious it comes from Docomo in the downstream data, and then also the crm_account_id to make it unique
    CONCAT('D-', "crm_account_id", ROW_NUMBER() OVER (PARTITION BY "crm_account_id" ORDER BY "crm_last_modified_timestamp")) AS "subscription_id"
    -- Very similar to the above field, but this will basically just be an cumulative count of the Active statuses for an account to get a unique ID for the trip/Subscription
    ,CONCAT('D-', "crm_account_id", ROW_NUMBER() OVER (PARTITION BY "crm_account_id", "account_status" ORDER BY "crm_last_modified_timestamp")) AS "subscription_name"
    ,"account_status" AS "subscription_status"
    -- We force only 2 versions, the initial active status as one, and then the move to cancelled as the other
    -- We don't get any other information about the subscription so there's no need for any other version changes
    ,CASE
        WHEN "account_status" = 'Active' THEN 1
        WHEN "account_status" = 'Cancelled' THEN 2
    END::INT AS "subscription_version"
    -- As we have already filtered for only status changes (that are either Active or Cancelled)
    -- then the last modified field is either the time it moved to Active (if it's currently Active), or the previous last modified field is (if it's Cancelled)
    ,CASE
        WHEN "account_status" = 'Cancelled' THEN LAG("crm_last_modified_timestamp") OVER (PARTITION BY "crm_account_id" ORDER BY "crm_last_modified_timestamp")
        ELSE "crm_last_modified_timestamp"
    END AS "subscription_name_original_created_timestamp"
    ,"crm_last_modified_timestamp" AS "subscription_id_created_timestamp"
    ,"crm_last_modified_timestamp" AS "subscription_id_updated_timestamp"
    ,"subscription_name_original_created_timestamp"::DATE AS "subscription_start_date"
    -- If it's Cancelled, then the end date is the last time it was updated to Cancelled, which is the last modified field
    -- If it's active, then the end date is indefinite, so 9999
    ,CASE
        WHEN "account_status" = 'Cancelled' THEN "crm_last_modified_timestamp"::DATE
        ELSE '9999-12-31'
    END AS "subscription_end_date"
    ,"crm_account_id" AS "billing_account_id"
    ,"crm_account_id"
    ,"dazn_user_id"
    -- Defining the country here so that it can be used in the Content Attribution model
    ,'Japan' AS "subscription_country"
    ,"crm_account_type" AS "subscription_source_system_name"
    -- The patch is defined in macros/Subscription/docomo_patch.sql
    -- The logic comes from advice from the Docomo/Japan team and is a way to cohort users based off exactly what they started their subscription
    ,{{ docomo_patch() }} AS "subscription_sign_up_campaign_id"
    ,"crm_account_created_timestamp"::DATE AS "billing_account_created_date"
    ,"is_test_user" IN ('true', 'True') OR docomo_test_fact."external_user_id" IS NOT NULL AS "billing_account_is_batch_50"
FROM status_changes
-- Joining on a table that contains a big list of only the test customers identified by Docomo that have not been correctly labelled in Salesforce
LEFT JOIN docomo_test_fact ON status_changes."partner_id" = docomo_test_fact."external_user_id"
WHERE
    CASE
        -- Filter out any first movements (lag NULL) that aren't to Active Paid, as they confuse the query
        WHEN "crm_account_status" NOT IN ('ActiveGrace', 'ActivePaid') AND "lag_crm_account_status" IS NULL THEN FALSE
        ELSE TRUE
    END
