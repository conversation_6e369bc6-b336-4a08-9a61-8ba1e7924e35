{{
    config(
        materialized='table',
        schema='TRANSIENT',
        tags=['presentation-subscription-domain']
    )
}}

WITH sub_id_docomo_dim AS (
    SELECT * FROM {{ ref('subscription_id_docomo__dim') }}
)

,sub_content_attribution AS (
    SELECT * FROM {{ ref('subscription_name_content_attribution__dim') }}
)

SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,sub_id_docomo_dim."subscription_id"
    ,sub_id_docomo_dim."subscription_name"
    ,sub_id_docomo_dim."subscription_status"
    ,sub_id_docomo_dim."subscription_status" = 'Cancelled' AS "is_soft_cancelled"
    ,sub_id_docomo_dim."subscription_version"
    ,sub_id_docomo_dim."subscription_name_original_created_timestamp"
    ,sub_id_docomo_dim."subscription_id_created_timestamp"
    ,sub_id_docomo_dim."subscription_id_updated_timestamp"
    ,sub_id_docomo_dim."subscription_start_date"
    ,sub_id_docomo_dim."subscription_end_date"
    ,LAST_DAY(sub_id_docomo_dim."subscription_start_date", 'week') AS "subscription_start_week"
    ,CASE
        WHEN sub_id_docomo_dim."subscription_end_date" = '9999-12-31' THEN '9999-12-31'
        ELSE LAST_DAY(sub_id_docomo_dim."subscription_end_date", 'week')
    END AS "subscription_end_week"
    ,sub_id_docomo_dim."billing_account_id"
    ,MIN("subscription_name_original_created_timestamp") OVER (PARTITION BY "crm_account_id")::DATE AS "min_billing_account_subscription_name_created_date"
    ,LAST_DAY("min_billing_account_subscription_name_created_date",'WEEK') AS "min_billing_account_subscription_name_created_week"
    ,ROW_NUMBER() OVER (PARTITION BY "crm_account_id", "subscription_status" ORDER BY "subscription_name_original_created_timestamp") AS "billing_account_trip_number"
    ,"billing_account_trip_number" AS "billing_account_product_group_trip_number"
    ,"billing_account_trip_number" > 1 AS "is_resubscription"
    ,NULL AS "subscription_direct_carrier_billing_carrier_name"
    ,"subscription_source_system_name"
    ,"subscription_source_system_name" AS "subscription_source_system_name_derived"
    ,"subscription_sign_up_campaign_id"
    ,NULL AS "subscription_sign_up_giftcode"
    ,NULL AS "subscription_tracking_id"
    ,'TERMED' AS "subscription_term_type"
    ,'DAZN' AS "subscription_product_group"
    ,NULL AS "subscription_payment_method_id"
    ,sub_id_docomo_dim."subscription_country"
    ,'Japan' AS "subscription_territory"
    ,'Month' AS "subscription_billing_period"
    ,'Monthly' AS "subscription_type"
    ,NULL AS "subscription_tier"
    ,NULL AS "subscription_monthly_recurring_revenue"
    ,NULL AS "previous_subscription_monthly_recurring_revenue"
    ,NULL AS "subscription_bill_cycle_day"
    ,NULL AS "subscription_rateplan_name"
    ,NULL AS "subscription_rateplan_charge_name"
    ,NULL AS "subscription_rateplan_charge_effective_start_date"
    ,NULL AS "subscription_rateplan_charge_effective_end_date"
    ,NULL AS "subscription_rateplan_charge_charged_through_date"
    ,NULL AS "subscription_auto_renew"
    ,NULL AS "subscription_instalment_period"
    ,FALSE AS "has_free_trial"
    ,NULL AS "subscription_free_trial_start_date"
    ,NULL AS "subscription_free_trial_end_date"
    ,0 AS "subscription_free_trial_length_days_advertised"
    ,0 AS "subscription_free_trial_length_days_actual"
    ,0 AS "subscription_free_trial_length_months_advertised"
    ,0 AS "subscription_free_trial_length_months_actual"
    ,0 AS "subscription_intro_discount_discount_percentage"
    ,NULL AS "subscription_intro_discount_effective_from_date"
    ,NULL AS "subscription_intro_discount_effective_until_date"
    ,NULL AS "subscription_intro_discount_discount_duration_days"
    ,NULL AS "subscription_intro_discount_discount_duration_months"
    ,FALSE AS "has_pause_period"
    ,NULL AS "subscription_pause_start_date"
    ,NULL AS "subscription_pause_end_date"
    ,NULL AS "subscription_pause_start_week"
    ,NULL AS "subscription_pause_end_week"
    ,NULL AS "pause_duration_weeks"
    ,NULL AS "first_post_sign_up_giftcode"
    ,NULL AS "last_post_sign_up_giftcode"
    ,NULL AS "first_post_sign_up_giftcode_campaign_name"
    ,NULL AS "last_post_sign_up_giftcode_campaign_name"
    ,NULL AS "first_post_sign_up_discount_context"
    ,NULL AS "last_post_sign_up_discount_context"
    ,NULL AS "is_introductory_discount"
    ,NULL AS "min_discount_percentage"
    ,NULL AS "max_discount_percentage"
    ,NULL AS "discount_percentage_distinct_count"
    ,NULL AS "post_sign_up_giftcode_distinct_count"
    ,NULL::ARRAY AS "post_sign_up_giftcode_list"
    ,NULL AS "post_sign_up_giftcode_campaign_name_distinct_count"
    ,NULL::ARRAY AS "post_sign_up_giftcode_campaign_name_list"
    ,NULL AS "discount_effective_start_date"
    ,NULL AS "discount_effective_start_week"
    ,NULL AS "discount_effective_end_date"
    ,NULL AS "discount_effective_end_week"
    ,NULL AS "discount_duration_days"
    ,NULL AS "discount_duration_weeks"
    ,NULL::ARRAY AS "discount_data"
    ,0 AS "subscription_add_on_count"
    ,FALSE AS "has_subscription_add_on"
    ,ARRAY_CONSTRUCT() AS "subscription_add_on_partner_ids"
    ,NULL AS "subscription_add_on_effective_start_date"
    ,NULL AS "subscription_add_on_effective_end_date"
    ,NULL AS "subscription_add_on_effective_start_week"
    ,NULL AS "subscription_add_on_effective_end_week"
    ,ARRAY_CONSTRUCT() AS "subscription_add_on_types"
    ,sub_content_attribution."fixture_id" AS "subscription_attributed_fixture_id"
    ,sub_content_attribution."fixture_name" AS "subscription_attributed_fixture_name"
    ,sub_content_attribution."fixture_start_date" AS "subscription_attributed_fixture_start_date"
    ,sub_content_attribution."competition_id" AS "subscription_attributed_competition_id"
    ,sub_content_attribution."competition_name" AS "subscription_attributed_competition_name"
    ,sub_content_attribution."sport_id" AS "subscription_attributed_sport_id"
    ,sub_content_attribution."sport_name" AS "subscription_attributed_sport_name"
    ,sub_content_attribution."ruleset_name" AS "subscription_attributed_ruleset_name"
    ,sub_content_attribution."has_ppv_required_entitlement" AS "subscription_attribution_has_ppv_required_entitlement"
    ,sub_content_attribution."is_final_content_attribution"
    ,"crm_account_id"
    ,"dazn_user_id"
    ,'JPY' AS "billing_account_currency_code"
    ,"billing_account_created_date"
    ,FALSE AS "billing_account_has_advanced_payment_manager"
    ,"billing_account_is_batch_50"
    ,"min_billing_account_subscription_name_created_date" AS "user_account_created_date"
    ,"min_billing_account_subscription_name_created_week" AS "user_account_created_week"
    ,HASH(sub_id_docomo_dim."subscription_country"
        ,"subscription_territory"
        ,"billing_account_currency_code"
        ,"billing_account_is_batch_50"
        ,"min_billing_account_subscription_name_created_week"
        ,"billing_account_has_advanced_payment_manager"
        ,"user_account_created_week")
        AS "billing_account__skey"
    ,HASH("is_soft_cancelled"
        ,"subscription_tier"
        ,"billing_account_trip_number"
        ,"is_resubscription"
        ,"subscription_product_group")
        AS "subscription_info__skey"
    ,HASH("subscription_start_week"
        ,"subscription_end_week")
        AS "subscription_term__skey"
    ,HASH("has_free_trial"
        ,"subscription_free_trial_length_days_advertised"
        ,"subscription_free_trial_length_months_advertised"
        ,"subscription_free_trial_end_date")
        AS "subscription_free_trial__skey"
    ,HASH("has_pause_period"
        ,"subscription_pause_start_week"
        ,"subscription_pause_end_week"
        ,"pause_duration_weeks")
        AS "subscription_pause__skey"
    ,HASH("has_subscription_add_on"
        ,"subscription_add_on_count"
        ,"subscription_add_on_effective_start_week"
        ,"subscription_add_on_effective_end_week"
        ,"subscription_add_on_types")
        AS "subscription_add_on__skey"
    ,HASH("subscription_attributed_fixture_id"
        ,"subscription_attributed_fixture_name"
        ,"subscription_attributed_fixture_start_date"
        ,"subscription_attributed_competition_id"
        ,"subscription_attributed_competition_name"
        ,"subscription_attributed_sport_id"
        ,"subscription_attributed_sport_name"
        ,"subscription_attributed_ruleset_name"
        ,"subscription_attribution_has_ppv_required_entitlement"
        ,"is_final_content_attribution")
        AS "subscription_content_attribution__skey"
    ,"subscription_source_system_name_derived" AS "subscription_source_system_name_derived__skey"
    ,"subscription_tracking_id" AS "subscription_tracking_id__skey"
    ,"subscription_sign_up_campaign_id" AS "subscription_sign_up_campaign_id__skey"
    ,"first_post_sign_up_giftcode_campaign_name" AS "first_post_sign_up_giftcode_campaign_name__skey"
    ,"last_post_sign_up_giftcode_campaign_name" AS "last_post_sign_up_giftcode_campaign_name__skey"
FROM sub_id_docomo_dim
LEFT JOIN sub_content_attribution 
ON sub_id_docomo_dim."subscription_name"=sub_content_attribution."subscription_name"
AND sub_id_docomo_dim."subscription_start_date"=sub_content_attribution."subscription_start_date"
