{{
    config(
        materialized='incremental',
        incremental_strategy='merge',
        unique_key='"subscription_id"',
        schema='PRESENTATION'
    )
}}

-- Removing the tag to make sure the model is not getting updated.
-- tags=['presentation-subscription-domain']

WITH subscription AS (
    SELECT * FROM {{ ref('staging__zuora__subscription_id_current') }}
)

,rateplan AS (
    SELECT * FROM {{ ref('staging__zuora__rateplan_current') }}
)

,rateplancharge AS (
    SELECT * FROM {{ ref('staging__zuora__rateplancharge_current') }}
)

,rateplanchargetier AS (
    SELECT * FROM {{ ref('staging__zuora__rateplanchargetier') }}
)

,final AS (
    SELECT
        subscription."subscription_id"
        ,subscription."subscription_name"
        ,subscription."billing_account_id"
        ,subscription."subscription_id_created_timestamp"
        ,rateplanchargetier."rateplan_charge_tier_discount_percentage" AS "subscription_intro_discount_discount_percentage"
        ,rateplancharge."rateplan_charge_effective_start_date" AS "subscription_intro_discount_effective_from_date"
        ,rateplancharge."rateplan_charge_effective_end_date" AS "subscription_intro_discount_effective_until_date"
        ,DATEDIFF('days', "subscription_intro_discount_effective_from_date", "subscription_intro_discount_effective_until_date") AS "subscription_intro_discount_discount_duration_days"
        ,ROUND(MONTHS_BETWEEN("subscription_intro_discount_effective_until_date", "subscription_intro_discount_effective_from_date")) AS "subscription_intro_discount_discount_duration_months"
    FROM subscription
    LEFT JOIN rateplan ON rateplan."subscription_id" = subscription."subscription_id"
    LEFT JOIN rateplancharge ON rateplan."rateplan_id" = rateplancharge."rateplan_id"
    LEFT JOIN rateplanchargetier ON rateplancharge."rateplan_charge_id" = rateplanchargetier."rateplan_charge_id"
    WHERE
        {% if is_incremental() %}
            ({{ build_mode_trigger(var('build_mode') , 'subscription."subscription_id_created_timestamp"' , '"subscription_id_created_timestamp"') }})
            AND
        {% endif %}
        rateplancharge."rateplan_charge_charge_model" = 'Discount-Percentage'
        AND
        subscription."subscription_version" = 1
    GROUP BY 1,2,3,4,5,6,7
    -- Deduplicate rows which have multiple introduction discounts applied to them to get the shorter of the discounts
    -- The only instances that occur of this is 24 month service credits applied to a <0.1% of subs
    QUALIFY ROW_NUMBER() OVER (PARTITION BY subscription."subscription_id" ORDER BY "subscription_intro_discount_discount_duration_days" ASC) = 1
)

SELECT * FROM final
