{{
    config(
        materialized='incremental',
        incremental_strategy='merge',
        unique_key='"subscription_id"',
        schema='PRESENTATION',
        tags=['presentation-subscription-domain']
    )
}}

WITH event_discount AS (
    SELECT * FROM {{ ref('staging__dmp_subscription_promotion') }}
    WHERE "event_status"='SUCCEEDED' and "is_active" and "message_type"='BILLING_PRODUCT_PURCHASE'
)

,subscription AS (
    SELECT * FROM {{ ref('staging__dmp_subscription_core') }}
)

,final AS (
    SELECT
        subscription."subscription_id"
        ,subscription."subscription_name"
        ,subscription."billing_account_id"
        ,subscription."subscription_id_created_timestamp"
        ,event_discount."promotion_percentage" AS "subscription_intro_discount_discount_percentage"
        ,CASE WHEN event_discount."type"='PERCENTAGE_PARTNER' THEN subscription."subscription_start_date" ELSE event_discount."start_date" END AS "subscription_intro_discount_effective_from_date"
        ,CASE WHEN event_discount."type"='PERCENTAGE_PARTNER' THEN subscription."subscription_end_date" ELSE event_discount."end_date" END AS "subscription_intro_discount_effective_until_date"
        --,PROMO_PAYLOAD
        ,DATEDIFF('days', "subscription_intro_discount_effective_from_date", "subscription_intro_discount_effective_until_date") AS "subscription_intro_discount_discount_duration_days"
        ,ROUND(MONTHS_BETWEEN("subscription_intro_discount_effective_until_date", "subscription_intro_discount_effective_from_date")) AS "subscription_intro_discount_discount_duration_months" -- NEED TO CROSS CHECK WTIH FREE TRAIL ADIVISERY MONTHS FIELD.
        ,event_discount."coupon_campaign" AS "subscription_sign_up_campaign_id"
        ,event_discount."coupon_code" AS "subscription_sign_up_giftcode"
    FROM subscription 
    LEFT JOIN event_discount
        ON  event_discount."billing_product_id"=subscription."subscription_id" 
    WHERE  
     {% if is_incremental() %}
            ({{ build_mode_trigger(var('build_mode') , 'subscription."subscription_id_created_timestamp"' , '"subscription_id_created_timestamp"') }})
            AND
        {% endif %}
        subscription."subscription_version" = 1   
    GROUP BY 1,2,3,4,5,6,7,10,11
    -- Deduplicate rows which have multiple introduction discounts applied to them to get the shorter of the discounts
    -- The only instances that occur of this is 24 month service credits applied to a <0.1% of subs
    QUALIFY ROW_NUMBER() OVER (PARTITION BY subscription."subscription_id" ORDER BY "subscription_intro_discount_discount_duration_days" ASC) = 1
)

SELECT * FROM final
WHERE "subscription_sign_up_campaign_id" IS NOT NULL OR "subscription_intro_discount_discount_percentage" IS NOT NULL
