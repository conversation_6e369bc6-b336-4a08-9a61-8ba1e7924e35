{{
    config(
        materialized='table',
        schema='TRANSIENT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_2XL_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH zuora_source AS (
    SELECT * FROM {{ ref('zr_subscription_id_discount__dim') }}
)

,dmp_source AS (
    SELECT * FROM {{ ref('dmp_subscription_id_discount__dim') }}
)

SELECT * FROM zuora_source
UNION ALL
SELECT * FROM dmp_source
