{{
    config(
        materialized='table',
        schema='TRANSIENT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_2XL_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH subscription_id_current AS (
    SELECT * FROM {{ ref('staging__zuora__subscription_id_current') }}
)

,rateplan_staging AS (
    SELECT * FROM {{ ref('staging__zuora__rateplan_current') }}
)

,rateplancharge_staging AS (
    SELECT * FROM {{ ref('staging__zuora__rateplancharge_current') }}
)

,rateplanchargetier_current AS (
    SELECT * FROM {{ ref('staging__zuora__rateplanchargetier_current') }}
)

,dmp_promo AS (
    SELECT * FROM {{ ref('staging__dmp_subscription_promotion') }}
    WHERE "legacy_promotion_id" IS NOT NULL
)

-- calculating some window functions over the whole of the rate plan charges (that we can't group by)
,windows AS (
    SELECT
        subscription_id_current."subscription_id"
        ,subscription_id_current."subscription_name"
        ,subscription_id_current."subscription_name_original_created_timestamp"
        ,subscription_id_current."subscription_id_created_timestamp"
        ,subscription_id_current."billing_account_id"
        ,rateplancharge_staging."rateplan_charge_post_sign_up_giftcode"
        ,FIRST_VALUE(rateplancharge_staging."rateplan_charge_post_sign_up_giftcode" IGNORE NULLS) OVER (PARTITION BY subscription_id_current."subscription_id" ORDER BY rateplancharge_staging."rateplan_charge_effective_start_date") AS "first_rateplan_charge_post_sign_up_giftcode"
        ,LAST_VALUE(rateplancharge_staging."rateplan_charge_post_sign_up_giftcode" IGNORE NULLS) OVER (PARTITION BY subscription_id_current."subscription_id" ORDER BY rateplancharge_staging."rateplan_charge_effective_start_date") AS "last_rateplan_charge_post_sign_up_giftcode"
        ,rateplancharge_staging."rateplan_charge_post_sign_up_giftcode_campaign_name"
        ,IFNULL(CASE WHEN "type" IN ('PERCENTAGE_OFF','PERCENTAGE') THEN dmp_promo."promotion_percentage" ELSE NULL END,rateplanchargetier_current."rateplan_charge_tier_discount_percentage") AS "rateplan_charge_tier_discount_percentage_derv"
        ,FIRST_VALUE(rateplancharge_staging."rateplan_charge_post_sign_up_giftcode_campaign_name" IGNORE NULLS) OVER (PARTITION BY subscription_id_current."subscription_id" ORDER BY rateplancharge_staging."rateplan_charge_effective_start_date") AS "first_rateplan_charge_post_sign_up_giftcode_campaign_name"
        ,LAST_VALUE(rateplancharge_staging."rateplan_charge_post_sign_up_giftcode_campaign_name" IGNORE NULLS) OVER (PARTITION BY subscription_id_current."subscription_id" ORDER BY rateplancharge_staging."rateplan_charge_effective_start_date") AS "last_rateplan_charge_post_sign_up_giftcode_campaign_name"
        ,rateplancharge_staging."rateplan_charge_effective_start_date"
        ,rateplancharge_staging."rateplan_charge_effective_end_date"
        ,rateplan_staging."rateplan_context"
        ,FIRST_VALUE(rateplan_staging."rateplan_context" IGNORE NULLS) OVER (PARTITION BY subscription_id_current."subscription_id" ORDER BY rateplancharge_staging."rateplan_charge_effective_start_date") AS "first_rateplan_charge_post_sign_up_context"
        ,LAST_VALUE(rateplan_staging."rateplan_context" IGNORE NULLS) OVER (PARTITION BY subscription_id_current."subscription_id" ORDER BY rateplancharge_staging."rateplan_charge_effective_start_date") AS "last_rateplan_charge_post_sign_up_context"
        ,subscription_id_current."subscription_sign_up_campaign_id"
        ,IFF(subscription_id_current."subscription_name_original_created_timestamp"::DATE = rateplancharge_staging."rateplan_charge_effective_start_date", TRUE, FALSE) AS "is_introductory_discount"
        ,OBJECT_CONSTRUCT(
            'is_introductory_discount', "is_introductory_discount"
            ,'subscription_sign_up_campaign_id', subscription_id_current."subscription_sign_up_campaign_id"
            ,'rateplan_charge_post_sign_up_giftcode', rateplancharge_staging."rateplan_charge_post_sign_up_giftcode"
            ,'rateplan_charge_post_sign_up_giftcode_campaign_name', rateplancharge_staging."rateplan_charge_post_sign_up_giftcode_campaign_name"
            ,'rateplan_charge_tier_discount_percentage', "rateplan_charge_tier_discount_percentage_derv"
            ,'rateplan_charge_effective_start_date', rateplancharge_staging."rateplan_charge_effective_start_date"
            ,'rateplan_charge_effective_end_date', rateplancharge_staging."rateplan_charge_effective_end_date"
            ,'rateplan_context', rateplan_staging."rateplan_context"
        ) AS "rateplan_charge_post_sign_up_object"
    FROM subscription_id_current
    LEFT JOIN rateplan_staging ON rateplan_staging."subscription_id" = subscription_id_current."subscription_id"
    LEFT JOIN rateplancharge_staging ON rateplancharge_staging."rateplan_id" = rateplan_staging."rateplan_id"
    LEFT JOIN rateplanchargetier_current ON rateplancharge_staging."rateplan_charge_id" = rateplanchargetier_current."rateplan_charge_id"

    LEFT JOIN dmp_promo ON rateplancharge_staging."rateplan_id" = dmp_promo."legacy_promotion_id" -- this need to be reverified
    WHERE
        -- post-sign-up giftcodes only come through in Discount RPCs
        rateplancharge_staging."rateplan_charge_charge_model" = 'Discount-Percentage'
        AND
        -- we only need to see RPCs that have post-sign-up discounts
        --rateplanchargetier_current."rateplan_charge_tier_discount_percentage" IS NOT NULL
        "rateplan_charge_tier_discount_percentage_derv" IS NOT NULL
        AND
        -- we only need to see RPCs that have post-sign-up giftcodes that are relevant to this SubId so are effective on or after the SubId was created
        rateplancharge_staging."rateplan_charge_effective_end_date" >= subscription_id_current."subscription_id_created_timestamp"::DATE
)

-- grouping the SubId to then add some additional summary data points and aggregations and ability to collect into ARRAYs
SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,"subscription_id"
    ,"subscription_name"
    ,"subscription_name_original_created_timestamp"
    ,"subscription_id_created_timestamp"
    ,"billing_account_id"
    ,"first_rateplan_charge_post_sign_up_giftcode"
    ,"last_rateplan_charge_post_sign_up_giftcode"
    ,"first_rateplan_charge_post_sign_up_giftcode_campaign_name"
    ,"last_rateplan_charge_post_sign_up_giftcode_campaign_name"
    ,"first_rateplan_charge_post_sign_up_context"
    ,"last_rateplan_charge_post_sign_up_context"
    ,"subscription_sign_up_campaign_id"
    ,MAX("is_introductory_discount") AS "is_introductory_discount"
    ,MIN("rateplan_charge_tier_discount_percentage_derv") as "rateplan_charge_tier_min_discount_percentage"
    ,MAX("rateplan_charge_tier_discount_percentage_derv") as "rateplan_charge_tier_max_discount_percentage"
    ,COUNT(DISTINCT "rateplan_charge_tier_discount_percentage_derv") AS "rateplan_charge_tier_discount_distinct_count"
    ,COUNT(DISTINCT "rateplan_charge_post_sign_up_giftcode") AS "rateplan_charge_post_sign_up_giftcode_distinct_count"
    ,ARRAY_AGG(DISTINCT "rateplan_charge_post_sign_up_giftcode") AS "rateplan_charge_post_sign_up_giftcode_list"
    ,COUNT(DISTINCT "rateplan_charge_post_sign_up_giftcode_campaign_name") AS "rateplan_charge_post_sign_up_giftcode_campaign_name_distinct_count"
    ,ARRAY_AGG(DISTINCT "rateplan_charge_post_sign_up_giftcode_campaign_name") AS "rateplan_charge_post_sign_up_giftcode_campaign_name_list"
    ,MIN("rateplan_charge_effective_start_date") AS "rateplan_charge_effective_start_date"
    ,MAX("rateplan_charge_effective_end_date") AS "rateplan_charge_effective_end_date"
    ,DATEDIFF(DAY,MIN("rateplan_charge_effective_start_date"),MAX("rateplan_charge_effective_end_date")) AS "discount_duration_days"
    ,DATEDIFF(WEEK,MIN("rateplan_charge_effective_start_date"),MAX("rateplan_charge_effective_end_date")) AS "discount_duration_weeks"
    ,ARRAY_AGG("rateplan_charge_post_sign_up_object") WITHIN GROUP (ORDER BY "rateplan_charge_effective_start_date", "rateplan_charge_effective_end_date") AS "discount_data"
FROM windows
GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13
