{{
    config(
        materialized='table',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_2XL_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH subscription_staging AS (
    SELECT
        *
        -- creating these here so there isn't any ambiguous fields
        ,"subscription_name_original_created_timestamp"::DATE AS "subscription_start_date_corrected"
        ,GREATEST("subscription_end_date", "subscription_id_created_timestamp")::DATE AS "subscription_end_date_corrected"
    FROM {{ ref('staging__zuora__subscription_id_current') }}
)

,region_dimension AS (
    SELECT * FROM {{ ref('region_dimension') }}
)

,account_current AS (
    SELECT * FROM {{ ref('staging__zuora__account_current') }}
)

,subscription_requested AS (
    SELECT * FROM {{ ref('subscription_id_requested__dim') }}
)

,subscription_intro_discount AS (
    SELECT * FROM {{ ref('zr_subscription_intro_discount__dim') }}
)

,subscription_free_trial AS (
    SELECT * FROM {{ ref('subscription_free_trial__dim') }}
)

,subscription_pause AS (
    SELECT * FROM {{ ref('zr_subscription_id_pause__dim') }}
)

,subscription_discount AS (
    SELECT * FROM {{ ref('zr_subscription_id_discount__dim') }}
)

,subscription_addon_count AS (
    SELECT
        "subscription_id"
        ,COUNT(DISTINCT "rateplan_partner_id") AS "subscription_add_on_count"
        ,MIN("subscription_add_on_effective_start_date") AS "min_subscription_add_on_effective_start_date"
        ,MAX("subscription_add_on_effective_end_date") AS "max_subscription_add_on_effective_end_date"
        ,ARRAY_AGG(DISTINCT "rateplan_partner_id") WITHIN GROUP (ORDER BY "rateplan_partner_id") AS "subscription_add_on_partner_ids"
        ,ARRAY_AGG(DISTINCT "subscription_add_on_type") WITHIN GROUP (ORDER BY "subscription_add_on_type") AS "subscription_add_on_types"
    FROM {{ ref('zr_subscription_id_add_on__dim') }}
    GROUP BY 1
)

,subscription_contact AS (
    SELECT * FROM {{ ref('subscription_id_contact__dim') }}
)

,subscription_content_attribution AS (
    SELECT * FROM {{ ref('subscription_name_content_attribution__dim') }}
)

,billing_account_id_test_user AS (
    SELECT * FROM {{ ref('billing_account_id_test__dim') }}
)

,docomo_dim AS (
    SELECT * FROM {{ ref('subscription_id_docomo_intermediate') }}
)

,user_account_created AS (
    SELECT * FROM {{ ref('user_account_created') }}
)

--EV CODE
,zr_name_current AS (
    SELECT * FROM {{ ref('staging__zuora__subscription_name_current') }}

)

,dmp_renwel AS (
    SELECT dmp_renwel.*,zr_name_current."subscription_id" FROM {{ ref('staging__dmp_subscription_renewal') }} dmp_renwel
    LEFT JOIN zr_name_current ON dmp_renwel."legacy_subscription_name" = zr_name_current."subscription_name"
    WHERE "legacy_subscription_name" IS NOT NULL
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "legacy_subscription_name" ORDER BY "event_timestamp" DESC) = 1
) 

,dmp_cancel AS (
    SELECT dmp_cancel.*,zr_name_current."subscription_id" FROM {{ ref('staging__dmp_subscription_cancel') }} dmp_cancel
    LEFT JOIN zr_name_current ON dmp_cancel."legacy_subscription_name" = zr_name_current."subscription_name"
    WHERE "legacy_subscription_name" IS NOT NULL 
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "legacy_subscription_name" ORDER BY "event_timestamp" DESC) = 1
) 

,dmp_mrr AS (
    select * from {{ ref('staging__dmp_subscription_mrr') }}
)

,zr_sub_id_dim AS (
    SELECT
        CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
        ,subscription_staging."subscription_id"
        ,subscription_staging."subscription_name"
        ,COALESCE(CASE 
            WHEN dmp_cancel."churn_type" IN ('VOLUNTARY','INVOLUNTARY')  THEN 'Cancelled'
            WHEN dmp_cancel."churn_type" IN ('SERVICE_CHANGE','RECONTRACT') THEN 'Expired'
            ELSE INITCAP(dmp_cancel."product_status") END,subscription_staging."subscription_status") AS "subscription_status"
        ,CASE 
            WHEN dmp_cancel."churn_type" IN ('VOLUNTARY','INVOLUNTARY') 
                    OR subscription_staging."subscription_status" = 'Cancelled' 
                THEN TRUE 
                ELSE FALSE 
         END AS "is_soft_cancelled"
        ,subscription_staging."subscription_version"
        ,subscription_staging."subscription_name_original_created_timestamp"
        ,subscription_staging."subscription_id_created_timestamp"
        ,IFNULL(CASE WHEN dmp_cancel."message_type"='BILLING_PRODUCT_KEEP' THEN NULL 
                     WHEN dmp_cancel."churn_type" IN ('SERVICE_CHANGE','RECONTRACT') THEN DATEADD('day', -1, dmp_cancel."event_timestamp")  ELSE dmp_cancel."event_timestamp" END,GREATEST_IGNORE_NULLS(dmp_renwel."event_timestamp",subscription_staging."subscription_id_updated_timestamp")) AS "subscription_id_updated_timestamp"
        ,subscription_staging."subscription_start_date_corrected" AS "subscription_start_date"
        --EV CODE
        ,IFNULL(CASE WHEN dmp_cancel."message_type"='BILLING_PRODUCT_KEEP' THEN NULL ELSE dmp_cancel."effective_cancellation_date"::DATE
     END
            ,GREATEST_IGNORE_NULLS( dmp_renwel."validity_end_date"::DATE,subscription_staging."subscription_end_date_corrected")) AS "subscription_end_date"
        ,LAST_DAY(subscription_staging."subscription_start_date_corrected", 'week') AS "subscription_start_week"
        ,CASE
            WHEN (IFNULL(CASE WHEN dmp_cancel."message_type"='BILLING_PRODUCT_KEEP' THEN NULL ELSE dmp_cancel."effective_cancellation_date"::DATE
     END
            ,GREATEST_IGNORE_NULLS( dmp_renwel."validity_end_date"::DATE,subscription_staging."subscription_end_date_corrected"))) >= '9999-01-01' THEN '9999-12-31'
            ELSE LAST_DAY((IFNULL(CASE WHEN dmp_cancel."message_type"='BILLING_PRODUCT_KEEP' THEN NULL ELSE dmp_cancel."effective_cancellation_date"::DATE
     END
            ,GREATEST_IGNORE_NULLS( dmp_renwel."validity_end_date"::DATE,subscription_staging."subscription_end_date_corrected"))), 'week')
         END AS "subscription_end_week"
        ,subscription_staging."billing_account_id"
        ,MIN(subscription_staging."subscription_name_original_created_timestamp") OVER (PARTITION BY subscription_staging."billing_account_id", subscription_staging."subscription_product_group")::DATE AS "min_billing_account_subscription_name_created_date"
        ,LAST_DAY("min_billing_account_subscription_name_created_date",'WEEK') AS "min_billing_account_subscription_name_created_week"
        ,DENSE_RANK() OVER (PARTITION BY subscription_staging."billing_account_id" ORDER BY subscription_staging."subscription_name_original_created_timestamp" ASC) AS "billing_account_trip_number"
        ,DENSE_RANK() OVER (PARTITION BY subscription_staging."billing_account_id", subscription_staging."subscription_product_group" ORDER BY subscription_staging."subscription_name_original_created_timestamp" ASC) AS "billing_account_product_group_trip_number"
        ,"billing_account_product_group_trip_number" > 1 AS "is_resubscription"
        ,subscription_staging."subscription_direct_carrier_billing_carrier_name"
        ,subscription_staging."subscription_source_system_name"
        ,COALESCE(CONCAT(subscription_staging."subscription_direct_carrier_billing_carrier_name",' DCB'), subscription_staging."subscription_source_system_name", 'Direct') AS "subscription_source_system_name_derived"
        ,subscription_staging."subscription_sign_up_campaign_id"
        ,subscription_staging."subscription_sign_up_giftcode"
        ,subscription_staging."subscription_tracking_id"
        ,subscription_staging."subscription_term_type"
        ,subscription_staging."subscription_product_group"
        ,subscription_staging."subscription_payment_method_id"
        ,subscription_contact."contact_country" AS "subscription_country"
        ,region_dimension."territory" AS "subscription_territory"
        ,subscription_requested."rateplan_charge_billing_period" AS "subscription_billing_period"
        --,subscription_requested."subscription_type"
        ,IFNULL(subscription_requested."subscription_type",LAG(subscription_requested."subscription_type") OVER (PARTITION BY subscription_staging."subscription_name" ORDER BY subscription_staging."subscription_version")) AS "subscription_type"
        ,subscription_requested."subscription_tier"
        ,subscription_requested."rateplan_charge_monthly_recurring_revenue" AS "subscription_monthly_recurring_revenue"
        ,subscription_requested."previous_rateplan_charge_monthly_recurring_revenue" AS "previous_subscription_monthly_recurring_revenue"
        ,subscription_requested."rateplan_charge_bill_cycle_day" AS "subscription_bill_cycle_day"
        ,subscription_requested."rateplan_name" AS "subscription_rateplan_name"
        ,subscription_requested."rateplan_charge_name" AS "subscription_rateplan_charge_name"
        ,subscription_requested."rateplan_charge_effective_start_date" AS "subscription_rateplan_charge_effective_start_date"
        ,COALESCE(dmp_renwel."validity_end_date"::DATE,subscription_requested."rateplan_charge_effective_end_date") AS "subscription_rateplan_charge_effective_end_date"
        ,COALESCE(dmp_renwel."validity_end_date"::DATE,subscription_requested."rateplan_charge_charged_through_date") AS "subscription_rateplan_charge_charged_through_date"
        ,subscription_requested."subscription_is_auto_renew" AS "subscription_auto_renew"
        ,subscription_requested."rateplan_charge_up_to_periods" AS "subscription_instalment_period"
        ,subscription_free_trial."subscription_free_trial_start_date" IS NOT NULL AS "has_free_trial"
        ,subscription_free_trial."subscription_free_trial_start_date"
        ,subscription_free_trial."subscription_free_trial_end_date"
        ,subscription_free_trial."subscription_free_trial_length_days_advertised"
        ,subscription_free_trial."subscription_free_trial_length_days_actual"
        ,subscription_free_trial."subscription_free_trial_length_months_advertised"
        ,subscription_free_trial."subscription_free_trial_length_months_actual"
        ,subscription_intro_discount."subscription_intro_discount_discount_percentage"
        ,subscription_intro_discount."subscription_intro_discount_effective_from_date"
        ,subscription_intro_discount."subscription_intro_discount_effective_until_date"
        ,subscription_intro_discount."subscription_intro_discount_discount_duration_days"
        ,subscription_intro_discount."subscription_intro_discount_discount_duration_months"
        ,subscription_pause."subscription_pause_start_date" IS NOT NULL AS "has_pause_period"
        ,subscription_pause."subscription_pause_start_date"
        ,subscription_pause."subscription_pause_end_date"
        ,LAST_DAY(subscription_pause."subscription_pause_start_date",'week') AS "subscription_pause_start_week"
        ,CASE
            WHEN subscription_pause."subscription_pause_end_date" >= '9999-01-01' THEN '9999-01-02'
            ELSE LAST_DAY(subscription_pause."subscription_pause_end_date",'week')
        END AS "subscription_pause_end_week"
        ,subscription_pause."pause_duration_weeks"
        ,subscription_discount."first_rateplan_charge_post_sign_up_giftcode" AS "first_post_sign_up_giftcode"
        ,subscription_discount."last_rateplan_charge_post_sign_up_giftcode" AS "last_post_sign_up_giftcode"
        ,subscription_discount."first_rateplan_charge_post_sign_up_giftcode_campaign_name" AS "first_post_sign_up_giftcode_campaign_name"
        ,subscription_discount."last_rateplan_charge_post_sign_up_giftcode_campaign_name" AS "last_post_sign_up_giftcode_campaign_name"
        ,subscription_discount."first_rateplan_charge_post_sign_up_context" AS "first_post_sign_up_discount_context"
        ,subscription_discount."last_rateplan_charge_post_sign_up_context" AS "last_post_sign_up_discount_context"
        ,subscription_discount."is_introductory_discount"
        ,subscription_discount."rateplan_charge_tier_min_discount_percentage" AS "min_discount_percentage"
        ,subscription_discount."rateplan_charge_tier_max_discount_percentage" AS "max_discount_percentage"
        ,subscription_discount."rateplan_charge_tier_discount_distinct_count" AS "discount_percentage_distinct_count"
        ,subscription_discount."rateplan_charge_post_sign_up_giftcode_distinct_count" AS "post_sign_up_giftcode_distinct_count"
        ,subscription_discount."rateplan_charge_post_sign_up_giftcode_list" AS "post_sign_up_giftcode_list"
        ,subscription_discount."rateplan_charge_post_sign_up_giftcode_campaign_name_distinct_count" AS "post_sign_up_giftcode_campaign_name_distinct_count"
        ,subscription_discount."rateplan_charge_post_sign_up_giftcode_campaign_name_list" AS "post_sign_up_giftcode_campaign_name_list"
        ,subscription_discount."rateplan_charge_effective_start_date" AS "discount_effective_start_date"
        ,LAST_DAY("discount_effective_start_date",'week') AS "discount_effective_start_week"
        ,subscription_discount."rateplan_charge_effective_end_date" AS "discount_effective_end_date"
        ,CASE
            WHEN subscription_discount."rateplan_charge_effective_end_date" >= '9999-01-01' THEN '9999-01-02'
            ELSE LAST_DAY(subscription_discount."rateplan_charge_effective_end_date",'week')
        END AS "discount_effective_end_week"
        ,subscription_discount."discount_duration_days"
        ,subscription_discount."discount_duration_weeks"
        ,subscription_discount."discount_data"
        ,IFNULL(subscription_addon_count."subscription_add_on_count", 0) AS "subscription_add_on_count"
        ,IFNULL(subscription_addon_count."subscription_add_on_count", 0) > 0 AS "has_subscription_add_on"
        ,IFNULL(subscription_addon_count."subscription_add_on_partner_ids",ARRAY_CONSTRUCT()) AS "subscription_add_on_partner_ids"
        ,subscription_addon_count."min_subscription_add_on_effective_start_date" AS "subscription_add_on_effective_start_date"
        ,subscription_addon_count."max_subscription_add_on_effective_end_date" AS "subscription_add_on_effective_end_date"
        ,LAST_DAY(subscription_addon_count."min_subscription_add_on_effective_start_date",'week') AS "subscription_add_on_effective_start_week"
        ,CASE
            WHEN subscription_addon_count."max_subscription_add_on_effective_end_date" >= '9999-01-01' 
            THEN '9999-01-02'
            ELSE LAST_DAY(subscription_addon_count."max_subscription_add_on_effective_end_date",'week')
         END AS "subscription_add_on_effective_end_week"
        ,IFNULL(subscription_addon_count."subscription_add_on_types",ARRAY_CONSTRUCT()) AS "subscription_add_on_types"
        ,subscription_content_attribution."fixture_id" AS "subscription_attributed_fixture_id"
        ,subscription_content_attribution."fixture_name" AS "subscription_attributed_fixture_name"
        ,subscription_content_attribution."fixture_start_date" AS "subscription_attributed_fixture_start_date"
        ,subscription_content_attribution."competition_id" AS "subscription_attributed_competition_id"
        ,subscription_content_attribution."competition_name" AS "subscription_attributed_competition_name"
        ,subscription_content_attribution."sport_id" AS "subscription_attributed_sport_id"
        ,subscription_content_attribution."sport_name" AS "subscription_attributed_sport_name"
        ,subscription_content_attribution."ruleset_name" AS "subscription_attributed_ruleset_name"
        ,subscription_content_attribution."has_ppv_required_entitlement" AS "subscription_attribution_has_ppv_required_entitlement"
        ,subscription_content_attribution."is_final_content_attribution"
        ,account_current."crm_account_id"
        ,account_current."dazn_user_id"
        ,account_current."billing_account_currency_code"
        ,account_current."billing_account_created_timestamp"::DATE AS "billing_acount_created_date"
        ,account_current."billing_account_has_advanced_payment_manager"
        ,billing_account_id_test_user."billing_account_is_batch_50"
        ,user_account_created."user_account_created_timestamp"::DATE AS "user_account_created_date"
        ,LAST_DAY("user_account_created_date", 'week') AS "user_account_created_week"
        ,HASH(subscription_contact."contact_country"
            ,"subscription_territory"
            ,"billing_account_currency_code"
            ,"billing_account_is_batch_50"
            ,"min_billing_account_subscription_name_created_week"
            ,"billing_account_has_advanced_payment_manager"
            ,"user_account_created_week")
            AS "billing_account__skey"
        ,HASH("is_soft_cancelled"
            ,"subscription_tier"
            ,"billing_account_product_group_trip_number"
            ,"is_resubscription"
            ,"subscription_product_group"
            ,"subscription_auto_renew"
            ,"subscription_instalment_period")
            AS "subscription_info__skey"
        ,HASH("subscription_start_week"
            ,"subscription_end_week")
            AS "subscription_term__skey"
        ,HASH("has_free_trial"
            ,"subscription_free_trial_length_days_advertised"
            ,"subscription_free_trial_length_months_advertised"
            ,"subscription_free_trial_end_date")
            AS "subscription_free_trial__skey"
        ,HASH("has_pause_period"
            ,"subscription_pause_start_week"
            ,"subscription_pause_end_week"
            ,"pause_duration_weeks")
            AS "subscription_pause__skey"
        ,HASH("has_subscription_add_on"
            ,"subscription_add_on_partner_ids"
            ,"subscription_add_on_count"
            ,"subscription_add_on_effective_start_week"
            ,"subscription_add_on_effective_end_week"
            ,"subscription_add_on_types")
            AS "subscription_add_on__skey"
        ,HASH(subscription_content_attribution."fixture_id"
            ,subscription_content_attribution."fixture_name"
            ,subscription_content_attribution."fixture_start_date"
            ,subscription_content_attribution."competition_id"
            ,subscription_content_attribution."competition_name"
            ,subscription_content_attribution."sport_id"
            ,subscription_content_attribution."sport_name"
            ,subscription_content_attribution."ruleset_name"
            ,subscription_content_attribution."has_ppv_required_entitlement"
            ,subscription_content_attribution."is_final_content_attribution")
            AS "subscription_content_attribution__skey"
        ,"subscription_source_system_name_derived" AS "subscription_source_system_name_derived__skey"
        ,"subscription_tracking_id" AS "subscription_tracking_id__skey"
        ,subscription_staging."subscription_sign_up_campaign_id" AS "subscription_sign_up_campaign_id__skey"
        ,"first_post_sign_up_giftcode_campaign_name" AS "first_post_sign_up_giftcode_campaign_name__skey"
        ,"last_post_sign_up_giftcode_campaign_name" AS "last_post_sign_up_giftcode_campaign_name__skey"
    FROM subscription_staging
    LEFT JOIN subscription_contact ON subscription_staging."subscription_id" = subscription_contact."subscription_id"
    LEFT JOIN region_dimension ON subscription_contact."contact_country" = region_dimension."join_key"
    LEFT JOIN account_current ON subscription_staging."billing_account_id" = account_current."billing_account_id"
    LEFT JOIN subscription_requested ON subscription_staging."subscription_id" = subscription_requested."subscription_id"
    LEFT JOIN subscription_free_trial ON subscription_staging."subscription_name" = subscription_free_trial."subscription_name"
    LEFT JOIN subscription_intro_discount ON subscription_staging."subscription_name" = subscription_intro_discount."subscription_name"
    LEFT JOIN subscription_pause ON subscription_staging."subscription_id" = subscription_pause."subscription_id"
    LEFT JOIN subscription_discount ON subscription_staging."subscription_id" = subscription_discount."subscription_id"
    LEFT JOIN subscription_addon_count ON subscription_staging."subscription_id" = subscription_addon_count."subscription_id"
    LEFT JOIN subscription_content_attribution ON subscription_staging."subscription_name" = subscription_content_attribution."subscription_name"
    LEFT JOIN billing_account_id_test_user ON subscription_staging."billing_account_id" = billing_account_id_test_user."billing_account_id"
    LEFT JOIN user_account_created ON account_current."dazn_user_id" = user_account_created."dazn_user_id"
    --ev code
   -- LEFT JOIN dmp_orders ON dmp_orders."subscription_id"=subscription_staging."subscription_id"
    LEFT JOIN dmp_renwel ON dmp_renwel."subscription_id" = subscription_staging."subscription_id"
    LEFT JOIN dmp_cancel ON dmp_cancel."subscription_id" = subscription_staging."subscription_id"
   -- LEFT JOIN dmp_mrr ON dmp_mrr."billing_product_id" = dmp_renwel."billing_product_id"
)

SELECT * FROM zr_sub_id_dim
UNION ALL
SELECT * FROM docomo_dim
