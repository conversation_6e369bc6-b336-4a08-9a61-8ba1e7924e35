{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"subscription_term__skey"',
        database='MART_DIMENSIONS__B2C__DOMAIN__' + snowflake_env(),
        schema='SUBSCRIPTION',
        alias='subscription_term__dim',
        tags=['presentation-subscription-domain']
    )
}}

WITH sub_id_dim AS (
    SELECT * FROM {{ ref('subscription_id__dim') }}
)

 SELECT DISTINCT
         sub_id_dim."subscription_term__skey"
        ,sub_id_dim."subscription_start_week"
        ,sub_id_dim."subscription_end_week"
 FROM sub_id_dim
