{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"subscription_content_attribution__skey"',
        database='MART_DIMENSIONS__B2C__DOMAIN__' + snowflake_env(),
        schema='SUBSCRIPTION',
        alias='subscription_content_attribution__dim',
        tags=['presentation-subscription-domain']
    )
}}

WITH sub_id_dim AS (
    SELECT * FROM {{ ref('subscription_id__dim') }}
)

 SELECT DISTINCT
        "subscription_content_attribution__skey"
        ,"subscription_attributed_fixture_id"
        ,"subscription_attributed_fixture_name"
        ,"subscription_attributed_fixture_start_date"
        ,"subscription_attributed_competition_id"
        ,"subscription_attributed_competition_name"
        ,"subscription_attributed_sport_id"
        ,"subscription_attributed_sport_name"
        ,"subscription_attributed_ruleset_name"
        ,"subscription_attribution_has_ppv_required_entitlement"
        ,"is_final_content_attribution"
 FROM sub_id_dim
