{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"subscription_info__skey"',
        database='MART_DIMENSIONS__B2C__DOMAIN__' + snowflake_env(),
        schema='SUBSCRIPTION',
        alias='subscription_info__dim',
        tags=['presentation-subscription-domain']
    )
}}
 
WITH sub_id_dim AS (
    SELECT * FROM {{ ref('subscription_name__scd') }}
)
 
SELECT DISTINCT
         sub_id_dim."subscription_info__skey"
        ,sub_id_dim."is_soft_cancelled"
        ,sub_id_dim."subscription_tier"
        ,sub_id_dim."billing_account_product_group_trip_number" AS "billing_account_trip_number"
        ,sub_id_dim."is_resubscription"
        ,sub_id_dim."subscription_product_group"
        ,sub_id_dim."subscription_auto_renew"
        ,sub_id_dim."subscription_instalment_period"
FROM sub_id_dim
