{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"subscription_free_trial__skey"',
        database='MART_DIMENSIONS__B2C__DOMAIN__' + snowflake_env(),
        schema='SUBSCRIPTION',
        alias='subscription_free_trial__dim',
        tags=['presentation-subscription-domain']
    )
}}

WITH sub_id_dim AS (
    SELECT * FROM {{ ref('subscription_id__dim') }}
)

SELECT DISTINCT
         sub_id_dim."subscription_free_trial__skey"
        ,sub_id_dim."has_free_trial"
        ,sub_id_dim."subscription_free_trial_length_days_advertised"
        ,sub_id_dim."subscription_free_trial_length_months_advertised"
        ,sub_id_dim."subscription_free_trial_end_date"
 FROM sub_id_dim
