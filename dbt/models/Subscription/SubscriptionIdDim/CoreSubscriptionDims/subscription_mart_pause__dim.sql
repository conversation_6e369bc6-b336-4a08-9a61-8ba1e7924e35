{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"subscription_pause__skey"',
        database='MART_DIMENSIONS__B2C__DOMAIN__' + snowflake_env(),
        schema='SUBSCRIPTION',
        alias='subscription_pause__dim',
        tags=['presentation-subscription-domain']
    )
}}

WITH sub_id_dim AS (
    SELECT * FROM {{ ref('subscription_id__dim') }}
)

SELECT DISTINCT
         sub_id_dim."subscription_pause__skey"
        ,sub_id_dim."has_pause_period"
        ,sub_id_dim."subscription_pause_start_week"
        ,sub_id_dim."subscription_pause_end_week"
        ,sub_id_dim."pause_duration_weeks"
 FROM sub_id_dim
