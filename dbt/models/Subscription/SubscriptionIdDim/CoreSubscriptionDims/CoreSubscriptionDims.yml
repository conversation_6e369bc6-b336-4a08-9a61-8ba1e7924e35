version: 2

models:
  - name: billing_account__dim
    description: "Dimension table surfacing information on billing account ex: country, territory.."
    columns:
      - name: billing_account__skey
        description: "The surrogate key (skey) used to join on the billing_account__dim"
        quote: true
        tests:
          - unique:
              config:
                severity: error

      - name: subscription_country
        description: "The country of the subscription, originating from the Zuora contact. E.g. Spain, Germany, Japan, United States, United Kingdom, ..."
        quote: true

      - name: subscription_territory
        description: "The territory of the subscription, pulled in by a join to region_dimension. E.g. Spain, DACH, Japan, United States, UK and Ireland, ..."
        quote: true

      - name: billing_account_currency_code
        description: "The currency code for the Zuora Account, E.g. EUR, USD, ..."
        quote: true

      - name: billing_account_is_batch_50
        description: "Boolean flag, true if the account has ever been on Batch50, which implies it is a test account"
        quote: true

      - name: min_billing_account_subscription_name_created_week
        description: "The week of the very first date that the subscription was first created"
        quote: true

      - name: billing_account_has_advanced_payment_manager
        description: "A flag for if subscriptions on the same account have different payment methods"
        quote: true

      - name: user_account_created_week
        description: "The minimum week_end_date we see the users being created in our systems, derived from Salesforce before migration to Segment"
        quote: true

      - name: country__skey
        description: "The skey for the COUNTRY__DIM that comes directly from the seed of region_dimension for country-territory mappings, this skey is actually just the exact value of the subscription_country"
        quote: true

  - name: subscription_mart_info__dim
    description: "Dimension table surfacing information on the subscription like tier, trip number.."
    columns:
      - name: subscription_info__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_info__dim"
        quote: true
        tests:
          - unique:
              config:
                severity: error

      - name: is_soft_cancelled
        description: "The status of the subscription is cancelled and it won't renew on the current end date"
        quote: true

      - name: subscription_tier
        description: "The Entitlement Set Id of the Rateplan, which is filtered to the associated RatePlanCharge's Flat Fee Pricing Model and Recurring Type, so producing the Tier for the Subscription"
        quote: true

      - name: billing_account_trip_number
        description: "The count of previous subscriptions from this billing account including this subscription for all product groups"
        quote: true

      - name: is_resubscription
        description: "Identifies if this is a resubscription for this product group or not"
        quote: true

      - name: subscription_product_group
        description: "Can be either 'NFL' whereas it is an NFL subscription, or 'DAZN' otherwise."
        quote: true

      - name: subscription_auto_renew
        description: "TBC"
        quote: true

      - name: subscription_instalment_period
        description: "TBC"
        quote: true

  - name: subscription_mart_add_on__dim
    description: "Dimension table surfacing information on the subscription add on like count, start week..."
    columns:
      - name: subscription_add_on__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_add_on__dim"
        quote: true
        tests:
          - unique:
              config:
                severity: error

      - name: has_subscription_add_on
        description: "A flag for if there is any AddOn active at the time the Subscription Id is created"
        quote: true

      - name: subscription_add_on_count
        description: "Number of unique AddOns that are active at the time the Subscription Id is created"
        quote: true

      - name: subscription_add_on_partner_ids
        description: "An array containing distinct values of the addon partner IDs a subscription ID has"
        quote: true

      - name: subscription_add_on_effective_start_week
        description: "The minimum start week that the add on is effective from"
        quote: true

      - name: subscription_add_on_effective_end_week
        description: "The maximum end week that the add on is effective until"
        quote: true

      - name: subscription_add_on_types
        description: "This is an array field identifies all the add-on types assiociated with Subscription, the viewership on multiple screens for the same ip."
        quote: true

  - name: subscription_mart_free_trial__dim
    description: "Dimension table surfacing information on the subscription free trial ex: free trial length days, length months"
    columns:
      - name: subscription_free_trial__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_free_trial__dim"
        quote: true
        tests:
          - unique:
              config:
                severity: error

      - name: has_free_trial
        description: "A flag to indicate if the subscription is a free trial"
        quote: true

      - name: subscription_free_trial_length_days_advertised
        description: "The length in days of the Free Trial as advertised to the user, so calculated directly from the Zuora Subscription field. This can be 0 if there is no Free Trial."
        quote: true

      - name: subscription_free_trial_length_months_advertised
        description: "The length in months of the Free Trial as advertised to the user, so calculated directly from the Zuora Subscription field. This can be 0 if there is no Free Trial."
        quote: true

      - name: subscription_free_trial_end_date
        description: "The date the Free Trial ends"
        quote: true

  - name: subscription_mart_pause__dim
    description: "Dimension table surfacing information on the subscription pause period ex: pause period start week, pause duration.."
    columns:
      - name: subscription_pause__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_pause__dim"
        quote: true
        tests:
          - unique:
              config:
                severity: error

      - name: has_pause_period
        description: "Flag to identify whether the subscription had a pause period"
        quote: true

      - name: subscription_pause_start_week
        description: "Week of the most recent pause starts for the subscription id"
        quote: true

      - name: subscription_pause_end_week
        description: "Week of the most recent pause ends for the subscription id"
        quote: true

      - name: pause_duration_weeks
        description: "The duration in weeks of the most recent pause for the subscription id"
        quote: true

  - name: subscription_mart_term__dim
    description: "Dimension table surfacing information on subscription term like subscription start week and end week"
    columns:
      - name: subscription_term__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_term__dim"
        quote: true
        tests:
          - unique:
              config:
                severity: error

      - name: subscription_start_week
        description: "The week end date the subscription starts, including any free trial"
        quote: true

      - name: subscription_end_week
        description: "The latest week end date Zuora has seen this subscription end, taking into account retrospective cancellation (when the cancel date is before the subscription is updated)"
        quote: true

  - name: subscription_mart_content_attribution__dim
    description: "Dimension describing the attributed content for a Subscription Name's acquisition based off the first days of their streaming behaviour"
    columns:
      - name: subscription_content_attribution__skey
        description: "The surrogate key (skey) used to join on the subscription_content_attribution__dim"
        quote: true
        tests:
          - unique:
              config:
                severity: error

      - name: subscription_attributed_fixture_id
        description: "The ID of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_fixture_name
        description: "The name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_fixture_start_date
        description: "The start date of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_competition_id
        description: "The competition ID of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_competition_name
        description: "The competition name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_sport_id
        description: "The sport ID of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_sport_name
        description: "The sport name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_ruleset_name
        description: "The ruleset name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attribution_has_ppv_required_entitlement
        description: "A flag to indicate if the content attributed was relating to an event that had a ppv entitlement required"
        quote: true

      - name: is_final_content_attribution
        description: "A flag to indicate if the attribution window has now passed for this fixture and so the attributed content can not change"
        quote: true
