{{
    config(
        materialized='incremental',
        incremental_strategy='merge',
        unique_key='"billing_account__skey"',
        database='MART_DIMENSIONS__B2C__DOMAIN__' + snowflake_env(),
        schema='CUSTOMER',
        tags=['presentation-subscription-domain']
    )
}}

WITH sub_id_dim AS (
    SELECT * FROM {{ ref('subscription_id__dim') }}
)

SELECT DISTINCT
    sub_id_dim."billing_account__skey"
    ,sub_id_dim."subscription_country"
    ,sub_id_dim."subscription_territory"
    ,sub_id_dim."billing_account_currency_code"
    ,sub_id_dim."billing_account_is_batch_50"
    ,sub_id_dim."min_billing_account_subscription_name_created_week"
    ,sub_id_dim."billing_account_has_advanced_payment_manager"
    ,sub_id_dim."user_account_created_week"
    ,sub_id_dim."subscription_country" AS "country__skey"
FROM sub_id_dim
{% if is_incremental() %}
WHERE "billing_account__skey" NOT IN (SELECT "billing_account__skey" FROM {{ this }})
{% endif %}
