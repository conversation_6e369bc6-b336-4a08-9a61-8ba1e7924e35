{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"subscription_add_on__skey"',
        database='MART_DIMENSIONS__B2C__DOMAIN__' + snowflake_env(),
        schema='SUBSCRIPTION',
        alias='subscription_add_on__dim',
        tags=['presentation-subscription-domain']
    )
}}

WITH sub_id_dim AS (
    SELECT * FROM {{ ref('subscription_id__dim') }}
)

 SELECT DISTINCT
     sub_id_dim."subscription_add_on__skey"
    ,sub_id_dim."has_subscription_add_on"
    ,sub_id_dim."subscription_add_on_count"
    ,sub_id_dim."subscription_add_on_effective_start_week"
    ,sub_id_dim."subscription_add_on_effective_end_week"
    ,sub_id_dim."subscription_add_on_partner_ids"
    ,sub_id_dim."subscription_add_on_types"
 FROM sub_id_dim
