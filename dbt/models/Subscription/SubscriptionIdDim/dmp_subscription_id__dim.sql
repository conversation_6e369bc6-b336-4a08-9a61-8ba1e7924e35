{{
    config(
        materialized='table',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_2XL_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH sub_core AS (
    SELECT
        *
        -- creating these here so there isn't any ambiguous fields
        ,"subscription_name_original_created_timestamp"::DATE AS "subscription_start_date_corrected"
        ,GREATEST("subscription_end_date", "subscription_id_created_timestamp")::DATE AS "subscription_end_date_corrected"
    FROM {{ ref('staging__dmp_subscription_core') }}
)

,subscription_intro_discount AS (
    SELECT * FROM {{ ref('dmp_subscription_intro_discount__dim') }}
)

,subscription_pause AS (
    SELECT * FROM {{ ref('dmp_subscription_id_pause__dim') }}
)

,subscription_discount AS (
    SELECT * FROM {{ ref('dmp_subscription_id_discount__dim') }}
)

,subscription_addon_count AS (
    SELECT
        "subscription_id"
        ,COUNT(DISTINCT "rateplan_partner_id") AS "subscription_add_on_count"
        ,MIN("subscription_add_on_effective_start_date") AS "min_subscription_add_on_effective_start_date"
        ,MAX("subscription_add_on_effective_end_date") AS "max_subscription_add_on_effective_end_date"
        ,MAX("event_timestamp") AS "event_timestamp"
        ,ARRAY_AGG(DISTINCT "rateplan_partner_id") WITHIN GROUP (ORDER BY "rateplan_partner_id") AS "subscription_add_on_partner_ids"
        ,ARRAY_AGG(DISTINCT "subscription_add_on_type") WITHIN GROUP (ORDER BY "subscription_add_on_type") AS "subscription_add_on_types"
    FROM {{ ref('dmp_subscription_id_add_on__dim') }}
    GROUP BY 1
)


,subscription_content_attribution AS (
    SELECT * FROM {{ ref('subscription_name_content_attribution__dim') }}
)

,billing_account_id_test_user AS (
    SELECT * FROM {{ ref('billing_account_id_test__dim') }}
)

,user_account_created AS (
    SELECT * FROM {{ ref('user_account_created') }}
)

,dmp_mrr AS (
select * from {{ ref('staging__dmp_subscription_mrr') }}
)

,ev_sub_id_dim AS (
    SELECT
        CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
        ,sub_core."subscription_id"
        ,sub_core."subscription_name"
        ,sub_core."subscription_status"
        ,LOWER(sub_core."subscription_status") = 'cancelled' AS "is_soft_cancelled"
        ,sub_core."subscription_version"
        ,sub_core."subscription_name_original_created_timestamp"
        ,sub_core."subscription_id_created_timestamp"
        ,GREATEST_IGNORE_NULLS(sub_core."subscription_id_updated_timestamp", subscription_addon_count."event_timestamp" ,"subscription_pause_request_timestamp" ) AS "subscription_id_updated_timestamp"
        ,sub_core."subscription_start_date_corrected" AS "subscription_start_date"
        ,GREATEST_IGNORE_NULLS(subscription_pause."subscription_validity_end_date",sub_core."subscription_end_date_corrected") AS "subscription_end_date"
        ,LAST_DAY(sub_core."subscription_start_date_corrected", 'week') AS "subscription_start_week"
        ,CASE
            WHEN sub_core."subscription_end_date_corrected" >= '9999-01-01' THEN '9999-12-31'
            ELSE LAST_DAY(sub_core."subscription_end_date_corrected", 'week')
        END AS "subscription_end_week"
        ,sub_core."billing_account_id"
        ,MIN(sub_core."subscription_name_original_created_timestamp") OVER (PARTITION BY sub_core."billing_account_id", sub_core."subscription_product_group")::DATE AS "min_billing_account_subscription_name_created_date"
        ,LAST_DAY("min_billing_account_subscription_name_created_date",'WEEK') AS "min_billing_account_subscription_name_created_week"
        ,DENSE_RANK() OVER (PARTITION BY sub_core."billing_account_id" ORDER BY sub_core."subscription_name_original_created_timestamp" ASC) AS "billing_account_trip_number"
        ,DENSE_RANK() OVER (PARTITION BY sub_core."billing_account_id", sub_core."subscription_product_group" ORDER BY sub_core."subscription_name_original_created_timestamp" ASC) AS "billing_account_product_group_trip_number"
        ,"billing_account_product_group_trip_number" > 1 AS "is_resubscription"
        ,sub_core."subscription_direct_carrier_billing_carrier_name"
        ,sub_core."subscription_source_system_name"
        ,COALESCE(CONCAT("subscription_direct_carrier_billing_carrier_name",' DCB'), sub_core."subscription_source_system_name", 'Direct') AS "subscription_source_system_name_derived"
        ,IFNULL(sub_core."subscription_sign_up_campaign_id",subscription_intro_discount."subscription_sign_up_campaign_id" ) AS "subscription_sign_up_campaign_id" 
        ,IFNULL(sub_core."subscription_sign_up_giftcode",subscription_intro_discount."subscription_sign_up_giftcode" ) AS "subscription_sign_up_giftcode"   
        ,IFNULL(sub_core."zr_subscription_tracking_id",sub_core."subscription_tracking_id") AS "subscription_tracking_id" 
        ,UPPER(sub_core."subscription_validity_period_unit") AS "subscription_term_type"
        ,sub_core."subscription_product_group"
        ,sub_core."subscription_payment_method_id" 
        ,sub_core."subscription_country" AS "subscription_country"
        ,sub_core."subscription_territory"
        ,sub_core."subscription_validity_period_unit" AS "subscription_billing_period" --Need to check
        ,dmp_mrr."subscription_type"
        ,sub_core."subscription_tier"
        ,dmp_mrr."subscription_monthly_recurring_revenue"
        ,LAG("subscription_monthly_recurring_revenue") OVER (PARTITION BY sub_core."subscription_name" ORDER BY sub_core."subscription_version") AS "previous_subscription_monthly_recurring_revenue"
        ,sub_core."subscription_bill_cycle_day" AS "subscription_bill_cycle_day" 
        ,sub_core."subscription_catalog_product_name" AS "subscription_rateplan_name"
        ,sub_core."subscription_catalog_product_name" AS "subscription_rateplan_charge_name"
        ,sub_core."subscription_rateplan_charge_effective_start_date" AS "subscription_rateplan_charge_effective_start_date" --startdate of billing product id
        ,GREATEST_IGNORE_NULLS(subscription_pause."subscription_rateplan_charge_charged_through_date",sub_core."subscription_rateplan_charge_charged_through_date") AS "subscription_rateplan_charge_effective_end_date" --renewaldate of the billing product id
        ,GREATEST_IGNORE_NULLS(subscription_pause."subscription_rateplan_charge_charged_through_date",sub_core."subscription_rateplan_charge_charged_through_date") AS "subscription_rateplan_charge_charged_through_date" 
       --,CASE WHEN sub_core."subscription_billing_charge_type"='RENEWABLE' THEN TRUE ELSE FALSE END AS "subscription_auto_renew" 
        ,IFNULL(sub_core."subscription_is_auto_renew",NULL ) AS "subscription_auto_renew" 
        ,sub_core."subscription_instalment_period" 
        ,IFNULL(sub_core."has_free_trial",FALSE) AS "has_free_trial"
        ,sub_core."subscription_free_trial_start_date"
        ,sub_core."subscription_free_trial_end_date"
        ,CASE
            -- Regardless of the PeriodType, if the Periods are 0, then there is no Free Trial, so dates would be NULL and DATEDIFF below does not work
            WHEN sub_core."subscription_free_trial_start_date" IS NULL THEN 0
            -- If there is a FT, then simply difference between start and end dates gives the length
            ELSE DATEDIFF('day', "subscription_free_trial_start_date", "subscription_free_trial_end_date")
        END::INT AS "subscription_free_trial_length_days_advertised"
        ,CASE
            -- Regardless of the PeriodType, if the Periods are 0, then there is no Free Trial, so dates would be NULL and DATEDIFF below does not work
            WHEN sub_core."subscription_free_trial_start_date" IS NULL THEN 0
            -- If there is a FT, then simply difference between start and end dates gives the length
            ELSE DATEDIFF('day', "subscription_free_trial_start_date", "subscription_free_trial_end_date")
        END::INT  AS "subscription_free_trial_length_days_actual"
        ,CASE
            -- Regardless of the PeriodType, if the Periods are 0, then there is no Free Trial, so dates would be NULL and DATEDIFF below does not work
            WHEN sub_core."subscription_free_trial_start_date" IS NULL THEN 0
            -- If there is a FT, then simply difference between start and end dates gives the length
            ELSE DATEDIFF('day', "subscription_free_trial_start_date", "subscription_free_trial_end_date")/30
        END::INT  AS "subscription_free_trial_length_months_advertised"
        ,CASE
            -- Regardless of the PeriodType, if the Periods are 0, then there is no Free Trial, so dates would be NULL and DATEDIFF below does not work
            WHEN sub_core."subscription_free_trial_start_date" IS NULL THEN 0
            -- If there is a FT, then simply difference between start and end dates gives the length
            ELSE DATEDIFF('day', "subscription_free_trial_start_date", "subscription_free_trial_end_date")/30
        END::INT  AS "subscription_free_trial_length_months_actual"

        ,subscription_intro_discount."subscription_intro_discount_discount_percentage"
        ,subscription_intro_discount."subscription_intro_discount_effective_from_date"
        ,subscription_intro_discount."subscription_intro_discount_effective_until_date"
        ,subscription_intro_discount."subscription_intro_discount_discount_duration_days"
        ,subscription_intro_discount."subscription_intro_discount_discount_duration_months"
        ,subscription_pause."subscription_pause_start_date" IS NOT NULL AS "has_pause_period"
        ,subscription_pause."subscription_pause_start_date"
        ,subscription_pause."subscription_pause_end_date"
        ,LAST_DAY(subscription_pause."subscription_pause_start_date",'week') AS "subscription_pause_start_week"
        ,CASE
            WHEN subscription_pause."subscription_pause_end_date" >= '9999-01-01' THEN '9999-01-02'
            ELSE LAST_DAY(subscription_pause."subscription_pause_end_date",'week')
        END AS "subscription_pause_end_week"
        ,subscription_pause."pause_duration_weeks"
        ,subscription_discount."first_rateplan_charge_post_sign_up_giftcode" AS "first_post_sign_up_giftcode"
        ,subscription_discount."last_rateplan_charge_post_sign_up_giftcode" AS "last_post_sign_up_giftcode"
        ,subscription_discount."first_rateplan_charge_post_sign_up_giftcode_campaign_name" AS "first_post_sign_up_giftcode_campaign_name"
        ,subscription_discount."last_rateplan_charge_post_sign_up_giftcode_campaign_name" AS "last_post_sign_up_giftcode_campaign_name"
        ,subscription_discount."first_rateplan_charge_post_sign_up_context" AS "first_post_sign_up_discount_context"
        ,subscription_discount."last_rateplan_charge_post_sign_up_context" AS "last_post_sign_up_discount_context"
        ,subscription_discount."is_introductory_discount"
        ,subscription_discount."rateplan_charge_tier_min_discount_percentage" AS "min_discount_percentage"
        ,subscription_discount."rateplan_charge_tier_max_discount_percentage" AS "max_discount_percentage"
        ,subscription_discount."rateplan_charge_tier_discount_distinct_count" AS "discount_percentage_distinct_count"
        ,subscription_discount."rateplan_charge_post_sign_up_giftcode_distinct_count" AS "post_sign_up_giftcode_distinct_count"
        ,subscription_discount."rateplan_charge_post_sign_up_giftcode_list" AS "post_sign_up_giftcode_list"
        ,subscription_discount."rateplan_charge_post_sign_up_giftcode_campaign_name_distinct_count" AS "post_sign_up_giftcode_campaign_name_distinct_count"
        ,subscription_discount."rateplan_charge_post_sign_up_giftcode_campaign_name_list" AS "post_sign_up_giftcode_campaign_name_list"
        ,subscription_discount."rateplan_charge_effective_start_date" AS "discount_effective_start_date"
        ,LAST_DAY("discount_effective_start_date",'week') AS "discount_effective_start_week"
        ,subscription_discount."rateplan_charge_effective_end_date" AS "discount_effective_end_date"
        ,CASE
            WHEN subscription_discount."rateplan_charge_effective_end_date" >= '9999-01-01' THEN '9999-01-02'
            ELSE LAST_DAY(subscription_discount."rateplan_charge_effective_end_date",'week')
        END AS "discount_effective_end_week"
        ,subscription_discount."discount_duration_days"
        ,subscription_discount."discount_duration_weeks"
        ,subscription_discount."discount_data"
        ,IFNULL(subscription_addon_count."subscription_add_on_count", 0) AS "subscription_add_on_count"
        ,IFNULL(subscription_addon_count."subscription_add_on_count", 0) > 0 AS "has_subscription_add_on"
        ,IFNULL(subscription_addon_count."subscription_add_on_partner_ids",ARRAY_CONSTRUCT()) AS "subscription_add_on_partner_ids"
        ,subscription_addon_count."min_subscription_add_on_effective_start_date" AS "subscription_add_on_effective_start_date"
        ,subscription_addon_count."max_subscription_add_on_effective_end_date" AS "subscription_add_on_effective_end_date"
        ,LAST_DAY(subscription_addon_count."min_subscription_add_on_effective_start_date",'week') AS "subscription_add_on_effective_start_week"
        ,CASE
            WHEN subscription_addon_count."max_subscription_add_on_effective_end_date" >= '9999-01-01' THEN '9999-01-02'
            ELSE LAST_DAY(subscription_addon_count."max_subscription_add_on_effective_end_date",'week')
        END AS "subscription_add_on_effective_end_week"
        ,IFNULL(subscription_addon_count."subscription_add_on_types",ARRAY_CONSTRUCT()) AS "subscription_add_on_types"
        ,subscription_content_attribution."fixture_id" AS "subscription_attributed_fixture_id"
        ,subscription_content_attribution."fixture_name" AS "subscription_attributed_fixture_name"
        ,subscription_content_attribution."fixture_start_date" AS "subscription_attributed_fixture_start_date"
        ,subscription_content_attribution."competition_id" AS "subscription_attributed_competition_id"
        ,subscription_content_attribution."competition_name" AS "subscription_attributed_competition_name"
        ,subscription_content_attribution."sport_id" AS "subscription_attributed_sport_id"
        ,subscription_content_attribution."sport_name" AS "subscription_attributed_sport_name"
        ,subscription_content_attribution."ruleset_name" AS "subscription_attributed_ruleset_name"
        ,subscription_content_attribution."has_ppv_required_entitlement" AS "subscription_attribution_has_ppv_required_entitlement"
        ,subscription_content_attribution."is_final_content_attribution"

        ,sub_core."crm_account_id"
        ,sub_core."dazn_user_id"
        ,sub_core."purchase_currency" AS "billing_account_currency_code" 
        ,sub_core."billing_account_created_timestamp"::DATE AS "billing_acount_created_date"
        ,NULL AS "billing_account_has_advanced_payment_manager"
        ,IFNULL(billing_account_id_test_user."billing_account_is_batch_50",FALSE) AS "billing_account_is_batch_50"
        ,user_account_created."user_account_created_timestamp"::DATE AS "user_account_created_date"
        ,LAST_DAY("user_account_created_date", 'week') AS "user_account_created_week"

        ,HASH(sub_core."subscription_country"
            ,"subscription_territory"
            ,"billing_account_currency_code"
            ,"billing_account_is_batch_50"
            ,"min_billing_account_subscription_name_created_week"
            ,"billing_account_has_advanced_payment_manager"
            ,"user_account_created_week")
            AS "billing_account__skey"
        ,HASH("is_soft_cancelled"
          --,"is_active_grace"
            ,"subscription_tier"
            ,"billing_account_product_group_trip_number"
            ,"is_resubscription"
            ,"subscription_product_group"
            ,"subscription_auto_renew"
            ,"subscription_instalment_period")
            AS "subscription_info__skey"
        ,HASH("subscription_start_week"
            ,"subscription_end_week")
            AS "subscription_term__skey"
        ,HASH("has_free_trial"
            ,"subscription_free_trial_length_days_advertised"
            ,"subscription_free_trial_length_months_advertised"
            ,"subscription_free_trial_end_date")
            AS "subscription_free_trial__skey"
        ,HASH("has_pause_period"
            ,"subscription_pause_start_week"
            ,"subscription_pause_end_week"
            ,"pause_duration_weeks")
            AS "subscription_pause__skey"
        ,HASH("has_subscription_add_on"
            ,"subscription_add_on_partner_ids"
            ,"subscription_add_on_count"
            ,"subscription_add_on_effective_start_week"
            ,"subscription_add_on_effective_end_week"
            ,"subscription_add_on_types")
            AS "subscription_add_on__skey"
        ,HASH(subscription_content_attribution."fixture_id"
            ,subscription_content_attribution."fixture_name"
            ,subscription_content_attribution."fixture_start_date"
            ,subscription_content_attribution."competition_id"
            ,subscription_content_attribution."competition_name"
            ,subscription_content_attribution."sport_id"
            ,subscription_content_attribution."sport_name"
            ,subscription_content_attribution."ruleset_name"
            ,subscription_content_attribution."has_ppv_required_entitlement"
            ,subscription_content_attribution."is_final_content_attribution")
            AS "subscription_content_attribution__skey"
        ,"subscription_source_system_name_derived" AS "subscription_source_system_name_derived__skey"
        ,IFNULL(sub_core."zr_subscription_tracking_id",sub_core."subscription_tracking_id")  AS "subscription_tracking_id__skey"
        ,IFNULL(sub_core."subscription_sign_up_campaign_id",subscription_intro_discount."subscription_sign_up_campaign_id" ) AS "subscription_sign_up_campaign_id__skey"
        ,"first_post_sign_up_giftcode_campaign_name" AS "first_post_sign_up_giftcode_campaign_name__skey"
        ,"last_post_sign_up_giftcode_campaign_name" AS "last_post_sign_up_giftcode_campaign_name__skey"
        ,dmp_mrr."discounted_monthly_recurring_revenue"
        ,sub_core."subscription_churn_type"
        ,'DMP' AS "data_source"
        ,sub_core."subscription_start_timestamp"
        ,sub_core."subscription_end_timestamp"
        ,sub_core."subscription_free_trial_start_timestamp"
        ,sub_core."subscription_free_trial_end_timestamp"
        --,sub_core."discount start timestamp"
      --  ,sub_core."discount end timestamp"
    FROM sub_core
 --   LEFT JOIN account_current ON sub_core."billing_account_id" = account_current.DAZNID
    LEFT JOIN dmp_mrr ON dmp_mrr."billing_product_id" = sub_core."subscription_id"
   -- LEFT JOIN subscription_free_trial ON subscription_staging."subscription_name" = subscription_free_trial."subscription_name"
    LEFT JOIN subscription_intro_discount ON sub_core."subscription_name" = subscription_intro_discount."subscription_name"
    LEFT JOIN subscription_pause ON sub_core."subscription_id" = subscription_pause."subscription_id"
    LEFT JOIN subscription_discount ON sub_core."subscription_id" = subscription_discount."subscription_id"
    LEFT JOIN subscription_addon_count ON sub_core."subscription_id" = subscription_addon_count."subscription_id"
    LEFT JOIN subscription_content_attribution ON sub_core."subscription_name" = subscription_content_attribution."subscription_name"
    LEFT JOIN billing_account_id_test_user ON sub_core."billing_account_id" = billing_account_id_test_user."billing_account_id"
    LEFT JOIN user_account_created ON sub_core."dazn_user_id" = user_account_created."dazn_user_id"
)

SELECT * FROM ev_sub_id_dim
