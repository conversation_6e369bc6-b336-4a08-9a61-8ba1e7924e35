{{
    config(
        materialized='table',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_2XL_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH zuora_source AS (
    SELECT 
        *
        ,NULL AS "discounted_monthly_recurring_revenue"
        ,NULL AS "subscription_churn_type"
        ,'ZUORA' AS "data_source"
        ,"subscription_start_date"::TIMESTAMP AS "subscription_start_timestamp"
        ,"subscription_end_date"::TIMESTAMP AS "subscription_end_timestamp"
        ,"subscription_free_trial_start_date"::TIMESTAMP AS "subscription_free_trial_start_timestamp"
        ,"subscription_free_trial_end_date"::TIMESTAMP AS "subscription_free_trial_end_timestamp" 
    FROM  {{ ref('zr_subscription_id__dim') }}
)

,dmp_source AS (
  SELECT * FROM {{ ref('dmp_subscription_id__dim') }}
)

,union_all AS (SELECT * FROM zuora_source
UNION ALL
SELECT * FROM dmp_source)
  
,final AS (
    SELECT 
"subscription_id"
,"subscription_name"
,"subscription_status" AS "subscription_statuss"
,"is_soft_cancelled"
,"subscription_version"::INT AS "subscription_versions"
,"subscription_name_original_created_timestamp"
,"subscription_id_created_timestamp"
,"subscription_id_updated_timestamp"
,"subscription_start_date"
,"subscription_end_date"
,"subscription_start_week"
,"subscription_end_week"
,"billing_account_id"
--,"min_billing_account_subscription_name_created_date"
--,"min_billing_account_subscription_name_created_week"
--,"billing_account_trip_number"
--,"billing_account_product_group_trip_number"
--,"is_resubscription"
,"subscription_direct_carrier_billing_carrier_name"
,"subscription_source_system_name"
,"subscription_source_system_name_derived"
,"subscription_sign_up_campaign_id"
,"subscription_sign_up_giftcode"
,"subscription_tracking_id"
,"subscription_term_type"
,"subscription_product_group"
,"subscription_payment_method_id"
,"subscription_country"
,"subscription_territory"
,"subscription_billing_period"
,"subscription_type" AS "subscription_typee"
,"subscription_tier"
,"subscription_monthly_recurring_revenue"
,"previous_subscription_monthly_recurring_revenue"
,"subscription_bill_cycle_day"::INT AS "subscription_bill_cycle_days"
,"subscription_rateplan_name"
,"subscription_rateplan_charge_name"
,"subscription_rateplan_charge_effective_start_date"
,"subscription_rateplan_charge_effective_end_date"
,"subscription_rateplan_charge_charged_through_date"
,"subscription_auto_renew"
,"subscription_instalment_period"::INT AS "subscription_instalment_periods"
,"has_free_trial"
,"subscription_free_trial_start_date"
,"subscription_free_trial_end_date"
,"subscription_free_trial_length_days_advertised"
,"subscription_free_trial_length_days_actual"
,"subscription_free_trial_length_months_advertised"
,"subscription_free_trial_length_months_actual"
,"subscription_intro_discount_discount_percentage"
,"subscription_intro_discount_effective_from_date"
,"subscription_intro_discount_effective_until_date"
,"subscription_intro_discount_discount_duration_days"::INT AS "subscription_intro_discount_discount_duration_day"
,"subscription_intro_discount_discount_duration_months"::INT AS "subscription_intro_discount_discount_duration_month"
,"has_pause_period"
,"subscription_pause_start_date"
,"subscription_pause_end_date"
,"subscription_pause_start_week"
,"subscription_pause_end_week"
,"pause_duration_weeks"::INT "pause_duration_week"
,"first_post_sign_up_giftcode"
,"last_post_sign_up_giftcode"
,"first_post_sign_up_giftcode_campaign_name"
,"last_post_sign_up_giftcode_campaign_name"
,"first_post_sign_up_discount_context"
,"last_post_sign_up_discount_context"
,"is_introductory_discount"
,"min_discount_percentage"
,"max_discount_percentage"
,"discount_percentage_distinct_count"::INT AS "discount_percentage_distinct_counts"
,"post_sign_up_giftcode_distinct_count"::INT AS "post_sign_up_giftcode_distinct_counts"
,"post_sign_up_giftcode_list"
,"post_sign_up_giftcode_campaign_name_distinct_count"::INT AS "post_sign_up_giftcode_campaign_name_distinct_counts"
,"post_sign_up_giftcode_campaign_name_list"
,"discount_effective_start_date"
,"discount_effective_start_week"
,"discount_effective_end_date"
,"discount_effective_end_week"
,"discount_duration_days"::INT AS "discount_duration_day"
,"discount_duration_weeks"::INT AS "discount_duration_week"
,"discount_data"
,"subscription_add_on_count"
,"has_subscription_add_on"
,"subscription_add_on_partner_ids"
,"subscription_add_on_effective_start_date"::DATE AS "subscription_add_on_effective_start_dates"
,"subscription_add_on_effective_end_date"
,"subscription_add_on_effective_start_week"
,"subscription_add_on_effective_end_week"
,"subscription_add_on_types"
,"subscription_attributed_fixture_id"
,"subscription_attributed_fixture_name"
,"subscription_attributed_fixture_start_date"
,"subscription_attributed_competition_id"
,"subscription_attributed_competition_name"
,"subscription_attributed_sport_id"
,"subscription_attributed_sport_name"
,"subscription_attributed_ruleset_name"
,"subscription_attribution_has_ppv_required_entitlement"
,"is_final_content_attribution"
,"crm_account_id"
,"dazn_user_id"
,"billing_account_currency_code"
,"billing_acount_created_date"
,"billing_account_has_advanced_payment_manager"
,"billing_account_is_batch_50"
,"user_account_created_date"
,"user_account_created_week"
,"billing_account__skey"
,"subscription_info__skey"
,"subscription_term__skey"
,"subscription_free_trial__skey"
,"subscription_pause__skey"
,"subscription_add_on__skey"
,"subscription_content_attribution__skey"
,"subscription_source_system_name_derived__skey"
,"subscription_tracking_id__skey"
,"subscription_sign_up_campaign_id__skey"
,"first_post_sign_up_giftcode_campaign_name__skey"
,"last_post_sign_up_giftcode_campaign_name__skey"
,"discounted_monthly_recurring_revenue"
,"subscription_churn_type"
,"data_source"
,"subscription_start_timestamp"
,"subscription_end_timestamp"
, "subscription_free_trial_start_timestamp"
,"subscription_free_trial_end_timestamp" 
FROM union_all
)

SELECT 
CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
,"subscription_id"
,"subscription_name"
,"subscription_statuss" AS "subscription_status"
,"is_soft_cancelled"
,"subscription_versions" AS "subscription_version"
,"subscription_name_original_created_timestamp"
,"subscription_id_created_timestamp"
,"subscription_id_updated_timestamp"
,"subscription_start_date"
,"subscription_end_date"
,"subscription_start_week"
,"subscription_end_week"
,"billing_account_id"
,MIN("subscription_name_original_created_timestamp") OVER (PARTITION BY "billing_account_id", "subscription_product_group")::DATE AS "min_billing_account_subscription_name_created_date"
,LAST_DAY("min_billing_account_subscription_name_created_date",'WEEK') AS "min_billing_account_subscription_name_created_week"
,DENSE_RANK() OVER (PARTITION BY "billing_account_id" ORDER BY "subscription_name_original_created_timestamp" ASC) AS "billing_account_trip_number"
,DENSE_RANK() OVER (PARTITION BY "billing_account_id", "subscription_product_group" ORDER BY "subscription_name_original_created_timestamp" ASC) AS "billing_account_product_group_trip_number"
,"billing_account_product_group_trip_number" > 1 AS "is_resubscription"
,"subscription_direct_carrier_billing_carrier_name"
,"subscription_source_system_name"
,"subscription_source_system_name_derived"
,"subscription_sign_up_campaign_id"
,"subscription_sign_up_giftcode"
,"subscription_tracking_id"
,"subscription_term_type"
,"subscription_product_group"
,"subscription_payment_method_id"
,"subscription_country"
,"subscription_territory"
,"subscription_billing_period"
,CASE WHEN "data_source"='ZUORA' THEN "subscription_typee" 
ELSE IFNULL("subscription_typee",LAG("subscription_typee") OVER (PARTITION BY "subscription_name" ORDER BY "subscription_version")) END AS "subscription_type"
,"subscription_tier"
,"subscription_monthly_recurring_revenue"
,CASE WHEN "data_source"='ZUORA' THEN "previous_subscription_monthly_recurring_revenue" 
ELSE LAG("subscription_monthly_recurring_revenue") OVER (PARTITION BY "subscription_name" ORDER BY "subscription_version" ) END AS "previous_subscription_monthly_recurring_revenue"
,"subscription_bill_cycle_days" AS "subscription_bill_cycle_day"
,"subscription_rateplan_name"
,"subscription_rateplan_charge_name"
,"subscription_rateplan_charge_effective_start_date"
,"subscription_rateplan_charge_effective_end_date"
,"subscription_rateplan_charge_charged_through_date"
,"subscription_auto_renew"
,"subscription_instalment_periods" AS "subscription_instalment_period"
,"has_free_trial"
,"subscription_free_trial_start_date"
,"subscription_free_trial_end_date"
,"subscription_free_trial_length_days_advertised"
,"subscription_free_trial_length_days_actual"
,"subscription_free_trial_length_months_advertised"
,"subscription_free_trial_length_months_actual"
,"subscription_intro_discount_discount_percentage"
,"subscription_intro_discount_effective_from_date"
,"subscription_intro_discount_effective_until_date"
,"subscription_intro_discount_discount_duration_day" AS "subscription_intro_discount_discount_duration_days"
,"subscription_intro_discount_discount_duration_month" AS "subscription_intro_discount_discount_duration_months"
,"has_pause_period"
,"subscription_pause_start_date"
,"subscription_pause_end_date"
,"subscription_pause_start_week"
,"subscription_pause_end_week"
,"pause_duration_week" AS "pause_duration_weeks"
,"first_post_sign_up_giftcode"
,"last_post_sign_up_giftcode"
,"first_post_sign_up_giftcode_campaign_name"
,"last_post_sign_up_giftcode_campaign_name"
,"first_post_sign_up_discount_context"
,"last_post_sign_up_discount_context"
,"is_introductory_discount"
,"min_discount_percentage"
,"max_discount_percentage"
,"discount_percentage_distinct_counts" AS "discount_percentage_distinct_count"
,"post_sign_up_giftcode_distinct_counts" AS "post_sign_up_giftcode_distinct_count"
,"post_sign_up_giftcode_list"
,"post_sign_up_giftcode_campaign_name_distinct_counts" AS "post_sign_up_giftcode_campaign_name_distinct_count"
,"post_sign_up_giftcode_campaign_name_list"
,"discount_effective_start_date"
,"discount_effective_start_week"
,"discount_effective_end_date"
,"discount_effective_end_week"
,"discount_duration_day" AS "discount_duration_days"
,"discount_duration_week" AS "discount_duration_weeks"
,"discount_data"
,"subscription_add_on_count"
,"has_subscription_add_on"
,"subscription_add_on_partner_ids"
,"subscription_add_on_effective_start_dates" AS "subscription_add_on_effective_start_date"
,"subscription_add_on_effective_end_date"
,"subscription_add_on_effective_start_week"
,"subscription_add_on_effective_end_week"
,"subscription_add_on_types"
,"subscription_attributed_fixture_id"
,"subscription_attributed_fixture_name"
,"subscription_attributed_fixture_start_date"
,"subscription_attributed_competition_id"
,"subscription_attributed_competition_name"
,"subscription_attributed_sport_id"
,"subscription_attributed_sport_name"
,"subscription_attributed_ruleset_name"
,"subscription_attribution_has_ppv_required_entitlement"
,"is_final_content_attribution"
,"crm_account_id"
,"dazn_user_id"
,"billing_account_currency_code"
,"billing_acount_created_date"
,"billing_account_has_advanced_payment_manager"
,"billing_account_is_batch_50"
,"user_account_created_date"
,"user_account_created_week"
,HASH("subscription_country"
            ,"subscription_territory"
            ,"billing_account_currency_code"
            ,"billing_account_is_batch_50"
            ,"min_billing_account_subscription_name_created_week"
            ,"billing_account_has_advanced_payment_manager"
            ,"user_account_created_week")
            AS "billing_account__skey"
        ,HASH("is_soft_cancelled"
            ,"subscription_tier"
            ,"billing_account_product_group_trip_number"
            ,"is_resubscription"
            ,"subscription_product_group"
            ,"subscription_auto_renew"
            ,"subscription_instalment_period")
            AS "subscription_info__skey"
        ,HASH("subscription_start_week"
            ,"subscription_end_week")
            AS "subscription_term__skey"
        ,HASH("has_free_trial"
            ,"subscription_free_trial_length_days_advertised"
            ,"subscription_free_trial_length_months_advertised"
            ,"subscription_free_trial_end_date")
            AS "subscription_free_trial__skey"
        ,HASH("has_pause_period"
            ,"subscription_pause_start_week"
            ,"subscription_pause_end_week"
            ,"pause_duration_weeks")
            AS "subscription_pause__skey"
        ,HASH("has_subscription_add_on"
            ,"subscription_add_on_partner_ids"
            ,"subscription_add_on_count"
            ,"subscription_add_on_effective_start_week"
            ,"subscription_add_on_effective_end_week"
            ,"subscription_add_on_types")
            AS "subscription_add_on__skey"
        ,HASH( "subscription_attributed_fixture_id"
            ,"subscription_attributed_fixture_name"
            ,"subscription_attributed_fixture_start_date"
            ,"subscription_attributed_competition_id"
            ,"subscription_attributed_competition_name"
            ,"subscription_attributed_sport_id"
            ,"subscription_attributed_sport_name"
            ,"subscription_attributed_ruleset_name"
            ,"subscription_attribution_has_ppv_required_entitlement"
            ,"is_final_content_attribution")
            AS "subscription_content_attribution__skey"
,"subscription_source_system_name_derived__skey"
,"subscription_tracking_id__skey"
,"subscription_sign_up_campaign_id__skey"
,"first_post_sign_up_giftcode_campaign_name__skey"
,"last_post_sign_up_giftcode_campaign_name__skey"
,"discounted_monthly_recurring_revenue"
,"subscription_churn_type"
,"data_source"
,"subscription_start_timestamp"
,"subscription_end_timestamp"
, "subscription_free_trial_start_timestamp"
,"subscription_free_trial_end_timestamp" 
FROM final 
