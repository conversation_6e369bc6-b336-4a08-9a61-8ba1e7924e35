version: 2

models:
  - name: zr_subscription_id__dim
    description: "Dimension table surfacing all Zuora Subscription to Rateplan Charge level data points relating to the Subscription ID"
    columns:
      - name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the flow"
        quote: true

      - name: subscription_id
        description: "The ID of the Subscription, Subscription Names can have multiple IDs relating to different types of changes to the Subscription"
        quote: true
        tests:
          - unique:
              config:
                severity: error
                error_if: ">10000"
                warn_if: ">0"

      - name: subscription_name
        description: "The Name of the Subscription E.g. A-S15283950"
        quote: true

      - name: subscription_status
        description: "The current status of the Subscription ID E.g. Active, Cancelled, Expired, ..."
        quote: true

      - name: is_soft_cancelled
        description: "The status of the subscription is cancelled and it won't renew on the current end date"
        quote: true

      - name: subscription_version
        description: "The version of the Subscription ID"
        quote: true

      - name: subscription_name_original_created_timestamp
        description: "The timestamp the Subscription Name was created in Zuora"
        quote: true

      - name: subscription_id_created_timestamp
        description: "The timestamp the Subscription ID was created in Zuora"
        quote: true

      - name: subscription_id_updated_timestamp
        description: "The last timestamp the Subscription ID was updated in Zuora"
        quote: true

      - name: subscription_start_date
        description: "The date the subscription starts, including any free trial"
        quote: true

      - name: subscription_end_date
        description: "The latest date Zuora has seen this subscription end, taking into account retrospective cancellation (when the cancel date is before the subscription is updated)"
        quote: true

      - name: subscription_start_week
        description: "The week end date the subscription starts, including any free trial"
        quote: true

      - name: subscription_end_week
        description: "The latest week end date Zuora has seen this subscription end, taking into account retrospective cancellation (when the cancel date is before the subscription is updated)"
        quote: true

      - name: billing_account_id
        description: "The Zuora Account ID coming from the Zuora Account entity E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: min_billing_account_subscription_name_created_date
        description: "The date the first ever subscription was created for the billing account within the subscriptions product group"
        quote: true

      - name: min_billing_account_subscription_name_created_week
        description: "The week end date the first ever subscription was created for the billing account within the subscriptions product group"
        quote: true

      - name: billing_account_trip_number
        description: "The count of previous subscriptions from this billing account including this subscription for all product groups"
        quote: true

      - name: billing_account_product_group_trip_number
        description: "The count of previous subscriptions from this billing account and product group including this subscription"
        quote: true

      - name: is_resubscription
        description: "Identifies if this is a resubscription for this product group or not"
        quote: true

      - name: subscription_direct_carrier_billing_carrier_name
        description: "The Carrier name of the Subscription if it's a DCB source system E.g. Telus, this is NULL if it's not DCB"
        quote: true

      - name: subscription_source_system_name
        description: "The name of the partner managing the subscription externally without DCB split out into their carriers. E.g. Apple, Telefonica, DCB_Boku"
        quote: true

      - name: subscription_source_system_name_derived
        description: "A combination of subscription_source_system_name and subscription_direct_carrier_billing_carrier_name to replace subscription_direct_carrier_billing_carrier_name when its empty"
        quote: true

      - name: subscription_sign_up_campaign_id
        description: "The campaign ID of the giftcode, if any"
        quote: true

      - name: subscription_sign_up_giftcode
        description: "The giftcode applied to the subscription on sign-up, if any"
        quote: true

      - name: subscription_tracking_id
        description: "The tracking ID for this subscription ID, if any"
        quote: true

      - name: subscription_term_type
        description: "The term type of the subscription, can either be termed (contract with a fixed period of time) or evergreen (contract constantly renews and is ongoing with no fixed term"
        quote: true

      - name: subscription_product_group
        description: "Can be either 'NFL' whereas it is an NFL subscription, or 'DAZN' otherwise."
        quote: true

      - name: subscription_payment_method_id
        description: "The ID of the payment method used by the customer at the time of the addon purchase"
        quote: true

      - name: subscription_country
        description: "The country of the subscription, originating from the Zuora contact. E.g. Spain, Germany, Japan, United States, United Kingdom, ..."
        quote: true

      - name: subscription_territory
        description: "The territory of the subscription, pulled in by a join to region_dimension. E.g. Spain, DACH, Japan, United States, UK and Ireland, ..."
        quote: true

      - name: subscription_billing_period
        description: "The frequency the customer is billed, can either be Month or Subscription Term"
        quote: true

      - name: subscription_type
        description: "A derived field from the term type and the billing period, or external giftcodes, to categorise the subscription type, can be Monthly, Annual, Instalment or Externally Billed"
        quote: true

      - name: subscription_tier
        description: "The Entitlement Set Id of the Rateplan, which is filtered to the associated RatePlanCharge's Flat Fee Pricing Model and Recurring Type, so producing the Tier for the Subscription"
        quote: true

      - name: subscription_monthly_recurring_revenue
        description: "The monthly recurring revenue, which is the amount the customer pays for their subscription ignoring Discounts and PPV, adjusting the subscription length down to one month and rounded to two decimal places"
        quote: true

      - name: previous_subscription_monthly_recurring_revenue
        description: "The subscription_monthly_recurring_revenue of the previously effective RatePlanCharge on the Subscription ID"
        quote: true

      - name: subscription_bill_cycle_day
        description: "The billing cycle day of the month that the customer is charged"
        quote: true

      - name: subscription_rateplan_name
        description: "The name of the RatePlan directly relating to this requested Sub Type/MRR/Tier of this Subscription ID"
        quote: true

      - name: subscription_rateplan_charge_name
        description: "The name of the RatePlanCharge directly relating to this requested Sub Type/MRR/Tier of this Subscription ID"
        quote: true

      - name: subscription_rateplan_charge_effective_start_date
        description: "The date the most recently effective RatePlanCharge, associated to the subscription ID, becomes effective, which could be after the subscription id is created for a change request in the future"
        quote: true

      - name: subscription_rateplan_charge_effective_end_date
        description: "The date the most recently effective RatePlanCharge, associated to the subscription ID, stops being effective"
        quote: true

      - name: subscription_rateplan_charge_charged_through_date
        description: "To identify the next billing cycle date"
        quote: true

      - name: subscription_auto_renew
        description: "TBC"
        quote: true

      - name: subscription_instalment_period
        description: "TBC"
        quote: true

      - name: has_free_trial
        description: "A flag to indicate if the subscription is a free trial"
        quote: true

      - name: subscription_free_trial_start_date
        description: "The date the Free Trial starts"
        quote: true

      - name: subscription_free_trial_end_date
        description: "The date the Free Trial ends"
        quote: true

      - name: subscription_free_trial_length_days_advertised
        description: "The length in days of the Free Trial as advertised to the user, so calculated directly from the Zuora Subscription field. This can be 0 if there is no Free Trial."
        quote: true

      - name: subscription_free_trial_length_days_actual
        description: "The length in days the user actually has access to DAZN as a Free Trial as calculated by the exact start/end timestamps of the Free Trial taking into account the buffer period. This can be 0 if there is no Free Trial."
        quote: true

      - name: subscription_free_trial_length_months_advertised
        description: "The length in months of the Free Trial as advertised to the user, so calculated directly from the Zuora Subscription field. This can be 0 if there is no Free Trial."
        quote: true

      - name: subscription_free_trial_length_months_actual
        description: "The length in months the user actually has access to DAZN as a Free Trial as calculated by the exact start/end timestamps of the Free Trial taking into account the buffer period. This can be 0 if there is no Free Trial."
        quote: true

      - name: subscription_intro_discount_discount_percentage
        description: "Percentage discount on their normal rateplan - 100 means a 100% discount, i.e. no charge"
        quote: true

      - name: subscription_intro_discount_effective_from_date
        description: "Date the discount is effective from"
        quote: true

      - name: subscription_intro_discount_effective_until_date
        description: "Date the discount is effective until"
        quote: true

      - name: subscription_intro_discount_discount_duration_days
        description: "Duration in days of the discount"
        quote: true

      - name: subscription_intro_discount_discount_duration_months
        description: "Duration in months of the discount"
        quote: true

      - name: has_pause_period
        description: "Flag to identify whether the subscription had a pause period"
        quote: true

      - name: subscription_pause_start_date
        description: "Date the most recent pause starts for the subscription id"
        quote: true

      - name: subscription_pause_end_date
        description: "Date the most recent pause ends for the subscription id"
        quote: true

      - name: subscription_pause_start_week
        description: "Week of the most recent pause starts for the subscription id"
        quote: true

      - name: subscription_pause_end_week
        description: "Week of the most recent pause ends for the subscription id"
        quote: true

      - name: pause_duration_weeks
        description: "The duration in weeks of the most recent pause for the subscription id"
        quote: true

      - name: first_post_sign_up_giftcode
        description: "The first post-sign-up giftcode associated with this Subscription Id, looking at the most recently effective charge after the Subscription Id creation"
        quote: true

      - name: last_post_sign_up_giftcode
        description: "The last post-sign-up giftcode associated with this Subscription Id, looking at the farthest future effective charge after the Subscription Id creation"
        quote: true

      - name: first_post_sign_up_giftcode_campaign_name
        description: "The campaign name of the first post-sign-up giftcode associated with this Subscription Id, looking at the most recently effective charge after the Subscription Id creation"
        quote: true

      - name: last_post_sign_up_giftcode_campaign_name
        description: "The campaign name of the first post-sign-up giftcode associated with this Subscription Id, looking at the farthest future effective charge after the Subscription Id creation"
        quote: true

      - name: first_post_sign_up_discount_context
        description: "Describes the first channel through which the discount was applied - CRM, in app, cancellation flow or via a customer service agent"
        quote: true

      - name: last_post_sign_up_discount_context
        description: "Describes the last channel through which the discount was applied - CRM, in app, cancellation flow or via a customer service agent"
        quote: true

      - name: is_introductory_discount
        description: "Boolean flag, true if the discount effective start is the same date as the subscription start"
        quote: true

      - name: min_discount_percentage
        description: "The minimum proportional discount applied to the subscription charges for the duration of the discount"
        quote: true

      - name: max_discount_percentage
        description: "The maximum proportional discount applied to the subscription charges for the duration of the discount"
        quote: true

      - name: discount_percentage_distinct_count
        description: "The distinct count of the amount of discounts that will ever be associated with this Subscription Id from the Subscription Id creation into the future"
        quote: true

      - name: post_sign_up_giftcode_distinct_count
        description: "The distinct count of the amount of post-sign-up giftcodes that will ever be associated with this Subscription Id from the Subscription Id creation into the future"
        quote: true

      - name: post_sign_up_giftcode_list
        description: "An array containing the list of all unique post-sign-up giftcodes associated with this Subscription Id from the Subscription Id creation into the future"
        quote: true

      - name: post_sign_up_giftcode_campaign_name_distinct_count
        description: "The distinct count of the amount of post-sign-up giftcode campaigns that will ever be associated with this Subscription Id from the Subscription Id creation into the future, different giftcodes could be associated with the same campaign"
        quote: true

      - name: post_sign_up_giftcode_campaign_name_list
        description: "An array containing the list of all unique post-sign-up giftcode campaigns associated with this Subscription Id from the Subscription Id creation into the future"
        quote: true

      - name: discount_effective_start_date
        description: "The very first date that any discount becomes effective on this Subscription Id, after the creation of the Subscription Id"
        quote: true

      - name: discount_effective_start_week
        description: "The week of the very first date that the discount will be effective on this Subscription Id"
        quote: true

      - name: discount_effective_end_date
        description: "The very last date that the discount will be effective on this Subscription Id"
        quote: true

      - name: discount_effective_end_week
        description: "The week of the very last date that the discount will be effective on this Subscription Id"
        quote: true

      - name: discount_duration_days
        description: "The duration (in days) of the discount applied to the subscription"
        quote: true

      - name: discount_duration_weeks
        description: "The duration (in weeks) of the discount applied to the subscription"
        quote: true

      - name: discount_data
        description: "An array containing a dictionary of all details for any discount effective from the creation of the Subscription Id"
        quote: true

      - name: subscription_add_on_count
        description: "Number of unique AddOns that are active at the time the Subscription Id is created"
        quote: true

      - name: has_subscription_add_on
        description: "A flag for if there is any AddOn active at the time the Subscription Id is created"
        quote: true

      - name: subscription_add_on_partner_ids
        description: "An array containing distinct values of the addon partner IDs a subscription ID has"
        quote: true

      - name: subscription_add_on_effective_start_date
        description: "The minimum start date that the add on is effective from"
        quote: true

      - name: subscription_add_on_effective_end_date
        description: "The minimum end date that the add on is effective until"
        quote: true

      - name: subscription_add_on_effective_start_week
        description: "The minimum start week that the add on is effective from"
        quote: true

      - name: subscription_add_on_effective_end_week
        description: "The maximum end week that the add on is effective until"
        quote: true

      - name: subscription_attributed_fixture_id
        description: "The ID of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_fixture_name
        description: "The name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_fixture_start_date
        description: "The start date of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_competition_id
        description: "The competition ID of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_competition_name
        description: "The competition name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_sport_id
        description: "The sport ID of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_sport_name
        description: "The sport name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_ruleset_name
        description: "The ruleset name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attribution_has_ppv_required_entitlement
        description: "A flag to indicate if the content attributed was relating to an event that had a ppv entitlement required"
        quote: true

      - name: is_final_content_attribution
        description: "A flag to indicate if the attribution window has now passed for this fixture and so the attributed content can not change"
        quote: true

      - name: has_subscription_add_on
        description: "A flag for if there is any AddOn active at the time the Subscription Id is created"
        quote: true

      - name: crm_account_id
        description: "The Salesforce Account ID coming from Zuora Account entity E.g 0011o00001m6OyEAAU"
        quote: true

      - name: dazn_user_id
        description: "The DAZN User Id of the Account E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: billing_account_currency_code
        description: "The currency code for the Zuora Account, E.g. EUR, USD, ..."
        quote: true

      - name: billing_acount_created_date
        description: "The Date  the billing account is created at, this is pulled from the account object"
        quote: true

      - name: billing_account_has_advanced_payment_manager
        description: "A flag for if subscriptions on the same account have different payment methods"
        quote: true

      - name: billing_account_is_batch_50
        description: "Boolean flag, true if the account has ever been on Batch50, which implies it is a test account"
        quote: true

      - name: user_account_created_date
        description: "The minimum date we see the users being created in our systems, derived from Salesforce before migration to Segment"
        quote: true

      - name: user_account_created_week
        description: "The minimum week_end_date we see the users being created in our systems, derived from Salesforce before migration to Segment"
        quote: true

      - name: billing_account__skey
        description: "The surrogate key (skey) used to join on the billing_account dim"
        quote: true

      - name: subscription_info__skey
        description: "The surrogate key (skey) used to join on the subscription_info dim"
        quote: true

      - name: subscription_term__skey
        description: "The surrogate key (skey) used to join on the subscription_term dim"
        quote: true

      - name: subscription_free_trial__skey
        description: "The surrogate key (skey) used to join on the subscription_free_trial dim"
        quote: true

      - name: subscription_pause__skey
        description: "The surrogate key (skey) used to join on the subscription_pause dim"
        quote: true

      - name: subscription_add_on__skey
        description: "The surrogate key (skey) used to join on the subscription_add_on dim"
        quote: true

      - name: subscription_content_attribution__skey
        description: "The surrogate key (skey) used to join on the subscription_content_attribution__dim"
        quote: true

      - name: subscription_source_system_name_derived__skey
        description: "The surrogate key (skey) used to join on the sub_source_system dim"
        quote: true

      - name: subscription_tracking_id__skey
        description: "The surrogate key (skey) used to join on the tracking_id dim"
        quote: true

      - name: subscription_sign_up_campaign_id__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: first_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: last_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: discounted_monthly_recurring_revenue
        description: ""
        quote: true

      - name: subscription_churn_type
        description: ""
        quote: true

      - name: data_source
        description: ""
        quote: true

  - name: dmp_subscription_id__dim
    description: "Dimension table surfacing all EV Subscription to Rateplan Charge level data points relating to the Subscription ID"
    columns:
      - name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the flow"
        quote: true

      - name: subscription_id
        description: "The ID of the Subscription, Subscription Names can have multiple IDs relating to different types of changes to the Subscription"
        quote: true
        tests:
          - unique:
              config:
                severity: error
                error_if: ">10000"
                warn_if: ">0"

      - name: subscription_name
        description: "The Name of the Subscription"
        quote: true

      - name: subscription_status
        description: "The current status of the Subscription ID E.g. Active, Cancelled, Expired, ..."
        quote: true

      - name: is_soft_cancelled
        description: "The status of the subscription is cancelled and it won't renew on the current end date"
        quote: true

      - name: subscription_version
        description: "The version of the Subscription ID"
        quote: true

      - name: subscription_name_original_created_timestamp
        description: "The timestamp the Subscription Name was created in EV"
        quote: true

      - name: subscription_id_created_timestamp
        description: "The timestamp the Subscription ID was created in EV"
        quote: true

      - name: subscription_id_updated_timestamp
        description: "The last timestamp the Subscription ID was updated in EV"
        quote: true

      - name: subscription_start_date
        description: "The date the subscription starts, including any free trial"
        quote: true

      - name: subscription_end_date
        description: "The latest date EV has seen this subscription end, taking into account retrospective cancellation (when the cancel date is before the subscription is updated)"
        quote: true

      - name: subscription_start_week
        description: "The week end date the subscription starts, including any free trial"
        quote: true

      - name: subscription_end_week
        description: "The latest week end date EV has seen this subscription end, taking into account retrospective cancellation (when the cancel date is before the subscription is updated)"
        quote: true

      - name: billing_account_id
        description: "The EV Account ID coming from the EV Account entity"
        quote: true

      - name: min_billing_account_subscription_name_created_date
        description: "The date the first ever subscription was created for the billing account within the subscriptions product group"
        quote: true

      - name: min_billing_account_subscription_name_created_week
        description: "The week end date the first ever subscription was created for the billing account within the subscriptions product group"
        quote: true

      - name: billing_account_trip_number
        description: "The count of previous subscriptions from this billing account including this subscription for all product groups"
        quote: true

      - name: billing_account_product_group_trip_number
        description: "The count of previous subscriptions from this billing account and product group including this subscription"
        quote: true

      - name: is_resubscription
        description: "Identifies if this is a resubscription for this product group or not"
        quote: true

      - name: subscription_direct_carrier_billing_carrier_name
        description: "The Carrier name of the Subscription if it's a DCB source system E.g. Telus, this is NULL if it's not DCB"
        quote: true

      - name: subscription_source_system_name
        description: "The name of the partner managing the subscription externally without DCB split out into their carriers. E.g. Apple, Telefonica, DCB_Boku"
        quote: true

      - name: subscription_source_system_name_derived
        description: "A combination of subscription_source_system_name and subscription_direct_carrier_billing_carrier_name to replace subscription_direct_carrier_billing_carrier_name when its empty"
        quote: true

      - name: subscription_sign_up_campaign_id
        description: "The campaign ID of the giftcode, if any"
        quote: true

      - name: subscription_sign_up_giftcode
        description: "The giftcode applied to the subscription on sign-up, if any"
        quote: true

      - name: subscription_tracking_id
        description: "The tracking ID for this subscription ID, if any"
        quote: true

      - name: subscription_term_type
        description: "The term type of the subscription, can either be termed (contract with a fixed period of time) or evergreen (contract constantly renews and is ongoing with no fixed term"
        quote: true

      - name: subscription_product_group
        description: "Can be either 'NFL' whereas it is an NFL subscription, or 'DAZN' otherwise."
        quote: true

      - name: subscription_payment_method_id
        description: "The ID of the payment method used by the customer at the time of the addon purchase"
        quote: true

      - name: subscription_country
        description: "The country of the subscription, originating from the EV contact. E.g. Spain, Germany, Japan, United States, United Kingdom, ..."
        quote: true

      - name: subscription_territory
        description: "The territory of the subscription, pulled in by a join to region_dimension. E.g. Spain, DACH, Japan, United States, UK and Ireland, ..."
        quote: true

      - name: subscription_billing_period
        description: "The frequency the customer is billed, can either be Month or Subscription Term"
        quote: true

      - name: subscription_type
        description: "A derived field from the term type and the billing period, or external giftcodes, to categorise the subscription type, can be Monthly, Annual, Instalment or Externally Billed"
        quote: true

      - name: subscription_tier
        description: "The Entitlement Set Id of the Rateplan, which is filtered to the associated RatePlanCharge's Flat Fee Pricing Model and Recurring Type, so producing the Tier for the Subscription"
        quote: true

      - name: subscription_monthly_recurring_revenue
        description: "The monthly recurring revenue, which is the amount the customer pays for their subscription ignoring Discounts and PPV, adjusting the subscription length down to one month and rounded to two decimal places"
        quote: true

      - name: previous_subscription_monthly_recurring_revenue
        description: "The subscription_monthly_recurring_revenue of the previously effective RatePlanCharge on the Subscription ID"
        quote: true

      - name: subscription_bill_cycle_day
        description: "The billing cycle day of the month that the customer is charged"
        quote: true

      - name: subscription_rateplan_name
        description: "The name of the RatePlan directly relating to this requested Sub Type/MRR/Tier of this Subscription ID"
        quote: true

      - name: subscription_rateplan_charge_name
        description: "The name of the RatePlanCharge directly relating to this requested Sub Type/MRR/Tier of this Subscription ID"
        quote: true

      - name: subscription_rateplan_charge_effective_start_date
        description: "The date the most recently effective RatePlanCharge, associated to the subscription ID, becomes effective, which could be after the subscription id is created for a change request in the future"
        quote: true

      - name: subscription_rateplan_charge_effective_end_date
        description: "The date the most recently effective RatePlanCharge, associated to the subscription ID, stops being effective"
        quote: true

      - name: subscription_rateplan_charge_charged_through_date
        description: "To identify the next billing cycle date"
        quote: true

      - name: subscription_auto_renew
        description: "TBC"
        quote: true

      - name: subscription_instalment_period
        description: "TBC"
        quote: true

      - name: has_free_trial
        description: "A flag to indicate if the subscription is a free trial"
        quote: true

      - name: subscription_free_trial_start_date
        description: "The date the Free Trial starts"
        quote: true

      - name: subscription_free_trial_end_date
        description: "The date the Free Trial ends"
        quote: true

      - name: subscription_free_trial_length_days_advertised
        description: "The length in days of the Free Trial as advertised to the user, so calculated directly from the EV Subscription field. This can be 0 if there is no Free Trial."
        quote: true

      - name: subscription_free_trial_length_days_actual
        description: "The length in days the user actually has access to DAZN as a Free Trial as calculated by the exact start/end timestamps of the Free Trial taking into account the buffer period. This can be 0 if there is no Free Trial."
        quote: true

      - name: subscription_free_trial_length_months_advertised
        description: "The length in months of the Free Trial as advertised to the user, so calculated directly from the EV Subscription field. This can be 0 if there is no Free Trial."
        quote: true

      - name: subscription_free_trial_length_months_actual
        description: "The length in months the user actually has access to DAZN as a Free Trial as calculated by the exact start/end timestamps of the Free Trial taking into account the buffer period. This can be 0 if there is no Free Trial."
        quote: true

      - name: subscription_intro_discount_discount_percentage
        description: "Percentage discount on their normal rateplan - 100 means a 100% discount, i.e. no charge"
        quote: true

      - name: subscription_intro_discount_effective_from_date
        description: "Date the discount is effective from"
        quote: true

      - name: subscription_intro_discount_effective_until_date
        description: "Date the discount is effective until"
        quote: true

      - name: subscription_intro_discount_discount_duration_days
        description: "Duration in days of the discount"
        quote: true

      - name: subscription_intro_discount_discount_duration_months
        description: "Duration in months of the discount"
        quote: true

      - name: has_pause_period
        description: "Flag to identify whether the subscription had a pause period"
        quote: true

      - name: subscription_pause_start_date
        description: "Date the most recent pause starts for the subscription id"
        quote: true

      - name: subscription_pause_end_date
        description: "Date the most recent pause ends for the subscription id"
        quote: true

      - name: subscription_pause_start_week
        description: "Week of the most recent pause starts for the subscription id"
        quote: true

      - name: subscription_pause_end_week
        description: "Week of the most recent pause ends for the subscription id"
        quote: true

      - name: pause_duration_weeks
        description: "The duration in weeks of the most recent pause for the subscription id"
        quote: true

      - name: first_post_sign_up_giftcode
        description: "The first post-sign-up giftcode associated with this Subscription Id, looking at the most recently effective charge after the Subscription Id creation"
        quote: true

      - name: last_post_sign_up_giftcode
        description: "The last post-sign-up giftcode associated with this Subscription Id, looking at the farthest future effective charge after the Subscription Id creation"
        quote: true

      - name: first_post_sign_up_giftcode_campaign_name
        description: "The campaign name of the first post-sign-up giftcode associated with this Subscription Id, looking at the most recently effective charge after the Subscription Id creation"
        quote: true

      - name: last_post_sign_up_giftcode_campaign_name
        description: "The campaign name of the first post-sign-up giftcode associated with this Subscription Id, looking at the farthest future effective charge after the Subscription Id creation"
        quote: true

      - name: first_post_sign_up_discount_context
        description: "Describes the first channel through which the discount was applied - CRM, in app, cancellation flow or via a customer service agent"
        quote: true

      - name: last_post_sign_up_discount_context
        description: "Describes the last channel through which the discount was applied - CRM, in app, cancellation flow or via a customer service agent"
        quote: true

      - name: is_introductory_discount
        description: "Boolean flag, true if the discount effective start is the same date as the subscription start"
        quote: true

      - name: min_discount_percentage
        description: "The minimum proportional discount applied to the subscription charges for the duration of the discount"
        quote: true

      - name: max_discount_percentage
        description: "The maximum proportional discount applied to the subscription charges for the duration of the discount"
        quote: true

      - name: discount_percentage_distinct_count
        description: "The distinct count of the amount of discounts that will ever be associated with this Subscription Id from the Subscription Id creation into the future"
        quote: true

      - name: post_sign_up_giftcode_distinct_count
        description: "The distinct count of the amount of post-sign-up giftcodes that will ever be associated with this Subscription Id from the Subscription Id creation into the future"
        quote: true

      - name: post_sign_up_giftcode_list
        description: "An array containing the list of all unique post-sign-up giftcodes associated with this Subscription Id from the Subscription Id creation into the future"
        quote: true

      - name: post_sign_up_giftcode_campaign_name_distinct_count
        description: "The distinct count of the amount of post-sign-up giftcode campaigns that will ever be associated with this Subscription Id from the Subscription Id creation into the future, different giftcodes could be associated with the same campaign"
        quote: true

      - name: post_sign_up_giftcode_campaign_name_list
        description: "An array containing the list of all unique post-sign-up giftcode campaigns associated with this Subscription Id from the Subscription Id creation into the future"
        quote: true

      - name: discount_effective_start_date
        description: "The very first date that any discount becomes effective on this Subscription Id, after the creation of the Subscription Id"
        quote: true

      - name: discount_effective_start_week
        description: "The week of the very first date that the discount will be effective on this Subscription Id"
        quote: true

      - name: discount_effective_end_date
        description: "The very last date that the discount will be effective on this Subscription Id"
        quote: true

      - name: discount_effective_end_week
        description: "The week of the very last date that the discount will be effective on this Subscription Id"
        quote: true

      - name: discount_duration_days
        description: "The duration (in days) of the discount applied to the subscription"
        quote: true

      - name: discount_duration_weeks
        description: "The duration (in weeks) of the discount applied to the subscription"
        quote: true

      - name: discount_data
        description: "An array containing a dictionary of all details for any discount effective from the creation of the Subscription Id"
        quote: true

      - name: subscription_add_on_count
        description: "Number of unique AddOns that are active at the time the Subscription Id is created"
        quote: true

      - name: has_subscription_add_on
        description: "A flag for if there is any AddOn active at the time the Subscription Id is created"
        quote: true

      - name: subscription_add_on_partner_ids
        description: "An array containing distinct values of the addon partner IDs a subscription ID has"
        quote: true

      - name: subscription_add_on_effective_start_date
        description: "The minimum start date that the add on is effective from"
        quote: true

      - name: subscription_add_on_effective_end_date
        description: "The minimum end date that the add on is effective until"
        quote: true

      - name: subscription_add_on_effective_start_week
        description: "The minimum start week that the add on is effective from"
        quote: true

      - name: subscription_add_on_effective_end_week
        description: "The maximum end week that the add on is effective until"
        quote: true

      - name: subscription_attributed_fixture_id
        description: "The ID of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_fixture_name
        description: "The name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_fixture_start_date
        description: "The start date of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_competition_id
        description: "The competition ID of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_competition_name
        description: "The competition name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_sport_id
        description: "The sport ID of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_sport_name
        description: "The sport name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_ruleset_name
        description: "The ruleset name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attribution_has_ppv_required_entitlement
        description: "A flag to indicate if the content attributed was relating to an event that had a ppv entitlement required"
        quote: true

      - name: is_final_content_attribution
        description: "A flag to indicate if the attribution window has now passed for this fixture and so the attributed content can not change"
        quote: true

      - name: has_subscription_add_on
        description: "A flag for if there is any AddOn active at the time the Subscription Id is created"
        quote: true

      - name: crm_account_id
        description: "The Salesforce Account ID coming from EV Account entity"
        quote: true

      - name: dazn_user_id
        description: "The DAZN User Id of the Account E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: billing_account_currency_code
        description: "The currency code for the EV Account, E.g. EUR, USD, ..."
        quote: true

      - name: billing_acount_created_date
        description: "The Date  the billing account is created at, this is pulled from the account object"
        quote: true

      - name: billing_account_has_advanced_payment_manager
        description: "A flag for if subscriptions on the same account have different payment methods"
        quote: true

      - name: billing_account_is_batch_50
        description: "Boolean flag, true if the account has ever been on Batch50, which implies it is a test account"
        quote: true

      - name: user_account_created_date
        description: "The minimum date we see the users being created in our systems, derived from Salesforce before migration to Segment"
        quote: true

      - name: user_account_created_week
        description: "The minimum week_end_date we see the users being created in our systems, derived from Salesforce before migration to Segment"
        quote: true

      - name: billing_account__skey
        description: "The surrogate key (skey) used to join on the billing_account dim"
        quote: true

      - name: subscription_info__skey
        description: "The surrogate key (skey) used to join on the subscription_info dim"
        quote: true

      - name: subscription_term__skey
        description: "The surrogate key (skey) used to join on the subscription_term dim"
        quote: true

      - name: subscription_free_trial__skey
        description: "The surrogate key (skey) used to join on the subscription_free_trial dim"
        quote: true

      - name: subscription_pause__skey
        description: "The surrogate key (skey) used to join on the subscription_pause dim"
        quote: true

      - name: subscription_add_on__skey
        description: "The surrogate key (skey) used to join on the subscription_add_on dim"
        quote: true

      - name: subscription_content_attribution__skey
        description: "The surrogate key (skey) used to join on the subscription_content_attribution__dim"
        quote: true

      - name: subscription_source_system_name_derived__skey
        description: "The surrogate key (skey) used to join on the sub_source_system dim"
        quote: true

      - name: subscription_tracking_id__skey
        description: "The surrogate key (skey) used to join on the tracking_id dim"
        quote: true

      - name: subscription_sign_up_campaign_id__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: first_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: last_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: discounted_monthly_recurring_revenue
        description: ""
        quote: true

      - name: subscription_churn_type
        description: ""
        quote: true

      - name: data_source
        description: ""
        quote: true

  - name: subscription_id__dim
    description: "Dimension table surfacing all Zuora Subscription to Rateplan Charge level data points relating to the Subscription ID"
    columns:
      - name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the flow"
        quote: true

      - name: subscription_id
        description: "The ID of the Subscription, Subscription Names can have multiple IDs relating to different types of changes to the Subscription"
        quote: true
        tests:
          - unique:
              config:
                severity: error
                error_if: ">10000"
                warn_if: ">0"

      - name: subscription_name
        description: "The Name of the Subscription E.g. A-S15283950"
        quote: true

      - name: subscription_status
        description: "The current status of the Subscription ID E.g. Active, Cancelled, Expired, ..."
        quote: true

      - name: is_soft_cancelled
        description: "The status of the subscription is cancelled and it won't renew on the current end date"
        quote: true

      - name: subscription_version
        description: "The version of the Subscription ID"
        quote: true

      - name: subscription_name_original_created_timestamp
        description: "The timestamp the Subscription Name was created in Zuora"
        quote: true

      - name: subscription_id_created_timestamp
        description: "The timestamp the Subscription ID was created in Zuora"
        quote: true

      - name: subscription_id_updated_timestamp
        description: "The last timestamp the Subscription ID was updated in Zuora"
        quote: true

      - name: subscription_start_date
        description: "The date the subscription starts, including any free trial"
        quote: true

      - name: subscription_end_date
        description: "The latest date Zuora has seen this subscription end, taking into account retrospective cancellation (when the cancel date is before the subscription is updated)"
        quote: true

      - name: subscription_start_week
        description: "The week end date the subscription starts, including any free trial"
        quote: true

      - name: subscription_end_week
        description: "The latest week end date Zuora has seen this subscription end, taking into account retrospective cancellation (when the cancel date is before the subscription is updated)"
        quote: true

      - name: billing_account_id
        description: "The Zuora Account ID coming from the Zuora Account entity E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: min_billing_account_subscription_name_created_date
        description: "The date the first ever subscription was created for the billing account within the subscriptions product group"
        quote: true

      - name: min_billing_account_subscription_name_created_week
        description: "The week end date the first ever subscription was created for the billing account within the subscriptions product group"
        quote: true

      - name: billing_account_trip_number
        description: "The count of previous subscriptions from this billing account including this subscription for all product groups"
        quote: true

      - name: billing_account_product_group_trip_number
        description: "The count of previous subscriptions from this billing account and product group including this subscription"
        quote: true

      - name: is_resubscription
        description: "Identifies if this is a resubscription for this product group or not"
        quote: true

      - name: subscription_direct_carrier_billing_carrier_name
        description: "The Carrier name of the Subscription if it's a DCB source system E.g. Telus, this is NULL if it's not DCB"
        quote: true

      - name: subscription_source_system_name
        description: "The name of the partner managing the subscription externally without DCB split out into their carriers. E.g. Apple, Telefonica, DCB_Boku"
        quote: true

      - name: subscription_source_system_name_derived
        description: "A combination of subscription_source_system_name and subscription_direct_carrier_billing_carrier_name to replace subscription_direct_carrier_billing_carrier_name when its empty"
        quote: true

      - name: subscription_sign_up_campaign_id
        description: "The campaign ID of the giftcode, if any"
        quote: true

      - name: subscription_sign_up_giftcode
        description: "The giftcode applied to the subscription on sign-up, if any"
        quote: true

      - name: subscription_tracking_id
        description: "The tracking ID for this subscription ID, if any"
        quote: true

      - name: subscription_term_type
        description: "The term type of the subscription, can either be termed (contract with a fixed period of time) or evergreen (contract constantly renews and is ongoing with no fixed term"
        quote: true

      - name: subscription_product_group
        description: "Can be either 'NFL' whereas it is an NFL subscription, or 'DAZN' otherwise."
        quote: true

      - name: subscription_payment_method_id
        description: "The ID of the payment method used by the customer at the time of the addon purchase"
        quote: true

      - name: subscription_country
        description: "The country of the subscription, originating from the Zuora contact. E.g. Spain, Germany, Japan, United States, United Kingdom, ..."
        quote: true

      - name: subscription_territory
        description: "The territory of the subscription, pulled in by a join to region_dimension. E.g. Spain, DACH, Japan, United States, UK and Ireland, ..."
        quote: true

      - name: subscription_billing_period
        description: "The frequency the customer is billed, can either be Month or Subscription Term"
        quote: true

      - name: subscription_type
        description: "A derived field from the term type and the billing period, or external giftcodes, to categorise the subscription type, can be Monthly, Annual, Instalment or Externally Billed"
        quote: true

      - name: subscription_tier
        description: "The Entitlement Set Id of the Rateplan, which is filtered to the associated RatePlanCharge's Flat Fee Pricing Model and Recurring Type, so producing the Tier for the Subscription"
        quote: true

      - name: subscription_monthly_recurring_revenue
        description: "The monthly recurring revenue, which is the amount the customer pays for their subscription ignoring Discounts and PPV, adjusting the subscription length down to one month and rounded to two decimal places"
        quote: true

      - name: previous_subscription_monthly_recurring_revenue
        description: "The subscription_monthly_recurring_revenue of the previously effective RatePlanCharge on the Subscription ID"
        quote: true

      - name: subscription_bill_cycle_day
        description: "The billing cycle day of the month that the customer is charged"
        quote: true

      - name: subscription_rateplan_name
        description: "The name of the RatePlan directly relating to this requested Sub Type/MRR/Tier of this Subscription ID"
        quote: true

      - name: subscription_rateplan_charge_name
        description: "The name of the RatePlanCharge directly relating to this requested Sub Type/MRR/Tier of this Subscription ID"
        quote: true

      - name: subscription_rateplan_charge_effective_start_date
        description: "The date the most recently effective RatePlanCharge, associated to the subscription ID, becomes effective, which could be after the subscription id is created for a change request in the future"
        quote: true

      - name: subscription_rateplan_charge_effective_end_date
        description: "The date the most recently effective RatePlanCharge, associated to the subscription ID, stops being effective"
        quote: true

      - name: subscription_rateplan_charge_charged_through_date
        description: "To identify the next billing cycle date"
        quote: true

      - name: subscription_auto_renew
        description: "TBC"
        quote: true

      - name: subscription_instalment_period
        description: "TBC"
        quote: true

      - name: has_free_trial
        description: "A flag to indicate if the subscription is a free trial"
        quote: true

      - name: subscription_free_trial_start_date
        description: "The date the Free Trial starts"
        quote: true

      - name: subscription_free_trial_end_date
        description: "The date the Free Trial ends"
        quote: true

      - name: subscription_free_trial_length_days_advertised
        description: "The length in days of the Free Trial as advertised to the user, so calculated directly from the Zuora Subscription field. This can be 0 if there is no Free Trial."
        quote: true

      - name: subscription_free_trial_length_days_actual
        description: "The length in days the user actually has access to DAZN as a Free Trial as calculated by the exact start/end timestamps of the Free Trial taking into account the buffer period. This can be 0 if there is no Free Trial."
        quote: true

      - name: subscription_free_trial_length_months_advertised
        description: "The length in months of the Free Trial as advertised to the user, so calculated directly from the Zuora Subscription field. This can be 0 if there is no Free Trial."
        quote: true

      - name: subscription_free_trial_length_months_actual
        description: "The length in months the user actually has access to DAZN as a Free Trial as calculated by the exact start/end timestamps of the Free Trial taking into account the buffer period. This can be 0 if there is no Free Trial."
        quote: true

      - name: subscription_intro_discount_discount_percentage
        description: "Percentage discount on their normal rateplan - 100 means a 100% discount, i.e. no charge"
        quote: true

      - name: subscription_intro_discount_effective_from_date
        description: "Date the discount is effective from"
        quote: true

      - name: subscription_intro_discount_effective_until_date
        description: "Date the discount is effective until"
        quote: true

      - name: subscription_intro_discount_discount_duration_days
        description: "Duration in days of the discount"
        quote: true

      - name: subscription_intro_discount_discount_duration_months
        description: "Duration in months of the discount"
        quote: true

      - name: has_pause_period
        description: "Flag to identify whether the subscription had a pause period"
        quote: true

      - name: subscription_pause_start_date
        description: "Date the most recent pause starts for the subscription id"
        quote: true

      - name: subscription_pause_end_date
        description: "Date the most recent pause ends for the subscription id"
        quote: true

      - name: subscription_pause_start_week
        description: "Week of the most recent pause starts for the subscription id"
        quote: true

      - name: subscription_pause_end_week
        description: "Week of the most recent pause ends for the subscription id"
        quote: true

      - name: pause_duration_weeks
        description: "The duration in weeks of the most recent pause for the subscription id"
        quote: true

      - name: first_post_sign_up_giftcode
        description: "The first post-sign-up giftcode associated with this Subscription Id, looking at the most recently effective charge after the Subscription Id creation"
        quote: true

      - name: last_post_sign_up_giftcode
        description: "The last post-sign-up giftcode associated with this Subscription Id, looking at the farthest future effective charge after the Subscription Id creation"
        quote: true

      - name: first_post_sign_up_giftcode_campaign_name
        description: "The campaign name of the first post-sign-up giftcode associated with this Subscription Id, looking at the most recently effective charge after the Subscription Id creation"
        quote: true

      - name: last_post_sign_up_giftcode_campaign_name
        description: "The campaign name of the first post-sign-up giftcode associated with this Subscription Id, looking at the farthest future effective charge after the Subscription Id creation"
        quote: true

      - name: first_post_sign_up_discount_context
        description: "Describes the first channel through which the discount was applied - CRM, in app, cancellation flow or via a customer service agent"
        quote: true

      - name: last_post_sign_up_discount_context
        description: "Describes the last channel through which the discount was applied - CRM, in app, cancellation flow or via a customer service agent"
        quote: true

      - name: is_introductory_discount
        description: "Boolean flag, true if the discount effective start is the same date as the subscription start"
        quote: true

      - name: min_discount_percentage
        description: "The minimum proportional discount applied to the subscription charges for the duration of the discount"
        quote: true

      - name: max_discount_percentage
        description: "The maximum proportional discount applied to the subscription charges for the duration of the discount"
        quote: true

      - name: discount_percentage_distinct_count
        description: "The distinct count of the amount of discounts that will ever be associated with this Subscription Id from the Subscription Id creation into the future"
        quote: true

      - name: post_sign_up_giftcode_distinct_count
        description: "The distinct count of the amount of post-sign-up giftcodes that will ever be associated with this Subscription Id from the Subscription Id creation into the future"
        quote: true

      - name: post_sign_up_giftcode_list
        description: "An array containing the list of all unique post-sign-up giftcodes associated with this Subscription Id from the Subscription Id creation into the future"
        quote: true

      - name: post_sign_up_giftcode_campaign_name_distinct_count
        description: "The distinct count of the amount of post-sign-up giftcode campaigns that will ever be associated with this Subscription Id from the Subscription Id creation into the future, different giftcodes could be associated with the same campaign"
        quote: true

      - name: post_sign_up_giftcode_campaign_name_list
        description: "An array containing the list of all unique post-sign-up giftcode campaigns associated with this Subscription Id from the Subscription Id creation into the future"
        quote: true

      - name: discount_effective_start_date
        description: "The very first date that any discount becomes effective on this Subscription Id, after the creation of the Subscription Id"
        quote: true

      - name: discount_effective_start_week
        description: "The week of the very first date that the discount will be effective on this Subscription Id"
        quote: true

      - name: discount_effective_end_date
        description: "The very last date that the discount will be effective on this Subscription Id"
        quote: true

      - name: discount_effective_end_week
        description: "The week of the very last date that the discount will be effective on this Subscription Id"
        quote: true

      - name: discount_duration_days
        description: "The duration (in days) of the discount applied to the subscription"
        quote: true

      - name: discount_duration_weeks
        description: "The duration (in weeks) of the discount applied to the subscription"
        quote: true

      - name: discount_data
        description: "An array containing a dictionary of all details for any discount effective from the creation of the Subscription Id"
        quote: true

      - name: subscription_add_on_count
        description: "Number of unique AddOns that are active at the time the Subscription Id is created"
        quote: true

      - name: has_subscription_add_on
        description: "A flag for if there is any AddOn active at the time the Subscription Id is created"
        quote: true

      - name: subscription_add_on_partner_ids
        description: "An array containing distinct values of the addon partner IDs a subscription ID has"
        quote: true

      - name: subscription_add_on_effective_start_date
        description: "The minimum start date that the add on is effective from"
        quote: true

      - name: subscription_add_on_effective_end_date
        description: "The minimum end date that the add on is effective until"
        quote: true

      - name: subscription_add_on_effective_start_week
        description: "The minimum start week that the add on is effective from"
        quote: true

      - name: subscription_add_on_effective_end_week
        description: "The maximum end week that the add on is effective until"
        quote: true

      - name: subscription_attributed_fixture_id
        description: "The ID of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_fixture_name
        description: "The name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_fixture_start_date
        description: "The start date of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_competition_id
        description: "The competition ID of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_competition_name
        description: "The competition name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_sport_id
        description: "The sport ID of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_sport_name
        description: "The sport name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_ruleset_name
        description: "The ruleset name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attribution_has_ppv_required_entitlement
        description: "A flag to indicate if the content attributed was relating to an event that had a ppv entitlement required"
        quote: true

      - name: is_final_content_attribution
        description: "A flag to indicate if the attribution window has now passed for this fixture and so the attributed content can not change"
        quote: true

      - name: has_subscription_add_on
        description: "A flag for if there is any AddOn active at the time the Subscription Id is created"
        quote: true

      - name: crm_account_id
        description: "The Salesforce Account ID coming from Zuora Account entity E.g 0011o00001m6OyEAAU"
        quote: true

      - name: dazn_user_id
        description: "The DAZN User Id of the Account E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: billing_account_currency_code
        description: "The currency code for the Zuora Account, E.g. EUR, USD, ..."
        quote: true

      - name: billing_acount_created_date
        description: "The Date  the billing account is created at, this is pulled from the account object"
        quote: true

      - name: billing_account_has_advanced_payment_manager
        description: "A flag for if subscriptions on the same account have different payment methods"
        quote: true

      - name: billing_account_is_batch_50
        description: "Boolean flag, true if the account has ever been on Batch50, which implies it is a test account"
        quote: true

      - name: user_account_created_date
        description: "The minimum date we see the users being created in our systems, derived from Salesforce before migration to Segment"
        quote: true

      - name: user_account_created_week
        description: "The minimum week_end_date we see the users being created in our systems, derived from Salesforce before migration to Segment"
        quote: true

      - name: billing_account__skey
        description: "The surrogate key (skey) used to join on the billing_account dim"
        quote: true

      - name: subscription_info__skey
        description: "The surrogate key (skey) used to join on the subscription_info dim"
        quote: true

      - name: subscription_term__skey
        description: "The surrogate key (skey) used to join on the subscription_term dim"
        quote: true

      - name: subscription_free_trial__skey
        description: "The surrogate key (skey) used to join on the subscription_free_trial dim"
        quote: true

      - name: subscription_pause__skey
        description: "The surrogate key (skey) used to join on the subscription_pause dim"
        quote: true

      - name: subscription_add_on__skey
        description: "The surrogate key (skey) used to join on the subscription_add_on dim"
        quote: true

      - name: subscription_content_attribution__skey
        description: "The surrogate key (skey) used to join on the subscription_content_attribution__dim"
        quote: true

      - name: subscription_source_system_name_derived__skey
        description: "The surrogate key (skey) used to join on the sub_source_system dim"
        quote: true

      - name: subscription_tracking_id__skey
        description: "The surrogate key (skey) used to join on the tracking_id dim"
        quote: true

      - name: subscription_sign_up_campaign_id__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: first_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: last_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: discounted_monthly_recurring_revenue
        description: ""
        quote: true

      - name: subscription_churn_type
        description: ""
        quote: true

      - name: data_source
        description: ""
        quote: true
