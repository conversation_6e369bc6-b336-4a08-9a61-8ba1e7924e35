{{
    config(
        materialized='table',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XL_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH sub_discount_intermediate AS (
    SELECT * FROM {{ ref('subscription_discount_intermediate') }}
)

-- Using the raw (not filtered) as the source so that we can have nothing filtered out at all and pull back as much information as possible about these giftcodes/subscriptions
,daily_subs_raw AS (
    SELECT * FROM {{ ref('daily_subscriptions_raw') }}
)

-- Just using this as a source until we bring in the state correctly into the Billing Account Dim
,sub_id_contact_dim AS (
    SELECT * FROM {{ ref('subscription_id_contact__dim') }}
)

SELECT
    sub_discount_intermediate."subscription_discount__hash"
    ,sub_discount_intermediate."subscription_id"
    ,sub_discount_intermediate."subscription_name"
    ,sub_discount_intermediate."subscription_start_date"
    ,sub_discount_intermediate."subscription_discount_requested_timestamp"
    ,sub_discount_intermediate."is_sign_up_discount"
    ,sub_discount_intermediate."subscription_giftcode"
    ,sub_discount_intermediate."subscription_giftcode_campaign_name"
    ,sub_discount_intermediate."subscription_discount_context"
    ,sub_discount_intermediate."subscription_discount_percentage"
    ,sub_discount_intermediate."subscription_discount_amount"
    ,sub_discount_intermediate."subscription_discount_start_date"
    ,sub_discount_intermediate."subscription_discount_end_date"
    ,sub_discount_intermediate."subscription_discount_duration_days"
    ,sub_discount_intermediate."subscription_discount_duration_weeks"
    ,sub_discount_intermediate."subscription_discount_duration_months"
    ,sub_discount_intermediate."subscription_product_group"
    ,sub_discount_intermediate."billing_account_id"
    ,sub_discount_intermediate."subscription_discount_segment"
    ,daily_subs_raw."billing_account__skey"
    ,daily_subs_raw."subscription_info__skey"
    ,daily_subs_raw."subscription_term__skey"
    ,daily_subs_raw."subscription_charge__skey"
    ,daily_subs_raw."subscription_free_trial__skey"
    ,daily_subs_raw."subscription_pause__skey"
    ,daily_subs_raw."subscription_add_on__skey"
    ,daily_subs_raw."subscription_content_attribution__skey"
    ,daily_subs_raw."subscription_source_system_name_derived__skey"
    ,daily_subs_raw."subscription_tracking_id__skey"
    ,daily_subs_raw."subscription_daily_status__skey"
    ,daily_subs_raw."daily_usd_fx_rate__skey"
    -- Just using this as a source until we bring in the state correctly into the Billing Account Dim
    ,sub_id_contact_dim."contact_state" AS "subscription_state"
FROM sub_discount_intermediate
LEFT JOIN daily_subs_raw
    ON sub_discount_intermediate."subscription_name" = daily_subs_raw."subscription_name"
    AND sub_discount_intermediate."subscription_discount_requested_timestamp"::DATE = daily_subs_raw."batch_date"
-- Just using this as a source until we bring in the state correctly into the Billing Account Dim
LEFT JOIN sub_id_contact_dim ON sub_discount_intermediate."subscription_id" = sub_id_contact_dim."subscription_id"
