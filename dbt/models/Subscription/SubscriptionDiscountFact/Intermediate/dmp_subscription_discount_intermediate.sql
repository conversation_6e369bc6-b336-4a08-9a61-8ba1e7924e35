{{
    config(
        materialized='table',
        schema='TRANSIENT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH event_discount AS (
    SELECT * FROM {{ ref('staging__dmp_subscription_promotion') }}
        WHERE "event_status"='SUCCEEDED' AND "start_date"<CURRENT_DATE --"is_active" 
)


,user_promotion AS (
    SELECT * FROM {{ ref('staging__dmp_user_promotion') }}
        WHERE "event_status"='SUCCEEDED'
)

,sub_core AS (
    SELECT  * FROM {{ ref('staging__dmp_subscription_core') }}
)

, sub_name_curr AS (
    SELECT * FROM {{ ref('staging__zuora__subscription_name_current') }}
)
SELECT 
--PROMO_PAYLOAD,
    HASH(
        COALESCE(sub_core."subscription_name",sub_name_curr."subscription_name")
        ,event_discount."start_date"
        ,event_discount."promotion_percentage" 
        ,NULL 
    ) AS "subscription_discount__hash"
    ,COALESCE(sub_core."subscription_id",sub_name_curr."subscription_id") AS "subscription_id"
    ,COALESCE(sub_core."subscription_name",sub_name_curr."subscription_name") AS "subscription_name"
    ,COALESCE(LEAST(sub_core."subscription_start_date",sub_core."subscription_name_original_created_timestamp"::DATE ),LEAST(sub_name_curr."subscription_start_date",sub_name_curr."subscription_name_original_created_timestamp"::DATE)) AS "subscription_start_date"
    ,COALESCE(user_promotion."event_timestamp",sub_core."subscription_id_created_timestamp",sub_name_curr."subscription_id_created_timestamp") AS "subscription_discount_requested_timestamp"
    -- If the Discount starts on the same day as the Sub starts, then we classify this as a sign-up discount as it will be effective from the very first day of the Sub
    -- Needs to be the exact sub_start_date field from Zuora as that is the one that will match to the RPC Start Date
    ,IFF(COALESCE(sub_core."subscription_start_date",sub_name_curr."subscription_start_date") = event_discount."start_date", TRUE, FALSE) AS "is_sign_up_discount"
    -- With the use of the sign_up_discount flag we can find out the exact giftcode/campaign relevant to this discount
    ,event_discount."coupon_code" AS "subscription_giftcode"
    ,event_discount."coupon_campaign" AS "subscription_giftcode_campaign_name"
    ,event_discount."channel" AS "subscription_discount_context"
    ,event_discount."catalog_product_name" AS "rateplan_name"
    ,event_discount."catalog_product_name" AS "rateplan_charge_name"
   -- ,CASE WHEN event_discount."type" IN ('PERCENTAGE_OFF','PERCENTAGE','PERCENTAGE_PARTNER') THEN event_discount."promotion_percentage" ELSE NULL END AS "subscription_discount_percentage"
   -- ,CASE WHEN event_discount."type" NOT IN ('PERCENTAGE_OFF','PERCENTAGE') THEN event_discount."promotion_amount" ELSE NULL END AS "subscription_discount_amount"
    ,event_discount."promotion_percentage" AS "subscription_discount_percentage"
    ,0 AS "subscription_discount_amount"
    ,CASE WHEN event_discount."type"='PERCENTAGE_PARTNER' THEN sub_core."subscription_start_date" ELSE event_discount."start_date" END AS "subscription_discount_start_date"
    ,CASE WHEN event_discount."type"='PERCENTAGE_PARTNER' THEN sub_core."subscription_end_date" ELSE event_discount."end_date" END AS "subscription_discount_end_date"
    ,DATEDIFF('day', "subscription_discount_start_date", "subscription_discount_end_date") AS "subscription_discount_duration_days"
    ,DATEDIFF('week', "subscription_discount_start_date", "subscription_discount_end_date") AS "subscription_discount_duration_weeks"
    ,DATEDIFF('month', "subscription_discount_start_date", "subscription_discount_end_date") AS "subscription_discount_duration_months"
    ,COALESCE(sub_core."subscription_product_group",sub_name_curr."subscription_product_group") AS "subscription_product_group"
    ,COALESCE(sub_core."billing_account_id",sub_name_curr."billing_account_id") AS "billing_account_id"
    ,event_discount."segment_id" AS "subscription_discount_segment"
FROM event_discount
LEFT JOIN sub_core
    --ON ( event_discount."legacy_subscription_name" = sub_core."subscription_name" AND event_discount."dazn_id"=sub_core."dazn_user_id" ) 
    ON event_discount."billing_product_id"=sub_core."subscription_id"
LEFT JOIN user_promotion
    ON  --event_discount."billing_product_id" = user_promotion."billing_product_id" AND
    event_discount."promotion_id"=user_promotion."promotion_id"
    AND event_discount."dazn_id"=user_promotion."dazn_id"
LEFT JOIN sub_name_curr
    ON sub_name_curr."subscription_name" = event_discount."legacy_subscription_name"

QUALIFY ROW_NUMBER() OVER (PARTITION BY "subscription_discount__hash" ORDER BY "subscription_discount_requested_timestamp" ASC, event_discount."event_timestamp" ASC) = 1
