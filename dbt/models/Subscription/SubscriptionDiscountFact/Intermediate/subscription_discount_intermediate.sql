{{
    config(
        materialized='table',
        schema='TRANSIENT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH zuora_source AS (
    SELECT * FROM {{ ref('zr_subscription_discount_intermediate')}}
)

,dmp_source AS (
    SELECT * FROM {{ ref('dmp_subscription_discount_intermediate')}}
    WHERE  "is_sign_up_discount"=FALSE OR 
    ("is_sign_up_discount" AND "subscription_discount_requested_timestamp"::DATE="subscription_start_date")
)

,final AS (
    SELECT *,'ZUORA' AS "data_source" FROM zuora_source
    UNION ALL
    SELECT *,'DMP' AS "data_source" FROM dmp_source
    )

SELECT * FROM final 
--QUALIFY ROW_NUMBER() OVER (PARTITION BY "subscription_discount__hash" ORDER BY "subscription_discount_requested_timestamp" ) = 1
