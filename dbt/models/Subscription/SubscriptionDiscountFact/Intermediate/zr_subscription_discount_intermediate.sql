{{
    config(
        materialized='table',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH'
    )
}}

-- Removing the tag to make sure the model is not getting updated.
-- tags=['presentation-subscription-domain']

WITH subscription AS (
    SELECT * FROM {{ ref('staging__zuora__subscription') }}
)

,rateplan_current AS (
    SELECT * FROM {{ ref('staging__zuora__rateplan_current') }}
)

,rateplancharge_current AS (
    SELECT * FROM {{ ref('staging__zuora__rateplancharge_current') }}
)

,rateplanchargetier_current AS (
    SELECT * FROM {{ ref('staging__zuora__rateplanchargetier_current') }}
)

/*,dmp_promo AS (
    SELECT * FROM {{ ref('staging__dmp_subscription_promotion') }}
    WHERE "legacy_promotion_id" IS NOT NULL
)

,user_promotion AS (
    SELECT * FROM {{ ref('staging__dmp_user_promotion') }}
        WHERE "event_status"='SUCCEEDED'
)*/

SELECT
    HASH(
        subscription."subscription_name"
        ,rateplancharge_current."rateplan_charge_effective_start_date"
        ,rateplanchargetier_current."rateplan_charge_tier_discount_percentage"
        ,rateplanchargetier_current."rateplan_charge_tier_discount_amount"
     ) AS "subscription_discount__hash"
    ,subscription."subscription_id"
    ,subscription."subscription_name"
    ,LEAST(subscription."subscription_start_date", subscription."subscription_name_original_created_timestamp"::DATE) AS "subscription_start_date"
    ,subscription."subscription_id_created_timestamp" AS "subscription_discount_requested_timestamp"
    -- If the Discount starts on the same day as the Sub starts, then we classify this as a sign-up discount as it will be effective from the very first day of the Sub
    -- Needs to be the exact sub_start_date field from Zuora as that is the one that will match to the RPC Start Date
    ,IFF(subscription."subscription_start_date" = rateplancharge_current."rateplan_charge_effective_start_date", TRUE, FALSE) AS "is_sign_up_discount"
    -- With the use of the sign_up_discount flag we can find out the exact giftcode/campaign relevant to this discount
    ,CASE
        -- When it's a PAC/Externally Billed, then always take the sign-up giftcode, as there are some weird PAC discounts that look like post-sign-up, but aren't
        WHEN subscription."subscription_source_system_name" IS NOT NULL AND subscription."subscription_sign_up_giftcode" != 'null' THEN subscription."subscription_sign_up_giftcode"
        WHEN "is_sign_up_discount" = TRUE THEN subscription."subscription_sign_up_giftcode"
        ELSE rateplancharge_current."rateplan_charge_post_sign_up_giftcode"
    END AS "subscription_giftcode"
    ,CASE
        -- When it's a PAC/Externally Billed, then always take the sign-up giftcode, as there are some weird PAC discounts that look like post-sign-up, but aren't
        WHEN subscription."subscription_source_system_name" IS NOT NULL AND subscription."subscription_sign_up_giftcode" != 'null' THEN subscription."subscription_sign_up_campaign_id"
        WHEN "is_sign_up_discount" = TRUE THEN subscription."subscription_sign_up_campaign_id"
        ELSE rateplancharge_current."rateplan_charge_post_sign_up_giftcode_campaign_name"
     END AS "subscription_giftcode_campaign_name"
    ,rateplan_current."rateplan_context" AS "subscription_discount_context"
    ,rateplan_current."rateplan_name" AS "rateplan_name"
    ,rateplancharge_current."rateplan_charge_name" AS "rateplan_charge_name"
    ,rateplanchargetier_current."rateplan_charge_tier_discount_percentage" AS "subscription_discount_percentage"
    ,rateplanchargetier_current."rateplan_charge_tier_discount_amount" AS "subscription_discount_amount"
    ,rateplancharge_current."rateplan_charge_effective_start_date" AS "subscription_discount_start_date"
    ,rateplancharge_current."rateplan_charge_effective_end_date" AS "subscription_discount_end_date"
    ,DATEDIFF('day', "subscription_discount_start_date", "subscription_discount_end_date") AS "subscription_discount_duration_days"
    ,DATEDIFF('week', "subscription_discount_start_date", "subscription_discount_end_date") AS "subscription_discount_duration_weeks"
    ,DATEDIFF('month', "subscription_discount_start_date", "subscription_discount_end_date") AS "subscription_discount_duration_months"
    ,subscription."subscription_product_group"
    ,subscription."billing_account_id"
    ,rateplan_current."rateplan_segment" AS "subscription_discount_segment"
FROM subscription
LEFT JOIN rateplan_current ON subscription."subscription_id" = rateplan_current."subscription_id"
LEFT JOIN rateplancharge_current USING ("rateplan_id")
LEFT JOIN rateplanchargetier_current USING ("rateplan_charge_id")
/*LEFT JOIN dmp_promo ON rateplancharge_current."rateplan_id" = dmp_promo."legacy_promotion_id" 
LEFT JOIN user_promotion
    ON -- dmp_promo."billing_product_id" = user_promotion."billing_product_id" AND
     dmp_promo."promotion_id"=user_promotion."promotion_id"
    AND dmp_promo."dazn_id"=user_promotion."dazn_id"*/
WHERE
    -- Filtering for any real percentage or fixed discounts
   ("subscription_discount_percentage" IS NOT NULL)-- OR "subscription_discount_amount" IS NOT NULL)
    AND
    -- There are some RPCs that are not effective for any amount of time, so we want to filter those out as they don't tell us anything
    rateplancharge_current."rateplan_charge_effective_start_date" != rateplancharge_current."rateplan_charge_effective_end_date"

-- Filtering for the first occurance of the discount so that we can find out when it was first requested/applied
-- Using the unique discount hash which is a combination of the SubName, RPCStartDate and Discount for the partition
QUALIFY ROW_NUMBER() OVER (PARTITION BY "subscription_discount__hash" ORDER BY "subscription_discount_requested_timestamp" ASC, subscription."subscription_id_updated_timestamp" ASC) = 1
