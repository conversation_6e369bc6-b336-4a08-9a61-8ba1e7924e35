version: 2

models:
  - name: zr_subscription_discount_intermediate
    description: "Table describing unique discounts (from the 'Discount-Percentage' and 'Discount-Fixed Amount' RPs ChargeModels) for all subscriptions and the exact periods they are active for, note PAC discounts will not be accurate"
    columns:
      - name: subscription_discount__hash
        description: "HASHed combination of fields that make up a unique discount"
        quote: true

      - name: subscription_id
        description: "The ID of the Subscription at the time it first was created in Zuora"
        quote: true

      - name: subscription_name
        description: "The Name of the Subscription E.g. A-S15283950"
        quote: true

      - name: subscription_start_date
        description: "The date the Subscription was started in Zuora"
        quote: true

      - name: subscription_discount_requested_timestamp
        description: "The timestamp the discount was first created/applied in Zuora"
        quote: true

      - name: is_sign_up_discount
        description: "Boolean flag, true if the discount effective start is the same date as the subscription start"
        quote: true

      - name: subscription_giftcode
        description: "The giftcode, if any, relating directly to this exact discount, could be a sign-up or post-sign-up discount"
        quote: true

      - name: subscription_giftcode_campaign_name
        description: "The campaign name attributed to the giftcode, if any, relating directly to this exact discount, could be a sign-up or post-sign-up discount"
        quote: true

      - name: subscription_discount_context
        description: "Describes the channel, if any, through which the discount was applied E.g CRM, InApp, CancellationFlow, CustomerServcie, ..."
        quote: true

      - name: rateplan_name
        description: "The name of the RatePlan"
        quote: true

      - name: rateplan_charge_name
        description: "The name of the RatePlanCharge"
        quote: true

      - name: subscription_discount_percentage
        description: "The percentage discount that will be applied to the MRR due to this discount, only present if the discount model is a Percentage Discount one"
        quote: true

      - name: subscription_discount_amount
        description: "The fixed amount discount that will be applied to the MRR due to this discount, only present if the discount model is a Fixed Discount one"
        quote: true

      - name: subscription_discount_start_date
        description: "The start date of the discount"
        quote: true

      - name: subscription_discount_end_date
        description: "The End date of the discount"
        quote: true

      - name: subscription_discount_duration_days
        description: "Duration in days of the discount"
        quote: true

      - name: subscription_discount_duration_weeks
        description: "Duration in weeks of the discount"
        quote: true

      - name: subscription_discount_duration_months
        description: "Duration in months of the discount"
        quote: true

      - name: subscription_product_group
        description: "Can be either 'NFL' whereas it is an NFL subscription, or 'DAZN' otherwise."
        quote: true

      - name: billing_account_id
        description: "The Zuora Account ID coming from the Zuora Account entity E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: subscription_discount_segment
        description: "Describes the segment, if any, through which the discount was applied"
        quote: true

  - name: dmp_subscription_discount_intermediate
    description: "Table describing unique discounts (from the 'Discount-Percentage' and 'Discount-Fixed Amount' RPs ChargeModels) for all subscriptions and the exact periods they are active for, note PAC discounts will not be accurate"
    columns:
      - name: subscription_discount__hash
        description: "HASHed combination of fields that make up a unique discount"
        quote: true

      - name: subscription_id
        description: "The ID of the Subscription at the time it first was created in Zuora"
        quote: true

      - name: subscription_name
        description: "The Name of the Subscription E.g. A-S15283950"
        quote: true

      - name: subscription_start_date
        description: "The date the Subscription was started in Zuora"
        quote: true

      - name: subscription_discount_requested_timestamp
        description: "The timestamp the discount was first created/applied in Zuora"
        quote: true

      - name: is_sign_up_discount
        description: "Boolean flag, true if the discount effective start is the same date as the subscription start"
        quote: true

      - name: subscription_giftcode
        description: "The giftcode, if any, relating directly to this exact discount, could be a sign-up or post-sign-up discount"
        quote: true

      - name: subscription_giftcode_campaign_name
        description: "The campaign name attributed to the giftcode, if any, relating directly to this exact discount, could be a sign-up or post-sign-up discount"
        quote: true

      - name: subscription_discount_context
        description: "Describes the channel, if any, through which the discount was applied E.g CRM, InApp, CancellationFlow, CustomerServcie, ..."
        quote: true

      - name: rateplan_name
        description: "The name of the RatePlan"
        quote: true

      - name: rateplan_charge_name
        description: "The name of the RatePlanCharge"
        quote: true

      - name: subscription_discount_percentage
        description: "The percentage discount that will be applied to the MRR due to this discount, only present if the discount model is a Percentage Discount one"
        quote: true

      - name: subscription_discount_amount
        description: "The fixed amount discount that will be applied to the MRR due to this discount, only present if the discount model is a Fixed Discount one"
        quote: true

      - name: subscription_discount_start_date
        description: "The start date of the discount"
        quote: true

      - name: subscription_discount_end_date
        description: "The End date of the discount"
        quote: true

      - name: subscription_discount_duration_days
        description: "Duration in days of the discount"
        quote: true

      - name: subscription_discount_duration_weeks
        description: "Duration in weeks of the discount"
        quote: true

      - name: subscription_discount_duration_months
        description: "Duration in months of the discount"
        quote: true

      - name: subscription_product_group
        description: "Can be either 'NFL' whereas it is an NFL subscription, or 'DAZN' otherwise."
        quote: true

      - name: billing_account_id
        description: "The Zuora Account ID coming from the Zuora Account entity E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: subscription_discount_segment
        description: "Describes the segment, if any, through which the discount was applied"
        quote: true

  - name: subscription_discount_intermediate
    description: "Table describing unique discounts (from the 'Discount-Percentage' and 'Discount-Fixed Amount' RPs ChargeModels) for all subscriptions and the exact periods they are active for, note PAC discounts will not be accurate"
    columns:
      - name: subscription_discount__hash
        description: "HASHed combination of fields that make up a unique discount"
        quote: true

      - name: subscription_id
        description: "The ID of the Subscription at the time it first was created in Zuora"
        quote: true

      - name: subscription_name
        description: "The Name of the Subscription E.g. A-S15283950"
        quote: true

      - name: subscription_start_date
        description: "The date the Subscription was started in Zuora"
        quote: true

      - name: subscription_discount_requested_timestamp
        description: "The timestamp the discount was first created/applied in Zuora"
        quote: true

      - name: is_sign_up_discount
        description: "Boolean flag, true if the discount effective start is the same date as the subscription start"
        quote: true

      - name: subscription_giftcode
        description: "The giftcode, if any, relating directly to this exact discount, could be a sign-up or post-sign-up discount"
        quote: true

      - name: subscription_giftcode_campaign_name
        description: "The campaign name attributed to the giftcode, if any, relating directly to this exact discount, could be a sign-up or post-sign-up discount"
        quote: true

      - name: subscription_discount_context
        description: "Describes the channel, if any, through which the discount was applied E.g CRM, InApp, CancellationFlow, CustomerServcie, ..."
        quote: true

      - name: rateplan_name
        description: "The name of the RatePlan"
        quote: true

      - name: rateplan_charge_name
        description: "The name of the RatePlanCharge"
        quote: true

      - name: subscription_discount_percentage
        description: "The percentage discount that will be applied to the MRR due to this discount, only present if the discount model is a Percentage Discount one"
        quote: true

      - name: subscription_discount_amount
        description: "The fixed amount discount that will be applied to the MRR due to this discount, only present if the discount model is a Fixed Discount one"
        quote: true

      - name: subscription_discount_start_date
        description: "The start date of the discount"
        quote: true

      - name: subscription_discount_end_date
        description: "The End date of the discount"
        quote: true

      - name: subscription_discount_duration_days
        description: "Duration in days of the discount"
        quote: true

      - name: subscription_discount_duration_weeks
        description: "Duration in weeks of the discount"
        quote: true

      - name: subscription_discount_duration_months
        description: "Duration in months of the discount"
        quote: true

      - name: subscription_product_group
        description: "Can be either 'NFL' whereas it is an NFL subscription, or 'DAZN' otherwise."
        quote: true

      - name: billing_account_id
        description: "The Zuora Account ID coming from the Zuora Account entity E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: subscription_discount_segment
        description: "Describes the segment, if any, through which the discount was applied"
        quote: true

  - name: subscription_discount__fact
    description: "Discount level fact table that enriches the unique discounts (from the 'Discount-Percentage' and 'Discount-Fixed Amount' RPs ChargeModels) of subscriptions with all the subscription skeys to give a full picture of the subscription details at the time the discount was requested, note PAC discounts will not be accurate"
    columns:
      - name: subscription_discount__hash
        description: "HASHed combination of fields that make up a unique discount"
        quote: true
        test:
          - unique:
              config:
                error_if: ">10000"

      - name: subscription_id
        description: "The ID of the Subscription at the time it first was created in Zuora"
        quote: true

      - name: subscription_name
        description: "The Name of the Subscription E.g. A-S15283950"
        quote: true

      - name: subscription_start_date
        description: "The date the Subscription was started in Zuora"
        quote: true

      - name: subscription_discount_requested_timestamp
        description: "The timestamp the discount was first created/applied in Zuora"
        quote: true

      - name: is_sign_up_discount
        description: "Boolean flag, true if the discount effective start is the same date as the subscription start"
        quote: true

      - name: subscription_giftcode
        description: "The giftcode, if any, relating directly to this exact discount, could be a sign-up or post-sign-up discount"
        quote: true

      - name: subscription_giftcode_campaign_name
        description: "The campaign name attributed to the giftcode, if any, relating directly to this exact discount, could be a sign-up or post-sign-up discount"
        quote: true

      - name: subscription_discount_context
        description: "Describes the channel, if any, through which the discount was applied E.g CRM, InApp, CancellationFlow, CustomerServcie, ..."
        quote: true

      - name: subscription_discount_percentage
        description: "The percentage discount that will be applied to the MRR due to this discount, only present if the discount model is a Percentage Discount one"
        quote: true

      - name: subscription_discount_amount
        description: "The fixed amount discount that will be applied to the MRR due to this discount, only present if the discount model is a Fixed Discount one"
        quote: true

      - name: subscription_discount_start_date
        description: "The start date of the discount"
        quote: true

      - name: subscription_discount_end_date
        description: "The End date of the discount"
        quote: true

      - name: subscription_discount_duration_days
        description: "Duration in days of the discount"
        quote: true

      - name: subscription_discount_duration_weeks
        description: "Duration in weeks of the discount"
        quote: true

      - name: subscription_discount_duration_months
        description: "Duration in months of the discount"
        quote: true

      - name: subscription_product_group
        description: "Can be either 'NFL' whereas it is an NFL subscription, or 'DAZN' otherwise."
        quote: true

      - name: billing_account_id
        description: "The Zuora Account ID coming from the Zuora Account entity E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: subscription_discount_segment
        description: "Describes the segment, if any, through which the discount was applied"
        quote: true

      - name: billing_account__skey
        description: "The surrogate key (skey) used to join on the billing_account dim at the time the discount was requested"
        quote: true

      - name: subscription_info__skey
        description: "The surrogate key (skey) used to join on the subscription_info dim at the time the discount was requested"
        quote: true

      - name: subscription_term__skey
        description: "The surrogate key (skey) used to join on the subscription_term dim at the time the discount was requested"
        quote: true

      - name: subscription_charge__skey
        description: "The surrogate key (skey) used to join on the subscription_charge dim at the time the discount was requested"
        quote: true

      - name: subscription_free_trial__skey
        description: "The surrogate key (skey) used to join on the subscription_free_trial dim at the time the discount was requested"
        quote: true

      - name: subscription_discounts__skey
        description: "The surrogate key (skey) used to join on the subscription_discount dim at the time the discount was requested"
        quote: true

      - name: subscription_pause__skey
        description: "The surrogate key (skey) used to join on the subscription_pause dim at the time the discount was requested"
        quote: true

      - name: subscription_add_on__skey
        description: "The surrogate key (skey) used to join on the subscription_add_on dim at the time the discount was requested"
        quote: true

      - name: subscription_content_attribution__skey
        description: "The surrogate key (skey) used to join on the subscription_content_attribution__dim at the time the discount was requested"
        quote: true

      - name: subscription_source_system_name_derived__skey
        description: "The surrogate key (skey) used to join on the sub_source_system dim at the time the discount was requested"
        quote: true

      - name: subscription_tracking_id__skey
        description: "The surrogate key (skey) used to join on the tracking_id dim at the time the discount was requested"
        quote: true

      - name: subscription_sign_up_campaign_id__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim at the time the discount was requested"
        quote: true

      - name: first_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim at the time the discount was requested"
        quote: true

      - name: last_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim at the time the discount was requested"
        quote: true

      - name: subscription_daily_status__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_daily_status__dim at the time the discount was requested"
        quote: true

      - name: daily_usd_fx_rate__skey
        description: "The surrogate key (skey) used to join on the daily_usd_fx_rate dim at the time the discount was requested"
        quote: true

      - name: subscription_state
        description: "The state of the account. E.g. California, Ontario, ..."
        quote: true
