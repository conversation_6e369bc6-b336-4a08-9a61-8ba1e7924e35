{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"subscription_daily_mrr_rank__skey"',
        on_schema_change='sync_all_columns',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_M_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH dim_date AS (
    SELECT * FROM {{ ref('dim_date') }}
)

,subscription_name__scd AS (
    SELECT * FROM {{ ref('subscription_name__scd') }}
)

,rank_prep AS (
    SELECT
        dim_date."date_day"
        ,subscription_name__scd."subscription_territory"
        ,subscription_name__scd."subscription_product_group"
        ,subscription_name__scd."billing_account_currency_code"
        ,subscription_name__scd."subscription_type"
        ,subscription_name__scd."subscription_tier"
        ,subscription_name__scd."subscription_monthly_recurring_revenue"
        ,COUNT(DISTINCT subscription_name__scd."subscription_name") AS "distinct_subscription_names"
        -- Calculate the % weight of this MRR for the dimensions we're grouping to in order to exclude from the rank
        ,"distinct_subscription_names" / SUM("distinct_subscription_names") OVER (PARTITION BY dim_date."date_day", subscription_name__scd."subscription_territory", subscription_name__scd."subscription_product_group", subscription_name__scd."subscription_type", subscription_name__scd."subscription_tier") AS "subscripiton_dimension_proportion"
        ,NOT COALESCE("subscripiton_dimension_proportion" < 0.01, FALSE) AS "is_valid_mrr"
    FROM dim_date
    LEFT JOIN subscription_name__scd
        ON dim_date."date_day" >= subscription_name__scd."record_valid_from_timestamp"::DATE
        AND dim_date."date_day" < subscription_name__scd."record_valid_until_timestamp"::DATE
    WHERE
        {% if is_incremental() %}
        dim_date."date_day" = '{{ var('batch_date') }}'
        AND
        {% endif %}
        -- Filter out before 2019, as sub countries do not come through before then, and it's long enough ago that we don't care
        dim_date."date_day" BETWEEN '2020-01-01' AND DATEADD('day', -1, CURRENT_DATE)
    GROUP BY 1,2,3,4,5,6,7
)

SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,HASH(
        "date_day"
        ,"subscription_territory"
        ,"subscription_product_group"
        ,"billing_account_currency_code"
        ,"subscription_type"
        ,"subscription_tier"
        ,"subscription_monthly_recurring_revenue"
    ) AS "subscription_daily_mrr_rank__skey"
    ,"date_day"
    ,"subscription_territory"
    ,"subscription_product_group"
    ,"billing_account_currency_code"
    ,"subscription_type"
    ,"subscription_tier"
    ,"subscription_monthly_recurring_revenue"
    ,"distinct_subscription_names"
    ,"subscripiton_dimension_proportion"
    ,"is_valid_mrr"
    ,RANK() OVER (PARTITION BY "date_day", "subscription_territory", "subscription_product_group", "billing_account_currency_code", "subscription_type", "subscription_tier", "is_valid_mrr" ORDER BY "subscription_monthly_recurring_revenue" DESC) AS "subscription_monthly_recurring_revenue_daily_rank"
FROM rank_prep
