version: 2

models:
  - name: subscription_daily_mrr_rank__dim
    description: "A dimension table used to lookup the rank of an MRR within certain dimensions, like Territory, Subscription Type and Tier by the date, and therefore can be used to define upgrades and downgrades or grandfathered pricing"
    columns:
      - name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the model"
        quote: true

      - name: subscription_daily_mrr_rank__skey
        description: "A created unique key for the dataset based off a concat of all dimensions that go into the lookup"
        quote: true
        tests:
          - unique:
              config:
                severity: error
                error_if: ">1000"
                warn_if: ">0"

      - name: date_day
        description: "The date slice this rank applies to"
        quote: true

      - name: subscription_territory
        description: "The territory of the subscription, pulled in by a join to region_dimension, E.g. Spain, DACH, Japan, United States, UK and Ireland, ..."
        quote: true

      - name: subscription_product_group
        description: "Can be either 'NFL' whereas it is an NFL subscription, or 'DAZN' otherwise."
        quote: true

      - name: billing_account_currency_code
        description: "The currency code for the Zuora Account, E.g. EUR, USD, ..."
        quote: true

      - name: subscription_type
        description: "A derived field from the term type and the billing period, or external giftcodes, to categorise the subscription type, can be Monthly, Annual, Instalment or Externally Billed"
        quote: true

      - name: subscription_tier
        description: "The Entitlement Set Id of the Rateplan, which is filtered to the associated RatePlanCharge's Flat Fee Pricing Model and Recurring Type, so producing the Tier for the Subscription"
        quote: true

      - name: subscription_monthly_recurring_revenue
        description: "The monthly recurring revenue, which is the amount the customer pays for their subscription ignoring Discounts and PPV, adjusting the subscription length down to one month and rounded to two decimal places"
        quote: true

      - name: distinct_subscription_names
        description: "The distinct count of the amount of Subscription Names there are active given the above dimensions, excluding MRR and Currency"
        quote: true

      - name: subscripiton_dimension_proportion
        description: "The proportion of Subscriptions that the MRR represents for the above dimensions, excluding Currency"
        quote: true

      - name: is_valid_mrr
        description: "A flag for if the MRR is lower than 1% of the total Subscription proportion to exclude outliers that would incorrectly influence the rank"
        quote: true

      - name: subscription_monthly_recurring_revenue_daily_rank
        description: "The rank of the MRR, high to low, of the above dimensions"
        quote: true
