{{
    config(
        materialized='incremental',
        unique_key = '"subscription_name"',
        incremental_strategy='delete+insert',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
    )
}}
-- Removing the tag to make sure the model is not getting updated as this is not signed off by anyone.
-- tags=['presentation-subscription-domain']
WITH subscription AS (
    SELECT *
    FROM {{ ref('staging__zuora__subscription') }}
    WHERE TRUE
     {% if is_incremental() %}
    AND "subscription_cancelled_date" >= DATEADD('day', -7, '{{ var('batch_date') }}')
        {% endif %}
    QUALIFY TRUE
    AND ROW_NUMBER() OVER (PARTITION BY "subscription_name" ORDER BY "subscription_id_updated_timestamp" DESC, "subscription_id_created_timestamp" DESC ,  "subscription_version" DESC) = 1
)


,final AS (
    SELECT
        "billing_account_id"
        ,"subscription_name"
        ,"subscription_product_group"
        ,"subscription_id"
        ,"subscription_cancelled_date"
        ,"subscription_source_system_name"
        ,CURRENT_TIMESTAMP AS "record_updated_timestamp"

    --     "subscription_status",
    --     "subscription_start_date",
    --     "subscription_end_date",
    --     "subscription_id_created_timestamp",
    FROM subscription
    WHERE TRUE
    AND "subscription_id_created_timestamp"::DATE = "subscription_cancelled_date"
    {% if is_incremental() %}
    AND "record_updated_timestamp"::DATE >= '{{ var('batch_date') }}'
    {% endif %}
)

SELECT * FROM final
