version: 2

models:
  - name: subscription_daily_counts__fact
    description: "A daily snapshot of our subs base across a number of dimensions, used for comparisons, understand base evolutions, revenue forecasting. Replacing for subscription_counts"
    columns:
      - name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the model"
        quote: true

      - name: batch_date
        description: "The date of the spine for daily subs"
        quote: true

      - name: billing_account__skey
        description: "he surrogate key (skey) used to join on the billing_account dim"
        quote: true

      - name: subscription_info__skey
        description: "The surrogate key (skey) used to join on the subscription_info dim"
        quote: true

      - name: subscription_term__skey
        description: "The surrogate key (skey) used to join on the subscription_term dim"
        quote: true

      - name: subscription_charge__skey
        description: "The surrogate key (skey) used to join on the subscription_charge dim"
        quote: true

      - name: subscription_free_trial__skey
        description: "The surrogate key (skey) used to join on the subscription_free_trial dim"
        quote: true

      - name: subscription_discount__skey
        description: "The surrogate key (skey) used to join on the subscription_discount dim"
        quote: true

      - name: subscription_pause__skey
        description: "The surrogate key (skey) used to join on the subscription_pause dim"
        quote: true

      - name: subscription_add_on__skey
        description: "The surrogate key (skey) used to join on the subscription_add_on dim"
        quote: true

      - name: subscription_content_attribution__skey
        description: "The surrogate key (skey) used to join on the subscription_content_attribution__dim"
        quote: true

      - name: subscription_source_system_name_derived__skey
        description: "The surrogate key (skey) used to join on the sub_source_system dim"
        quote: true

      - name: subscription_tracking_id__skey
        description: "The surrogate key (skey) used to join on the tracking_id dim"
        quote: true

      - name: subscription_sign_up_campaign_id__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: first_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: last_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: subscription_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: subscription_daily_status__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_daily_status__dim"
        quote: true

      - name: daily_usd_fx_rate__skey
        description: "The surrogate key (skey) used to join on the daily_usd_fx_rate dim"
        quote: true

      - name: billing_account_product_groups__skey
        description: "The surrogate key (skey) used to join on the billing_account_product_groups__dim"
        quote: true

      - name: subscription_count
        description: "Count of subscription name grouped by skeys and batch date"
        quote: true
