version: 2

models:
  - name: daily_subscription_discounts
    description: "An intermediate table to calculate the daily active discounts per subscription on each batch_date to feed into the Daily Subs Raw table"
    columns:
      - &META__DBT_INSERT_DTTS
        name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the model"
        quote: true

      - &batch_date
        name: batch_date
        description: "The date of the spine for daily subs"
        quote: true

      - &subscription_id
        name: subscription_id
        description: "The ID of the Subscription, Subscription Names can have multiple IDs relating to different types of changes"
        quote: true

      - &subscription_name
        name: subscription_name
        description: "The Name of the Subscription E.g. A-S15283950"
        quote: true

      - &subscription_discount__hash
        name: subscription_discount__hash
        description: "HASHed combination of fields that make up a unique discount"
        quote: true

      - &subscription_discount_requested_timestamp
        name: subscription_discount_requested_timestamp
        description: "The timestamp the discount was first created/applied in Zuora"
        quote: true

      - &subscription_discount_start_date
        name: subscription_discount_start_date
        description: "The start date of the discount"
        quote: true

      - &subscription_discount_end_date
        name: subscription_discount_end_date
        description: "The end date of the discount"
        quote: true

      - &subscription_discount_duration_days
        name: subscription_discount_duration_weeks
        description: "Duration in weeks of the discount"
        quote: true

      - &subscription_discount_duration_weeks
        name: subscription_discount_duration_weeks
        description: "Duration in weeks of the discount"
        quote: true

      - &subscription_discount_duration_months
        name: subscription_discount_duration_weeks
        description: "Duration in weeks of the discount"
        quote: true

      - &is_sign_up_discount
        name: is_sign_up_discount
        description: "Boolean flag, true if the discount effective start is the same date as the subscription start"
        quote: true

      - &subscription_giftcode
        name: subscription_giftcode
        description: "The giftcode, if any, relating directly to this exact discount, could be a sign-up or post-sign-up discount"
        quote: true

      - &subscription_giftcode_campaign_name
        name: subscription_giftcode_campaign_name
        description: "The campaign name attributed to the giftcode, if any, relating directly to this exact discount, could be a sign-up or post-sign-up discount"
        quote: true

      - &subscription_discount_context
        name: subscription_discount_context
        description: "Describes the channel, if any, through which the discount was applied E.g CRM, InApp, CancellationFlow, CustomerServcie, ..."
        quote: true

      - &subscription_discount_percentage
        name: subscription_discount_percentage
        description: "The percentage discount that will be applied to the MRR due to this discount, only present if the discount model is a Percentage Discount one"
        quote: true

      - &subscription_discount_amount
        name: subscription_discount_amount
        description: "The fixed amount discount that will be applied to the MRR due to this discount, only present if the discount model is a Fixed Discount one"
        quote: true

      - &billing_account_id
        name: billing_account_id
        description: "The Zuora Account ID coming from the Zuora Account entity E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

  - name: daily_subscriptions_raw
    description: "An intermediate table containing the date spine of daily subscriptions for the Sub Mart without any filters in order to do QA"
    columns:
      - *META__DBT_INSERT_DTTS

      - *batch_date

      - &batch_week_end_date
        name: batch_week_end_date
        description: "The batch_week_end_date of the date_day for the spine of daily subs"
        quote: true

      - *subscription_id

      - *subscription_name

      - *billing_account_id

      - &crm_account_id
        name: crm_account_id
        description: "The Salesforce Account ID coming from Zuora Account entity E.g 0011o00001m6OyEAAU"
        quote: true

      - &dazn_user_id
        name: dazn_user_id
        description: "The DAZN User Id of the Account E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - &billing_account_currency_code
        name: billing_account_currency_code
        description: "The currency code for the Zuora Account, E.g. EUR, USD, ..."
        quote: true

      - &subscription_product_group
        name: subscription_product_group
        description: "Can be either 'NFL' whereas it is an NFL subscription, or 'DAZN' otherwise."
        quote: true

      - &record_valid_from_timestamp
        name: record_valid_from_timestamp
        description: "The datetime this data point is valid from (inclusive)"
        quote: true

      - &record_valid_until_timestamp
        name: record_valid_until_timestamp
        description: "The datetime this data point is valid until (exlusive)"
        quote: true

      - &subscription_start_date
        name: subscription_start_date
        description: "The latest planned subscription end date for this subscription ID, note this can be reversed or amended and in certain retrospective cancellation examples can be before they actually cancel"
        quote: true

      - &subscription_planned_end_date
        name: subscription_planned_end_date
        description: "The latest planned subscription end date for this subscription ID, note this can be reversed or amended and in certain retrospective cancellation examples can be before they actually cancel"
        quote: true

      - &subscription_free_trial_start_date
        name: subscription_free_trial_start_date
        description: "The date the Free Trial starts"
        quote: true

      - &subscription_free_trial_end_date
        name: subscription_free_trial_end_date
        description: "The date the Free Trial ends"
        quote: true

      - &is_soft_cancelled
        name: is_soft_cancelled
        description: "A flag for if the Subscription Id has a status of Cancelled, meaning that the subscription is soft_cancelled"
        quote: true

      - &is_draft
        name: is_draft
        description: "A flag for if the Subscription Id has a status of Draft, implying that they did not make it through the full checkout process and are not active"
        quote: true

      - &is_suspended
        name: is_suspended
        description: "A flag for if the Subscription Id has a status of Suspended, which implies they might not be active, but not in all cases"
        quote: true

      - &is_resubscription
        name: is_resubscription
        description: "Identifies if this is a resubscription for this product group or not"
        quote: true

      - &billing_account_is_batch_50
        name: billing_account_is_batch_50
        description: "Boolean flag, true if the account has ever been on Batch50, which implies it is a test account"
        quote: true

      - &subscription_pause_start_date
        name: subscription_pause_start_date
        description: "Date the most recent pause starts for the subscription id"
        quote: true

      - &subscription_pause_end_date
        name: subscription_pause_end_date
        description: "Date the most recent pause ends for the subscription id"
        quote: true

      - &subscription_add_on_effective_start_date
        name: subscription_add_on_effective_start_date
        description: "Date the most recent Add On starts for the subscription id"
        quote: true

      - &subscription_add_on_effective_end_date
        name: subscription_add_on_effective_end_date
        description: "Date the most recent Add On ends for the subscription id"
        quote: true

      - &next_subscription_monthly_recurring_revenue
        name: next_subscription_monthly_recurring_revenue
        description: "The monthly recurring revenue that they will be on at the next Bill Cycle Day, which is the amount the customer pays for their subscription ignoring Discounts and PPV, adjusting the subscription length down to one month and rounded to two decimal places"
        quote: true

      - &subscription_monthly_recurring_revenue_current
        name: subscription_monthly_recurring_revenue_current
        description: "The monthly recurring revenue that they were on on the last Bill Cycle Day, which is the amount the customer pays for their subscription ignoring Discounts and PPV, adjusting the subscription length down to one month and rounded to two decimal places"
        quote: true

      - &subscription_monthly_recurring_revenue
        name: subscription_monthly_recurring_revenue
        description: "The monthly recurring revenue that they were on on the last Bill Cycle Day, which is the amount the customer pays for their subscription ignoring Discounts and PPV, adjusting the subscription length down to one month and rounded to two decimal places"
        quote: true

      - &discount_effective_start_date
        name: discount_effective_start_date
        description: "The very first date that any post-sign-up discount will be effective on this Subscription Id"
        quote: true

      - &discount_effective_end_date
        name: discount_effective_end_date
        description: "The very last date that any post-sign-up discount will be effective on this Subscription Id"
        quote: true

      - &is_introductory_discount
        name: is_introductory_discount
        description: "Boolean flag, true if the discount effective start is the same date as the subscription start"
        quote: true

      - &max_discount_percentage
        name: max_discount_percentage
        description: "The maximum proportional discount applied to the subscription charges for the duration of the discount"
        quote: true

      - &discount_percentage_distinct_count
        name: discount_percentage_distinct_count
        description: "The distinct count of the amount of post-sign-up discounts that will ever be associated with this Subscription Id from the Subscription Id creation into the future"
        quote: true

      - &post_sign_up_giftcode_campaign_name_distinct_count
        name: post_sign_up_giftcode_campaign_name_distinct_count
        description: "The distinct count of the amount of post-sign-up giftcode campaigns that will ever be associated with this Subscription Id from the Subscription Id creation into the future, different giftcodes could be associated with the same campaign"
        quote: true

      - &discount_data
        name: discount_data
        description: "An array containing a dictionary of all details for any discount effective from the creation of the Subscription Id"
        quote: true

      - &subscription_country
        name: subscription_country
        description: "The country of the subscription, E.g. Spain, Germany, Japan, United States, United Kingdom, ..."
        quote: true

      - &subscription_territory
        name: subscription_territory
        description: "The territory of the subscription, pulled in by a join to region_dimension, E.g. Spain, DACH, Japan, United States, UK and Ireland, ..."
        quote: true

      - &subscription_tier
        name: subscription_tier
        description: "The Entitlement Set Id of the Rateplan, which is filtered to the associated RatePlanCharge's Flat Fee Pricing Model and Recurring Type, so producing the Tier for the Subscription"
        quote: true

      - &subscription_type
        name: subscription_type
        description: "A derived field from the term type and the billing period, or external giftcodes, to categorise the subscription type, can be Monthly, Annual, Instalment or Externally Billed"
        quote: true

      - &subscription_rateplan_name
        name: subscription_rateplan_name
        description: "The name of the RatePlan directly relating to this requested Sub Type/MRR/Tier of this Subscription ID"
        quote: true

      - &subscription_rateplan_charge_name
        name: subscription_rateplan_charge_name
        description: "The name of the RatePlanCharge directly relating to this requested Sub Type/MRR/Tier of this Subscription ID"
        quote: true

      - &subscription_bill_cycle_day
        name: subscription_bill_cycle_day
        description: "The billing cycle day of the month that the customer is charged"
        quote: true

      - &subscription_source_system_name_derived
        name: subscription_source_system_name_derived
        description: "A combination of subscription_source_system_name and subscription_direct_carrier_billing_carrier_name to replace subscription_direct_carrier_billing_carrier_name when its empty"
        quote: true

      - &subscription_tracking_id
        name: subscription_tracking_id
        description: "The tracking ID for this subscription ID, if any"
        quote: true

      - &subscription_sign_up_giftcode
        name: subscription_sign_up_giftcode
        description: "The giftcode applied to the subscription on sign-up, if any"
        quote: true

      - &subscription_sign_up_campaign_id
        name: subscription_sign_up_campaign_id
        description: "The campaign ID of the giftcode, if any"
        quote: true

      - &first_post_sign_up_giftcode
        name: first_post_sign_up_giftcode
        description: "The first post-sign-up giftcode associated with this Subscription Id, looking at the most recently effective charge after the Subscription Id creation"
        quote: true

      - &last_post_sign_up_giftcode
        name: last_post_sign_up_giftcode
        description: "The last post-sign-up giftcode associated with this Subscription Id, looking at the farthest future effective charge after the Subscription Id creation"
        quote: true

      - &first_post_sign_up_giftcode_campaign_name
        name: first_post_sign_up_giftcode_campaign_name
        description: "The campaign name of the first post-sign-up giftcode associated with this Subscription Id, looking at the most recently effective charge after the Subscription Id creation"
        quote: true

      - &last_post_sign_up_giftcode_campaign_name
        name: last_post_sign_up_giftcode_campaign_name
        description: "The campaign name of the first post-sign-up giftcode associated with this Subscription Id, looking at the farthest future effective charge after the Subscription Id creation"
        quote: true

      - &first_post_sign_up_discount_context
        name: first_post_sign_up_discount_context
        description: "Describes the first channel through which the discount was applied - CRM, in app, cancellation flow or via a customer service agent"
        quote: true

      - &discount_effective_start_week
        name: discount_effective_start_week
        description: "The week of the very first date that the discount will be effective on this Subscription Id"
        quote: true

      - &discount_effective_end_week
        name: discount_effective_end_week
        description: "The week of the very last date that the discount will be effective on this Subscription Id"
        quote: true

      - &discount_duration_weeks
        name: discount_duration_weeks
        description: "The duration (in weeks) of the discount applied to the subscription"
        quote: true

      - &subscription_discount_requested_date
        name: subscription_discount_requested_date
        description: "The date the discount was first created/applied in Zuora"
        quote: true

      - &subscription_current_discount_start_date
        name: subscription_current_discount_start_date
        description: "The start date of the discount"
        quote: true

      - &subscription_current_discount_start_week
        name: subscription_current_discount_start_week
        description: "The week end date of the start of the discount"
        quote: true

      - &subscription_current_discount_end_date
        name: subscription_current_discount_end_date
        description: "The end date of the discount"
        quote: true

      - &subscription_current_discount_end_week
        name: subscription_current_discount_end_week
        description: "The week end date of the end of the discount"
        quote: true

      - *is_sign_up_discount

      - *subscription_giftcode

      - *subscription_giftcode_campaign_name

      - *subscription_discount_context

      - *subscription_discount_percentage

      - *subscription_discount_amount

      - *subscription_discount_duration_weeks

      - &billing_account_product_group_count
        name: billing_account_product_group_count
        description: "The count of the amount of product groups (DAZN/NFL) the billing account has active on the given batch_date"
        quote: true

      - &billing_account_product_group_array
        name: billing_account_product_group_array
        description: "An array of all subscription_product_group values that are active on the billing account on the given batch_date"
        quote: true

      - &billing_account_trip_number
        name: billing_account_trip_number
        description: "The count of previous subscriptions from this billing account including this subscription for all product groups"
        quote: true

      - &billing_account__skey
        name: billing_account__skey
        description: "The surrogate key (skey) used to join on the billing_account dim"
        quote: true

      - &subscription_info__skey
        name: subscription_info__skey
        description: "The surrogate key (skey) used to join on the subscription_info dim"
        quote: true

      - &subscription_term__skey
        name: subscription_term__skey
        description: "The surrogate key (skey) used to join on the subscription_term dim"
        quote: true

      - &subscription_charge__skey
        name: subscription_charge__skey
        description: "The surrogate key (skey) used to join on the subscription_charge dim"
        quote: true

      - &subscription_free_trial__skey
        name: subscription_free_trial__skey
        description: "The surrogate key (skey) used to join on the subscription_free_trial dim"
        quote: true

      - &subscription_discount__skey
        name: subscription_discount__skey
        description: "The surrogate key (skey) used to join on the subscription_discount dim"
        quote: true

      - &subscription_pause__skey
        name: subscription_pause__skey
        description: "The surrogate key (skey) used to join on the subscription_pause dim"
        quote: true

      - &subscription_add_on__skey
        name: subscription_add_on__skey
        description: "The surrogate key (skey) used to join on the subscription_add_on dim"
        quote: true

      - &subscription_content_attribution__skey
        name: subscription_content_attribution__skey
        description: "The surrogate key (skey) used to join on the subscription_content_attribution__dim"
        quote: true

      - &subscription_source_system_name_derived__skey
        name: subscription_source_system_name_derived__skey
        description: "The surrogate key (skey) used to join on the sub_source_system dim"
        quote: true

      - &subscription_tracking_id__skey
        name: subscription_tracking_id__skey
        description: "The surrogate key (skey) used to join on the tracking_id dim"
        quote: true

      - &subscription_sign_up_campaign_id__skey
        name: subscription_sign_up_campaign_id__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - &first_post_sign_up_giftcode_campaign_name__skey
        name: first_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - &last_post_sign_up_giftcode_campaign_name__skey
        name: last_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - &subscription_giftcode_campaign_name__skey
        name: subscription_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - &is_paused
        name: is_paused
        description: "Boolean flag, true if the subscription is paused on the exact date_day of the spine"
        quote: true

      - &has_active_free_trial
        name: has_active_free_trial
        description: "Boolean flag, true if the subscription has an active free trial on the exact date_day of the spine"
        quote: true

      - &has_active_discount
        name: has_active_discount
        description: "Boolean flag, true if the subscription has any active discount on the exact date_day of the spine"
        quote: true

      - &active_discount_percentage
        name: active_discount_percentage
        description: "Percentage of the discount on that day, if there is one active"
        quote: true

      - &has_active_introductory_discount
        name: has_active_introductory_discount
        description: "Boolean flag, true if the subscription has an active introductory discount on the exact date_day of the spine"
        quote: true

      - &has_active_post_sign_up_discount
        name: has_active_post_sign_up_discount
        description: "Boolean flag, true if the subscription has an active post-sign-up discount on the exact date_day of the spine"
        quote: true

      - &discounted_monthly_recurring_revenue
        name: discounted_monthly_recurring_revenue
        description: "The MRR with discounts applied: MRR * active_discount_percentage"
        quote: true

      - &daily_revenue_estimate
        name: daily_revenue_estimate
        description: "The discounted MRR brought down to the daily level: Disc MRR / days_in_month"
        quote: true

      - &has_active_add_on
        name: has_active_add_on
        description: "Boolean flag, true if the subscription has an active Add On on the exact date_day of the spine"
        quote: true

      - &subscription_age_months
        name: subscription_age_months
        description: "The current duration, in months, of the Subscription from the very start of the subscription, including free trial period, until the batch_date"
        quote: true

      - name: subscription_monthly_recurring_revenue_daily_rank
        description: "The rank of the MRR, high to low, of the above dimensions"
        quote: true

      - &subscription_daily_status__skey
        name: subscription_daily_status__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_daily_status__dim"
        quote: true

      - &daily_usd_fx_rate__skey
        name: daily_usd_fx_rate__skey
        description: "The surrogate key (skey) used to join on the daily_usd_fx_rate dim"
        quote: true

      - &billing_account_product_groups__skey
        name: billing_account_product_groups__skey
        description: "The surrogate key (skey) used to join on the billing_account_product_groups__dim"
        quote: true

      - &subscription_auto_renew
        name: subscription_auto_renew
        description: "TBC"
        quote: true

      - &subscription_instalment_period
        name: subscription_instalment_period
        description: "TBC"
        quote: true

  - name: daily_subscriptions_filtered
    description: "An intermediate table containing the date spine of daily subscriptions for the Sub Mart with all filters needed for the end Mart"
    columns:
      - *META__DBT_INSERT_DTTS

      - *batch_date

      - *batch_week_end_date

      - *subscription_id

      - *subscription_name

      - *billing_account_id

      - *crm_account_id

      - *dazn_user_id

      - *billing_account_currency_code

      - *subscription_product_group

      - *record_valid_from_timestamp

      - *record_valid_until_timestamp

      - *subscription_start_date

      - *subscription_planned_end_date

      - *subscription_free_trial_start_date

      - *subscription_free_trial_end_date

      - *is_soft_cancelled

      - *is_draft

      - *is_suspended

      - *is_resubscription

      - *billing_account_is_batch_50

      - *subscription_pause_start_date

      - *subscription_pause_end_date

      - *subscription_add_on_effective_start_date

      - *subscription_add_on_effective_end_date

      - *subscription_monthly_recurring_revenue

      - *discount_effective_start_date

      - *discount_effective_end_date

      - *is_introductory_discount

      - *max_discount_percentage

      - *discount_percentage_distinct_count

      - *post_sign_up_giftcode_campaign_name_distinct_count

      - *discount_data

      - *subscription_discount_requested_date

      - *subscription_current_discount_start_date

      - *subscription_current_discount_start_week

      - *subscription_current_discount_end_date

      - *subscription_current_discount_end_week

      - *is_sign_up_discount

      - *subscription_giftcode

      - *subscription_country

      - *subscription_territory

      - *subscription_tier

      - *subscription_type

      - *subscription_rateplan_name

      - *subscription_rateplan_charge_name

      - *subscription_source_system_name_derived

      - *subscription_tracking_id

      - *subscription_sign_up_giftcode

      - *subscription_sign_up_campaign_id

      - *first_post_sign_up_giftcode

      - *last_post_sign_up_giftcode

      - *first_post_sign_up_giftcode_campaign_name

      - *last_post_sign_up_giftcode_campaign_name

      - *billing_account__skey

      - *subscription_info__skey

      - *subscription_term__skey

      - *subscription_charge__skey

      - *subscription_free_trial__skey

      - *subscription_discount__skey

      - *subscription_pause__skey

      - *subscription_add_on__skey

      - *subscription_content_attribution__skey

      - *subscription_source_system_name_derived__skey

      - *subscription_tracking_id__skey

      - *subscription_sign_up_campaign_id__skey

      - *first_post_sign_up_giftcode_campaign_name__skey

      - *last_post_sign_up_giftcode_campaign_name__skey

      - *subscription_giftcode_campaign_name__skey

      - *is_paused

      - *has_active_free_trial

      - *has_active_discount

      - *active_discount_percentage

      - *has_active_introductory_discount

      - *has_active_post_sign_up_discount

      - *discounted_monthly_recurring_revenue

      - *daily_revenue_estimate

      - *has_active_add_on

      - *subscription_age_months

      - *subscription_daily_status__skey

      - *daily_usd_fx_rate__skey

      - *billing_account_product_groups__skey

      - *subscription_auto_renew

      - *subscription_instalment_period

  - name: subscription_daily_counts_intermediate__fact
    description: "An intermediate table for the Subscription Daily Counts Mart containing the subscription level date spine of daily subscriptions with all filters needed for the end fact table"
    columns:
      - *META__DBT_INSERT_DTTS

      - *batch_date

      - *batch_week_end_date

      - *subscription_id

      - *subscription_name

      - *billing_account_id

      - *crm_account_id

      - *dazn_user_id

      - *billing_account_currency_code

      - *subscription_product_group

      - *subscription_start_date

      - *subscription_planned_end_date

      - *subscription_free_trial_start_date

      - *subscription_free_trial_end_date

      - *subscription_pause_start_date

      - *subscription_pause_end_date

      - *subscription_sign_up_giftcode

      - *first_post_sign_up_giftcode

      - *last_post_sign_up_giftcode

      - *subscription_current_discount_start_date

      - *subscription_current_discount_end_date

      - *subscription_giftcode

      - *billing_account__skey

      - *subscription_info__skey

      - *subscription_term__skey

      - *subscription_charge__skey

      - *subscription_free_trial__skey

      - *subscription_discount__skey

      - *subscription_pause__skey

      - *subscription_add_on__skey

      - *subscription_content_attribution__skey

      - *subscription_source_system_name_derived__skey

      - *subscription_tracking_id__skey

      - *subscription_sign_up_campaign_id__skey

      - *first_post_sign_up_giftcode_campaign_name__skey

      - *last_post_sign_up_giftcode_campaign_name__skey

      - *subscription_giftcode_campaign_name__skey

      - *subscription_daily_status__skey

      - *daily_usd_fx_rate__skey

      - *billing_account_product_groups__skey

  - name: current_subscriptions
    description: "A table surfacing the current view of every Subscription Name including changes/additions from the current day by using the Zuora Share data"
    columns:
      - *batch_date

      - *subscription_name

      - *billing_account_id

      - *dazn_user_id

      - *subscription_country

      - *subscription_territory

      - *subscription_start_date

      - *subscription_planned_end_date

      - *subscription_pause_start_date

      - *subscription_pause_end_date

      - *subscription_source_system_name_derived__skey

      - *subscription_sign_up_giftcode

      - *subscription_sign_up_campaign_id__skey

      - *subscription_tracking_id__skey

      - *subscription_product_group

      - *subscription_tier

      - *subscription_type

      - name: data_source
        description: "The source of the data, either 'batch_model' for subscriptions that have not updated int he current day, or 'share' for those that have"
        quote: true

      - &subscription_started
        name: subscription_started
        description: "Flag column denoting whether the subscription started or not on the batch date, True or False"
        quote: true

      - &subscription_ended
        name: subscription_ended
        description: "Flag column denoting whether subscription Ended or not on the batch date, True or False"
        quote: true

      - &pause_started
        name: pause_started
        description: "Flag column denoting whether pause period started or not on the batch date, True or False"
        quote: true

      - &pause_ended
        name: pause_started
        description: "Flag column denoting whether pause period ended or not on the batch date, True or False"
        quote: true

      - &free_trial_started
        name: free_trial_started
        description: "Flag column denoting whether Free Trial started or not on the batch date, True or False"
        quote: true

      - &free_trial_ended
        name: free_trial_ended
        description: "Flag column denoting whether Free Trial Ended or not on the batch date, True or False"
        quote: true

      - *is_resubscription

      - &is_immediate_resubscription
        name: is_immediate_resubscription
        description: "Flag column denoting whether immediate re-subscribed or not on the batch date, True or False"
        quote: true

      - &is_immediate_resubscription_cancel
        name: is_immediate_resubscription_cancel
        description: "Flag column denoting whether immediate re-subscription cancelled or not on the batch date, True or False"
        quote: true

      - *has_active_free_trial

      - *is_paused

      - &subscription_term_changes__skey
        name: subscription_term_changes__skey
        description: "Hash key of all the fields - subscription_started, subscription_ended, pause_started, pause_ended, free_trial_started, free_trial_ended, is_resubscription, is_immediate_resubscription, is_immediate_resubscription_cancel, has_active_free_trial and is_paused"
        quote: true

      - &churn_date
        name: churn_date
        description: "Date from which the subscription is not re-activated"
        quote: true

      - &aquisition_date
        name: aquisition_date
        description: "Date on which the First subscription got activated"
        quote: true

      - *subscription_free_trial_start_date

      - *subscription_free_trial_end_date

      - &free_trial_acquisition
        name: free_trial_acquisition
        description: "Flag column denoting whether subscription is activated from free trial or not on the batch date, True or False"
        quote: true
