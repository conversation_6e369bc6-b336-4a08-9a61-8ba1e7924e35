{{
    config(
        materialized='incremental',
        unique_key='"batch_date"',
        incremental_strategy='delete+insert',
        schema='TRANSIENT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        cluster_by=['"batch_date"'],
        tags=['presentation-subscription-domain']
    )
}}

{% set subscription_mart_variables = subscription_mart_variables() %}

WITH dim_date AS (
    SELECT * FROM {{ ref('dim_date') }}
    WHERE
        -- Running from the start of DAZN onwards until yesterday so any full rebuilds build the whole time frame
        "date_day" BETWEEN CASE WHEN CURRENT_USER() LIKE '%PROD%' THEN '2016-08-08' ELSE DATEADD('month',-1,CURRENT_DATE()) END
                            AND DATEADD('day', -1, CURRENT_DATE)
        {% if is_incremental() %}
        AND
        -- If incremental, then run for the rebuild days based on this variables macro: dbt/macros/Subscription/subscription_mart_variables.sql
        "date_day" BETWEEN DATEADD('day', -{{ subscription_mart_variables.rebuild_days}}, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
        {% endif %}
)

,sub_name_scd AS (
    SELECT * FROM {{ ref('subscription_name__scd') }}
)

,mrr_rank AS (
    SELECT * FROM {{ ref('subscription_daily_mrr_rank__dim') }}
    {% if is_incremental() %}
    -- If incremental, then run for the rebuild days based on this variables macro: dbt/macros/Subscription/subscription_mart_variables.sql
    WHERE "date_day" BETWEEN DATEADD('day', -{{ subscription_mart_variables.rebuild_days}}, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
    {% endif %}
)

,daily_discounts AS (
    SELECT * FROM {{ ref('daily_subscription_discounts') }}
    {% if is_incremental() %}
    -- If incremental, then run for the rebuild days based on this variables macro: dbt/macros/Subscription/subscription_mart_variables.sql
    WHERE "batch_date" BETWEEN DATEADD('day', -{{ subscription_mart_variables.rebuild_days}}, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
    {% endif %}
)

SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,dim_date."date_day" AS "batch_date"
    ,dim_date."week_end_date" AS "batch_week_end_date"
    ,sub_name_scd."subscription_id"
    ,sub_name_scd."subscription_name"
    ,sub_name_scd."billing_account_id"
    ,sub_name_scd."crm_account_id"
    ,sub_name_scd."dazn_user_id"
    ,sub_name_scd."billing_account_currency_code"
    ,sub_name_scd."subscription_product_group"
    ,sub_name_scd."record_valid_from_timestamp"
    ,sub_name_scd."record_valid_until_timestamp"
    ,sub_name_scd."subscription_start_date"
    ,sub_name_scd."subscription_planned_end_date"
    ,sub_name_scd."subscription_free_trial_start_date"
    ,sub_name_scd."subscription_free_trial_end_date"
    ,sub_name_scd."is_soft_cancelled"
    ,sub_name_scd."is_draft"
    ,sub_name_scd."is_suspended"
    ,sub_name_scd."is_resubscription"
    ,sub_name_scd."billing_account_is_batch_50"
    ,sub_name_scd."subscription_pause_start_date"
    ,sub_name_scd."subscription_pause_end_date"
    ,sub_name_scd."subscription_add_on_effective_start_date"
    ,sub_name_scd."subscription_add_on_effective_end_date"
    ,sub_name_scd."subscription_monthly_recurring_revenue" AS "next_subscription_monthly_recurring_revenue"
    ,CASE
        WHEN sub_name_scd."subscription_rateplan_charge_effective_start_date" > dim_date."date_day" THEN IFNULL(sub_name_scd."previous_subscription_monthly_recurring_revenue", sub_name_scd."subscription_monthly_recurring_revenue")
        ELSE sub_name_scd."subscription_monthly_recurring_revenue"
    END AS "subscription_monthly_recurring_revenue_current"
    -- Need to create an extra field so we can use the _current field in calculations below without issues of duplicate field names
    ,"subscription_monthly_recurring_revenue_current" AS "subscription_monthly_recurring_revenue"
    ,sub_name_scd."discount_effective_start_date"
    ,sub_name_scd."discount_effective_end_date"
    ,sub_name_scd."is_introductory_discount"
    ,sub_name_scd."max_discount_percentage"
    ,sub_name_scd."discount_percentage_distinct_count"
    ,sub_name_scd."post_sign_up_giftcode_campaign_name_distinct_count"
    ,sub_name_scd."discount_data"
    ,sub_name_scd."subscription_country"
    ,sub_name_scd."subscription_territory"
    ,sub_name_scd."subscription_tier"
    ,sub_name_scd."subscription_type"
    ,sub_name_scd."subscription_rateplan_name"
    ,sub_name_scd."subscription_rateplan_charge_name"
    ,sub_name_scd."subscription_bill_cycle_day"
    ,sub_name_scd."subscription_source_system_name_derived"
    ,sub_name_scd."subscription_tracking_id"
    -- These are all old discount fields that we are leaving here only until deprecating the old model after migration
    ,sub_name_scd."subscription_sign_up_giftcode"
    ,sub_name_scd."subscription_sign_up_campaign_id"
    ,sub_name_scd."first_post_sign_up_giftcode"
    ,sub_name_scd."last_post_sign_up_giftcode"
    ,sub_name_scd."first_post_sign_up_giftcode_campaign_name"
    ,sub_name_scd."last_post_sign_up_giftcode_campaign_name"
    ,sub_name_scd."first_post_sign_up_discount_context"
    ,sub_name_scd."discount_effective_start_week"
    ,sub_name_scd."discount_effective_end_week"
    ,sub_name_scd."discount_duration_weeks"
    -- New discount fields from here on
    ,daily_discounts."subscription_discount_requested_timestamp"::DATE AS "subscription_discount_requested_date"
    ,daily_discounts."subscription_discount_start_date" AS "subscription_current_discount_start_date"
    ,LAST_DAY(daily_discounts."subscription_discount_start_date", 'week') AS "subscription_current_discount_start_week"
    ,daily_discounts."subscription_discount_end_date" AS "subscription_current_discount_end_date"
    ,CASE
        WHEN daily_discounts."subscription_discount_end_date" >= '9999-01-01' THEN '9999-01-02'
        ELSE LAST_DAY(daily_discounts."subscription_discount_end_date", 'week')
    END AS "subscription_current_discount_end_week"
    ,daily_discounts."is_sign_up_discount"
    ,daily_discounts."subscription_giftcode"
    ,CASE
        WHEN sub_name_scd."subscription_source_system_name_derived__skey" = 'Docomo' THEN sub_name_scd."subscription_sign_up_campaign_id__skey"
--Added case to fullfill the below requirment
--https://livesport.atlassian.net/wiki/spaces/HYD/pages/6317742291/Diversify+Access+Codes+Shorter+Period+Codes 
        WHEN "subscription_free_trial_length_days_advertised" in ( 1,3,7,10,14) AND "subscription_sign_up_campaign_id__skey" IS NOT NULL
        THEN "subscription_sign_up_campaign_id__skey"
        ELSE daily_discounts."subscription_giftcode_campaign_name"
    END "subscription_giftcode_campaign_name_patched"
    ,daily_discounts."subscription_discount_context"
    ,daily_discounts."subscription_discount_percentage"
    ,daily_discounts."subscription_discount_amount"
    ,daily_discounts."subscription_discount_duration_weeks" AS "subscription_current_discount_duration_weeks"
    ,COUNT(DISTINCT sub_name_scd."subscription_product_group") OVER (PARTITION BY sub_name_scd."billing_account_id", dim_date."date_day") AS "billing_account_product_group_count"
    ,ARRAY_AGG(DISTINCT sub_name_scd."subscription_product_group") WITHIN GROUP (ORDER BY sub_name_scd."subscription_product_group") OVER (PARTITION BY sub_name_scd."billing_account_id", dim_date."date_day") AS "billing_account_product_group_array"
    ,sub_name_scd."billing_account_trip_number"
    ,sub_name_scd."billing_account__skey"
    ,sub_name_scd."subscription_info__skey"
    ,sub_name_scd."subscription_term__skey"
    ,HASH(sub_name_scd."subscription_type"
        ,"subscription_monthly_recurring_revenue_current"
        ,sub_name_scd."subscription_monthly_recurring_revenue"
        ,sub_name_scd."subscription_bill_cycle_day"
        ,mrr_rank."subscription_monthly_recurring_revenue_daily_rank")
        AS "subscription_charge__skey"
    ,sub_name_scd."subscription_free_trial__skey"
    ,HASH(
        -- Old fields that will be deprecated after migration
        sub_name_scd."first_post_sign_up_giftcode_campaign_name"
        ,sub_name_scd."last_post_sign_up_giftcode_campaign_name"
        ,sub_name_scd."post_sign_up_giftcode_campaign_name_distinct_count"
        ,sub_name_scd."discount_percentage_distinct_count"
        ,sub_name_scd."max_discount_percentage"
        ,sub_name_scd."first_post_sign_up_discount_context"
        ,sub_name_scd."discount_effective_start_week"
        ,sub_name_scd."discount_effective_end_week"
        ,sub_name_scd."discount_duration_weeks"
        ,sub_name_scd."is_introductory_discount"
        -- New fields from here on
        ,"subscription_current_discount_start_week"
        ,"subscription_current_discount_end_week"
        ,daily_discounts."subscription_discount_duration_weeks"
        ,daily_discounts."is_sign_up_discount"
        ,"subscription_giftcode_campaign_name_patched"
        ,daily_discounts."subscription_discount_context"
        ,daily_discounts."subscription_discount_percentage"
        ,daily_discounts."subscription_discount_amount")
        AS "subscription_discount__skey"
    ,sub_name_scd."subscription_pause__skey"
    ,sub_name_scd."subscription_add_on__skey"
    ,sub_name_scd."subscription_content_attribution__skey"
    ,sub_name_scd."subscription_source_system_name_derived__skey"
    ,sub_name_scd."subscription_tracking_id__skey"
    ,"subscription_giftcode_campaign_name_patched" AS "subscription_giftcode_campaign_name__skey"
    ,sub_name_scd."subscription_sign_up_campaign_id__skey"
    ,sub_name_scd."first_post_sign_up_giftcode_campaign_name__skey"
    ,sub_name_scd."last_post_sign_up_giftcode_campaign_name__skey"
    ,CASE 
        WHEN dim_date."date_day" >= sub_name_scd."subscription_pause_start_date" 
            AND dim_date."date_day" < sub_name_scd."subscription_pause_end_date" 
            THEN TRUE 
            ELSE FALSE 
    END  AS "is_paused"
    ,CASE 
        WHEN dim_date."date_day" >= sub_name_scd."subscription_free_trial_start_date" 
            AND dim_date."date_day" < sub_name_scd."subscription_free_trial_end_date" 
            THEN TRUE 
            ELSE FALSE 
    END  AS "has_active_free_trial"
    ,CASE 
        WHEN dim_date."date_day" >= daily_discounts."subscription_discount_start_date" 
            AND dim_date."date_day" < daily_discounts."subscription_discount_end_date" 
            THEN TRUE 
            ELSE FALSE 
    END  AS "has_active_discount"
    ,CASE 
        WHEN "has_active_discount" THEN IFNULL(daily_discounts."subscription_discount_percentage", 0)/100 ELSE 0 
    END AS "active_discount_percentage"
    ,CASE 
        WHEN "has_active_discount" THEN IFNULL(daily_discounts."subscription_discount_amount", 0) ELSE 0 
    END AS "active_discount_amount"
    -- These next two fields can be deprecated once we've fully mirgated to the new discount dim, as they don't tell us anything more than the new is_sign_up_discount field
    ,CASE 
        WHEN "has_active_discount" AND "is_introductory_discount" THEN TRUE ELSE FALSE 
    END AS "has_active_introductory_discount"
    ,CASE 
        WHEN "has_active_discount" AND "is_introductory_discount" = FALSE THEN TRUE ELSE FALSE 
    END AS "has_active_post_sign_up_discount"
    ,CASE WHEN "has_active_discount" THEN 
    ROUND(("subscription_monthly_recurring_revenue_current" - "active_discount_amount") * (1 - "active_discount_percentage"),5) 
    ELSE "subscription_monthly_recurring_revenue_current" END AS "discounted_monthly_recurring_revenue"
    ,"discounted_monthly_recurring_revenue" / DAY(dim_date."month_end_date") AS "daily_revenue_estimate"
    ,CASE 
        WHEN dim_date."date_day" >= "subscription_add_on_effective_start_date" AND dim_date."date_day" < "subscription_add_on_effective_end_date" THEN TRUE ELSE FALSE 
    END AS "has_active_add_on"
    ,DATEDIFF('month', sub_name_scd."subscription_name_original_created_timestamp", dim_date."date_day") AS "subscription_age_months"
    ,mrr_rank."subscription_monthly_recurring_revenue_daily_rank"
    ,HASH("is_paused"
        ,"has_active_post_sign_up_discount"
        ,"has_active_free_trial"
        ,"has_active_introductory_discount"
        ,"discounted_monthly_recurring_revenue"
        ,"daily_revenue_estimate"
        ,"has_active_add_on"
    ) AS "subscription_daily_status__skey"
    ,HASH(dim_date."date_day"
        ,sub_name_scd."billing_account_currency_code"
    ) AS "daily_usd_fx_rate__skey"
    ,HASH("billing_account_product_group_count"
        ,"billing_account_product_group_array"
        ,"billing_account_trip_number"
    ) AS "billing_account_product_groups__skey"
    ,sub_name_scd."subscription_auto_renew"
    ,sub_name_scd."subscription_instalment_period"
FROM dim_date
LEFT JOIN sub_name_scd
    -- SubNames that are currently active/valid in Zuora at the end of the batch_date
    ON dim_date."date_day" >= sub_name_scd."record_valid_from_timestamp"::DATE
    AND dim_date."date_day" < sub_name_scd."record_valid_until_timestamp"::DATE
LEFT JOIN mrr_rank
    ON mrr_rank."is_valid_mrr"
    AND dim_date."date_day" = mrr_rank."date_day"
    AND sub_name_scd."subscription_territory" = mrr_rank."subscription_territory"
    AND sub_name_scd."subscription_product_group" = mrr_rank."subscription_product_group"
    AND sub_name_scd."subscription_type" = mrr_rank."subscription_type"
    AND IFNULL(sub_name_scd."subscription_tier", 'Empty') = IFNULL(mrr_rank."subscription_tier", 'Empty')
    AND sub_name_scd."billing_account_currency_code" = mrr_rank."billing_account_currency_code"
    AND sub_name_scd."subscription_monthly_recurring_revenue" = mrr_rank."subscription_monthly_recurring_revenue"
LEFT JOIN daily_discounts
    ON dim_date."date_day" = daily_discounts."batch_date"
    AND sub_name_scd."subscription_name" = daily_discounts."subscription_name"
