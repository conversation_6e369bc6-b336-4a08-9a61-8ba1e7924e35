{{
    config(
        materialized='incremental',
        unique_key='"batch_date"',
        incremental_strategy='delete+insert',
        schema='TRANSIENT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        cluster_by=['"batch_date"'],
        tags=['presentation-subscription-domain']
    )
}}

{% set subscription_mart_variables = subscription_mart_variables() %}

WITH sub_counts_raw AS (
    SELECT * FROM {{ ref('daily_subscriptions_raw') }}
)

SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,"batch_date"
    ,"batch_week_end_date"
    ,"subscription_id"
    ,"subscription_name"
    ,"billing_account_id"
    ,"crm_account_id"
    ,"dazn_user_id"
    ,"billing_account_currency_code"
    ,"subscription_product_group"
    ,"record_valid_from_timestamp"
    ,"record_valid_until_timestamp"
    ,"subscription_start_date"
    ,"subscription_planned_end_date"
    ,"subscription_free_trial_start_date"
    ,"subscription_free_trial_end_date"
    ,"is_soft_cancelled"
    ,"is_draft"
    ,"is_suspended"
    ,"is_resubscription"
    ,"billing_account_is_batch_50"
    ,"subscription_pause_start_date"
    ,"subscription_pause_end_date"
    ,"subscription_add_on_effective_start_date"
    ,"subscription_add_on_effective_end_date"
    ,"subscription_monthly_recurring_revenue"
    ,"discount_effective_start_date"
    ,"discount_effective_end_date"
    ,"is_introductory_discount"
    ,"max_discount_percentage"
    ,"discount_percentage_distinct_count"
    ,"post_sign_up_giftcode_campaign_name_distinct_count"
    ,"discount_data"
    ,"subscription_discount_requested_date"
    ,"subscription_current_discount_start_date"
    ,"subscription_current_discount_end_date"
    ,"is_sign_up_discount"
    ,"subscription_giftcode"
    ,"subscription_country"
    ,"subscription_territory"
    ,"subscription_tier"
    ,"subscription_type"
    ,"subscription_rateplan_name"
    ,"subscription_rateplan_charge_name"
    ,"subscription_source_system_name_derived"
    ,"subscription_tracking_id"
    ,"subscription_sign_up_giftcode"
    ,"subscription_sign_up_campaign_id"
    ,"first_post_sign_up_giftcode"
    ,"last_post_sign_up_giftcode"
    ,"first_post_sign_up_giftcode_campaign_name"
    ,"last_post_sign_up_giftcode_campaign_name"
    ,"billing_account__skey"
    ,"subscription_info__skey"
    ,"subscription_term__skey"
    ,"subscription_charge__skey"
    ,"subscription_free_trial__skey"
    ,"subscription_discount__skey"
    ,"subscription_pause__skey"
    ,"subscription_add_on__skey"
    ,"subscription_content_attribution__skey"
    ,"subscription_source_system_name_derived__skey"
    ,"subscription_tracking_id__skey"
    ,"subscription_sign_up_campaign_id__skey"
    ,"first_post_sign_up_giftcode_campaign_name__skey"
    ,"last_post_sign_up_giftcode_campaign_name__skey"
    ,"subscription_giftcode_campaign_name__skey"
    ,"is_paused"
    ,"has_active_free_trial"
    ,"has_active_introductory_discount"
    ,"has_active_post_sign_up_discount"
    ,"has_active_add_on"
    ,"discounted_monthly_recurring_revenue"
    ,"daily_revenue_estimate"
    ,"subscription_age_months"
    ,"subscription_daily_status__skey"
    ,"daily_usd_fx_rate__skey"
    ,"billing_account_product_groups__skey"
    ,"subscription_auto_renew"
    ,"subscription_instalment_period"
FROM sub_counts_raw
WHERE
    {% if is_incremental() %}
    -- If incremental, then run for the rebuild days based on this variables macro: dbt/macros/Subscription/subscription_mart_variables.sql
    "batch_date" BETWEEN DATEADD('day', -{{ subscription_mart_variables.rebuild_days}}, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
    AND
    {% endif %}
    -- Sub Status not Draft
    "is_draft" = FALSE
    AND
    -- and Sub Status not suspended
    "is_suspended" = FALSE
    AND
    -- and not 'test' accounts
    IFNULL("billing_account_is_batch_50", FALSE) = FALSE
    AND
    -- and not commercial premises subs
    IFNULL("subscription_source_system_name_derived", '-') != 'Commercial Premises'
    AND
    -- and not any kind of test account that might not be included in Batch50 based on the list in this variables macro: dbt/macros/Subscription/subscription_mart_variables.sql
    IFNULL("subscription_giftcode_campaign_name__skey", '-') NOT IN ({{ in_clause_from_list(subscription_mart_variables.test_campaign_ids) }})
-- take into account overlapping subs by deduping them using the most recently created as the source of truth
QUALIFY ROW_NUMBER() OVER (PARTITION BY "billing_account_id", "subscription_product_group", "batch_date" ORDER BY "subscription_planned_end_date" DESC, "subscription_start_date" DESC) = 1
