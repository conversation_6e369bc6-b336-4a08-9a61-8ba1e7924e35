{{
    config(
        materialized='view',
        database='SUBSCRIPTION_DAILY_COUNT__B2C__MART__' + snowflake_env(),
        schema='INTERMEDIATE',
        tags=['presentation-subscription-domain']
    )
}}

{% set subscription_mart_variables = subscription_mart_variables() %}

WITH zuora_share_sub_name_current AS (
    SELECT * FROM {{ ref('staging__zuora_share__subscription_name_current') }}
)

,zuora_share_rateplan_current AS (
    SELECT * FROM {{ ref('staging__zuora_share__rateplan_current') }}
)

,zuora_share_rateplancharge_current AS (
    SELECT * FROM {{ ref('staging__zuora_share__rateplancharge_current') }}
)

,zuora_share_account_current AS (
    SELECT * FROM {{ ref('staging__zuora_share__account_current') }}
)

,region_dim AS (
    SELECT * FROM {{ ref('region_dimension') }}
)

-- Newly added Dimension for the JIRA 
,subscription_name__scd AS (
    SELECT * FROM {{ ref('subscription_name__scd')}}
)

, subname_scd AS (
        SELECT 
           *
        ,HASH(
        "subscription_started"
        ,"subscription_ended"
        ,"pause_started"
        ,"pause_ended"
        ,"free_trial_started"
        ,"free_trial_ended"
        ,"is_resubscription"
        ,"is_immediate_resubscription"
        ,"is_immediate_resubscription_cancel"
        ,"has_active_free_trial"
        ,"is_paused"
        ) AS "subscription_term_changes__skey"

        FROM 
        (
            SELECT 
            "subscription_name"
            , "subscription_start_date" 
            ,"billing_account_id"
            ,"subscription_product_group"
            -- ,"subscription_name" 
                 ,FALSE AS "subscription_ended"
            ,"is_resubscription"
            , "subscription_free_trial_start_date"
            , "subscription_free_trial_end_date"
            ,"subscription_pause_start_date"
            , "subscription_pause_end_date" 
            -- ,"has_active_free_trial"
            --  ,"is_paused"
                ,FALSE AS "is_immediate_resubscription_cancel"
                 ,CASE WHEN DATEADD('day',-1,Current_date()) = "subscription_free_trial_start_date" THEN TRUE ELSE FALSE END AS "free_trial_started"
            ,CASE WHEN DATEADD('day',-1,Current_date())= "subscription_free_trial_end_date" THEN TRUE ELSE FALSE END AS "free_trial_ended"

            ,CASE WHEN DATEADD('day',-1,Current_date()) = "subscription_pause_start_date" 
            THEN TRUE ELSE FALSE END AS "pause_started"
            ,CASE WHEN DATEADD('day',-1,Current_date()) = "subscription_pause_end_date" THEN TRUE ELSE FALSE END AS "pause_ended"  

            ,CASE WHEN DATEADD('day',-1,Current_date()) >= subname_scd."subscription_pause_start_date" AND DATEADD('day',-1,Current_date()) < subname_scd."subscription_pause_end_date" THEN TRUE ELSE FALSE END  AS "is_paused"
            ,CASE WHEN DATEADD('day',-1,Current_date()) >= subname_scd."subscription_free_trial_start_date" AND DATEADD('day',-1,Current_date()) < subname_scd."subscription_free_trial_end_date" THEN TRUE ELSE FALSE END  AS "has_active_free_trial"

             ,LAG(Date("record_valid_from_timestamp")) OVER (PARTITION BY "billing_account_id", "subscription_product_group" ORDER BY "record_valid_from_timestamp") AS "previous_billing_account_batch_date"
            ,LAG("subscription_name") OVER (PARTITION BY "billing_account_id", "subscription_product_group" ORDER BY "record_valid_from_timestamp") AS "previous_billing_account_subscription_name"
            ,LEAD(DATE("record_valid_from_timestamp")) OVER (PARTITION BY "billing_account_id", "subscription_product_group" ORDER BY "record_valid_from_timestamp") AS "next_billing_account_batch_date"
            ,LEAD("subscription_name") OVER (PARTITION BY "billing_account_id", "subscription_product_group" ORDER BY "record_valid_from_timestamp") AS "next_billing_account_subscription_name"
            ,DATEADD('day',-1,Current_date()) >= "subscription_start_date"
            and DATEADD('day',-1,Current_date()) < "subscription_planned_end_date"
            AS "subscription_started"
            ,CASE WHEN "previous_billing_account_batch_date" = DATEADD('day',-1,DATEADD('day',-1,Current_date()) )  AND "previous_billing_account_subscription_name" != "subscription_name" THEN TRUE ELSE FALSE END AS "is_immediate_resubscription"
            ,"record_valid_until_timestamp"   
            FROM subscription_name__scd subname_scd
            WHERE current_date() BETWEEN "record_valid_from_timestamp" AND "record_valid_until_timestamp"
            --GROUP BY 1
        ) sub_name_scd
    )

,freetrial_subs as
 (
    select "subscription_name",MAX(case when "subscription_start_date" = dateadd(day,-1,current_date())  then TRUE ELSE FALSE END) "subscription_started" 
    FROM subscription_name__scd subname_scd
    WHERE dateadd(day,-1,current_date()) BETWEEN "record_valid_from_timestamp" AND "record_valid_until_timestamp"
    and "subscription_free_trial_end_date" in( dateadd(day,-1,current_date()) ,current_date()) group by 1
)

, billing_subgroup_count as (
    select "billing_account_id","subscription_product_group" As subscription_product_group_scd , count(distinct "subscription_name") sub_count , 
    max(DATE("record_valid_until_timestamp")) EFF_ENDDATE
    from subname_scd group by 1,2
)

,max_batch_date AS (
    SELECT max("batch_date") AS MAX_BATCH_DT FROM {{ ref('daily_subscriptions_filtered') }}
)
,daily_subs_filtered AS (
    SELECT * FROM {{ ref('daily_subscriptions_filtered') }} INNER JOIN  max_batch_date ON "batch_date"=MAX_BATCH_DT
)

,bill_acc_dim AS (
    SELECT * FROM {{ ref('daily_counts__billing_account__dim') }}
)

,sub_info_dim AS (
    SELECT * FROM {{ ref('daily_counts__subscription_mart_info__dim') }}
)

,sub_charge_dim AS (
    SELECT * FROM {{ ref('daily_counts__subscription_mart_charge__dim') }}

)


,share_model AS (

    SELECT
    CURRENT_DATE AS "batch_date"
    ,zuora_share_sub_name_current."subscription_name"
    ,zuora_share_sub_name_current."billing_account_id"
    ,zuora_share_account_current."dazn_user_id"
    ,zuora_share_sub_name_current."subscription_country"
    ,region_dim."territory" AS "subscription_territory"
    ,LEAST(zuora_share_sub_name_current."subscription_start_date", zuora_share_sub_name_current."subscription_name_original_created_timestamp")::DATE AS "subscription_start_date"
    ,GREATEST(IFNULL(zuora_share_sub_name_current."subscription_end_date", '9999-12-31'), zuora_share_sub_name_current."subscription_id_created_timestamp")::DATE AS "subscription_planned_end_date"
    -- NULLing out, because if there is a change to their Sub, then they probably arent paused, and we still want the pause date to come through th ebatch model
    ,NULL AS "subscription_pause_start_date"
    ,NULL AS "subscription_pause_end_date"
    ,COALESCE(CONCAT(zuora_share_sub_name_current."subscription_direct_carrier_billing_carrier_name",' DCB'), zuora_share_sub_name_current."subscription_source_system_name", 'Direct') AS "subscription_source_system_name_derived__skey"
    ,zuora_share_sub_name_current."subscription_sign_up_giftcode"
    ,zuora_share_sub_name_current."subscription_sign_up_campaign_id" AS "subscription_sign_up_campaign_id__skey"
    ,zuora_share_sub_name_current."subscription_tracking_id" AS "subscription_tracking_id__skey"
    ,zuora_share_sub_name_current."subscription_product_group"
    ,zuora_share_rateplan_current."entitlement_set_id" AS "subscription_tier"
    ,CASE
    WHEN zuora_share_sub_name_current."subscription_source_system_name" != '' AND zuora_share_sub_name_current."subscription_sign_up_giftcode" NOT IN ('', 'null') THEN 'Externally Billed'
    WHEN zuora_share_rateplancharge_current."rateplan_charge_name" ILIKE '%month%' THEN 'Monthly'
    WHEN zuora_share_rateplancharge_current."rateplan_charge_name" ILIKE '%annual%' THEN 'Annual'
    WHEN zuora_share_rateplancharge_current."rateplan_charge_name" ILIKE ANY ('%instal%', '%ppi%') THEN 'Instalment'
    WHEN zuora_share_sub_name_current."subscription_term_type" = 'EVERGREEN' AND zuora_share_rateplancharge_current."rateplan_charge_billing_period" = 'Month' THEN 'Monthly'
    WHEN zuora_share_sub_name_current."subscription_term_type" = 'TERMED' AND zuora_share_rateplancharge_current."rateplan_charge_billing_period" = 'Subscription Term' THEN 'Annual'
    WHEN zuora_share_sub_name_current."subscription_term_type" = 'TERMED' AND zuora_share_rateplancharge_current."rateplan_charge_billing_period" = 'Month' THEN 'Instalment'
    -- NULLing out the Unknowns to match how they will come through the batch model
    ELSE NULL
    END AS "subscription_type"
    ,'share' AS "data_source"
    ,case when GREATEST(IFNULL(zuora_share_sub_name_current."subscription_end_date", '9999-12-31'), zuora_share_sub_name_current."subscription_id_created_timestamp")::DATE ='9999-12-31' then TRUE ELSE FALSE END "subscription_started"
    ,case when GREATEST(IFNULL(zuora_share_sub_name_current."subscription_end_date", '9999-12-31'), zuora_share_sub_name_current."subscription_id_created_timestamp")::DATE =CURRENT_DATE() then TRUE ELSE FALSE END "subscription_ended"
    ,FALSE AS "pause_started"
    ,FALSE AS "pause_ended"
    ,
    CASE WHEN (CURRENT_DATE() = 
    CASE

    WHEN trim("subscription_number_of_free_trial_periods") = '0' OR "subscription_number_of_free_trial_periods" IS NULL THEN NULL

    ELSE "subscription_name_original_created_timestamp"::DATE
    END
    ) 
    THEN TRUE ELSE FALSE END  "free_trial_started"  


    ,
    case when ( CURRENT_DATE() =
    CASE

    WHEN trim("subscription_number_of_free_trial_periods") = '0' OR "subscription_number_of_free_trial_periods" IS NULL THEN NULL

    WHEN "subscription_free_trial_periods_type" = 'months' OR "subscription_free_trial_periods_type" IS NULL THEN DATEADD('month', IFNULL(TRY_TO_DECIMAL("subscription_number_of_free_trial_periods"), '0'), "subscription_name_original_created_timestamp"::DATE)

    WHEN "subscription_product_group" = 'NFL' AND "subscription_free_trial_periods_type" = 'days' AND "subscription_start_date" = '2023-08-01' THEN DATEADD('day', IFNULL(TRY_TO_DECIMAL("subscription_number_of_free_trial_periods"), '0'), "subscription_name_original_created_timestamp"::DATE)



    WHEN "subscription_free_trial_periods_type" = 'days' THEN "subscription_start_date"
    END) then  true else false End "free_trial_ended"

    ,  case when subname_Scd."subscription_name" is not null and b.sub_count > 1 then TRUE
    when subname_scd."subscription_name" is null and  b.sub_count >0  then TRUE
    ELSE FALSE END "is_resubscription"

    ,  case when subname_Scd."subscription_name" is  null and DATEADD('day',-1 ,current_date())=b.eff_enddate then TRUE ELSE FALSE END  AS "is_immediate_resubscription"

    , FALSE AS "is_immediate_resubscription_cancel"
    , case when current_date() >= (CASE

    WHEN trim("subscription_number_of_free_trial_periods") = '0' OR "subscription_number_of_free_trial_periods" IS NULL THEN NULL

    ELSE "subscription_name_original_created_timestamp"::DATE
    END)  AND current_date() < (CASE

    WHEN trim("subscription_number_of_free_trial_periods") = '0' OR "subscription_number_of_free_trial_periods" IS NULL THEN NULL

    WHEN "subscription_free_trial_periods_type" = 'months' OR "subscription_free_trial_periods_type" IS NULL THEN DATEADD('month', IFNULL(TRY_TO_DECIMAL("subscription_number_of_free_trial_periods"), '0'), "subscription_name_original_created_timestamp"::DATE)

    WHEN "subscription_product_group" = 'NFL' AND "subscription_free_trial_periods_type" = 'days' AND "subscription_start_date" = '2023-08-01' THEN DATEADD('day', IFNULL(TRY_TO_DECIMAL("subscription_number_of_free_trial_periods"), '0'), "subscription_name_original_created_timestamp"::DATE)



    WHEN "subscription_free_trial_periods_type" = 'days' THEN "subscription_start_date"
    END) THEN TRUE ELSE FALSE END "has_active_free_trial"
    ,FALSE  "is_paused"

    ,case when "subscription_end_date"=CURRENT_DATE()
    then  "subscription_end_date" ELSE NULL END CHURN_DATE,
    "subscription_start_date" AS ACQUISTION_DATE

    ,CASE

    WHEN trim("subscription_number_of_free_trial_periods") = '0' OR "subscription_number_of_free_trial_periods" IS NULL THEN NULL

    ELSE "subscription_name_original_created_timestamp"::DATE
    END as "subscription_free_trial_start_date"
    , (CASE

    WHEN trim("subscription_number_of_free_trial_periods") = '0' OR "subscription_number_of_free_trial_periods" IS NULL THEN NULL

    WHEN "subscription_free_trial_periods_type" = 'months' OR "subscription_free_trial_periods_type" IS NULL THEN DATEADD('month', IFNULL(TRY_TO_DECIMAL("subscription_number_of_free_trial_periods"), '0'), "subscription_name_original_created_timestamp"::DATE)

    WHEN "subscription_product_group" = 'NFL' AND "subscription_free_trial_periods_type" = 'days' AND "subscription_start_date" = '2023-08-01' THEN DATEADD('day', IFNULL(TRY_TO_DECIMAL("subscription_number_of_free_trial_periods"), '0'), "subscription_name_original_created_timestamp"::DATE)



    WHEN "subscription_free_trial_periods_type" = 'days' THEN "subscription_start_date"
    END) as "subscription_free_trial_end_date"
    FROM zuora_share_sub_name_current 
    LEFT JOIN zuora_share_rateplan_current USING ("subscription_id")
    LEFT JOIN zuora_share_rateplancharge_current USING ("rateplan_id")
    LEFT JOIN zuora_share_account_current USING ("billing_account_id")
    LEFT JOIN region_dim ON zuora_share_sub_name_current."subscription_country" = region_dim."join_key"
    LEFT JOIN subname_scd  USING ("subscription_name") 
    LEFT JOIN billing_subgroup_count b on  b."billing_account_id"=zuora_share_sub_name_current."billing_account_id"
    and b.subscription_product_group_scd= zuora_share_sub_name_current."subscription_product_group"


    WHERE
    -- Only looking at subscriptions that have been updated today
    zuora_share_sub_name_current."subscription_id_updated_timestamp" >= DATEADD('day', 1, '2023-10-16')
    AND
    -- Removing any test customers in Batch50 (IFNULLing just in case the account isn't created in time somehow)
    IFNULL(zuora_share_account_current."billing_account_batch_name", 'Batch0') != 'Batch50'
    AND
    -- and not commercial premises subs
    IFNULL(zuora_share_sub_name_current."subscription_source_system_name", '-') != 'Commercial Premises'
    AND
    -- and not any kind of test account that might not be included in Batch50 based on the list in this variables macro: dbt/macros/Subscription/subscription_mart_variables.sql
    IFNULL(zuora_share_sub_name_current."subscription_sign_up_campaign_id", '-') NOT IN ({{ in_clause_from_list(subscription_mart_variables.test_campaign_ids) }})
    -- Filtering for only valid Subscription RPCs
    AND
    IFNULL(zuora_share_rateplan_current."rateplan_product_type", 'NULL') NOT IN ('addon', 'ppv')
    AND
    (zuora_share_rateplancharge_current."rateplan_charge_charge_model" = 'Flat Fee Pricing' OR zuora_share_rateplancharge_current."rateplan_charge_charge_model" IS NULL)
    AND
    (zuora_share_rateplancharge_current."rateplan_charge_charge_type" = 'Recurring' OR zuora_share_rateplancharge_current."rateplan_charge_charge_type" IS NULL)
    AND
    (zuora_share_rateplancharge_current."rateplan_charge_effective_start_date" != zuora_share_rateplancharge_current."rateplan_charge_effective_end_date" OR zuora_share_rateplancharge_current."rateplan_charge_effective_start_date" IS NULL)
    QUALIFY ROW_NUMBER() OVER (PARTITION BY zuora_share_sub_name_current."subscription_id" ORDER BY zuora_share_rateplancharge_current."rateplan_charge_effective_end_date" DESC, zuora_share_rateplancharge_current."rateplan_charge_effective_start_date" DESC NULLS LAST, zuora_share_rateplancharge_current."rateplan_charge_version"::INT DESC) = 1
)

,batch_model AS (
     SELECT
         "batch_date"
        ,daily_subs_filtered."subscription_name"
        ,daily_subs_filtered."billing_account_id"
        ,daily_subs_filtered."dazn_user_id"
        ,bill_acc_dim."subscription_country"
        ,bill_acc_dim."subscription_territory"
        ,daily_subs_filtered."subscription_start_date"
        ,daily_subs_filtered."subscription_planned_end_date"
        ,daily_subs_filtered."subscription_pause_start_date"
        ,daily_subs_filtered."subscription_pause_end_date"
        ,daily_subs_filtered."subscription_source_system_name_derived__skey"
        ,daily_subs_filtered."subscription_sign_up_giftcode"
        ,daily_subs_filtered."subscription_sign_up_campaign_id__skey"
        ,daily_subs_filtered."subscription_tracking_id__skey"
        ,sub_info_dim."subscription_product_group"
        ,sub_info_dim."subscription_tier"
        ,sub_charge_dim."subscription_type"
        ,'batch_model' AS "data_source"
    
           , SUBNAME_scd ."subscription_started"
            ,"subscription_ended"
            ,"pause_started"
            ,"pause_ended"
            ,"free_trial_started"
            ,"free_trial_ended"
            ,"is_resubscription"
            ,"is_immediate_resubscription"
            ,"is_immediate_resubscription_cancel"
            ,"has_active_free_trial"
            ,"is_paused"
        , "subscription_term_changes__skey"
,case when "is_soft_cancelled"=TRUE then "batch_date" else null end "churn_date"
, min("subscription_start_date") OVER (PARTITION BY "subscription_name" ) AS "acquisition_date"
,SUBNAME_scd."subscription_free_trial_start_date"
        , SUBNAME_scd."subscription_free_trial_end_date"
      ,case when freetrial_subs."subscription_name"  is not null and SUBNAME_scd."subscription_started" =TRUE then TRUE
      ELSE FALSE END "free_trail_acquisition"

    FROM daily_subs_filtered
   INNER JOIN SUBNAME_scd  USING ("subscription_name")
    LEFT JOIN bill_acc_dim USING ("billing_account__skey")
    LEFT JOIN sub_info_dim USING ("subscription_info__skey")
    LEFT JOIN sub_charge_dim USING ("subscription_charge__skey")
    LEFT JOIN (SELECT "subscription_name" new_sub_name FROM share_model group  by 1)  share_model
    On daily_subs_filtered."subscription_name" = share_model.new_sub_name
    LEFT JOIN freetrial_subs USING ("subscription_name")
   where share_model.new_sub_name is null
)

SELECT 

"batch_date"

,s."subscription_name"

,"billing_account_id"

,"dazn_user_id"

,"subscription_country"

,"subscription_territory"

,"subscription_start_date"

,"subscription_planned_end_date"

,"subscription_pause_start_date"

,"subscription_pause_end_date"

,"subscription_source_system_name_derived__skey"

,"subscription_sign_up_giftcode"

,"subscription_sign_up_campaign_id__skey"

,"subscription_tracking_id__skey"

,"subscription_product_group"

,"subscription_tier"

,"subscription_type"

,"data_source"

,"subscription_started"

,"subscription_ended"

,"pause_started"

,"pause_ended"

,"free_trial_started"

,"free_trial_ended"

,"is_resubscription"

,"is_immediate_resubscription"

,"is_immediate_resubscription_cancel"

,"has_active_free_trial"

,"is_paused"

,HASH( "subscription_started"

,"subscription_ended"

,"pause_started"

,"pause_ended"

,"free_trial_started"

,"free_trial_ended"

,"is_resubscription"

,"is_immediate_resubscription"

,"is_immediate_resubscription_cancel"

,"has_active_free_trial"

,"is_paused") AS "subscription_term_changes__skey"
,"CHURN_DATE" as "churn_date"

,"ACQUISTION_DATE" as "aquisition_date"
,"subscription_free_trial_start_date"
        , "subscription_free_trial_end_date"
        ,case when (f."subscription_name" is not null or "free_trial_ended" =TRUE) and (s."subscription_started"=TRUE or f."subscription_started"=TRUE) then true else false end "free_trail_acquisition"
FROM share_model s
left join freetrial_subs f using ("subscription_name")
UNION ALL
SELECT * FROM batch_model
