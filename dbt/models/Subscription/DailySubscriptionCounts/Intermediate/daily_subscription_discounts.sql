{{
    config(
        materialized='incremental',
        unique_key='"batch_date"',
        incremental_strategy='delete+insert',
        schema='TRANSIENT',
        cluster_by=['"batch_date"'],
        tags=['presentation-subscription-domain']
    )
}}

{% set subscription_mart_variables = subscription_mart_variables() %}

WITH dim_date AS (
    SELECT * FROM {{ ref('dim_date') }}
    WHERE
        -- Running from the start of DAZN onwards until yesterday so any full rebuilds build the whole time frame
        "date_day" BETWEEN '2016-08-08' AND DATEADD('day', -1, CURRENT_DATE)
        {% if is_incremental() %}
        AND
        -- If incremental, then run for the rebuild days based on this variables macro: dbt/macros/Subscription/subscription_mart_variables.sql
        "date_day" BETWEEN DATEADD('day', -{{ subscription_mart_variables.rebuild_days}}, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
        {% endif %}
)

,sub_discounts AS (
    SELECT * FROM {{ ref('subscription_discount_intermediate') }}
)

SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,dim_date."date_day" AS "batch_date"
    ,sub_discounts."subscription_id"
    ,sub_discounts."subscription_name"
    ,sub_discounts."subscription_discount__hash"
    ,sub_discounts."subscription_discount_requested_timestamp"
    ,sub_discounts."subscription_discount_start_date"
    ,sub_discounts."subscription_discount_end_date"
    ,sub_discounts."subscription_discount_duration_days"
    ,sub_discounts."subscription_discount_duration_weeks"
    ,sub_discounts."subscription_discount_duration_months"
    ,sub_discounts."is_sign_up_discount"
    ,sub_discounts."subscription_giftcode"
    ,sub_discounts."subscription_giftcode_campaign_name"
    ,sub_discounts."subscription_discount_context"
    ,sub_discounts."subscription_discount_percentage"
    ,sub_discounts."subscription_discount_amount"
    ,sub_discounts."billing_account_id"
FROM dim_date
LEFT JOIN sub_discounts
    -- All discounts that are valid on that exact date
    ON dim_date."date_day" >= sub_discounts."subscription_discount_start_date"
    AND dim_date."date_day" < sub_discounts."subscription_discount_end_date"
-- Deduping as there are some overlapping discounts, but a very small amount (apart from PACs) and they are all pretty messed up, in general we've seen it's probably best to just take the latest (instead of aggregate or anything)
QUALIFY ROW_NUMBER() OVER (PARTITION BY dim_date."date_day", sub_discounts."subscription_name" ORDER BY sub_discounts."subscription_discount_end_date" DESC, sub_discounts."subscription_discount_start_date" DESC) = 1
