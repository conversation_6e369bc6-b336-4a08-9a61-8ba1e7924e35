{{
    config(
        materialized='view',
        database='SUBSCRIPTION_DAILY_COUNT__B2C__MART__' + snowflake_env(),
        schema='INTERMEDIATE',
        tags=['presentation-subscription-domain']
    )
}}

WITH sub_daily_filtered AS (
    SELECT * FROM {{ ref('daily_subscriptions_filtered') }}
)

SELECT
    "META__DBT_INSERT_DTTS"
    ,"batch_date"
    ,"batch_week_end_date"
    ,"subscription_id"
    ,"subscription_name"
    ,"billing_account_id"
    ,"crm_account_id"
    ,"dazn_user_id"
    ,"billing_account_currency_code"
    ,"subscription_start_date"
    ,"subscription_planned_end_date"
    ,"subscription_free_trial_start_date"
    ,"subscription_free_trial_end_date"
    ,"subscription_pause_start_date"
    ,"subscription_pause_end_date"
    ,"subscription_sign_up_giftcode"
    ,"first_post_sign_up_giftcode"
    ,"last_post_sign_up_giftcode"
    ,"subscription_current_discount_start_date"
    ,"subscription_current_discount_end_date"
    ,"subscription_giftcode"
    ,"billing_account__skey"
    ,"subscription_info__skey"
    ,"subscription_term__skey"
    ,"subscription_charge__skey"
    ,"subscription_free_trial__skey"
    ,"subscription_discount__skey"
    ,"subscription_pause__skey"
    ,"subscription_add_on__skey"
    ,"subscription_content_attribution__skey"
    ,"subscription_source_system_name_derived__skey"
    ,"subscription_tracking_id__skey"
    ,"subscription_sign_up_campaign_id__skey"
    ,"first_post_sign_up_giftcode_campaign_name__skey"
    ,"last_post_sign_up_giftcode_campaign_name__skey"
    ,"subscription_giftcode_campaign_name__skey"
    ,"subscription_daily_status__skey"
    ,"daily_usd_fx_rate__skey"
    ,"billing_account_product_groups__skey"
FROM sub_daily_filtered
