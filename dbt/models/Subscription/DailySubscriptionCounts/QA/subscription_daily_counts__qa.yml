version: 2

models:
  - name: subscription_daily_counts__qa
    description: "A table containing a daily summary of various QA/error check relating to the Sub Daily Counts dataset"
    columns:
      - name: batch_date
        description: "The date of the spine for active daily subscriptions"
        quote: true

      - name: is_week_end
        description: "Flag for if the batch_date is the end of a week"
        quote: true

      - name: is_month_end
        description: "Flag for if the batch_date is the end of a month"
        quote: true

      - name: is_year_end
        description: "Flag for if the batch_date is the end of a year"
        quote: true

      - name: subscription_product_group
        description: "Can be either 'NFL' whereas it is an NFL subscription, or 'DAZN' otherwise."
        quote: true

      - name: billing_account_is_batch_50
        description: "Boolean flag, true if the account has ever been on Batch50, which implies it is a test account"
        quote: true

      - name: subscription_count
        description: "The count of the total amount of subscriptions active on the batch_date"
        quote: true

      - name: distinct_subscription_count
        description: "The count of the total amount of distinct subscriptions active on the batch_date"
        quote: true

      - name: duplicate_subscription_count
        description: "The count of the total amount of duplicate subscriptions active on the batch_date"
        quote: true

      - name: dazn_subscription_count
        description: "The count of the amount of DAZN subscriptions active on the batch_date"
        quote: true

      - name: distinct_dazn_billing_account_count
        description: "The count of the amount of distinct billing accounts for DAZN subscriptions active on the batch_date"
        quote: true

      - name: overlapping_dazn_subscription_count
        description: "The count of the amount of DAZN subscriptions that overlap with other DAZN subscriptions from the billing account on the batch_date"
        quote: true

      - name: nfl_subscription_count
        description: "The count of the amount of NFL subscriptions active on the batch_date"
        quote: true

      - name: distinct_nfl_billing_account_count
        description: "The count of the amount of distinct billing accounts for NFL subscriptions active on the batch_date"
        quote: true

      - name: overlapping_nfl_subscription_count
        description: "The count of the amount of DAZN subscriptions that overlap with other NFL subscriptions from the billing account on the batch_date"
        quote: true

      - name: billing_account__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the billing_account__skey"
        quote: true

      - name: subscription_daily_status__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the subscription_daily_status__skey"
        quote: true

      - name: subscription_add_on__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the subscription_add_on__skey"
        quote: true

      - name: subscription_charge__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the subscription_charge__skey"
        quote: true

      - name: subscription_discount__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the subscription_discount__skey"
        quote: true

      - name: subscription_free_trial__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the subscription_free_trial__skey"
        quote: true

      - name: subscription_info__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the subscription_info__skey"
        quote: true

      - name: subscription_pause__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the subscription_pause__skey"
        quote: true

      - name: subscription_term__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the subscription_term__skey"
        quote: true

      - name: daily_usd_fx_rate__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the daily_usd_fx_rate__skey"
        quote: true

      - name: billing_account_product_groups__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the billing_account_product_groups__skey"
        quote: true

      - name: subscription_content_attribution__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the subscription_content_attribution__skey"
        quote: true

      - name: subscription_source_system_name_derived__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the subscription_source_system_name_derived__skey"
        quote: true

      - name: subscription_sign_up_campaign_id__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the subscription_sign_up_campaign_id__skey"
        quote: true

      - name: first_post_sign_up_giftcode_campaign_name__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the first_post_sign_up_giftcode_campaign_name__skey"
        quote: true

      - name: subscription_giftcode_campaign_name__skey
        description: "The count of the total amount of subscriptions that do not have a join for the subscription_giftcode_campaign_name__skey"
        quote: true

      - name: subscription_tracking_id__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the subscription_tracking_id__skey"
        quote: true

      - name: billing_account_id__null_count
        description: "The count of the total amount of subscriptions that have a NULL billing_account_id"
        quote: true

      - name: crm_account_id__null_count
        description: "The count of the total amount of subscriptions that have a NULL crm_account_id"
        quote: true

      - name: dazn_user_id__null_count
        description: "The count of the total amount of subscriptions that have a NULL dazn_user_id"
        quote: true

      - name: subscription_country__null_count
        description: "The count of the total amount of subscriptions that have a NULL subscription_country"
        quote: true

      - name: subscription_territory__null_count
        description: "The count of the total amount of subscriptions that have a NULL subscription_territory"
        quote: true

      - name: billing_account_currency_code__null_count
        description: "The count of the total amount of subscriptions that have a NULL billing_account_currency_code"
        quote: true

      - name: subscription_tier__null_count
        description: "The count of the total amount of subscriptions that have a NULL subscription_tier"
        quote: true

      - name: subscription_type__null_count
        description: "The count of the total amount of subscriptions that have a NULL subscription_type"
        quote: true

      - name: subscription_monthly_recurring_revenue__null_count
        description: "The count of the total amount of subscriptions that have a NULL subscription_monthly_recurring_revenue"
        quote: true

      - name: subscription_attributed_fixture_id__null_count
        description: "The count of the total amount of subscriptions that have a NULL subscription_attributed_fixture_id"
        quote: true

      - name: subscription_content_attribution__no_streams_count
        description: "The count of the total amount of subscriptions that have not been attributed any content to their acquisition"
        quote: true

      - name: subscription_type__unknown_count
        description: "The count of the total amount of subscriptions that have an unknown subscription_type"
        quote: true

      - name: early_instalment_subscription_type_count
        description: "The count of the total amount of subscriptions that have an instalment before the date instalments were launched"
        quote: true

      - name: early_add_on_count
        description: "The count of the total amount of subscriptions that have an Add On before the date Add Ons were launched"
        quote: true

      - name: subscription_pause_end_before_start_count
        description: "The count of the total amount of subscriptions that have a pause end before their start"
        quote: true

      - name: unidentified_subscription_test_campaign_count
        description: "The count of the total amount of subscriptions that have a campaign starting 'test x' that is not in our exclusion list or batch50 that we may need to action to exclude"
        quote: true

      - name: max_dbt_insert_dtts
        description: "The maximum datetime records have been inserted into this table for the batch_date"
        quote: true
