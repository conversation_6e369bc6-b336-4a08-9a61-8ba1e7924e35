{{
    config(
        materialized='incremental',
        unique_key='"batch_date"',
        incremental_strategy='delete+insert',
        schema='METADATA',
        tags=['presentation-subscription-domain']
    )
}}

{% set subscription_mart_variables = subscription_mart_variables() %}

WITH daily_subs_raw AS (
    SELECT * FROM {{ ref('daily_subscriptions_raw') }}
    WHERE
        -- If it's more than 90 days ago, then only calculate monthly to save processing
        (("batch_date" < DATEADD('day', -90, CURRENT_DATE) AND "batch_date" = DATE_TRUNC('month', "batch_date"))
        OR
        -- Within 90 days we should look daily (incrementally)
        ("batch_date" >= DATEADD('day', -90, CURRENT_DATE)))
        {% if is_incremental() %}
        AND
        -- Going back 14 days to make sure the full rebuilds of the dimensions haven't somehow missed previous skeys
        "batch_date" BETWEEN DATEADD('day', -14, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
        {% endif %}
)

,date_dim AS (
    SELECT * FROM {{ ref('daily_counts__date__dim') }}
)

,bill_acc_dim AS (
    SELECT * FROM {{ ref('billing_account__dim') }}
)

,sub_daily_status_dim AS (
    SELECT * FROM {{ ref('subscription_daily_status__dim') }}
)

,sub_add_on_dim AS (
    SELECT * FROM {{ ref('subscription_mart_add_on__dim') }}
)

,sub_charge_dim AS (
    SELECT * FROM {{ ref('subscription_mart_charge__dim') }}
)

,sub_discount_dim AS (
    SELECT * FROM {{ ref('subscription_mart_discount__dim') }}
)

,sub_free_trial_dim AS (
    SELECT * FROM {{ ref('subscription_mart_free_trial__dim') }}
)

,sub_info_dim AS (
    SELECT * FROM {{ ref('subscription_mart_info__dim') }}
)

,sub_pause_dim AS (
    SELECT * FROM {{ ref('subscription_mart_pause__dim') }}
)

,sub_term_dim AS (
    SELECT * FROM {{ ref('subscription_mart_term__dim') }}
)

,daily_fx_dim AS (
    SELECT * FROM {{ ref('currency__daily_usd_fx_rate__dim') }}
)

,prod_groups_dim AS (
    SELECT * FROM {{ ref('billing_account_product_groups__dim') }}
)

,sub_cont_attr_dim AS (
    SELECT * FROM {{ ref('subscription_mart_content_attribution__dim') }}
)

,sub_source_dim AS (
    SELECT * FROM {{ ref('subscription_source_system_name_mapping_dim') }}
)

,sub_campaign_dim AS (
    SELECT * FROM {{ ref('daily_counts__subscription_campaign_mapping__dim') }}
)

,sub_tracking_dim AS (
    SELECT * FROM {{ ref('subscription_tracking_id_mapping__dim') }}
)

SELECT
    daily_subs_raw."batch_date"
    ,date_dim."is_week_end"
    ,date_dim."is_month_end"
    ,date_dim."is_year_end"
    -- Adding IFNULLs in case the info or bill_acc dims have null joins, and so that the filters on the dashboard still work
    ,IFNULL(sub_info_dim."subscription_product_group", 'DAZN') AS "subscription_product_group"
    ,IFNULL(bill_acc_dim."billing_account_is_batch_50", FALSE) AS "billing_account_is_batch_50"

    -- Duplicate check
    ,COUNT(daily_subs_raw."subscription_name") AS "subscription_count"
    ,COUNT(DISTINCT daily_subs_raw."subscription_name") AS "distinct_subscription_count"
    ,"subscription_count" - "distinct_subscription_count" AS "duplicate_subscription_count"

    -- Overlapping Subscriptions
    ,COUNT_IF(sub_info_dim."subscription_product_group" = 'DAZN') AS "dazn_subscription_count"
    ,COUNT(DISTINCT IFF(sub_info_dim."subscription_product_group" = 'DAZN', "billing_account_id", NULL)) AS "distinct_dazn_billing_account_count"
    ,"dazn_subscription_count" - "distinct_dazn_billing_account_count" AS "overlapping_dazn_subscription_count"
    ,COUNT_IF(sub_info_dim."subscription_product_group" = 'NFL') AS "nfl_subscription_count"
    ,COUNT(DISTINCT IFF(sub_info_dim."subscription_product_group" = 'NFL', "billing_account_id", NULL)) AS "distinct_nfl_billing_account_count"
    ,"nfl_subscription_count" - "distinct_nfl_billing_account_count" AS "overlapping_nfl_subscription_count"

    -- NULL skey joins
    ,COUNT_IF(bill_acc_dim."billing_account__skey" IS NULL) AS "billing_account__skey__null_count"
    ,COUNT_IF(sub_daily_status_dim."subscription_daily_status__skey" IS NULL) AS "subscription_daily_status__skey__null_count"
    ,COUNT_IF(sub_add_on_dim."subscription_add_on__skey" IS NULL) AS "subscription_add_on__skey__null_count"
    ,COUNT_IF(sub_charge_dim."subscription_charge__skey" IS NULL) AS "subscription_charge__skey__null_count"
    ,COUNT_IF(sub_discount_dim."subscription_discount__skey" IS NULL) AS "subscription_discount__skey__null_count"
    ,COUNT_IF(sub_free_trial_dim."subscription_free_trial__skey" IS NULL) AS "subscription_free_trial__skey__null_count"
    ,COUNT_IF(sub_info_dim."subscription_info__skey" IS NULL) AS "subscription_info__skey__null_count"
    ,COUNT_IF(sub_pause_dim."subscription_pause__skey" IS NULL) AS "subscription_pause__skey__null_count"
    ,COUNT_IF(sub_term_dim."subscription_term__skey" IS NULL) AS "subscription_term__skey__null_count"
    ,COUNT_IF(daily_fx_dim."daily_usd_fx_rate__skey" IS NULL) AS "daily_usd_fx_rate__skey__null_count"
    ,COUNT_IF(prod_groups_dim."billing_account_product_groups__skey" IS NULL) AS "billing_account_product_groups__skey__null_count"
    ,COUNT_IF(sub_cont_attr_dim."subscription_content_attribution__skey" IS NULL) AS "subscription_content_attribution__skey__null_count"
    ,COUNT_IF(sub_source_dim."subscription_source_system_name_derived__skey" IS NULL) AS "subscription_source_system_name_derived__skey__null_count"
    ,COUNT_IF(sub_su_campaign_dim."subscription_sign_up_campaign_id__skey" IS NULL AND daily_subs_raw."subscription_sign_up_campaign_id__skey" IS NOT NULL) AS "subscription_sign_up_campaign_id__skey__null_count"
    ,COUNT_IF(sub_psu_campaign_dim."first_post_sign_up_giftcode_campaign_name__skey" IS NULL AND daily_subs_raw."first_post_sign_up_giftcode_campaign_name__skey" IS NOT NULL) AS "first_post_sign_up_giftcode_campaign_name__skey__null_count"
    ,COUNT_IF(sub_campaign_dim."subscription_giftcode_campaign_name__skey" IS NULL AND daily_subs_raw."subscription_giftcode_campaign_name__skey" IS NOT NULL) AS "subscription_giftcode_campaign_name__skey__null_count"
    ,COUNT_IF(sub_tracking_dim."subscription_tracking_id__skey" IS NULL AND daily_subs_raw."subscription_tracking_id__skey" IS NOT NULL) AS "subscription_tracking_id__skey__null_count"

    -- NULL key fields
    ,COUNT_IF(daily_subs_raw."billing_account_id" IS NULL) AS "billing_account_id__null_count"
    ,COUNT_IF(daily_subs_raw."crm_account_id" IS NULL) AS "crm_account_id__null_count"
    ,COUNT_IF(daily_subs_raw."dazn_user_id" IS NULL) AS "dazn_user_id__null_count"
    ,COUNT_IF(bill_acc_dim."subscription_country" IS NULL) AS "subscription_country__null_count"
    ,COUNT_IF(bill_acc_dim."subscription_territory" IS NULL) AS "subscription_territory__null_count"
    ,COUNT_IF(bill_acc_dim."billing_account_currency_code" IS NULL) AS "billing_account_currency_code__null_count"
    ,COUNT_IF(sub_info_dim."subscription_tier" IS NULL AND bill_acc_dim."subscription_territory" NOT IN ('Canada', 'United States') AND daily_subs_raw."subscription_source_system_name_derived__skey" != 'Docomo') AS "subscription_tier__null_count"
    ,COUNT_IF(sub_charge_dim."subscription_type" IS NULL) AS "subscription_type__null_count"
    ,COUNT_IF(IFNULL(sub_charge_dim."subscription_monthly_recurring_revenue", 0) = 0 AND sub_charge_dim."subscription_type" != 'Externally Billed' AND daily_subs_raw."subscription_source_system_name_derived__skey" != 'Docomo') AS "subscription_monthly_recurring_revenue__null_count"
    ,COUNT_IF(sub_cont_attr_dim."subscription_attributed_fixture_id" IS NULL AND sub_cont_attr_dim."is_final_content_attribution" = TRUE) AS "subscription_attributed_fixture_id__null_count"
    ,COUNT_IF(sub_cont_attr_dim."subscription_attributed_fixture_id" = '<no streams found>' AND sub_cont_attr_dim."is_final_content_attribution" = TRUE) AS "subscription_content_attribution__no_streams_count"

    -- Unexpected value counts
    ,COUNT_IF(sub_charge_dim."subscription_type" = 'Unknown') AS "subscription_type__unknown_count"
    ,COUNT_IF(sub_charge_dim."subscription_type" = 'Instalment' AND daily_subs_raw."batch_date" < '2022-01-01') AS "early_instalment_subscription_type_count"
    ,COUNT_IF(sub_add_on_dim."has_subscription_add_on" AND daily_subs_raw."batch_date" < '2022-08-01') AS "early_add_on_count"
    ,COUNT_IF(sub_pause_dim."subscription_pause_start_week" > sub_pause_dim."subscription_pause_end_week") AS "subscription_pause_end_before_start_count"
    ,COUNT_IF(daily_subs_raw."subscription_sign_up_campaign_id__skey" ILIKE 'test x%' AND IFNULL(daily_subs_raw."billing_account_is_batch_50", FALSE) = FALSE AND IFNULL(daily_subs_raw."subscription_sign_up_campaign_id__skey", '-') NOT IN ({{ in_clause_from_list(subscription_mart_variables.test_campaign_ids) }})) AS "unidentified_subscription_test_campaign_count"

    -- Last updated
    ,MAX(daily_subs_raw."META__DBT_INSERT_DTTS") AS "max_dbt_insert_dtts"

FROM daily_subs_raw
LEFT JOIN date_dim USING ("batch_date")
LEFT JOIN bill_acc_dim USING ("billing_account__skey")
LEFT JOIN sub_daily_status_dim USING ("subscription_daily_status__skey")
LEFT JOIN sub_add_on_dim USING ("subscription_add_on__skey")
LEFT JOIN sub_charge_dim USING ("subscription_charge__skey")
LEFT JOIN sub_discount_dim USING ("subscription_discount__skey")
LEFT JOIN sub_free_trial_dim USING ("subscription_free_trial__skey")
LEFT JOIN sub_info_dim USING ("subscription_info__skey")
LEFT JOIN sub_pause_dim USING ("subscription_pause__skey")
LEFT JOIN sub_term_dim USING ("subscription_term__skey")
LEFT JOIN daily_fx_dim USING ("daily_usd_fx_rate__skey")
LEFT JOIN prod_groups_dim USING ("billing_account_product_groups__skey")
LEFT JOIN sub_cont_attr_dim USING ("subscription_content_attribution__skey")
LEFT JOIN sub_source_dim USING ("subscription_source_system_name_derived__skey")
LEFT JOIN sub_campaign_dim sub_su_campaign_dim ON daily_subs_raw."subscription_sign_up_campaign_id__skey" = sub_su_campaign_dim."subscription_sign_up_campaign_id__skey"
LEFT JOIN sub_campaign_dim sub_psu_campaign_dim ON daily_subs_raw."first_post_sign_up_giftcode_campaign_name__skey" = sub_psu_campaign_dim."first_post_sign_up_giftcode_campaign_name__skey"
LEFT JOIN sub_campaign_dim ON daily_subs_raw."subscription_giftcode_campaign_name__skey" = sub_campaign_dim."subscription_giftcode_campaign_name__skey"
LEFT JOIN sub_tracking_dim USING ("subscription_tracking_id__skey")
GROUP BY 1,2,3,4,5,6
