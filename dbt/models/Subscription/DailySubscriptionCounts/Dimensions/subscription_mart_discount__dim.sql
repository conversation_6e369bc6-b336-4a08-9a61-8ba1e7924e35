{{
    config(
        materialized='incremental',
        incremental_strategy='merge',
        unique_key='"subscription_discount__skey"',
        database='MART_DIMENSIONS__B2C__DOMAIN__' + snowflake_env(),
        schema='SUBSCRIPTION',
        alias='subscription_discount__dim',
        tags=['presentation-subscription-domain']
    )
}}

{% set subscription_mart_variables = subscription_mart_variables() %}

WITH daily_subs_raw AS (
    SELECT * FROM {{ ref('daily_subscriptions_raw') }}
)

 SELECT DISTINCT
        "subscription_discount__skey"
        -- Old fields that will be deprecated after migration
        ,"first_post_sign_up_giftcode_campaign_name"
        ,"last_post_sign_up_giftcode_campaign_name"
        ,"post_sign_up_giftcode_campaign_name_distinct_count"
        ,"discount_percentage_distinct_count"
        ,"max_discount_percentage"
        ,"first_post_sign_up_discount_context"
        ,"discount_effective_start_week"
        ,"discount_effective_end_week"
        ,"discount_duration_weeks"
        ,"is_introductory_discount"
        -- New fields from here on
        ,"subscription_current_discount_start_week"
        ,"subscription_current_discount_end_week"
        ,"subscription_current_discount_duration_weeks"
        ,"is_sign_up_discount"
        ,"subscription_giftcode_campaign_name_patched" AS "subscription_giftcode_campaign_name"
        ,"subscription_discount_context"
        ,"subscription_discount_percentage"
        ,"subscription_discount_amount"
 FROM daily_subs_raw
{% if is_incremental() %}
WHERE
    "batch_date" BETWEEN DATEADD('day', -{{ subscription_mart_variables.rebuild_days}}, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
    AND
    "subscription_discount__skey" NOT IN (SELECT "subscription_discount__skey" FROM {{ this }})
{% endif %}
