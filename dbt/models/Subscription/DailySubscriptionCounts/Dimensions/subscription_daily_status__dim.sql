{{
    config(
        materialized='incremental',
        incremental_strategy='merge',
        unique_key='"subscription_daily_status__skey"',
        database='SUBSCRIPTION_DAILY_COUNT__B2C__MART__' + snowflake_env(),
        schema='SUBSCRIPTION_DAILY_COUNTS',
        tags=['presentation-subscription-domain']
    )
}}

{% set subscription_mart_variables = subscription_mart_variables() %}

WITH daily_subs_raw AS (
    SELECT * FROM {{ ref('daily_subscriptions_raw') }}
)

SELECT DISTINCT
    "subscription_daily_status__skey"
    ,"is_paused"
    ,"has_active_post_sign_up_discount"
    ,"has_active_free_trial"
    ,"has_active_introductory_discount"
    ,"discounted_monthly_recurring_revenue"
    ,"daily_revenue_estimate"
    ,"has_active_add_on"
FROM daily_subs_raw
{% if is_incremental() %}
WHERE
    "batch_date" BETWEEN DATEADD('day', -{{ subscription_mart_variables.rebuild_days}}, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
    AND
    "subscription_daily_status__skey" NOT IN (SELECT "subscription_daily_status__skey" FROM {{ this }})
{% endif %}
