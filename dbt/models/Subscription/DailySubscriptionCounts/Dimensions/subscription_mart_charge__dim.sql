{{
    config(
        materialized='incremental',
        incremental_strategy='merge',
        unique_key='"subscription_charge__skey"',
        database='MART_DIMENSIONS__B2C__DOMAIN__' + snowflake_env(),
        schema='SUBSCRIPTION',
        alias='subscription_charge__dim',
        tags=['presentation-subscription-domain']
    )
}}

{% set subscription_mart_variables = subscription_mart_variables() %}

WITH daily_subs_raw AS (
    SELECT * FROM {{ ref('daily_subscriptions_raw') }}
)

SELECT DISTINCT
    "subscription_charge__skey"
    ,"subscription_type"
    ,"subscription_monthly_recurring_revenue"
    ,"next_subscription_monthly_recurring_revenue"
    ,"subscription_bill_cycle_day"
    ,"subscription_monthly_recurring_revenue_daily_rank"
FROM daily_subs_raw
{% if is_incremental() %}
WHERE
    "batch_date" BETWEEN DATEADD('day', -{{ subscription_mart_variables.rebuild_days}}, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
    AND
    "subscription_charge__skey" NOT IN (SELECT "subscription_charge__skey" FROM {{ this }})
{% endif %}
