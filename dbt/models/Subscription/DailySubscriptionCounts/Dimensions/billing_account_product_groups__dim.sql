{{
    config(
        materialized='incremental',
        incremental_strategy='merge',
        unique_key='"billing_account_product_groups__skey"',
        database='SUBSCRIPTION_DAILY_COUNT__B2C__MART__' + snowflake_env(),
        schema='CUSTOMER',
        tags=['presentation-subscription-domain']
    )
}}

{% set subscription_mart_variables = subscription_mart_variables() %}

WITH daily_subs_raw AS (
    SELECT * FROM {{ ref('daily_subscriptions_raw') }}
)

SELECT DISTINCT
    "billing_account_product_groups__skey"
    ,"billing_account_product_group_count"
    ,"billing_account_product_group_array"
    ,"billing_account_trip_number"
FROM daily_subs_raw
{% if is_incremental() %}
WHERE
    "batch_date" BETWEEN DATEADD('day', -{{ subscription_mart_variables.rebuild_days}}, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
    AND
    "billing_account_product_groups__skey" NOT IN (SELECT "billing_account_product_groups__skey" FROM {{ this }})
{% endif %}
