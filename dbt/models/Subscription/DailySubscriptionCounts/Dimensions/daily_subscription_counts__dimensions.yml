version: 2

models:
  - name: daily_counts__billing_account__dim
    description: "Dimension table surfacing information on billing account ex: country, territory.."
    columns:
      - name: billing_account__skey
        description: "The surrogate key (skey) used to join on the billing_account__dim"
        quote: true

      - name: subscription_country
        description: "The country of the subscription, originating from the Zuora contact. E.g. Spain, Germany, Japan, United States, United Kingdom, ..."
        quote: true

      - name: subscription_territory
        description: "The territory of the subscription, pulled in by a join to region_dimension. E.g. Spain, DACH, Japan, United States, UK and Ireland, ..."
        quote: true

      - name: billing_account_currency_code
        description: "The currency code for the Zuora Account, E.g. EUR, USD, ..."
        quote: true

      - name: billing_account_is_batch_50
        description: "Boolean flag, true if the account has ever been on Batch50, which implies it is a test account"
        quote: true

      - name: min_billing_account_subscription_name_created_week
        description: "The week of the very first date that the subscription was first created"
        quote: true

      - name: billing_account_has_advanced_payment_manager
        description: "A flag for if subscriptions on the same account have different payment methods"
        quote: true

      - name: user_account_created_week
        description: "The minimum week_end_date we see the users being created in our systems, derived from Salesforce before migration to Segment"
        quote: true

      - name: country__skey
        description: "The skey for the COUNTRY__DIM that comes directly from the seed of region_dimension for country-territory mappings, this skey is actually just the exact value of the subscription_country"
        quote: true

  - name: daily_counts__subscription_mart_add_on__dim
    description: "Dimension table surfacing information on the subscription add on like count, start week..."
    columns:
      - name: subscription_add_on__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_add_on__dim"
        quote: true

      - name: has_subscription_add_on
        description: "A flag for if there is any AddOn active at the time the Subscription Id is created"
        quote: true

      - name: subscription_add_on_count
        description: "Number of unique AddOns that are active at the time the Subscription Id is created"
        quote: true

      - name: subscription_add_on_partner_ids
        description: "An array containing distinct values of the addon partner IDs a subscription ID has"
        quote: true

      - name: subscription_add_on_effective_start_week
        description: "The minimum start week that the add on is effective from"
        quote: true

      - name: subscription_add_on_effective_end_week
        description: "The maximum end week that the add on is effective until"
        quote: true

  - name: daily_counts__subscription_mart_charge__dim
    description: "Dimension table surfacing information on the subscription charge like term type, billing period..."
    columns:
      - name: subscription_charge__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_charge__dim"
        quote: true

      - name: subscription_type
        description: "A derived field from the term type and the billing period, or external giftcodes, to categorise the subscription type, can be Monthly, Annual, Instalment or Externally Billed"
        quote: true

      - name: subscription_monthly_recurring_revenue
        description: "The monthly recurring revenue, which is the amount the customer pays for their subscription ignoring Discounts and PPV, adjusting the subscription length down to one month and rounded to two decimal places"
        quote: true

      - name: next_subscription_monthly_recurring_revenue
        description: "The next effective monthly recurring revenue, that could be different from the current monthly recurring revenue and will be effective in the future"
        quote: true

      - name: subscription_bill_cycle_day
        description: "The billing cycle day of the month that the customer is charged"
        quote: true

      - name: subscription_monthly_recurring_revenue_daily_rank
        description: "The rank of the MRR, high to low, on the current batch_date across the dimensions of territory, currency, sub_type and tier"
        quote: true

  - name: subscription_daily_status__dim
    description: "Dimension table surfacing information on the subscription daily status like pause, addon, discount.."
    columns:
      - name: subscription_daily_status__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_daily_status__dim"
        quote: true
        tests:
          - unique:
              config:
                severity: error

      - name: is_paused
        description: "Boolean flag, true if the subscription is paused on the exact date_day of the spine"
        quote: true

      - name: has_active_post_sign_up_discount
        description: "Boolean flag, true if the subscription has an active post-sign-up discount on the exact date_day of the spine"
        quote: true

      - name: has_active_free_trial
        description: "Boolean flag, true if the subscription has an active free trial on the exact date_day of the spine"
        quote: true

      - name: has_active_introductory_discount
        description: "Boolean flag, true if the subscription has an active introductory discount on the exact date_day of the spine"
        quote: true

      - name: discounted_monthly_recurring_revenue
        description: "The MRR with discounts applied: MRR * active_discount_percentage"
        quote: true

      - name: daily_revenue_estimate
        description: "The discounted MRR brought down to the daily level: Disc MRR / days_in_month"
        quote: true

      - name: has_active_add_on
        description: "Boolean flag, true if the subscription has an active addon on the exact date_day of the spine"
        quote: true

  - name: subscription_mart_discount__dim
    description: "Dimension table surfacing information on the subscription discount like first post signup campaign name"
    columns:
      - name: subscription_discount__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_discount__dim"
        quote: true
        tests:
          - unique:
              config:
                severity: error

      - name: first_post_sign_up_giftcode_campaign_name
        description: "The campaign name of the first post-sign-up giftcode associated with this Subscription Id, looking at the most recently effective charge after the Subscription Id creation"
        quote: true

      - name: last_post_sign_up_giftcode_campaign_name
        description: "The campaign name of the first post-sign-up giftcode associated with this Subscription Id, looking at the farthest future effective charge after the Subscription Id creation"
        quote: true

      - name: post_sign_up_giftcode_campaign_name_distinct_count
        description: "The distinct count of the amount of post-sign-up giftcode campaigns that will ever be associated with this Subscription Id from the Subscription Id creation into the future, different giftcodes could be associated with the same campaign"
        quote: true

      - name: discount_percentage_distinct_count
        description: "The distinct count of the amount of discounts that will ever be associated with this Subscription Id from the Subscription Id creation into the future"
        quote: true

      - name: max_discount_percentage
        description: "The maximum proportional discount applied to the subscription charges for the duration of the discount"
        quote: true

      - name: first_post_sign_up_discount_context
        description: "Describes the first channel through which the discount was applied - CRM, in app, cancellation flow or via a customer service agent"
        quote: true

      - name: discount_effective_start_week
        description: "The week of the very first date that the discount will be effective on this Subscription Id"
        quote: true

      - name: discount_effective_end_week
        description: "The week of the very last date that the discount will be effective on this Subscription Id"
        quote: true

      - name: discount_duration_weeks
        description: "The duration (in weeks) of the discount applied to the subscription"
        quote: true

      - name: is_introductory_discount
        description: "Boolean flag, true if the discount is applied to the first version of the subscription, meaning it was active at the start of the subscription"
        quote: true

      - name: subscription_current_discount_start_week
        description: "The week end date of the start of the discount"
        quote: true

      - name: subscription_current_discount_end_week
        description: "The week end date of the end of the discount"
        quote: true

      - name: subscription_current_discount_duration_weeks
        description: "Duration in weeks of the discount"
        quote: true

      - name: is_sign_up_discount
        description: "Boolean flag, true if the discount effective start is the same date as the subscription start"
        quote: true

      - name: subscription_giftcode_campaign_name
        description: "The campaign name attributed to the giftcode, if any, relating directly to this exact discount, could be a sign-up or post-sign-up discount"
        quote: true

      - name: subscription_discount_context
        description: "Describes the channel, if any, through which the discount was applied E.g CRM, InApp, CancellationFlow, CustomerServcie, ..."
        quote: true

      - name: subscription_discount_percentage
        description: "The percentage discount that will be applied to the MRR due to this discount, only present if the discount model is a Percentage Discount one"
        quote: true

      - name: subscription_discount_amount
        description: "The fixed amount discount that will be applied to the MRR due to this discount, only present if the discount model is a Fixed Discount one"
        quote: true

  - name: daily_counts__subscription_mart_discount__dim
    description: "Dimension table surfacing information on the subscription discount like first post signup campaign name"
    columns:
      - name: subscription_discount__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_discount__dim"
        quote: true

      - name: first_post_sign_up_giftcode_campaign_name
        description: "The campaign name of the first post-sign-up giftcode associated with this Subscription Id, looking at the most recently effective charge after the Subscription Id creation"
        quote: true

      - name: last_post_sign_up_giftcode_campaign_name
        description: "The campaign name of the first post-sign-up giftcode associated with this Subscription Id, looking at the farthest future effective charge after the Subscription Id creation"
        quote: true

      - name: post_sign_up_giftcode_campaign_name_distinct_count
        description: "The distinct count of the amount of post-sign-up giftcode campaigns that will ever be associated with this Subscription Id from the Subscription Id creation into the future, different giftcodes could be associated with the same campaign"
        quote: true

      - name: discount_percentage_distinct_count
        description: "The distinct count of the amount of discounts that will ever be associated with this Subscription Id from the Subscription Id creation into the future"
        quote: true

      - name: max_discount_percentage
        description: "The maximum proportional discount applied to the subscription charges for the duration of the discount"
        quote: true

      - name: first_post_sign_up_discount_context
        description: "Describes the first channel through which the discount was applied - CRM, in app, cancellation flow or via a customer service agent"
        quote: true

      - name: discount_effective_start_week
        description: "The week of the very first date that the discount will be effective on this Subscription Id"
        quote: true

      - name: discount_effective_end_week
        description: "The week of the very last date that the discount will be effective on this Subscription Id"
        quote: true

      - name: discount_duration_weeks
        description: "The duration (in weeks) of the discount applied to the subscription"
        quote: true

      - name: is_introductory_discount
        description: "Boolean flag, true if the discount is applied to the first version of the subscription, meaning it was active at the start of the subscription"
        quote: true

      - name: subscription_current_discount_start_week
        description: "The week end date of the start of the discount"
        quote: true

      - name: subscription_current_discount_end_week
        description: "The week end date of the end of the discount"
        quote: true

      - name: subscription_current_discount_duration_weeks
        description: "Duration in weeks of the discount"
        quote: true

      - name: is_sign_up_discount
        description: "Boolean flag, true if the discount effective start is the same date as the subscription start"
        quote: true

      - name: subscription_giftcode_campaign_name
        description: "The campaign name attributed to the giftcode, if any, relating directly to this exact discount, could be a sign-up or post-sign-up discount"
        quote: true

      - name: subscription_discount_context
        description: "Describes the channel, if any, through which the discount was applied E.g CRM, InApp, CancellationFlow, CustomerServcie, ..."
        quote: true

      - name: subscription_discount_percentage
        description: "The percentage discount that will be applied to the MRR due to this discount, only present if the discount model is a Percentage Discount one"
        quote: true

      - name: subscription_discount_amount
        description: "The fixed amount discount that will be applied to the MRR due to this discount, only present if the discount model is a Fixed Discount one"
        quote: true

  - name: daily_counts__subscription_mart_free_trial__dim
    description: "Dimension table surfacing information on the subscription free trial ex: free trial length days, length months"
    columns:
      - name: subscription_free_trial__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_free_trial__dim"
        quote: true

      - name: has_free_trial
        description: "A flag to indicate if the subscription is a free trial"
        quote: true

      - name: subscription_free_trial_length_days_advertised
        description: "The length in days of the Free Trial as advertised to the user, so calculated directly from the Zuora Subscription field. This can be 0 if there is no Free Trial."
        quote: true

      - name: subscription_free_trial_length_months_advertised
        description: "The length in months of the Free Trial as advertised to the user, so calculated directly from the Zuora Subscription field. This can be 0 if there is no Free Trial."
        quote: true

      - name: subscription_free_trial_end_date
        description: "The date the Free Trial ends"
        quote: true

  - name: daily_counts__subscription_mart_info__dim
    description: "Dimension table surfacing information on the subscription like tier, trip number.."
    columns:
      - name: subscription_info__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_info__dim"
        quote: true

      - name: is_soft_cancelled
        description: "The status of the subscription is cancelled and it won't renew on the current end date"
        quote: true

      - name: subscription_tier
        description: "The Entitlement Set Id of the Rateplan, which is filtered to the associated RatePlanCharge's Flat Fee Pricing Model and Recurring Type, so producing the Tier for the Subscription"
        quote: true

      - name: billing_account_trip_number
        description: "The count of previous subscriptions from this billing account including this subscription for all product groups"
        quote: true

      - name: is_resubscription
        description: "Identifies if this is a resubscription for this product group or not"
        quote: true

      - name: subscription_product_group
        description: "Can be either 'NFL' whereas it is an NFL subscription, or 'DAZN' otherwise."
        quote: true

  - name: daily_counts__subscription_mart_pause__dim
    description: "Dimension table surfacing information on the subscription pause period ex: pause period start week, pause duration.."
    columns:
      - name: subscription_pause__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_pause__dim"
        quote: true

      - name: has_pause_period
        description: "Flag to identify whether the subscription had a pause period"
        quote: true

      - name: subscription_pause_start_week
        description: "Week of the most recent pause starts for the subscription id"
        quote: true

      - name: subscription_pause_end_week
        description: "Week of the most recent pause ends for the subscription id"
        quote: true

      - name: pause_duration_weeks
        description: "The duration in weeks of the most recent pause for the subscription id"
        quote: true

  - name: daily_counts__subscription_mart_term__dim
    description: "Dimension table surfacing information on subscription term like subscription start week and end week"
    columns:
      - name: subscription_term__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_term__dim"
        quote: true

      - name: subscription_start_week
        description: "The week end date the subscription starts, including any free trial"
        quote: true

      - name: subscription_end_week
        description: "The latest week end date Zuora has seen this subscription end, taking into account retrospective cancellation (when the cancel date is before the subscription is updated)"
        quote: true

  - name: daily_counts__date__dim
    description: "Date Dimension, used for any information around dates. This is not the official DAZN date dimension, but one created by a dbt macro used for reports"
    columns:
      - name: date_day
        description: ""
        quote: true

      - name: prior_date_day
        description: ""
        quote: true

      - name: next_date_day
        description: ""
        quote: true

      - name: prior_year_date_day
        description: ""
        quote: true

      - name: prior_year_over_year_date_day
        description: ""
        quote: true

      - name: day_of_week
        description: ""
        quote: true

      - name: day_of_week_name
        description: ""
        quote: true

      - name: day_of_week_name_short
        description: ""
        quote: true

      - name: day_of_month
        description: ""
        quote: true

      - name: day_of_year
        description: ""
        quote: true

      - name: week_start_date
        description: ""
        quote: true

      - name: week_end_date
        description: ""
        quote: true

      - name: prior_year_week_start_date
        description: ""
        quote: true

      - name: prior_year_week_end_date
        description: ""
        quote: true

      - name: week_of_year
        description: ""
        quote: true

      - name: prior_year_week_of_year
        description: ""
        quote: true

      - name: month_of_year
        description: ""
        quote: true

      - name: month_name
        description: ""
        quote: true

      - name: month_name_short
        description: ""
        quote: true

      - name: month_start_date
        description: ""
        quote: true

      - name: month_end_date
        description: ""
        quote: true

      - name: prior_year_month_start_date
        description: ""
        quote: true

      - name: prior_year_month_end_date
        description: ""
        quote: true

      - name: quarter_of_year
        description: ""
        quote: true

      - name: quarter_start_date
        description: ""
        quote: true

      - name: quarter_end_date
        description: ""
        quote: true

      - name: year_number
        description: ""
        quote: true

      - name: year_start_date
        description: ""
        quote: true

      - name: year_end_date
        description: ""
        quote: true

      - name: is_week_end
        description: ""
        quote: true

      - name: is_month_end
        description: ""
        quote: true

      - name: is_year_end
        description: ""
        quote: true

      - name: batch_date
        description: ""
        quote: true

  - name: daily_counts__subscription_campaign_mapping__dim
    description: "Dimension used for mapping the subscription campaigns (sign-up and post sign-up) to their partner and relevant details"
    columns:
      - name: subscription_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true
        tests:
          - unique:
              config:
                severity: error

      - name: subscription_sign_up_campaign_id__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: first_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: last_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: campaign_type
        description: "The type of subscription campaign, E.g. Direct, Indirect, Agents, ..."
        quote: true

      - name: campaign_detailed_type
        description: "The detailed type of subscription campaign, E.g. Direct, Operators, Retail, ..."
        quote: true

      - name: campaign_partner_name
        description: "The partner associated with the subscrpition campaign, E.g. TIM, Orange, Telefonica, ..."
        quote: true

  - name: daily_counts__subscription_source_system_name_mapping__dim
    description: "Dimension used for mapping the subscription source system names to their partner and relevant details"
    columns:
      - name: subscription_source_system_name_derived__skey
        description: "The surrogate key (skey) used to join on the sub_source_system dim"
        quote: true
        tests:
          - unique:
              config:
                severity: error

      - name: partner_type
        description: "The type of partner, E.g. Direct, Indirect, Agents, ..."
        quote: true

      - name: partner_detailed_type
        description: "The detailed type of partner, E.g. Direct, Operators, Retail, ..."
        quote: true

      - name: partner_name
        description: "The cleaned up version of the source system name, that we will call partner, E.g. TIM, Orange, Telefonica, ..."
        quote: true

  - name: daily_counts__subscription_tracking_id_mapping__dim
    description: "Dimension used for mapping the subscription tracking IDs to their partner and relevant details"
    columns:
      - name: subscription_tracking_id__skey
        description: "The surrogate key (skey) used to join on the tracking_id dim"
        quote: true
        tests:
          - unique:
              config:
                severity: error

      - name: tracking_id_type
        description: "The type of subscription tracking ID, E.g. Direct, Indirect, Agents, ..."
        quote: true

      - name: tracking_id_detailed_type
        description: "The detailed type of subscription tracking ID, E.g. Direct, Operators, Retail, ..."
        quote: true

      - name: tracking_id_partner_name
        description: "The partner associated with the subscrpition tracking ID, E.g. TIM, Orange, Telefonica, ..."
        quote: true

      - name: tracking_id_campaign_name
        description: "The campaign name of the subscription Tracking ID"
        quote: true

  - name: daily_counts__subscription_mart_content_attribution__dim
    description: "Dimension describing the attributed content for a Subscription Name's acquisition based off the first days of their streaming behaviour"
    columns:
      - name: subscription_content_attribution__skey
        description: "The surrogate key (skey) used to join on the subscription_content_attribution__dim"
        quote: true

      - name: subscription_attributed_fixture_id
        description: "The ID of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_fixture_name
        description: "The name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_fixture_start_date
        description: "The start date of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_competition_id
        description: "The competition ID of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_competition_name
        description: "The competition name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_sport_id
        description: "The sport ID of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_sport_name
        description: "The sport name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attributed_ruleset_name
        description: "The ruleset name of the fixture attributed to the subscription acquisition"
        quote: true

      - name: subscription_attribution_has_ppv_required_entitlement
        description: "A flag to indicate if the content attributed was relating to an event that had a ppv entitlement required"
        quote: true

      - name: is_final_content_attribution
        description: "A flag to indicate if the attribution window has now passed for this fixture and so the attributed content can not change"
        quote: true

  - name: daily_counts__daily_usd_fx_rate__dim
    description: "Dimension used for mapping daily exchange rates to USD for all available currencies"
    columns:
      - name: date
        description: "The date of the conversion to USD"
        quote: true

      - name: currency
        description: "The currency before the conversion to USD"
        quote: true

      - name: fx_rate
        description: "The exchange rate to USD"
        quote: true

      - name: daily_usd_fx_rate__skey
        description: "Hashed reference to all rows of this table"
        quote: true

  - name: subscription_mart_charge__dim
    description: "Dimension table surfacing information on the subscription charge like term type, billing period..."
    columns:
      - name: subscription_charge__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_charge__dim"
        quote: true
        tests:
          - unique:
              config:
                severity: error

      - name: subscription_type
        description: "A derived field from the term type and the billing period, or external giftcodes, to categorise the subscription type, can be Monthly, Annual, Instalment or Externally Billed"
        quote: true

      - name: subscription_monthly_recurring_revenue
        description: "The monthly recurring revenue, which is the amount the customer pays for their subscription ignoring Discounts and PPV, adjusting the subscription length down to one month and rounded to two decimal places"
        quote: true

      - name: next_subscription_monthly_recurring_revenue
        description: "The next effective monthly recurring revenue, that could be different from the current monthly recurring revenue and will be effective in the future"
        quote: true

      - name: subscription_bill_cycle_day
        description: "The billing cycle day of the month that the customer is charged"
        quote: true

      - name: subscription_monthly_recurring_revenue_daily_rank
        description: "The rank of the MRR, high to low, on the current batch_date across the dimensions of territory, currency, sub_type and tier"
        quote: true

  - name: billing_account_product_groups__dim
    description: "Dimension table surfacing information on the billing accounts full product group details"
    columns:
      - name: billing_account_product_groups__skey
        description: "The surrogate key (skey) used to join on the billing_account_product_groups__dim"
        quote: true
        tests:
          - unique:
              config:
                severity: error

      - name: billing_account_product_group_count
        description: "The count of the amount of product groups (DAZN/NFL) the billing account has active on the given batch_date"
        quote: true

      - name: billing_account_product_group_array
        description: "An array of all subscription_product_group values that are active on the billing account on the given batch_date"
        quote: true

      - name: billing_account_trip_number
        description: "The count of previous subscriptions from this billing account including this subscription for all product groups"
        quote: true

  - name: daily_counts__country__dim
    description: "Dimension containing a mapping of countries to territories"
    columns:
      - name: country__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_charge__dim"
        quote: true

      - name: country
        description: "The long form country name for this country mapped from the join_key"
        quote: true

      - name: territory
        description: "The DAZN territory mapping for this corresponding country mapped from the join_key"
        quote: true

      - name: region
        description: "The DAZN region mapping for this corresponding country mapped from the join_key"
        quote: true

      - name: country_code
        description: "The two-letter country code for this country mapped form the join_key"
        quote: true

      - name: join_key
        description: "A field containing both long form country names and short two-letter country codes that you can use to be the only field used to join to other datasest and not cause duplication"
        quote: true

      - name: has_nfl_gpi
        description: "1 or 0 Flag for if this country is a country where NFL GPI is available"
        quote: true
