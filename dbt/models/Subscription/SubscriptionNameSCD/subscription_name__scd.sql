{{
    config(
        materialized='table',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH subscription_scd AS (
    SELECT * FROM {{ ref('subscription_name_base__scd') }}
)

,subscription_dim AS (
    SELECT * FROM {{ ref('subscription_id__dim') }}
)

SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,subscription_scd."subscription_id"
    ,subscription_scd."subscription_name"
    ,subscription_scd."subscription_version"
    ,subscription_scd."subscription_name_original_created_timestamp"
    ,subscription_scd."subscription_id_created_timestamp"
    ,CASE 
        WHEN subscription_dim."data_source"='DMP' 
            THEN  subscription_dim."subscription_id_updated_timestamp" 
            ELSE subscription_scd."subscription_id_updated_timestamp" 
    END AS "subscription_id_updated_timestamp"
    ,subscription_dim."subscription_start_date"
    ,subscription_scd."subscription_end_date" AS "subscription_planned_end_date"
    ,subscription_dim."subscription_start_week"
    ,subscription_dim."subscription_end_week" AS "subscription_planned_end_week"
    ,subscription_scd."subscription_status" = 'Draft' AS "is_draft"
    ,subscription_scd."subscription_status" = 'Suspended' AS "is_suspended"
    ,subscription_scd."is_soft_cancelled"
    ,subscription_scd."billing_account_id"
    ,subscription_dim."min_billing_account_subscription_name_created_date"
    ,subscription_dim."billing_account_trip_number"
    ,subscription_dim."billing_account_product_group_trip_number"
    ,subscription_dim."is_resubscription"
    ,subscription_dim."subscription_direct_carrier_billing_carrier_name"
    ,subscription_dim."subscription_source_system_name"
    ,subscription_dim."subscription_source_system_name_derived"
    ,subscription_dim."subscription_sign_up_campaign_id"
    ,subscription_dim."subscription_sign_up_giftcode"
    ,subscription_dim."subscription_tracking_id"
    ,subscription_dim."subscription_term_type"
    ,subscription_dim."subscription_country"
    ,subscription_dim."subscription_territory"
    ,subscription_dim."subscription_billing_period"
    ,subscription_dim."subscription_type"
    ,subscription_dim."subscription_tier"
    ,subscription_dim."subscription_monthly_recurring_revenue"
    ,subscription_dim."previous_subscription_monthly_recurring_revenue"
    ,subscription_dim."subscription_product_group"
    ,subscription_dim."subscription_payment_method_id"
    ,subscription_dim."subscription_bill_cycle_day"
    ,subscription_dim."subscription_rateplan_name"
    ,subscription_dim."subscription_rateplan_charge_name"
    ,subscription_dim."subscription_rateplan_charge_effective_start_date"
    ,subscription_dim."subscription_rateplan_charge_effective_end_date"
    ,subscription_dim."subscription_rateplan_charge_charged_through_date"
    ,subscription_dim."subscription_auto_renew"
    ,subscription_dim."subscription_instalment_period"
    ,subscription_dim."subscription_free_trial_start_date"
    ,subscription_dim."subscription_free_trial_end_date"
    ,subscription_dim."subscription_free_trial_length_days_advertised"
    ,subscription_dim."subscription_free_trial_length_days_actual"
    ,subscription_dim."subscription_free_trial_length_months_advertised"
    ,subscription_dim."subscription_free_trial_length_months_actual"
    ,subscription_dim."subscription_intro_discount_discount_percentage"
    ,subscription_dim."subscription_intro_discount_effective_from_date"
    ,subscription_dim."subscription_intro_discount_effective_until_date"
    ,subscription_dim."subscription_intro_discount_discount_duration_days"
    ,subscription_dim."subscription_intro_discount_discount_duration_months"
    ,subscription_dim."subscription_pause_start_date"
    ,subscription_dim."subscription_pause_end_date"
    ,subscription_dim."subscription_pause_start_week"
    ,subscription_dim."subscription_pause_end_week"
    ,subscription_dim."first_post_sign_up_giftcode"
    ,subscription_dim."last_post_sign_up_giftcode"
    ,subscription_dim."first_post_sign_up_giftcode_campaign_name"
    ,subscription_dim."last_post_sign_up_giftcode_campaign_name"
    ,subscription_dim."first_post_sign_up_discount_context"
    ,subscription_dim."last_post_sign_up_discount_context"
    ,subscription_dim."is_introductory_discount"
    ,subscription_dim."min_discount_percentage"
    ,subscription_dim."max_discount_percentage"
    ,subscription_dim."discount_percentage_distinct_count"
    ,subscription_dim."post_sign_up_giftcode_distinct_count"
    ,subscription_dim."post_sign_up_giftcode_list"
    ,subscription_dim."post_sign_up_giftcode_campaign_name_distinct_count"
    ,subscription_dim."post_sign_up_giftcode_campaign_name_list"
    ,subscription_dim."discount_effective_start_date"
    ,subscription_dim."discount_effective_start_week"
    ,subscription_dim."discount_effective_end_date"
    ,subscription_dim."discount_effective_end_week"
    ,subscription_dim."discount_duration_days"
    ,subscription_dim."discount_duration_weeks"
    ,subscription_dim."discount_data"
    ,subscription_dim."subscription_add_on_count"
    ,subscription_dim."has_subscription_add_on"
    ,subscription_dim."subscription_add_on_partner_ids"
    ,subscription_dim."subscription_add_on_types"
    ,subscription_dim."subscription_add_on_effective_start_date"
    ,subscription_dim."subscription_add_on_effective_end_date"
    ,subscription_dim."subscription_add_on_effective_start_week"
    ,subscription_dim."subscription_add_on_effective_end_week"
    ,subscription_dim."crm_account_id"
    ,subscription_dim."dazn_user_id"
    ,subscription_dim."billing_account_currency_code"
    ,subscription_dim."billing_acount_created_date"
    ,subscription_dim."billing_account_is_batch_50"
    ,subscription_dim."billing_account_has_advanced_payment_manager"
    ,subscription_scd."record_valid_from_timestamp"
    ,subscription_scd."record_valid_until_timestamp"
    ,subscription_dim."discounted_monthly_recurring_revenue" AS "subscription_discounted_monthly_recurring_revenue"
    ,subscription_scd."data_source"
    ,subscription_dim."billing_account__skey"
    ,HASH(subscription_scd."is_soft_cancelled"
          ,subscription_dim."subscription_tier"
          ,subscription_dim."billing_account_product_group_trip_number"
          ,subscription_dim."is_resubscription"
          ,subscription_dim."subscription_product_group"
          ,subscription_dim."subscription_auto_renew"
          ,subscription_dim."subscription_instalment_period")
            AS "subscription_info__skey"
    ,subscription_dim."subscription_term__skey"
    ,subscription_dim."subscription_free_trial__skey"
    ,subscription_dim."subscription_pause__skey"
    ,subscription_dim."subscription_add_on__skey"
    ,subscription_dim."subscription_content_attribution__skey"
    ,subscription_dim."subscription_source_system_name_derived__skey"
    ,subscription_dim."subscription_tracking_id__skey"
    ,subscription_dim."subscription_sign_up_campaign_id__skey"
    ,subscription_dim."first_post_sign_up_giftcode_campaign_name__skey"
    ,subscription_dim."last_post_sign_up_giftcode_campaign_name__skey"
FROM subscription_scd
LEFT JOIN subscription_dim ON subscription_scd."subscription_id" = subscription_dim."subscription_id"
