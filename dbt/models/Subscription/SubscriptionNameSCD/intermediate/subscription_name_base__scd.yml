version: 2

models:
  - name: dmp_subscription_name_base__scd
    description: "SCD table representing exactly when each Subscription Name is effective from and until, breaking down into when each Subscription ID is effective from and until (maybe not including all SubIds, if they are canceled and therefore not effective for any period of time)"
    columns:
      - name: subscription_id
        description: "The ID of the Subscription, Subscription Names can have multiple IDs relating to different types of changes"
        quote: true

      - name: subscription_name
        description: "The Name of the Subscription"
        quote: true

      - name: subscription_status
        description: "The current status of the Subscription ID E.g. Active, Cancelled, Expired, ..."
        quote: true

      - name: subscription_version
        description: "The version of the Subscription ID"
        quote: true

      - name: subscription_name_original_created_timestamp
        description: "The timestamp of the creation of the original/first Subscription ID for this Subscription Name"
        quote: true

      - name: subscription_id_created_timestamp
        description: "The timestamp this Subscription ID was created"
        quote: true

      - name: subscription_id_updated_timestamp
        description: "The last time the Subscription ID was updated in EV"
        quote: true

      - name: subscription_start_date
        description: "The date the subscription starts, including any free trial"
        quote: true

      - name: subscription_end_date
        description: "The latest date EV has seen this subscription end, taking into account retrospective cancellation (when the cancel date is before the subscription is updated)"
        quote: true

      - name: billing_account_id
        description: "The EV Account ID coming from the EV Account entity"
        quote: true

      - name: is_soft_cancelled
        description: "The status of the subscription is cancelled and it won't renew on the current end date"
        quote: true

      - name: record_valid_from_timestamp
        description: "The datetime this data point is valid from (inclusive)"
        quote: true

      - name: record_valid_until_timestamp
        description: "The datetime this data point is valid until (exlusive)"
        quote: true

      - name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the model"
        quote: true

      - name: message_type
        description: ""
        quote: true

      - name: event_id
        description: ""
        quote: true

      - name: dazn_order_id
        description: ""
        quote: true

      - name: gross_price
        description: ""
        quote: true

      - name: charge_amount
        description: ""
        quote: true

  - name: subscription_name_base__scd
    description: "SCD table representing exactly when each Subscription Name is effective from and until, breaking down into when each Subscription ID is effective from and until (maybe not including all SubIds, if they are canceled and therefore not effective for any period of time)"
    columns:
      - name: subscription_id
        description: "The ID of the Subscription, Subscription Names can have multiple IDs relating to different types of changes"
        quote: true

      - name: subscription_name
        description: "The Name of the Subscription E.g. A-S15283950"
        quote: true

      - name: subscription_status
        description: "The current status of the Subscription ID E.g. Active, Cancelled, Expired, ..."
        quote: true

      - name: subscription_version
        description: "The version of the Subscription ID"
        quote: true

      - name: subscription_name_original_created_timestamp
        description: "The timestamp of the creation of the original/first Subscription ID for this Subscription Name"
        quote: true

      - name: subscription_id_created_timestamp
        description: "The timestamp this Subscription ID was created"
        quote: true

      - name: subscription_id_updated_timestamp
        description: "The last time the Subscription ID was updated in Zuora"
        quote: true

      - name: subscription_start_date
        description: "The date the subscription starts, including any free trial"
        quote: true

      - name: subscription_end_date
        description: "The latest date Zuora has seen this subscription end, taking into account retrospective cancellation (when the cancel date is before the subscription is updated)"
        quote: true

      - name: billing_account_id
        description: "The Zuora Account ID coming from the Zuora Account entity E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: is_soft_cancelled
        description: "The status of the subscription is cancelled and it won't renew on the current end date"
        quote: true

      - name: record_valid_from_timestamp
        description: "The datetime this data point is valid from (inclusive)"
        quote: true

      - name: record_valid_until_timestamp
        description: "The datetime this data point is valid until (exlusive)"
        quote: true

      - name: data_source
        description: ""
        quote: true

      - name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the model"
        quote: true

      - name: message_type
        description: ""
        quote: true

      - name: event_id
        description: ""
        quote: true

      - name: dazn_order_id
        description: ""
        quote: true

      - name: gross_price
        description: ""
        quote: true

      - name: charge_amount
        description: ""
        quote: true

  - name: zr_subscription_name_base__scd
    description: "SCD table representing exactly when each Subscription Name is effective from and until, breaking down into when each Subscription ID is effective from and until (maybe not including all SubIds, if they are canceled and therefore not effective for any period of time)"
    columns:
      - name: subscription_id
        description: "The ID of the Subscription, Subscription Names can have multiple IDs relating to different types of changes"
        quote: true

      - name: subscription_name
        description: "The Name of the Subscription"
        quote: true

      - name: subscription_status
        description: "The current status of the Subscription ID E.g. Active, Cancelled, Expired, ..."
        quote: true

      - name: subscription_version
        description: "The version of the Subscription ID"
        quote: true

      - name: subscription_name_original_created_timestamp
        description: "The timestamp of the creation of the original/first Subscription ID for this Subscription Name"
        quote: true

      - name: subscription_id_created_timestamp
        description: "The timestamp this Subscription ID was created"
        quote: true

      - name: subscription_id_updated_timestamp
        description: "The last time the Subscription ID was updated in EV"
        quote: true

      - name: subscription_start_date
        description: "The date the subscription starts, including any free trial"
        quote: true

      - name: subscription_end_date
        description: "The latest date EV has seen this subscription end, taking into account retrospective cancellation (when the cancel date is before the subscription is updated)"
        quote: true

      - name: billing_account_id
        description: "The EV Account ID coming from the EV Account entity"
        quote: true

      - name: is_soft_cancelled
        description: "The status of the subscription is cancelled and it won't renew on the current end date"
        quote: true

      - name: record_valid_from_timestamp
        description: "The datetime this data point is valid from (inclusive)"
        quote: true

      - name: record_valid_until_timestamp
        description: "The datetime this data point is valid until (exlusive)"
        quote: true

      - name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the model"
        quote: true

      - name: message_type
        description: ""
        quote: true

      - name: event_id
        description: ""
        quote: true

      - name: dazn_order_id
        description: ""
        quote: true

      - name: gross_price
        description: ""
        quote: true

      - name: charge_amount
        description: ""
        quote: true
