{{
    config(
        materialized='table',
        transient=true,
        schema='TRANSIENT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_2XL_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH subscription AS (
    SELECT 
        "event_timestamp" as "subscription_id_updated_timestamp"
        ,* 
    FROM {{ ref('staging__dmp_billing_events') }}
    WHERE "business_type"='B2C' AND IFNULL("churn_type",'OK') NOT IN ('SERVICE_CHANGE','RECONTRACT' )
    AND UPPER("event_status") IN ('SUCCEEDED','SCHEDULED')  
    AND  LOWER("product_type") != 'addon' OR
        (
            UPPER("message_type") ='BILLING_PRODUCT_RENEWAL' 
            AND LOWER("product_type")='subscription'
            AND "event_status"='FAILED' 
            AND "business_type"='B2C'
        )
)

,subscription_core AS (
    SELECT * FROM {{ ref('staging__dmp_subscription_core') }}
    /*QUALIFY ROW_NUMBER() OVER (PARTITION BY "subscription_name" ORDER BY "subscription_id_updated_timestamp" DESC, "subscription_id_created_timestamp" DESC, "subscription_version"::INT DESC) = 1*/

)

,subscription_name_current AS (
    SELECT * FROM {{ ref('staging__zuora__subscription_name_current') }}
)
 
,docomo_dim AS (
    SELECT * FROM {{ ref('subscription_id_docomo__dim') }}
)

,final AS (
    SELECT
    IFNULL(subscription_core."subscription_id",subscription_name_current."subscription_id") AS "subscription_id"
    ,IFNULL(subscription_core."subscription_name",subscription_name_current."subscription_name") AS "subscription_name"
    ,IFNULL(subscription_core."subscription_status",
            CASE WHEN subscription."churn_type" IN ('VOLUNTARY','INVOLUNTARY')  THEN 'Cancelled' 
            WHEN subscription."message_type"='BILLING_PRODUCT_RENEWAL' AND subscription."product_status"='ACTIVE_CANCEL' THEN 'Cancelled'
            ELSE 'Active' END) AS "subscription_status"
    --,INITCAP(subscription.PRODUCT_STATUS) as "subscription_status"
    ,IFNULL(subscription_core."subscription_version",subscription_name_current."subscription_version") AS "subscription_version"
    ,IFNULL(subscription_core."subscription_name_original_created_timestamp",subscription_name_current."subscription_name_original_created_timestamp") AS "subscription_name_original_created_timestamp"
    ,IFNULL(subscription_core."subscription_id_created_timestamp",subscription_name_current."subscription_id_created_timestamp") AS "subscription_id_created_timestamp"
    ,CASE WHEN UPPER(subscription."churn_type") = 'RECONTRACT' THEN TIMESTAMPADD('minute',-80,subscription."subscription_id_updated_timestamp") 
   --  WHEN LOWER(subscription."product_type") IN ('addon') AND subscription."churn_type" IN ('VOLUNTARY','INVOLUNTARY')  THEN TIMESTAMPADD('minute',-1,subscription."subscription_id_updated_timestamp") 
    ELSE subscription."subscription_id_updated_timestamp" END AS "subscription_id_updated_timestamp"
    ,IFNULL(subscription_core."subscription_start_date",subscription_name_current."subscription_start_date") AS "subscription_start_date"
    ,COALESCE(subscription_core."subscription_end_date",CASE WHEN  LOWER(subscription."product_type")  IN ('subscription','pass')  THEN subscription."validity_end_date"::DATE ELSE subscription_name_current."subscription_end_date" END) AS "subscription_end_date"
    ,IFNULL(subscription_core."billing_account_id",subscription_name_current."billing_account_id") AS "billing_account_id"
    ,CASE WHEN subscription."churn_type" IN ('VOLUNTARY','INVOLUNTARY') THEN TRUE 
         WHEN subscription."message_type"='BILLING_PRODUCT_RENEWAL' AND subscription."product_status"='ACTIVE_CANCEL'  THEN TRUE 
         ELSE FALSE END AS "is_soft_cancelled"
    ,CASE WHEN subscription."message_type"='BILLING_PRODUCT_CANCELATION' 
           AND subscription."churn_type" IN ('VOLUNTARY','INVOLUNTARY')
           AND LOWER(subscription."product_type")  IN ('subscription','pass') 
           AND UPPER("event_status") = 'SUCCEEDED' THEN subscription."event_timestamp"::DATE  --effective_cancellation_date
           ELSE NULL END AS "sub_cancellation_date"
    ,subscription."message_type"
    ,subscription."event_id"
    ,subscription."dazn_order_id"
    ,subscription."gross_price"
    ,subscription."charge_amount"
    ,LOWER("product_type") "product_type"
FROM subscription 
LEFT JOIN subscription_core
    ON CASE 
        WHEN LOWER(subscription."product_type") IN ('addon') 
            THEN subscription."linked_billing_product_id" 
            ELSE subscription."billing_product_id" 
        END  = subscription_core."subscription_id"
LEFT JOIN subscription_name_current
ON subscription."legacy_subscription_name"=subscription_name_current."subscription_name"
)

SELECT 
    final."subscription_id"
    ,final."subscription_name"
    ,final."subscription_status" 
    ,final."subscription_version"
    ,final."subscription_name_original_created_timestamp"
    ,final."subscription_id_created_timestamp"
    ,final."subscription_id_updated_timestamp"
    ,final."subscription_start_date"
    ,final."subscription_end_date"
    ,final."billing_account_id"
    ,final."is_soft_cancelled"
    ,CASE
        -- When it's the first row for the Subscription, then use the CreatedDate
        -- Accounts for Subs that get updated on the day it's created
        WHEN LAG("subscription_id_created_timestamp") OVER (PARTITION BY "subscription_name" ORDER BY "subscription_id_updated_timestamp", "subscription_id_created_timestamp", "subscription_version") IS NULL
            THEN "subscription_name_original_created_timestamp"
        -- Else, just use the current UpdatedDate of the SubId
        ELSE "subscription_id_updated_timestamp"
    END AS "record_valid_from_timestamp"
    ,CASE
        -- If it's the last created ID for that Name, and it has been created after the Sub has actually ended (retrospective cancellation)
        -- Then it is the cancellation and so take the created timestamp, so we can filter these out in the QUALIFY
        WHEN LEAD("subscription_id_updated_timestamp") OVER (PARTITION BY "subscription_name" ORDER BY "subscription_id_updated_timestamp", "subscription_id_created_timestamp", "subscription_version") IS NULL AND "subscription_id_created_timestamp" >= "subscription_end_date"
            THEN "subscription_id_updated_timestamp"
        -- When it's the last row for the Subscription
        -- Then use the SubEndDate plus 1 day minus 1 second (which is 86399 seconds) (to account for the Sub ending at some point in that day), or at least 9999 to not add 1 day to 9999
        WHEN LEAD("subscription_id_updated_timestamp") OVER (PARTITION BY "subscription_name" ORDER BY "subscription_id_updated_timestamp", "subscription_id_created_timestamp", "subscription_version") IS NULL
            THEN LEAST_IGNORE_NULLS(DATEADD('second', 86399, "sub_cancellation_date"), '9999-12-31')
        -- Else, just use the next UpdatedDate for the SubName
        ELSE LEAD("subscription_id_updated_timestamp") OVER (PARTITION BY "subscription_name" ORDER BY "subscription_id_updated_timestamp", "subscription_id_created_timestamp", "subscription_version")
    END AS "record_valid_until_timestamp"

  --  ,INITCAP(PRODUCT_TYPE) as "purchase_product_type"
    ,CURRENT_TIMESTAMP() AS "META__DBT_INSERT_DTTS"
    ,final."message_type"
    ,final."event_id"
    ,final."dazn_order_id"
    ,final."gross_price"
    ,final."charge_amount"
    ,final."product_type"
    FROM final 

UNION ALL

SELECT
    docomo_dim."subscription_id"
    ,docomo_dim."subscription_name"
    ,docomo_dim."subscription_status"
    ,docomo_dim."subscription_version"
    ,docomo_dim."subscription_name_original_created_timestamp"
    ,docomo_dim."subscription_id_created_timestamp"
    ,docomo_dim."subscription_id_updated_timestamp"
    ,docomo_dim."subscription_start_date"
    ,docomo_dim."subscription_end_date"
    ,docomo_dim."billing_account_id"
    ,docomo_dim."subscription_status" = 'Cancelled' AS "is_soft_cancelled"
    ,CASE
        -- When it's the first row for the Subscription, then use the CreatedDate
        -- Accounts for Subs that get updated on the day it's created
        WHEN LAG(docomo_dim."subscription_id_created_timestamp") OVER (PARTITION BY docomo_dim."subscription_name" ORDER BY docomo_dim."subscription_id_updated_timestamp", docomo_dim."subscription_id_created_timestamp", docomo_dim."subscription_version") IS NULL
            THEN docomo_dim."subscription_name_original_created_timestamp"
        -- Else, just use the current UpdatedDate of the SubId
        ELSE docomo_dim."subscription_id_updated_timestamp"
    END AS "record_valid_from_timestamp"
    ,CASE
        -- If it's the last created ID for that Name, and it has been created after the Sub has actually ended (retrospective cancellation)
        -- Then it is the cancellation and so take the created timestamp, so we can filter these out in the QUALIFY
        WHEN LEAD(docomo_dim."subscription_id_updated_timestamp") OVER (PARTITION BY docomo_dim."subscription_name" ORDER BY docomo_dim."subscription_id_updated_timestamp", docomo_dim."subscription_id_created_timestamp", docomo_dim."subscription_version") IS NULL AND docomo_dim."subscription_id_created_timestamp" >= docomo_dim."subscription_end_date"
            THEN docomo_dim."subscription_id_updated_timestamp"
        -- When it's the last row for the Subscription
        -- Then use the SubEndDate plus 1 day minus 1 second (which is 86399 seconds) (to account for the Sub ending at some point in that day), or at least 9999 to not add 1 day to 9999
        WHEN LEAD(docomo_dim."subscription_id_updated_timestamp") OVER (PARTITION BY docomo_dim."subscription_name" ORDER BY docomo_dim."subscription_id_updated_timestamp", docomo_dim."subscription_id_created_timestamp", docomo_dim."subscription_version") IS NULL
            THEN LEAST(DATEADD('second', 86399, docomo_dim."subscription_end_date"), '9999-12-31')
        -- Else, just use the next UpdatedDate for the SubName
        ELSE LEAD(docomo_dim."subscription_id_updated_timestamp") OVER (PARTITION BY docomo_dim."subscription_name" ORDER BY docomo_dim."subscription_id_updated_timestamp", docomo_dim."subscription_id_created_timestamp", docomo_dim."subscription_version")
    END AS "record_valid_until_timestamp"
    ,CURRENT_TIMESTAMP() AS "META__DBT_INSERT_DTTS"
    ,NULL "message_type"
    ,NULL "event_id"
    ,NULL "dazn_order_id"
    ,NULL "gross_price"
    ,NULL "charge_amount"
    ,NULL "product_type"
FROM docomo_dim
-- Filter out rows that are not effective for any time (like hard or retro cancel rows)
QUALIFY "record_valid_from_timestamp" < "record_valid_until_timestamp"
