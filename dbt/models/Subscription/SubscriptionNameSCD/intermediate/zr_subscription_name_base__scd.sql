{{
    config(
        materialized='table',
        transient=true,
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_2XL_WH'
    )
}}

-- Removing the tag to make sure the model is not getting updated.
-- tags=['presentation-subscription-domain']

WITH subscription AS (
    SELECT * FROM {{ ref('staging__zuora__subscription') }}
)

,subscription_name_current AS (
    SELECT * FROM {{ ref('staging__zuora__subscription_name_current') }}
)

SELECT
    subscription."subscription_id"
    ,subscription."subscription_name"
    ,subscription."subscription_status"
    ,subscription."subscription_version"
    ,subscription."subscription_name_original_created_timestamp"
    ,subscription."subscription_id_created_timestamp"
    ,subscription."subscription_id_updated_timestamp"
    ,subscription."subscription_start_date"
    ,subscription."subscription_end_date"
    ,subscription."billing_account_id"
    ,subscription."subscription_status" = 'Cancelled' AS "is_soft_cancelled"
    ,CASE
        -- When it's the first row for the Subscription, then use the CreatedDate
        -- Accounts for Subs that get updated on the day it's created
        WHEN LAG(subscription."subscription_id_created_timestamp") OVER (PARTITION BY subscription."subscription_name" ORDER BY subscription."subscription_id_updated_timestamp", subscription."subscription_id_created_timestamp", subscription."subscription_version") IS NULL
            THEN subscription."subscription_name_original_created_timestamp"
        -- Else, just use the current UpdatedDate of the SubId
        ELSE subscription."subscription_id_updated_timestamp"
    END AS "record_valid_from_timestamp"
    ,CASE
        -- If it's the last created ID for that Name, and it has been created after the Sub has actually ended (retrospective cancellation)
        -- Then it is the cancellation and so take the created timestamp, so we can filter these out in the QUALIFY
        WHEN LEAD(subscription."subscription_id_updated_timestamp") OVER (PARTITION BY subscription."subscription_name" ORDER BY subscription."subscription_id_updated_timestamp", subscription."subscription_id_created_timestamp", subscription."subscription_version") IS NULL AND subscription."subscription_id_created_timestamp" >= subscription."subscription_end_date"
            THEN subscription."subscription_id_updated_timestamp"
        -- When it's the last row for the Subscription
        -- Then use the SubEndDate plus 1 day minus 1 second (which is 86399 seconds) (to account for the Sub ending at some point in that day), or at least 9999 to not add 1 day to 9999
        WHEN LEAD(subscription."subscription_id_updated_timestamp") OVER (PARTITION BY subscription."subscription_name" ORDER BY subscription."subscription_id_updated_timestamp", subscription."subscription_id_created_timestamp", subscription."subscription_version") IS NULL
            THEN LEAST(DATEADD('second', 86399, subscription."subscription_end_date"), '9999-12-31')
        -- Else, just use the next UpdatedDate for the SubName
        ELSE LEAD(subscription."subscription_id_updated_timestamp") OVER (PARTITION BY subscription."subscription_name" ORDER BY subscription."subscription_id_updated_timestamp", subscription."subscription_id_created_timestamp", subscription."subscription_version")
    END AS "record_valid_until_timestamp"
    ,CURRENT_TIMESTAMP() AS "META__DBT_INSERT_DTTS"
    ,NULL "message_type"
    ,NULL "event_id"
    ,NULL "dazn_order_id"
    ,NULL "gross_price"
    ,NULL "charge_amount"
    ,NULL "product_type"
FROM subscription
LEFT JOIN subscription_name_current
    ON subscription."subscription_name" = subscription_name_current."subscription_name"
WHERE
    -- We exclude Expired Subscription Statuses, as all old subscriptions get classed as Expired when a new Subscription ID is created
    -- and that messes with this model, as those updates happen at the same time as the creation
    -- and occasionally the expired subscriptions get mistakenly updated
    subscription."subscription_status" != 'Expired'
    OR
    -- But we need to keep Version 1s as pre mid-2019 we bulk ingested all the data from Zuora and all old Subscription IDs were Expired from the start
    -- which messes with our data model as we need the Version 1 for the retrospective cancellation formula to work correctly
    subscription."subscription_version" = '1'
QUALIFY
    -- Filter out rows that are not effective for any time (like hard or retro cancel rows)
    "record_valid_from_timestamp" < "record_valid_until_timestamp"
    AND
    -- Filter out any row that is valid after the Sub Name Current model says the Sub has ended (accounting for Retro Cancels)
    (("record_valid_until_timestamp"::DATE <= CASE
        WHEN subscription_name_current."subscription_id_created_timestamp" > subscription_name_current."subscription_end_date"
            THEN subscription_name_current."subscription_id_created_timestamp"::DATE
        ELSE LEAST(DATEADD('day', 1, subscription_name_current."subscription_end_date"), '9999-12-31')
        END)
        OR
        -- Unless it's the very first row of the Sub, as we always need at least one row per sub, and it sometimes is very weird and the first row gets updated after the end.
        (ROW_NUMBER() OVER (PARTITION BY subscription."subscription_name" ORDER BY subscription."subscription_id_updated_timestamp", subscription."subscription_id_created_timestamp", subscription."subscription_version") = 1))
