{{
    config(
        materialized='table',
        transient=true,
        schema='TRANSIENT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_2XL_WH',
        tags=['presentation-subscription-domain']
    )
}}

WITH zr_base_scd AS (
    SELECT * FROM {{ ref('zr_subscription_name_base__scd') }}
)

 ,dmp_base_scd AS (
    SELECT * FROM {{ ref('dmp_subscription_name_base__scd') }}
 )

 ,zr_ev_merge AS (
    SELECT *,'ZUORA' AS "data_source" FROM zr_base_scd
    UNION ALL
    SELECT *,'DMP' AS "data_source" FROM dmp_base_scd
 )

,record_valid_from AS ( 
     SELECT 
    "subscription_id"
    ,"subscription_name"
    ,"subscription_status" 
    ,"subscription_version"::INT AS "subscription_versions"
    ,"subscription_name_original_created_timestamp"
    ,"subscription_id_created_timestamp"
    ,"subscription_id_updated_timestamp"
    ,"subscription_start_date"
    ,"subscription_end_date"
    ,"billing_account_id"
    ,"is_soft_cancelled"
    ,"record_valid_until_timestamp" as "record_valid_until_timestampp"
    ,"record_valid_from_timestamp" AS "record_valid_from_timestampp"
    , "message_type"
    , "event_id"
    , "dazn_order_id"
    , "gross_price"
    , "charge_amount"
    ,"data_source"
    FROM zr_ev_merge
)
 
 ,final AS (
    SELECT 
    "subscription_id"
    ,"subscription_name"
    ,"subscription_status" 
    ,"subscription_versions" AS "subscription_version"
    ,"subscription_name_original_created_timestamp"
    ,"subscription_id_created_timestamp"
    ,"subscription_id_updated_timestamp"
    ,"subscription_start_date"
    ,"subscription_end_date"
    ,"billing_account_id"
    ,"is_soft_cancelled"
    ,CASE
        -- When it's the first row for the Subscription, then use the CreatedDate
        -- Accounts for Subs that get updated on the day it's created
        WHEN LAG("subscription_id_created_timestamp") OVER (PARTITION BY "subscription_name" ORDER BY "subscription_id_updated_timestamp", "subscription_id_created_timestamp", "subscription_version") IS NULL
            THEN "subscription_name_original_created_timestamp"
        --cancel case 
        WHEN "data_source"='ZUORA' AND "subscription_status"='Cancelled' THEN "record_valid_from_timestampp"    
        -- Else, just use the current UpdatedDate of the SubId
        ELSE "subscription_id_updated_timestamp"
    END AS "record_valid_from_timestamp"
    ,"record_valid_until_timestampp"
    , "message_type"
    , "event_id"
    , "dazn_order_id"
    , "gross_price"
    , "charge_amount"
    ,"data_source"
 FROM  record_valid_from
 )

 SELECT 
    "subscription_id"
    ,"subscription_name"
    ,"subscription_status" 
    ,"subscription_version"
    ,"subscription_name_original_created_timestamp"
    ,"subscription_id_created_timestamp"
    ,"subscription_id_updated_timestamp"
    ,"subscription_start_date"
    ,"subscription_end_date"
    ,"billing_account_id"
    ,"is_soft_cancelled"
   ,CASE
            WHEN "subscription_end_date" >= '9999-01-01' THEN '9999-12-31'
            ELSE LAST_DAY("subscription_end_date", 'week')
        END AS "subscription_end_week"
    ,"record_valid_from_timestamp"
    ,CASE
        -- If it's the last created ID for that Name, and it has been created after the Sub has actually ended (retrospective cancellation)
        -- Then it is the cancellation and so take the created timestamp, so we can filter these out in the QUALIFY
        WHEN LEAD("record_valid_from_timestamp") OVER (PARTITION BY "subscription_name" ORDER BY "subscription_id_updated_timestamp", "subscription_id_created_timestamp", "subscription_version") IS NULL AND "subscription_id_created_timestamp" >= "subscription_end_date"
            THEN "subscription_id_updated_timestamp"
        -- When it's the last row for the Subscription
        -- Then use the SubEndDate plus 1 day minus 1 second (which is 86399 seconds) (to account for the Sub ending at some point in that day), or at least 9999 to not add 1 day to 9999
        WHEN LEAD("record_valid_from_timestamp") OVER (PARTITION BY "subscription_name" ORDER BY "subscription_id_updated_timestamp", "subscription_id_created_timestamp", "subscription_version") IS NULL
            THEN "record_valid_until_timestampp"
        -- Else, just use the next UpdatedDate for the SubName
        ELSE LEAD("record_valid_from_timestamp") OVER (PARTITION BY "subscription_name" ORDER BY "subscription_id_updated_timestamp", "subscription_id_created_timestamp", "subscription_version")
    END AS "record_valid_until_timestamp"
    ,"data_source"
    ,CURRENT_TIMESTAMP() AS "META__DBT_INSERT_DTTS"
    , "message_type"
    , "event_id"
    , "dazn_order_id"
    , "gross_price"
    , "charge_amount"
    FROM final