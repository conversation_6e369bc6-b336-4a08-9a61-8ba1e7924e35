{{
    config(
        materialized='table',
        schema='PRESENTATION'
    )
}}

-- Removing the tag to make sure the model is not getting updated.
-- tags=['presentation-subscription-domain']

WITH subscription_name_current AS (
    SELECT * FROM {{ ref('staging__zuora__subscription_name_current') }}
)

,rateplan_current AS (
    SELECT * FROM {{ ref('staging__zuora__rateplan_current') }}
)

,rateplancharge AS (
    SELECT * FROM {{ ref('staging__zuora__rateplancharge') }}
)

SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,HASH(
        subscription_name_current."subscription_name"
        ,rateplan_current."rateplan_partner_user_id"
        ,rateplancharge."rateplan_charge_effective_start_date"
    ) AS "subscription_add_on__skey"
    ,subscription_name_current."subscription_id"
    ,subscription_name_current."subscription_name"
    ,subscription_name_current."subscription_id_created_timestamp"
    ,subscription_name_current."subscription_start_date"
    ,subscription_name_current."subscription_end_date"
    ,subscription_name_current."billing_account_id"
    ,rateplan_current."rateplan_id"
    ,rateplan_current."rateplan_name"
    ,rateplan_current."rateplan_created_timestamp"
    ,rateplan_current."entitlement_set_id"
    ,rateplan_current."rateplan_source_system_name"
    ,rateplan_current."rateplan_tracking_id"
    ,rateplan_current."rateplan_partner_id"
    ,rateplan_current."rateplan_partner_user_id"
    ,rateplan_current."rateplan_addon_type" AS "subscription_add_on_type"
    ,rateplan_current."rateplan_next_invoice_date"
    ,rateplancharge."rateplan_charge_id"
    ,rateplancharge."rateplan_charge_name"
    ,rateplancharge."rateplan_charge_charge_type"
    ,rateplancharge."rateplan_charge_effective_start_date" AS "subscription_add_on_effective_start_date"
    ,CASE
        -- When it's a PAC/3PP AddOn, and has the 1 day Effectiveness
        WHEN rateplancharge."rateplan_charge_charge_type" = 'OneTime' AND DATEDIFF('day', rateplancharge."rateplan_charge_effective_start_date", rateplancharge."rateplan_charge_effective_end_date") = 1
            -- Then use the least of the NextInvoiceDate (plus active grace period) or the SubEndDate (if it's cancelled before the NextInvoiceDate)
            THEN LEAST(subscription_name_current."subscription_end_date", DATEADD('day', 12, rateplan_current."rateplan_next_invoice_date"))
        -- Else (when it's a direct sub, or 0 day 3PP/PAC) use the RPC Eff End, as that correctly accounts for pauses and cancels
        ELSE "rateplan_charge_effective_end_date"
    END AS "subscription_add_on_effective_end_date"
FROM subscription_name_current
LEFT JOIN rateplan_current ON rateplan_current."subscription_id" = subscription_name_current."subscription_id"
LEFT JOIN rateplancharge ON rateplancharge."rateplan_id" = rateplan_current."rateplan_id"
WHERE
    rateplan_current."rateplan_product_type" = 'addon'
