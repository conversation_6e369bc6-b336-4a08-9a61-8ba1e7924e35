version: 2

models:
  - name: subscription_add_on__dim
    description: "Dimension describing the latest state of a unique Subscription AddOn"
    columns:
      - name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the model"
        quote: true

      - name: subscription_add_on__skey
        description: "A created unique key for the dataset based off a concatenation of Subscription Name, Partner User ID and the Effective Start Date"
        quote: true
        # tests:
        #   - unique:
        #       config:
        #         severity: error
        #         error_if: ">1000"
        #         warn_if: ">0"

      - name: subscription_id
        description: "The ID of the Subscription, Subscription Names can have multiple IDs relating to different types of changes to the Subscription"
        quote: true

      - name: subscription_name
        description: "The Subscription Name of the Subscription E.g. A-S15283950"
        quote: true

      - name: subscription_id_created_timestamp
        description: "The timestamp the Subscription ID was created in Zuora"
        quote: true

      - name: subscription_start_date
        description: "The date the subscription starts, including any free trial"
        quote: true

      - name: subscription_end_date
        description: "The latest date Zuora has seen this subscription end, taking into account retrospective cancellation (when the cancel date is before the subscription is updated)"
        quote: true

      - name: billing_account_id
        description: "The Zuora Account ID coming from the Zuora Account entity E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: rateplan_id
        description: "The ID of the RatePlan entity directly relating to this Subscription ID"
        quote: true

      - name: rateplan_name
        description: "The name of the RatePlan directly relating to this Subscription ID"
        quote: true

      - name: rateplan_created_timestamp
        description: "The timestamp the Rate Plan associated to this Subscription ID was created"
        quote: true

      - name: entitlement_set_id
        description: "The Entitlement Set Id of the Rateplan"
        quote: true

      - name: rateplan_source_system_name
        description: "The Name of the Source System of the Rate Plan. For AddOns, this field is expected to remain unpopulated."
        quote: true

      - name: rateplan_tracking_id
        description: "The Tracking ID associated to the landing page the user has purchased their AddOn through"
        quote: true

      - name: rateplan_partner_id
        description: "The Partner ID of the Rate Plan, which refers to the Linear Channel the user has purchased"
        quote: true

      - name: subscription_add_on_type
        description: "This field identifies the add-on type, the viewership on multiple screens for the same ip."
        quote: true

      - name: rateplan_partner_user_id
        description: "The Partner User ID of the Rate Plan, which refers to the Linear Channel the user has purchased"
        quote: true

      - name: rateplan_next_invoice_date
        description: "This field will be populated for the first time when the initial amendment is submitted. It will be equal to the contract effective date + 1 month. After then it will be set to the next month."
        quote: true

      - name: rateplan_charge_id
        description: "The ID of the RatePlanCharge entity directly relating to this Subscription ID"
        quote: true

      - name: rateplan_charge_name
        description: "The name of the RatePlanCharge directly relating to this Subscription ID"
        quote: true

      - name: rateplan_charge_charge_type
        description: "The Type of the Rate Plan Charge, for AddOns this can be Recurring for Direct Subscriptions, or OneTime for 3PP/PAC Subscriptions"
        quote: true

      - name: subscription_add_on_effective_start_date
        description: "The date that this RatePlanCharge is effective from"
        quote: true

      - name: subscription_add_on_effective_end_date
        description: "The date that this RatePlanCharge is effective until, overwritten as 1 month after the from for OneTime AddOn charges"
        quote: true
