version: 2

models:
  - name: subscription_daily_changes__fact
    description: "Fact table of the count of all relevant changes to a subscription (start, end, pause, price change, ...) with all skeys that you can join on to bring in required data about the subscription at the time (before and after) of change"
    columns:
      - name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the model"
        quote: true

      - name: batch_date
        description: "The date of the change in the subscription"
        quote: true

      - name: billing_account__skey
        description: "The surrogate key (skey) used to join on the billing_account dim"
        quote: true

      - name: previous_billing_account__skey
        description: "The previous state of billing account skey, before a change was applied to this subscription"
        quote: true

      - name: subscription_info__skey
        description: "The hashed reference to subscription info"
        quote: true

      - name: previous_subscription_info__skey
        description: "The previous state of subscription info skey, before a change was applied to this subscription"
        quote: true

      - name: subscription_term__skey
        description: "The hashed reference to subscription term"
        quote: true

      - name: previous_subscription_term__skey
        description: "The previous state of subscription term skey, before a change was applied to this subscription"
        quote: true

      - name: subscription_charge__skey
        description: "The hashed reference to subscription charge"
        quote: true

      - name: previous_subscription_charge__skey
        description: "The previous state of subscription charge skey, before a change was applied to this subscription"
        quote: true

      - name: subscription_free_trial__skey
        description: "The hashed reference to subscription free trial"
        quote: true

      - name: previous_subscription_free_trial__skey
        description: "The previous state of subscription free trial skey, before a change was applied to this subscription"
        quote: true

      - name: subscription_discount__skey
        description: "The hashed reference to subscription discount"
        quote: true

      - name: previous_subscription_discount__skey
        description: "The previous state of subscription discount skey, before a change was applied to this subscription"
        quote: true

      - name: subscription_pause__skey
        description: "The hashed reference to subscription pause"
        quote: true

      - name: previous_subscription_pause__skey
        description: "The previous state of subscription pause skey, before a change was applied to this subscription"
        quote: true

      - name: subscription_add_on__skey
        description: "The hashed reference to subscription AddOn"
        quote: true

      - name: previous_subscription_add_on__skey
        description: "The previous state of subscription AddOn skey, before a change was applied to this subscription"
        quote: true

      - name: subscription_dimension_changes__skey
        description: "Hashed reference to all rows of the subscription dimension changes table"
        quote: true

      - name: subscription_term_changes__skey
        description: "Hashed reference to all rows of the subscription term changes table"
        quote: true

      - name: add_on_change__skey
        description: "Hashed reference to all rows of the AddOn change table"
        quote: true

      - name: subscription_charge_changes__skey
        description: "Hashed reference to all rows of the subscription charge changes table"
        quote: true

      - name: subscription_content_attribution__skey
        description: "The surrogate key (skey) used to join on the subscription_content_attribution__dim"
        quote: true

      - name: subscription_source_system_name_derived__skey
        description: "The surrogate key (skey) used to join on the sub_source_system dim"
        quote: true

      - name: subscription_tracking_id__skey
        description: "The surrogate key (skey) used to join on the tracking_id dim"
        quote: true

      - name: subscription_sign_up_campaign_id__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: first_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: last_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: subscription_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: subscription_daily_status__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_daily_status__dim"
        quote: true

      - name: previous_subscription_daily_status__skey
        description: "The previous state of subscription daily status skey, before a change was applied to this subscription"
        quote: true

      - name: daily_usd_fx_rate__skey
        description: "The surrogate key (skey) used to join on the daily_usd_fx_rate dim"
        quote: true

      - name: billing_account_product_groups__skey
        description: "The surrogate key (skey) used to join on the billing_account_product_groups__dim"
        quote: true

      - name: previous_billing_account_product_groups__skey
        description: "The previous state of the billing_account_product_groups__skey, before a change was applied to this subscription"
        quote: true

      - name: subscription_count
        description: "The count of subscriptions that have those specific data points"
        quote: true
