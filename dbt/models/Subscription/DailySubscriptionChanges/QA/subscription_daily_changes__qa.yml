version: 2

models:
  - name: subscription_daily_changes__qa
    description: "A table containing a daily summary of various QA/error check relating to the Sub Daily Changes dataset"
    columns:
      - name: batch_date
        description: "The date of the change in the subscription"
        quote: true

      - name: subscription_product_group
        description: "Can be either 'NFL' whereas it is an NFL subscription, or 'DAZN' otherwise."
        quote: true

      - name: subscription_count
        description: "The count of the total subscriptions that have had a change on the batch_date"
        quote: true

      - name: subscription_dimension_changes__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the subscription_dimension_changes__skey"
        quote: true

      - name: subscription_charge_changes__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the subscription_charge_changes__skey"
        quote: true

      - name: add_on_change__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the add_on_change__skey"
        quote: true

      - name: subscription_term_changes__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the subscription_term_changes__skey"
        quote: true

      - name: subscription_term_changes_metrics__skey__null_count
        description: "The count of the total amount of subscriptions that do not have a join for the subscription_term_changes_metrics__skey"
        quote: true

      - name: is_free_trial_start
        description: "The count of the total amount of subscriptions on the batch_date that have started their free trial"
        quote: true

      - name: is_free_trial_churn
        description: "The count of the total amount of subscriptions on the batch_date that have ended their free trial without moving to a paid subscription"
        quote: true

      - name: is_free_trial_acquisition
        description: "The count of the total amount of subscriptions on the batch_date that have ended their free trial and moved to a paid subscription"
        quote: true

      - name: is_new_acqusition
        description: "The count of the total amount of subscriptions on the batch_date that have started their paid subscription for the first time"
        quote: true

      - name: is_resubscription_acquisition
        description: "The count of the total amount of subscriptions on the batch_date that have started their paid subscription and have had a subscription before"
        quote: true

      - name: is_unpause_acquisition
        description: "The count of the total amount of subscriptions on the batch_date that have resumed their subscription after a pause period"
        quote: true

      - name: is_gross_churn
        description: "The count of the total amount of subscriptions on the batch_date that have ended their paid subscription"
        quote: true

      - name: is_pause_churn
        description: "The count of the total amount of subscriptions on the batch_date that have started a pause period"
        quote: true

      - name: retrospective_cancellation_count
        description: "The count of the total amount of subscriptions that have had a cancellation created in Zuora with a SubscriptionEndDate in the past"
        quote: true

      - name: retrospective_cancellation_over_1_day_count
        description: "The count of the total amount of subscriptions that have had a cancellation created in Zuora with a SubscriptionEndDate more than 1 day in the past"
        quote: true

      - name: max_dbt_insert_dtts
        description: "The maximum datetime records have been inserted into this table for the batch_date"
        quote: true
