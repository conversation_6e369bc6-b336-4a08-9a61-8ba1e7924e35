{{
    config(
        materialized='table',
        schema='METADATA',
        tags=['presentation-subscription-domain']
    )
}}

WITH sub_daily_changes AS (
    SELECT * FROM {{ ref('subscription_daily_changes') }}
)

,sub_dim_changes_dim AS (
    SELECT * FROM {{ ref('subscription_dimension_changes__dim') }}
)

,sub_charge_changes_dim AS (
    SELECT * FROM {{ ref('subscription_charge_changes__dim') }}
)

,sub_add_on_changes_dim AS (
    SELECT * FROM {{ ref('subscription_add_on_changes__dim') }}
)

,sub_term_changes_dim AS (
    SELECT * FROM {{ ref('subscription_term_changes__dim') }}
)

,sub_term_metrics_changes_dim AS (
    SELECT * FROM {{ ref('daily_subscription_changes__subscription_term_changes_metrics__dim') }}
)

,sub_info_dim AS (
    SELECT * FROM {{ ref('subscription_mart_info__dim') }}
)

,sub_name_current AS (
    SELECT * FROM {{ ref('staging__zuora__subscription_name_current') }}
)

,daily_retro_canc AS (
    SELECT
        "subscription_id_created_timestamp"::DATE AS "retrospetive_cancellation_date"
        ,COUNT_IF("subscription_id_created_timestamp"::DATE > "subscription_end_date") AS "retrospective_cancellation_count"
        ,COUNT_IF(DATEDIFF('day', "subscription_end_date", "subscription_id_created_timestamp") > 1) AS "retrospective_cancellation_over_1_day_count"
    FROM sub_name_current
    GROUP BY 1
)

SELECT
    sub_daily_changes."batch_date"
    ,sub_info_dim."subscription_product_group"
    ,COUNT(sub_daily_changes."subscription_name") AS "subscription_count"

    -- NULL skey joins
    ,COUNT_IF(sub_dim_changes_dim."subscription_dimension_changes__skey" IS NULL) AS "subscription_dimension_changes__skey__null_count"
    ,COUNT_IF(sub_charge_changes_dim."subscription_charge_changes__skey" IS NULL) AS "subscription_charge_changes__skey__null_count"
    ,COUNT_IF(sub_add_on_changes_dim."add_on_change__skey" IS NULL) AS "add_on_change__skey__null_count"
    ,COUNT_IF(sub_term_changes_dim."subscription_term_changes__skey" IS NULL) AS "subscription_term_changes__skey__null_count"
    ,COUNT_IF(sub_term_metrics_changes_dim."subscription_term_changes__skey" IS NULL) AS "subscription_term_changes_metrics__skey__null_count"

    -- Metrics Counts
    ,SUM(sub_term_metrics_changes_dim."is_free_trial_start") AS "is_free_trial_start"
    ,SUM(sub_term_metrics_changes_dim."is_free_trial_churn") AS "is_free_trial_churn"
    ,SUM(sub_term_metrics_changes_dim."is_free_trial_acquisition") AS "is_free_trial_acquisition"
    ,SUM(sub_term_metrics_changes_dim."is_new_acqusition") AS "is_new_acqusition"
    ,SUM(sub_term_metrics_changes_dim."is_resubscription_acquisition") AS "is_resubscription_acquisition"
    ,SUM(sub_term_metrics_changes_dim."is_unpause_acquisition") AS "is_unpause_acquisition"
    ,SUM(sub_term_metrics_changes_dim."is_gross_churn") AS "is_gross_churn"
    ,SUM(sub_term_metrics_changes_dim."is_pause_churn") AS "is_pause_churn"

    -- Retro Cancellation
    ,MAX(daily_retro_canc."retrospective_cancellation_count") AS "retrospective_cancellation_count"
    ,MAX(daily_retro_canc."retrospective_cancellation_over_1_day_count") AS "retrospective_cancellation_over_1_day_count"

    -- Last updated
    ,MAX(sub_daily_changes."META__DBT_INSERT_DTTS") AS "max_dbt_insert_dtts"

FROM sub_daily_changes
LEFT JOIN sub_dim_changes_dim USING ("subscription_dimension_changes__skey")
LEFT JOIN sub_charge_changes_dim USING ("subscription_charge_changes__skey")
LEFT JOIN sub_add_on_changes_dim USING ("add_on_change__skey")
LEFT JOIN sub_term_changes_dim ON sub_daily_changes."subscription_term_changes__skey" = sub_term_changes_dim."subscription_term_changes__skey"
LEFT JOIN sub_term_metrics_changes_dim ON sub_daily_changes."subscription_term_changes__skey" = sub_term_metrics_changes_dim."subscription_term_changes__skey"
LEFT JOIN sub_info_dim USING ("subscription_info__skey")
LEFT JOIN daily_retro_canc ON sub_daily_changes."batch_date" = daily_retro_canc."retrospetive_cancellation_date"
GROUP BY 1,2
