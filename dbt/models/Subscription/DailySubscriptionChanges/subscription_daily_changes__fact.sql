{{
    config(
        materialized='incremental',
        unique_key='"batch_date"',
        incremental_strategy='delete+insert',
        database='SUBSCRIPTION_DAILY_CHANGES__B2C__MART__' + snowflake_env(),
        schema='FACT',
        cluster_by=['"batch_date"'],
        tags=['presentation-subscription-domain']
    )
}}

{% set subscription_mart_variables = subscription_mart_variables() %}

WITH daily_changes AS (
    SELECT * FROM {{ ref('subscription_daily_changes') }}
    {% if is_incremental() %}
    WHERE "batch_date" BETWEEN DATEADD('day', -{{ subscription_mart_variables.rebuild_days}}, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
    {% endif %}
)

SELECT
    CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
    ,"batch_date"
    ,"billing_account__skey"
    ,"previous_billing_account__skey"
    ,"subscription_info__skey"
    ,"previous_subscription_info__skey"
    ,"subscription_term__skey"
    ,"previous_subscription_term__skey"
    ,"subscription_charge__skey"
    ,"previous_subscription_charge__skey"
    ,"subscription_free_trial__skey"
    ,"previous_subscription_free_trial__skey"
    ,"subscription_discount__skey"
    ,"previous_subscription_discount__skey"
    ,"subscription_pause__skey"
    ,"previous_subscription_pause__skey"
    ,"subscription_add_on__skey"
    ,"previous_subscription_add_on__skey"
    ,"subscription_dimension_changes__skey"
    ,"subscription_term_changes__skey"
    ,"add_on_change__skey"
    ,"subscription_charge_changes__skey"
    ,"subscription_content_attribution__skey"
    ,"subscription_source_system_name_derived__skey"
    ,"subscription_tracking_id__skey"
    ,"subscription_sign_up_campaign_id__skey"
    ,"first_post_sign_up_giftcode_campaign_name__skey"
    ,"last_post_sign_up_giftcode_campaign_name__skey"
    ,"subscription_giftcode_campaign_name__skey"
    ,"subscription_daily_status__skey"
    ,"previous_subscription_daily_status__skey"
    ,"daily_usd_fx_rate__skey"
    ,"billing_account_product_groups__skey"
    ,"previous_billing_account_product_groups__skey"
    ,COUNT("subscription_name") AS "subscription_count"
FROM daily_changes
GROUP BY ALL
