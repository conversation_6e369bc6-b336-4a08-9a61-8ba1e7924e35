{{
    config(
        materialized='view',
        database='SUBSCRIPTION_DAILY_CHANGES__B2C__MART__' + snowflake_env(),
        schema='SUBSCRIPTION',
        alias='subscription_daily_status__dim',
        tags=['presentation-subscription-domain'],
        query_tag='presentation_subscription_domain'
    )
}}

SELECT
    *
    ,"subscription_daily_status__skey" AS "previous_subscription_daily_status__skey"
FROM {{ ref('subscription_daily_status__dim') }}
