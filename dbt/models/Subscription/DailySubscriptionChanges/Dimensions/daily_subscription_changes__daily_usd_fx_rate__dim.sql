{{
    config(
        materialized='view',
        database='SUBSCRIPTION_DAILY_CHANGES__B2C__MART__' + snowflake_env(),
        schema='GLOBAL',
        alias='currency__daily_usd_fx_rate__dim',
        tags=['presentation-subscription-domain'],
        query_tag='presentation_subscription_domain'
    )
}}

SELECT
    *
    ,"daily_usd_fx_rate__skey" as "previous_daily_usd_fx_rate__skey"
FROM {{ ref('currency__daily_usd_fx_rate__dim') }}
