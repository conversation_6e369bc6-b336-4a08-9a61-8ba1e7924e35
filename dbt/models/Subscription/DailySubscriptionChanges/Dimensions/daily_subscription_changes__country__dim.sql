{{
    config(
        materialized='view',
        database='SUBSCRIPTION_DAILY_CHANGES__B2C__MART__' + snowflake_env(),
        schema='GLOBAL',
        alias='COUNTRY__DIM',
        tags=['presentation-subscription-domain']
    )
}}

SELECT
    -- Keeping this unhashed but renaming so that we can easily see which countries are missing mappings and keeps the join simple and still has the right naming convention
    "join_key" AS "country__skey"
    ,*
FROM {{ ref('region_dimension') }}
