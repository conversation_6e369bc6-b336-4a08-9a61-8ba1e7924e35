{{
    config(
        materialized='view',
        database='SUBSCRIPTION_DAILY_CHANGES__B2C__MART__' + snowflake_env(),
        schema='SUBSCRIPTION',
        alias='subscription_info__dim',
        tags=['presentation-subscription-domain'],
        query_tag='presentation_subscription_domain'
    )
}}

SELECT
    *
    ,"subscription_info__skey" as "previous_subscription_info__skey"
FROM {{ ref('subscription_mart_info__dim') }}
