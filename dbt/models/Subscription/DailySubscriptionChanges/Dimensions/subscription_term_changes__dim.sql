{{
    config(
        materialized='table',
        database='SUBSCRIPTION_DAILY_CHANGES__B2C__MART__' + snowflake_env(),
        schema='SUBSCRIPTION_DAILY_CHANGES',
        tags=['presentation-subscription-domain']
    )
}}

WITH daily_changes AS (
    SELECT * FROM {{ ref('subscription_daily_changes') }}
)

SELECT DISTINCT
    "subscription_started"
    ,"subscription_ended"
    ,"pause_started"
    ,"pause_ended"
    ,"free_trial_started"
    ,"free_trial_ended"
    ,"is_resubscription"
    ,"is_immediate_resubscription"
    ,"is_immediate_resubscription_cancel"
    ,"has_active_free_trial"
    ,"is_paused"
    ,"subscription_term_changes__skey"
FROM daily_changes
