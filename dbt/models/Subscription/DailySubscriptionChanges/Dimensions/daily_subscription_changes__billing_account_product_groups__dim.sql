{{
    config(
        materialized='view',
        database='SUBSCRIPTION_DAILY_CHANGES__B2C__MART__' + snowflake_env(),
        schema='CUSTOMER',
        alias='billing_account_product_groups__dim',
        tags=['presentation-subscription-domain'],
        query_tag='presentation_subscription_domain'
    )
}}

SELECT
    *
    ,"billing_account_product_groups__skey" as "previous_billing_account_product_groups__skey"
FROM {{ ref('billing_account_product_groups__dim') }}
