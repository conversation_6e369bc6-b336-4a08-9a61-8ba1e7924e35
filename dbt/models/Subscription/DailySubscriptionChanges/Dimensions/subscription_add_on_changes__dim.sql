{{
    config(
        materialized='table',
        database='SUBSCRIPTION_DAILY_CHANGES__B2C__MART__' + snowflake_env(),
        schema='SUBSCRIPTION_DAILY_CHANGES',
        tags=['presentation-subscription-domain']
    )
}}

WITH daily_changes AS (
    SELECT * FROM {{ ref('subscription_daily_changes') }}
)

SELECT
    "add_on_started"
    ,"add_on_ended"
    ,"add_on_change__skey"
FROM daily_changes
GROUP BY 1,2,3
