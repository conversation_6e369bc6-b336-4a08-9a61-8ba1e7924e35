{{
    config(
        materialized='view',
        database='SUBSCRIPTION_DAILY_CHANGES__B2C__MART__' + snowflake_env(),
        schema='SUBSCRIPTION_DAILY_CHANGES',
        alias='subscription_term_changes_metrics__dim',
        tags=['presentation-subscription-domain'],
        query_tag='presentation_subscription_domain'
    )
}}

SELECT
    "is_free_trial_start"
    ,"is_free_trial_acquisition"
    ,"is_new_acqusition"
    ,"is_resubscription_acquisition"
    ,"is_unpause_acquisition"
    ,"is_gross_churn"
    ,"is_pause_churn"
    ,"is_free_trial_churn"
    ,HASH(
        IFF("subscription_started" = 'Yes', TRUE, FALSE)
        ,IFF("subscription_ended" = 'Yes', TRUE, FALSE)
        ,IFF("pause_started" = 'Yes', TRUE, FALSE)
        ,IFF("pause_ended" = 'Yes', TRUE, FALSE)
        ,IFF("free_trial_started" = 'Yes', TRUE, FALSE)
        ,IFF("free_trial_ended" = 'Yes', TRUE, FALSE)
        ,IFF("is_resubscription" = 'Yes', TRUE, FALSE)
        ,IFF("is_immediate_resubscription" = 'Yes', TRUE, FALSE)
        ,IFF("is_immediate_resubscription_cancel" = 'Yes', TRUE, FALSE)
        ,IFF("has_active_free_trial" = 'Yes', TRUE, FALSE)
        ,IFF("is_paused" = 'Yes', TRUE, FALSE)
    ) AS "subscription_term_changes__skey"
FROM {{ ref('subscription_term_changes_metrics__dim') }}
