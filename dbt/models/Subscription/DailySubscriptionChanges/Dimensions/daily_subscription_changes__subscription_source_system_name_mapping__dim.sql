{{
    config(
        materialized='view',
        database='SUBSCRIPTION_DAILY_CHANGES__B2C__MART__' + snowflake_env(),
        schema='SUBSCRIPTION',
        alias='subscription_source_system_name_mapping__dim',
        tags=['presentation-subscription-domain'],
        query_tag='presentation_subscription_domain'
    )
}}

SELECT
    *
FROM {{ ref('subscription_source_system_name_mapping_dim') }}
