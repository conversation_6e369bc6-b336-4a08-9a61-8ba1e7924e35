{{
    config(
        materialized='table',
        database='SUBSCRIPTION_DAILY_CHANGES__B2C__MART__' + snowflake_env(),
        schema='SUBSCRIPTION_DAILY_CHANGES',
        tags=['presentation-subscription-domain']
    )
}}

WITH daily_changes AS (
    SELECT * FROM {{ ref('subscription_daily_changes') }}
)

SELECT
    "is_billing_account_change"
    ,"is_subscription_info_change"
    ,"is_subscription_term_change"
    ,"is_subscription_charge_change"
    ,"is_subscription_free_trial_change"
    ,"is_subscription_discount_offer_change"
    ,"is_subscription_pause_change"
    ,"is_subscription_add_on_change"
    ,"subscription_dimension_changes__skey"
FROM daily_changes
GROUP BY 1,2,3,4,5,6,7,8,9
