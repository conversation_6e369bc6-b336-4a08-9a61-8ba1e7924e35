{{
    config(
        materialized='view',
        database='SUBSCRIPTION_DAILY_CHANGES__B2C__MART__' + snowflake_env(),
        schema='SUBSCRIPTION',
        alias='subscription_discount__dim',
        tags=['presentation-subscription-domain'],
        query_tag='presentation_subscription_domain'
    )
}}

SELECT
    *
    ,"subscription_discount__skey" as "previous_subscription_discount__skey"
FROM {{ ref('subscription_mart_discount__dim') }}
