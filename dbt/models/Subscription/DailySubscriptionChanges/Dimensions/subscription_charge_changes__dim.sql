{{
    config(
        materialized='table',
        database='SUBSCRIPTION_DAILY_CHANGES__B2C__MART__' + snowflake_env(),
        schema='SUBSCRIPTION_DAILY_CHANGES',
        tags=['presentation-subscription-domain']
    )
}}

WITH daily_changes AS (
    SELECT * FROM {{ ref('subscription_daily_changes') }}
)

,final AS (
    SELECT 
        "is_monthly_recurring_revenue_change"
        ,"monthly_recurring_revenue_difference"
        ,"is_discounted_monthly_recurring_revenue_change"
        ,"discounted_monthly_recurring_revenue_difference"
        ,"discount_applied"
        ,"discount_started"
        ,"discount_ended"
        ,"introductory_discount_ended"
        ,"sign_up_discount_ended"
        ,"is_subscription_type_change"
        ,"is_subscription_tier_change"
        ,NULL AS "is_subscription_auto_renew_change"
        ,NULL AS "is_subscription_instalment_period_change"
        ,"subscription_soft_cancelled"
        ,"subscription_soft_cancel_reversed"
        ,HASH(
            "is_monthly_recurring_revenue_change"
            ,"monthly_recurring_revenue_difference"
            ,"is_discounted_monthly_recurring_revenue_change"
            ,"discounted_monthly_recurring_revenue_difference"
            ,"discount_applied"
            ,"discount_started"
            ,"discount_ended"
            ,"introductory_discount_ended"
            ,"sign_up_discount_ended"
            ,"is_subscription_type_change"
            ,"subscription_soft_cancelled"
            ,"subscription_soft_cancel_reversed"
            ,"is_subscription_tier_change"
        ) AS "subscription_charge_changes__skey"
    FROM daily_changes
    UNION ALL
    SELECT  
        "is_monthly_recurring_revenue_change"
        ,"monthly_recurring_revenue_difference"
        ,"is_discounted_monthly_recurring_revenue_change"
        ,"discounted_monthly_recurring_revenue_difference"
        ,"discount_applied"
        ,"discount_started"
        ,"discount_ended"
        ,"introductory_discount_ended"
        ,"sign_up_discount_ended"
        ,"is_subscription_type_change"
        ,"is_subscription_tier_change"
        , "is_subscription_auto_renew_change"
        , "is_subscription_instalment_period_change"
        ,"subscription_soft_cancelled"
        ,"subscription_soft_cancel_reversed"
        ,"subscription_charge_changes__skey"
    FROM daily_changes
)

SELECT DISTINCT final.* FROM final
