{{
    config(
        materialized='view',
        database='SUBSCRIPTION_DAILY_CHANGES__B2C__MART__' + snowflake_env(),
        schema='SUBSCRIPTION',
        alias='subscription_charge__dim',
        tags=['presentation-subscription-domain'],
        query_tag='presentation_subscription_domain'
    )
}}

SELECT
    *
    ,"subscription_charge__skey" as "previous_subscription_charge__skey"
FROM {{ ref('subscription_mart_charge__dim') }}
