{{
    config(
        materialized='view',
        database='SUBSCRIPTION_DAILY_CHANGES__B2C__MART__' + snowflake_env(),
        schema='SUBSCRIPTION',
        alias='subscription_campaign_mapping__dim',
        tags=['presentation-subscription-domain'],
        query_tag='presentation_subscription_domain'
    )
}}

SELECT
    "subscription_sign_up_campaign_id__skey" AS "subscription_giftcode_campaign_name__skey"
    ,*
FROM {{ ref('subscription_campaign_mapping__dim') }}
