{{
    config(
        materialized='incremental',
        unique_key='"batch_date"',
        incremental_strategy='delete+insert',
        schema='TRANSIENT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        cluster_by=['"batch_date"'],
        tags=['presentation-subscription-domain']
    )
}}

{% set subscription_mart_variables = subscription_mart_variables() %}

WITH daily_subscriptions_filtered AS (
    SELECT * FROM {{ ref('daily_subscriptions_filtered') }}
)

,changes_prep AS (
    SELECT
        CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
        ,"batch_date"
        ,"billing_account_id"
        ,"crm_account_id"
        ,"dazn_user_id"
        ,"subscription_id"
        ,"subscription_name"
        ,"subscription_product_group"

        ---------------------------------------------------------- subscription_dimension_changes ----------------------------------------------------------

        ,"billing_account__skey"
        ,LAG("billing_account__skey") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") AS "previous_billing_account__skey"
        ,CASE WHEN "billing_account__skey" != "previous_billing_account__skey" THEN TRUE ELSE FALSE END AS "is_billing_account_change"
        ,"subscription_info__skey"
        ,LAG("subscription_info__skey") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") AS "previous_subscription_info__skey"
        ,CASE WHEN "subscription_info__skey" != "previous_subscription_info__skey" THEN TRUE ELSE FALSE END AS "is_subscription_info_change"
        ,"subscription_term__skey"
        ,LAG("subscription_term__skey") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") AS "previous_subscription_term__skey"
        ,CASE WHEN "subscription_term__skey" != "previous_subscription_term__skey" THEN TRUE ELSE FALSE END AS "is_subscription_term_change"
        ,"subscription_charge__skey"
        ,LAG("subscription_charge__skey") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") AS "previous_subscription_charge__skey"
        ,CASE WHEN "subscription_charge__skey" != "previous_subscription_charge__skey" THEN TRUE ELSE FALSE END AS "is_subscription_charge_change"
        ,"subscription_free_trial__skey"
        ,LAG("subscription_free_trial__skey") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") AS "previous_subscription_free_trial__skey"
        ,CASE WHEN "subscription_free_trial__skey" != "previous_subscription_free_trial__skey" THEN TRUE ELSE FALSE END AS "is_subscription_free_trial_change"
        ,"subscription_discount__skey"
        ,LAG("subscription_discount__skey") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") AS "previous_subscription_discount__skey"
        ,CASE WHEN "subscription_discount__skey" != "previous_subscription_discount__skey" THEN TRUE ELSE FALSE END AS "is_subscription_discount_offer_change"
        ,"subscription_pause__skey"
        ,LAG("subscription_pause__skey") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") AS "previous_subscription_pause__skey"
        ,CASE WHEN "subscription_pause__skey" != "previous_subscription_pause__skey" THEN TRUE ELSE FALSE END AS "is_subscription_pause_change"
        ,"subscription_add_on__skey"
        ,LAG("subscription_add_on__skey") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") AS "previous_subscription_add_on__skey"
        ,CASE WHEN "subscription_add_on__skey" != "previous_subscription_add_on__skey" THEN TRUE ELSE FALSE END AS "is_subscription_add_on_change"
        ,HASH(
            "is_billing_account_change"
            ,"is_subscription_info_change"
            ,"is_subscription_term_change"
            ,"is_subscription_charge_change"
            ,"is_subscription_free_trial_change"
            ,"is_subscription_discount_offer_change"
            ,"is_subscription_pause_change"
            ,"is_subscription_add_on_change"
        ) AS "subscription_dimension_changes__skey"

        ---------------------------------------------------------- subscription_term_changes ----------------------------------------------------------------

        ,LAG("batch_date") OVER (PARTITION BY "billing_account_id", "subscription_product_group" ORDER BY "batch_date") AS "previous_billing_account_batch_date"
        ,LAG("subscription_name") OVER (PARTITION BY "billing_account_id", "subscription_product_group" ORDER BY "batch_date") AS "previous_billing_account_subscription_name"
        ,LEAD("batch_date") OVER (PARTITION BY "billing_account_id", "subscription_product_group" ORDER BY "batch_date") AS "next_billing_account_batch_date"
        ,LEAD("subscription_name") OVER (PARTITION BY "billing_account_id", "subscription_product_group" ORDER BY "batch_date") AS "next_billing_account_subscription_name"
        ,"batch_date" = "subscription_start_date" AS "subscription_started"
        -- Adding a last full active day flag and then FALSEing the sub_end, so that we can push the churn to the exact day of churn in the next cte below
        ,"batch_date" != DATEADD('day', -1, CURRENT_DATE) AND LEAD("batch_date") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") IS NULL AS "is_last_full_active_day"
        ,FALSE AS "subscription_ended"
        ,"is_resubscription"
        ,CASE WHEN "previous_billing_account_batch_date" = DATEADD('day', -1, "batch_date") AND "previous_billing_account_subscription_name" != "subscription_name" THEN TRUE ELSE FALSE END AS "is_immediate_resubscription"
        -- Same method as the sub_ended as this relates dircetly to that
        ,CASE WHEN "next_billing_account_batch_date" = DATEADD('day', 1, "batch_date") AND "next_billing_account_subscription_name" != "subscription_name" THEN TRUE ELSE FALSE END AS "is_immediate_resubscription_last_full_active_day"
        ,FALSE AS "is_immediate_resubscription_cancel"
        ,CASE WHEN "batch_date" = "subscription_free_trial_start_date" THEN TRUE ELSE FALSE END AS "free_trial_started"
        ,CASE WHEN "batch_date" = "subscription_free_trial_end_date" THEN TRUE ELSE FALSE END AS "free_trial_ended"
        ,"has_active_free_trial"
        ,CASE WHEN "batch_date" = "subscription_pause_start_date" THEN TRUE ELSE FALSE END AS "pause_started"
        ,CASE WHEN "batch_date" = "subscription_pause_end_date" THEN TRUE ELSE FALSE END AS "pause_ended"
        ,"is_paused"
        ,HASH(
            "subscription_started"
            ,"subscription_ended"
            ,"pause_started"
            ,"pause_ended"
            ,"free_trial_started"
            ,"free_trial_ended"
            ,"is_resubscription"
            ,"is_immediate_resubscription"
            ,"is_immediate_resubscription_cancel"
            ,"has_active_free_trial"
            ,"is_paused"
        ) AS "subscription_term_changes__skey"

        ---------------------------------------------------------- add_on_changes ---------------------------------------------------------------------------

        ,CASE WHEN "batch_date" = "subscription_add_on_effective_start_date" THEN TRUE ELSE FALSE END AS "add_on_started"
        ,CASE WHEN "batch_date" = "subscription_add_on_effective_end_date" THEN TRUE ELSE FALSE END AS "add_on_ended"
        ,HASH(
            "add_on_started"
            ,"add_on_ended"
        ) AS "add_on_change__skey"

        ---------------------------------------------------------- subscription_info_changes ----------------------------------------------------------------

        ,"subscription_tier"
        ,LAG("subscription_tier") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") AS "previous_subscription_tier"
        ,CASE WHEN "subscription_tier" != "previous_subscription_tier" THEN TRUE ELSE FALSE END AS "is_subscription_tier_change"
        ,"is_soft_cancelled"
        ,LAG("is_soft_cancelled") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") AS "previous_is_soft_cancelled"
        ,"subscription_auto_renew"
        ,LAG("subscription_auto_renew") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") AS "previous_subscription_auto_renew"
        ,CASE WHEN "subscription_auto_renew" != "previous_subscription_auto_renew" THEN TRUE ELSE FALSE END AS "is_subscription_auto_renew_change"
        ,"subscription_instalment_period"
        ,LAG("subscription_instalment_period") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") AS "previous_subscription_instalment_period"
        ,CASE WHEN "subscription_instalment_period" != "previous_subscription_instalment_period" THEN TRUE ELSE FALSE END AS "is_subscription_instalment_period_change"

        ---------------------------------------------------------- subscription_charge_changes --------------------------------------------------------------

        ,CASE WHEN IFNULL("previous_is_soft_cancelled", FALSE) = FALSE AND "is_soft_cancelled" THEN TRUE ELSE FALSE END AS "subscription_soft_cancelled"
        ,CASE WHEN "previous_is_soft_cancelled" AND "is_soft_cancelled" = FALSE THEN TRUE ELSE FALSE END AS "subscription_soft_cancel_reversed"
        ,"subscription_monthly_recurring_revenue"
        ,LAG("subscription_monthly_recurring_revenue") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") AS "previous_subscription_monthly_recurring_revenue"
        ,CASE WHEN "subscription_monthly_recurring_revenue" != "previous_subscription_monthly_recurring_revenue" THEN TRUE ELSE FALSE END AS "is_monthly_recurring_revenue_change"
        ,"subscription_monthly_recurring_revenue" - "previous_subscription_monthly_recurring_revenue" AS "monthly_recurring_revenue_difference"
        ,"discounted_monthly_recurring_revenue"
        ,LAG("discounted_monthly_recurring_revenue") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") AS "previous_discounted_monthly_recurring_revenue"
        ,CASE WHEN "discounted_monthly_recurring_revenue" != "previous_discounted_monthly_recurring_revenue" THEN TRUE ELSE FALSE END AS "is_discounted_monthly_recurring_revenue_change"
        ,"discounted_monthly_recurring_revenue" - "previous_discounted_monthly_recurring_revenue" AS "discounted_monthly_recurring_revenue_difference"
        ,CASE WHEN "discount_effective_end_date" != IFNULL(LAG("discount_effective_end_date") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date"), '2000-01-01') THEN TRUE ELSE FALSE END AS "discount_applied"
        ,CASE WHEN "batch_date" = "subscription_current_discount_start_date" THEN TRUE ELSE FALSE END AS "discount_started"
        -- Need to use LAGs here as when the discount becomes inactive it will not come through in a discount field
        ,CASE WHEN "batch_date" = LAG("subscription_current_discount_end_date") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") THEN TRUE ELSE FALSE END AS "discount_ended"
        ,CASE WHEN "is_introductory_discount" THEN TRUE ELSE FALSE END AS "is_introductory_discount"
        ,CASE WHEN LAG("is_introductory_discount") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") THEN TRUE ELSE FALSE END AS "previous_is_introductory_discount"
        ,CASE WHEN "previous_is_introductory_discount" AND "is_introductory_discount" = FALSE THEN TRUE ELSE FALSE END AS "introductory_discount_ended"
        ,CASE WHEN "is_sign_up_discount" THEN TRUE ELSE FALSE END AS "is_sign_up_discount"
        ,CASE WHEN LAG("is_sign_up_discount") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") THEN TRUE ELSE FALSE END AS "previous_is_sign_up_discount"
        ,CASE WHEN "previous_is_sign_up_discount" AND IFNULL("is_sign_up_discount", FALSE) = FALSE THEN TRUE ELSE FALSE END AS "sign_up_discount_ended"
        ,"subscription_type"
        ,LAG("subscription_type") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") AS "previous_subscription_type"
        ,CASE WHEN "subscription_type" != "previous_subscription_type" THEN TRUE ELSE FALSE END AS "is_subscription_type_change"
        ,HASH(
            "is_monthly_recurring_revenue_change"
            ,"monthly_recurring_revenue_difference"
            ,"is_discounted_monthly_recurring_revenue_change"
            ,"discounted_monthly_recurring_revenue_difference"
            ,"discount_applied"
            ,"discount_started"
            ,"discount_ended"
            -- We have two of these fields with extremely similar definitions just for the migration from old to new discount models
            ,"introductory_discount_ended"
            ,"sign_up_discount_ended"
            ,"is_subscription_type_change"
            -- Including these three info dimensions in charges as it's the only ones, so is most efficient to combine
            ,"subscription_soft_cancelled"
            ,"subscription_soft_cancel_reversed"
            ,"is_subscription_tier_change"
            ,"is_subscription_auto_renew_change"
            ,"is_subscription_instalment_period_change"
        ) AS "subscription_charge_changes__skey"

        ,"subscription_content_attribution__skey"
        ,"subscription_source_system_name_derived__skey"
        ,"subscription_tracking_id__skey"
        ,"subscription_sign_up_campaign_id__skey"
        ,"first_post_sign_up_giftcode_campaign_name__skey"
        ,"last_post_sign_up_giftcode_campaign_name__skey"
        ,"subscription_giftcode_campaign_name__skey"
        ,"subscription_daily_status__skey"
        ,LAG("subscription_daily_status__skey") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") AS "previous_subscription_daily_status__skey"
        ,"daily_usd_fx_rate__skey"
        ,"billing_account_product_groups__skey"
        ,LAG("billing_account_product_groups__skey") OVER (PARTITION BY "subscription_name" ORDER BY "batch_date") AS "previous_billing_account_product_groups__skey"

    FROM daily_subscriptions_filtered
    {% if is_incremental() %}
    -- Bring in one day more either side of the rebuild period so that so that the window functions work
    WHERE "batch_date" BETWEEN DATEADD('day', -{{ subscription_mart_variables.rebuild_days}}-1, '{{ var('batch_date') }}') AND DATEADD('day', 1, '{{ var('batch_date') }}')
    {% endif %}
    QUALIFY
        (
        "is_subscription_charge_change"
        OR
        "is_subscription_info_change"
        OR
        "is_subscription_term_change"
        OR
        "is_subscription_charge_change"
        OR
        "is_subscription_free_trial_change"
        OR
        "is_subscription_discount_offer_change"
        OR
        "is_subscription_pause_change"
        OR
        "is_subscription_add_on_change"
        OR
        "subscription_started"
        OR
        "is_last_full_active_day"
        OR
        "is_immediate_resubscription"
        OR
        "is_immediate_resubscription_last_full_active_day"
        OR
        "free_trial_ended"
        OR
        "pause_started"
        OR
        "pause_ended"
        OR
        "add_on_started"
        OR
        "add_on_ended"
        OR
        "discount_started"
        OR
        "discount_ended"
        )
)

-- Because of how we calculate changes based off the Daily Count spine
-- Subscriptions that churn on one day are not active at the end of that day
-- therefore, we can’t classify that day as churn as it does not exist.
-- This means we need to model the below cte to take the last_full_active_day
-- and add one day to the batch_date to push the sub_end flag to be the exact date the end was enacted.
,sub_ends AS (
    SELECT
        "META__DBT_INSERT_DTTS"
        -- Adding one day to the batch_date so that we can show the exact date of the sub_end (which doesn't appear in changes normally as they are not active at the end of that day)
        ,DATEADD('day', 1, "batch_date") AS "batch_date"

        ,"billing_account_id"
        ,"crm_account_id"
        ,"dazn_user_id"
        ,"subscription_id"
        ,"subscription_name"
        ,"subscription_product_group"

        ---------------------------------------------------------- subscription_dimension_changes ----------------------------------------------------------

        -- Using the current skey for all rows including the previous, to make sure there are no changes on this last sub_end day
        ,"billing_account__skey"
        ,"billing_account__skey" AS "previous_billing_account__skey"
        -- FALSEing out all change flags to make sure no changes come through that shouldn't as it's the sub_end day
        ,FALSE AS "is_billing_account_change"
        ,"subscription_info__skey"
        ,"subscription_info__skey" AS "previous_subscription_info__skey"
        ,FALSE AS "is_subscription_info_change"
        ,"subscription_term__skey"
        ,"subscription_term__skey" AS "previous_subscription_term__skey"
        ,FALSE AS "is_subscription_term_change"
        ,"subscription_charge__skey"
        ,"subscription_charge__skey" AS "previous_subscription_charge__skey"
        ,FALSE AS "is_subscription_charge_change"
        ,"subscription_free_trial__skey"
        ,"subscription_free_trial__skey" AS "previous_subscription_free_trial__skey"
        ,FALSE AS "is_subscription_free_trial_change"
        ,"subscription_discount__skey"
        ,"subscription_discount__skey" AS "previous_subscription_discount__skey"
        ,FALSE AS "is_subscription_discount_offer_change"
        ,"subscription_pause__skey"
        ,"subscription_pause__skey" AS "previous_subscription_pause__skey"
        ,FALSE AS "is_subscription_pause_change"
        ,"subscription_add_on__skey"
        ,"subscription_add_on__skey" AS "previous_subscription_add_on__skey"
        ,FALSE AS "is_subscription_add_on_change"
        ,HASH(
            -- Need to specifically FALSE these out, as if we use the aliases it will bring in the change status from the previous cte, not the FALSEing out we have done in the lines above
            FALSE
            ,FALSE
            ,FALSE
            ,FALSE
            ,FALSE
            ,FALSE
            ,FALSE
            ,FALSE
        ) AS "subscription_dimension_changes__skey"

        ---------------------------------------------------------- subscription_term_changes ----------------------------------------------------------------

        ,"previous_billing_account_batch_date"
        ,"previous_billing_account_subscription_name"
        ,"next_billing_account_batch_date"
        ,"next_billing_account_subscription_name"
        ,FALSE AS "subscription_started"
        -- FALSEing out the is_last_full_active_day as, with the addition of 1 day to the batch_date, this is not true anymore
        ,FALSE AS "is_last_full_active_day"
        -- Using the is_last_full_active_day to just be the flag directly for the sub_end, as with the addition of 1 day to the batch_date, this will be the day the sub_end takes effect
        ,changes_prep."is_last_full_active_day" AS "subscription_ended"
        ,"is_resubscription"
        ,FALSE AS "is_immediate_resubscription"
        ,FALSE AS "is_immediate_resubscription_last_full_active_day"
        ,changes_prep."is_immediate_resubscription_last_full_active_day" AS "is_immediate_resubscription_cancel"
        ,FALSE AS "free_trial_started"
        ,FALSE AS "free_trial_ended"
        ,"has_active_free_trial"
        ,FALSE AS "pause_started"
        ,FALSE AS "pause_ended"
        ,"is_paused"
        ,HASH(
            FALSE
            ,changes_prep."is_last_full_active_day"
            ,FALSE
            ,FALSE
            ,FALSE
            ,FALSE
            ,"is_resubscription"
            ,FALSE
            ,changes_prep."is_immediate_resubscription_last_full_active_day"
            ,"has_active_free_trial"
            ,"is_paused"
        ) AS "subscription_term_changes__skey"

        ---------------------------------------------------------- add_on_changes ---------------------------------------------------------------------------

        ,FALSE AS "add_on_started"
        ,FALSE AS "add_on_ended"
        ,HASH(
            FALSE
            ,FALSE
        ) AS "add_on_change__skey"

        ---------------------------------------------------------- subscription_info_changes ----------------------------------------------------------------

        ,"subscription_tier"
        ,"subscription_tier" AS "previous_subscription_tier"
        ,FALSE AS "is_subscription_tier_change"
        ,"is_soft_cancelled"
        ,"is_soft_cancelled" AS "previous_is_soft_cancelled"
        ,"subscription_auto_renew"
        ,"subscription_auto_renew" AS "previous_subscription_auto_renew"
        ,FALSE AS "is_subscription_auto_renew_change"
        ,"subscription_instalment_period"
        ,"subscription_instalment_period" AS "previous_subscription_instalment_period"
        ,FALSE AS "is_subscription_instalment_period_change"

        ---------------------------------------------------------- subscription_charge_changes --------------------------------------------------------------

        ,FALSE AS "subscription_soft_cancelled"
        ,FALSE AS "subscription_soft_cancel_reversed"
        ,"subscription_monthly_recurring_revenue"
        ,"subscription_monthly_recurring_revenue" AS "previous_subscription_monthly_recurring_revenue"
        ,FALSE AS "is_monthly_recurring_revenue_change"
        ,0 AS "monthly_recurring_revenue_difference"
        ,"discounted_monthly_recurring_revenue"
        ,"discounted_monthly_recurring_revenue" AS "previous_discounted_monthly_recurring_revenue"
        ,FALSE AS "is_discounted_monthly_recurring_revenue_change"
        ,0 AS "discounted_monthly_recurring_revenue_difference"
        ,FALSE AS "discount_applied"
        ,FALSE AS "discount_started"
        ,FALSE AS "discount_ended"
        ,"is_introductory_discount"
        ,"is_introductory_discount" AS "previous_is_introductory_discount"
        ,FALSE AS "introductory_discount_ended"
        ,"is_sign_up_discount"
        ,"is_sign_up_discount" AS "previous_is_sign_up_discount"
        ,FALSE AS "sign_up_discount_ended"
        ,"subscription_type"
        ,"subscription_type" AS "previous_subscription_type"
        ,FALSE "is_subscription_type_change"
        ,HASH(
            FALSE
            ,0
            ,FALSE
            ,0
            ,FALSE
            ,FALSE
            ,FALSE
            ,FALSE
            ,FALSE
            ,FALSE
            ,FALSE
            ,FALSE
            ,FALSE
            ,FALSE
            ,FALSE
        ) AS "subscription_charge_changes__skey"

        ,"subscription_content_attribution__skey"

        ,"subscription_source_system_name_derived__skey"
        ,"subscription_tracking_id__skey"
        ,"subscription_sign_up_campaign_id__skey"
        ,"first_post_sign_up_giftcode_campaign_name__skey"
        ,"last_post_sign_up_giftcode_campaign_name__skey"
        ,"subscription_giftcode_campaign_name__skey"
        ,"subscription_daily_status__skey"
        ,"previous_subscription_daily_status__skey"
        ,"daily_usd_fx_rate__skey"
        ,"billing_account_product_groups__skey"
        ,"previous_billing_account_product_groups__skey"

    FROM changes_prep
    -- Filtering for only the last full active days, so that we're only amending the sub_ends
    WHERE changes_prep."is_last_full_active_day" = TRUE
)

(SELECT * FROM changes_prep
{% if is_incremental() %}
WHERE "batch_date" BETWEEN DATEADD('day', -{{ subscription_mart_variables.rebuild_days}}, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
{% endif %})
UNION ALL
(SELECT * FROM sub_ends
{% if is_incremental() %}
WHERE "batch_date" BETWEEN DATEADD('day', -{{ subscription_mart_variables.rebuild_days}}, '{{ var('batch_date') }}') AND '{{ var('batch_date') }}'
{% endif %})
