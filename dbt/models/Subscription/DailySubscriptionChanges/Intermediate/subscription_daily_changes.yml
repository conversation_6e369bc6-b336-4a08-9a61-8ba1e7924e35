version: 2

models:
  - name: subscription_daily_changes
    description: "Staging table to generate the insert into daily_sub_change_fact"
    columns:
      - name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the model"
        quote: true

      - name: batch_date
        description: "The date of the change in the subscription"
        quote: true

      - name: billing_account_id
        description: "The Zuora Account ID coming from the Zuora Account entity E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: crm_account_id
        description: "The Salesforce Account ID coming from Zuora Account entity E.g 0011o00001m6OyEAAU"
        quote: true

      - name: dazn_user_id
        description: "The DAZN User Id of the Account E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: subscription_id
        description: "The ID of the Subscription, Subscription Names can have multiple IDs relating to different types of changes"
        quote: true

      - name: subscription_name
        description: "The Name of the Subscription E.g. A-S15283950"
        quote: true

      - name: subscription_product_group
        description: "Can be either 'NFL' whereas it is an NFL subscription, or 'DAZN' otherwise."
        quote: true

      - name: billing_account__skey
        description: "The surrogate key (skey) used to join on the billing_account dim"
        quote: true

      - name: previous_billing_account__skey
        description: "The previous state of billing account skey, before a change was applied to this subscription"
        quote: true

      - name: is_billing_account_change
        description: "Boolean flag, true if the billing account skey changed"
        quote: true

      - name: subscription_info__skey
        description: "The hashed reference to subscription info"
        quote: true

      - name: previous_subscription_info__skey
        description: "The previous state of subscription info skey, before a change was applied to this subscription"
        quote: true

      - name: is_subscription_info_change
        description: "Boolean flag, true if the subscription info skey changed"
        quote: true

      - name: subscription_term__skey
        description: "The hashed reference to subscription term"
        quote: true

      - name: previous_subscription_term__skey
        description: "The previous state of subscription term skey, before a change was applied to this subscription"
        quote: true

      - name: is_subscription_term_change
        description: "Boolean flag, true if the subscription term skey changed"
        quote: true

      - name: subscription_charge__skey
        description: "The hashed reference to subscription charge"
        quote: true

      - name: previous_subscription_charge__skey
        description: "The previous state of subscription charge skey, before a change was applied to this subscription"
        quote: true

      - name: is_subscription_charge_change
        description: "Boolean flag, true if the subscription charge skey changed"
        quote: true

      - name: subscription_free_trial__skey
        description: "The hashed reference to subscription free trial"
        quote: true

      - name: previous_subscription_free_trial__skey
        description: "The previous state of subscription free trial skey, before a change was applied to this subscription"
        quote: true

      - name: is_subscription_free_trial_change
        description: "Boolean flag, true if the subscription free trial skey changed"
        quote: true

      - name: subscription_discount__skey
        description: "The hashed reference to subscription discount"
        quote: true

      - name: previous_subscription_discount__skey
        description: "The previous state of subscription discount skey, before a change was applied to this subscription"
        quote: true

      - name: is_subscription_discount_offer_change
        description: "Boolean flag, true if the subscription discount skey changed"
        quote: true

      - name: subscription_pause__skey
        description: "The hashed reference to subscription pause"
        quote: true

      - name: previous_subscription_pause__skey
        description: "The previous state of subscription pause skey, before a change was applied to this subscription"
        quote: true

      - name: is_subscription_pause_change
        description: "Boolean flag, true if the subscription pause skey changed"
        quote: true

      - name: subscription_add_on__skey
        description: "The hashed reference to subscription AddOn"
        quote: true

      - name: previous_subscription_add_on__skey
        description: "The previous state of subscription AddOn skey, before a change was applied to this subscription"
        quote: true

      - name: is_subscription_add_on_change
        description: "Boolean flag, true if the subscription AddOn skey changed"
        quote: true

      - name: subscription_dimension_changes__skey
        description: "Hashed reference to all rows of the subscription dimension changes table"
        quote: true

      - name: previous_billing_account_batch_date
        description: "The previous batch_date that is active for that billing_account, used to derive immediate resubscriptions"
        quote: true

      - name: previous_billing_account_subscription_name
        description: "The previous subscription_name that is active for that billing_account, used to derive immediate resubscriptions"
        quote: true

      - name: next_billing_account_batch_date
        description: "The next batch_date that is active for that billing_account, used to derive immediate resubscriptions"
        quote: true

      - name: next_billing_account_subscription_name
        description: "The next subscription_name that is active for that billing_account, used to derive immediate resubscriptions"
        quote: true

      - name: subscription_started
        description: "Boolean flag, true if the subscription has started"
        quote: true

      - name: is_last_full_active_day
        description: "Boolean flag, true if it's the last full active day of the subscription"
        quote: true

      - name: subscription_ended
        description: "Boolean flag, true if it's the exact day that the subscription has ended at some point in that day"
        quote: true

      - name: is_resubscription
        description: "Identifies if this is a resubscription for this product group or not"
        quote: true

      - name: is_immediate_resubscription
        description: "Boolean flag, true if the subscription has started but had a different subscription name active only yesterday, and therefore has been immediately resubscribed"
        quote: true

      - name: is_immediate_resubscription_last_full_active_day
        description: "Boolean flag, true if it's the last full active day of a subscription that has ended but has a different subscription name becoming active the very next day, and therefore has been immediately resubscribed"
        quote: true

      - name: is_immediate_resubscription_cancel
        description: "Boolean flag, true if it's the exact day the subscription has ended (at some point within that day) but has a different subscription name becoming active the very next day, and therefore has been immediately resubscribed"
        quote: true

      - name: free_trial_started
        description: "Boolean flag, true if the free trial has started"
        quote: true

      - name: free_trial_ended
        description: "Boolean flag, true if the free trial has ended"
        quote: true

      - name: has_active_free_trial
        description: "Boolean flag, true if the subscription has an active free trial on the exact date_day of the spine"
        quote: true

      - name: pause_started
        description: "Boolean flag, true if date day is equal to subscription pause start date"
        quote: true

      - name: pause_ended
        description: "Boolean flag, true if date day is equal to subscription pause end date"
        quote: true

      - name: is_paused
        description: "Boolean flag, true if the subscription is paused on the exact date_day of the spine"
        quote: true

      - name: subscription_term_changes__skey
        description: "Hashed reference to all rows of the subscription term changes table"
        quote: true

      - name: add_on_started
        description: "Boolean flag, true if date day is equal to subscription AddOn effective start date"
        quote: true

      - name: add_on_ended
        description: "Boolean flag, true if date day is equal to subscription AddOn effective end date"
        quote: true

      - name: add_on_change__skey
        description: "Hashed reference to all rows of the AddOn change table"
        quote: true

      - name: subscription_tier
        description: "The Entitlement Set Id of the Rateplan, which is filtered to the associated RatePlanCharge's Flat Fee Pricing Model and Recurring Type, so producing the Tier for the Subscription"
        quote: true

      - name: previous_subscription_tier
        description: "The previous state of subscription tier, before a change was applied to this subscription"
        quote: true

      - name: is_subscription_tier_change
        description: "Boolean flag, true if the subscription tier changed"
        quote: true

      - name: is_soft_cancelled
        description: "Boolean flag, true if the subscription is soft cancelled"
        quote: true

      - name: previous_is_soft_cancelled
        description: "The previous state of the is soft cancelled field"
        quote: true

      - name: subscription_auto_renew
        description: "Boolean flag, true if the subscription is auto renew"
        quote: true

      - name: previous_subscription_auto_renew
        description: "The previous state of the is subscription is auto renew field"
        quote: true

      - name: is_subscription_auto_renew_change
        description: "Boolean flag, true if the subscription auto renew changed"
        quote: true

      - name: subscription_instalment_period
        description: TBC"
        quote: true

      - name: previous_subscription_instalment_period
        description: "The previous state of the is subscription instalment period field"
        quote: true

      - name: is_subscription_instalment_period_change
        description: "Boolean flag, true if the subscription instalment period changed"
        quote: true

      - name: subscription_soft_cancelled
        description: "Boolean flag, true if there was a soft cancel to this subscription"
        quote: true

      - name: subscription_soft_cancel_reversed
        description: "Boolean flag, true if the soft cancel to this subscription has been reversed"
        quote: true

      - name: subscription_monthly_recurring_revenue
        description: "The monthly recurring revenue, which is the amount the customer pays for their subscription ignoring Discounts and PPV, adjusting the subscription length down to one month and rounded to two decimal places"
        quote: true

      - name: previous_subscription_monthly_recurring_revenue
        description: "The previous state of subscription monthly recurring revenue, before a change was applied to this subscription"
        quote: true

      - name: is_monthly_recurring_revenue_change
        description: "Boolean flag, true if there was a change by comparing the state of each subscription monthly recurring revenue to it's previous state"
        quote: true

      - name: monthly_recurring_revenue_difference
        description: "The difference between the current monthly recurring revenue and the previous one"
        quote: true

      - name: discounted_monthly_recurring_revenue
        description: "The discounted monthly recurring revenue"
        quote: true

      - name: previous_discounted_monthly_recurring_revenue
        description: "The previous state of discounted monthly recurring revenue, before a change was applied to this subscription"
        quote: true

      - name: is_discounted_monthly_recurring_revenue_change
        description: "Boolean flag, true if there was a change by comparing the state of each subscription discount monthly recurring revenue to it's previous state"
        quote: true

      - name: discounted_monthly_recurring_revenue_difference
        description: "The difference between the current discounted monthly recurring revenue and the previous one"
        quote: true

      - name: discount_applied
        description: "Boolean flag, true if a discount has been applied to the subscription"
        quote: true

      - name: discount_started
        description: "Boolean flag, true if the discount applied to the subscription has started"
        quote: true

      - name: discount_ended
        description: "Boolean flag, true if the discount applied to the subscription has ended"
        quote: true

      - name: is_introductory_discount
        description: "Boolean flag, true if this is an introductory discount"
        quote: true

      - name: previous_is_introductory_discount
        description: "The previous state of is introductory discount skey, before a change was applied to this subscription"
        quote: true

      - name: introductory_discount_ended
        description: "Boolean flag, true if this is an introductory discount"
        quote: true

      - name: is_sign_up_discount
        description: "Boolean flag, true if this is a sign-up discount"
        quote: true

      - name: previous_is_sign_up_discount
        description: "The previous state of is_sign_up_discount, before a change was applied to this subscription"
        quote: true

      - name: sign_up_discount_ended
        description: "Boolean flag, true if the sign-up discount has ended"
        quote: true

      - name: subscription_type
        description: "A derived field from the term type and the billing period, or external giftcodes, to categorise the subscription type, can be Monthly, Annual, Instalment or Externally Billed"
        quote: true

      - name: previous_subscription_type
        description: "The previous state of subscription type, before a change was applied to this subscription"
        quote: true

      - name: is_subscription_type_change
        description: "Boolean flag, true if the subscription type changed"
        quote: true

      - name: subscription_charge_changes__skey
        description: "Hashed reference to all rows of the subscription charge changes table"
        quote: true

      - name: subscription_content_attribution__skey
        description: "The surrogate key (skey) used to join on the subscription_content_attribution__dim"
        quote: true

      - name: subscription_source_system_name_derived__skey
        description: "The surrogate key (skey) used to join on the sub_source_system dim"
        quote: true

      - name: subscription_tracking_id__skey
        description: "The surrogate key (skey) used to join on the tracking_id dim"
        quote: true

      - name: subscription_sign_up_campaign_id__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: first_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: last_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: subscription_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: subscription_daily_status__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_daily_status__dim"
        quote: true

      - name: previous_subscription_daily_status__skey
        description: "The previous state of subscription daily status skey, before a change was applied to this subscription"
        quote: true

      - name: daily_usd_fx_rate__skey
        description: "The surrogate key (skey) used to join on the daily_usd_fx_rate dim"
        quote: true

      - name: billing_account_product_groups__skey
        description: "The surrogate key (skey) used to join on the billing_account_product_groups__dim"
        quote: true

      - name: previous_billing_account_product_groups__skey
        description: "The previous state of the billing_account_product_groups__skey, before a change was applied to this subscription"
        quote: true

  - name: subscription_daily_changes_intermediate__fact
    description: "An intermediate table for the Subscription Daily Changes Mart that shows changes at a subscription level with all skeys and change flags"
    columns:
      - name: META__DBT_INSERT_DTTS
        description: "The datetime these rows were inserted into this table marked by the start of the last query in the model"
        quote: true

      - name: batch_date
        description: "The date of the change in the subscription"
        quote: true

      - name: subscription_id
        description: "The ID of the Subscription, Subscription Names can have multiple IDs relating to different types of changes"
        quote: true

      - name: subscription_name
        description: "The Name of the Subscription E.g. A-S15283950"
        quote: true

      - name: billing_account_id
        description: "The Zuora Account ID coming from the Zuora Account entity E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: crm_account_id
        description: "The Salesforce Account ID coming from Zuora Account entity E.g 0011o00001m6OyEAAU"
        quote: true

      - name: dazn_user_id
        description: "The DAZN User Id of the Account E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: subscription_product_group
        description: "Can be either 'NFL' whereas it is an NFL subscription, or 'DAZN' otherwise."
        quote: true

      - name: billing_account__skey
        description: "The surrogate key (skey) used to join on the billing_account dim"
        quote: true

      - name: previous_billing_account__skey
        description: "The previous state of billing account skey, before a change was applied to this subscription"
        quote: true

      - name: is_billing_account_change
        description: "Boolean flag, true if the billing account skey changed"
        quote: true

      - name: subscription_info__skey
        description: "The hashed reference to subscription info"
        quote: true

      - name: previous_subscription_info__skey
        description: "The previous state of subscription info skey, before a change was applied to this subscription"
        quote: true

      - name: is_subscription_info_change
        description: "Boolean flag, true if the subscription info skey changed"
        quote: true

      - name: subscription_term__skey
        description: "The hashed reference to subscription term"
        quote: true

      - name: previous_subscription_term__skey
        description: "The previous state of subscription term skey, before a change was applied to this subscription"
        quote: true

      - name: is_subscription_term_change
        description: "Boolean flag, true if the subscription term skey changed"
        quote: true

      - name: subscription_charge__skey
        description: "The hashed reference to subscription charge"
        quote: true

      - name: previous_subscription_charge__skey
        description: "The previous state of subscription charge skey, before a change was applied to this subscription"
        quote: true

      - name: is_subscription_charge_change
        description: "Boolean flag, true if the subscription charge skey changed"
        quote: true

      - name: subscription_free_trial__skey
        description: "The hashed reference to subscription free trial"
        quote: true

      - name: previous_subscription_free_trial__skey
        description: "The previous state of subscription free trial skey, before a change was applied to this subscription"
        quote: true

      - name: is_subscription_free_trial_change
        description: "Boolean flag, true if the subscription free trial skey changed"
        quote: true

      - name: subscription_discount__skey
        description: "The hashed reference to subscription discount"
        quote: true

      - name: previous_subscription_discount__skey
        description: "The previous state of subscription discount skey, before a change was applied to this subscription"
        quote: true

      - name: is_subscription_discount_offer_change
        description: "Boolean flag, true if the subscription discount skey changed"
        quote: true

      - name: subscription_pause__skey
        description: "The hashed reference to subscription pause"
        quote: true

      - name: previous_subscription_pause__skey
        description: "The previous state of subscription pause skey, before a change was applied to this subscription"
        quote: true

      - name: is_subscription_pause_change
        description: "Boolean flag, true if the subscription pause skey changed"
        quote: true

      - name: subscription_add_on__skey
        description: "The hashed reference to subscription AddOn"
        quote: true

      - name: previous_subscription_add_on__skey
        description: "The previous state of subscription AddOn skey, before a change was applied to this subscription"
        quote: true

      - name: is_subscription_add_on_change
        description: "Boolean flag, true if the subscription AddOn skey changed"
        quote: true

      - name: subscription_dimension_changes__skey
        description: "Hashed reference to all rows of the subscription dimension changes table"
        quote: true

      - name: subscription_started
        description: "Boolean flag, true if the subscription has started"
        quote: true

      - name: subscription_ended
        description: "Boolean flag, true if the subscription has ended"
        quote: true

      - name: pause_started
        description: "Boolean flag, true if date day is equal to subscription pause start date"
        quote: true

      - name: pause_ended
        description: "Boolean flag, true if date day is equal to subscription pause end date"
        quote: true

      - name: free_trial_started
        description: "Boolean flag, true if the free trial has started"
        quote: true

      - name: free_trial_ended
        description: "Boolean flag, true if the free trial has ended"
        quote: true

      - name: is_resubscription
        description: "Identifies if this is a resubscription for this product group or not"
        quote: true

      - name: is_immediate_resubscription
        description: "Boolean flag, true if the subscription has started but had a different subscription name active only yesterday, and therefore has been immediately resubscribed"
        quote: true

      - name: is_immediate_resubscription_cancel
        description: "Boolean flag, true if the subscription has ended but has a different subscription name becoming active the very next day, and therefore has been immediately resubscribed"
        quote: true

      - name: has_active_free_trial
        description: "Boolean flag, true if the subscription has an active free trial on the exact date_day of the spine"
        quote: true

      - name: subscription_term_changes__skey
        description: "Hashed reference to all rows of the subscription term changes table"
        quote: true

      - name: add_on_started
        description: "Boolean flag, true if date day is equal to subscription AddOn effective start date"
        quote: true

      - name: add_on_ended
        description: "Boolean flag, true if date day is equal to subscription AddOn effective end date"
        quote: true

      - name: add_on_change__skey
        description: "Hashed reference to all rows of the AddOn change table"
        quote: true

      - name: is_discounted_monthly_recurring_revenue_change
        description: "Boolean flag, true if there was a change by comparing the state of each subscription discount monthly recurring revenue to it's previous state"
        quote: true

      - name: discount_applied
        description: "Boolean flag, true if a discount has been applied to the subscription"
        quote: true

      - name: discount_started
        description: "Boolean flag, true if the discount applied to the subscription has started"
        quote: true

      - name: discount_ended
        description: "Boolean flag, true if the discount applied to the subscription has ended"
        quote: true

      - name: is_introductory_discount
        description: "Boolean flag, true if this is an introductory discount"
        quote: true

      - name: introductory_discount_ended
        description: "Boolean flag, true if this is an introductory discount"
        quote: true

      - name: is_subscription_type_change
        description: "Boolean flag, true if the subscription type changed"
        quote: true

      - name: subscription_soft_cancelled
        description: "Boolean flag, true if there was a soft cancel to this subscription"
        quote: true

      - name: subscription_soft_cancel_reversed
        description: "Boolean flag, true if the soft cancel to this subscription has been reversed"
        quote: true

      - name: is_subscription_tier_change
        description: "Boolean flag, true if the subscription tier changed"
        quote: true

      - name: is_subscription_auto_renew_change
        description: "Boolean flag, true if the subscription autorenew changed"
        quote: true

      - name: is_subscription_instalment_period_change
        description: "Boolean flag, true if the subscription instalment period changed"
        quote: true

      - name: subscription_charge_changes__skey
        description: "Hashed reference to all rows of the subscription charge changes table"
        quote: true

      - name: subscription_content_attribution__skey
        description: "The surrogate key (skey) used to join on the subscription_content_attribution__dim"
        quote: true

      - name: subscription_source_system_name_derived__skey
        description: "The surrogate key (skey) used to join on the sub_source_system dim"
        quote: true

      - name: subscription_tracking_id__skey
        description: "The surrogate key (skey) used to join on the tracking_id dim"
        quote: true

      - name: subscription_sign_up_campaign_id__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: first_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: last_post_sign_up_giftcode_campaign_name__skey
        description: "The surrogate key (skey) used to join on the subscription_campaign dim"
        quote: true

      - name: subscription_daily_status__skey
        description: "The surrogate key (skey) used to join on the subscription_mart_daily_status__dim"
        quote: true

      - name: previous_subscription_daily_status__skey
        description: "The previous state of subscription daily status skey, before a change was applied to this subscription"
        quote: true

      - name: daily_usd_fx_rate__skey
        description: "The surrogate key (skey) used to join on the daily_usd_fx_rate dim"
        quote: true

      - name: billing_account_product_groups__skey
        description: "The surrogate key (skey) used to join on the billing_account_product_groups__dim"
        quote: true

      - name: previous_billing_account_product_groups__skey
        description: "The previous state of the billing_account_product_groups__skey, before a change was applied to this subscription"
        quote: true
