{{
    config(
        materialized='table',
        schema='PRESENTATION'
    )
}}

--        tags=['presentation-subscription-domain']--removed from dag-21-May-2025 
--         This is suspended becuase the pass data is shifted to the subscription models 

WITH purchase AS (
    SELECT * FROM {{ ref("staging__dmp_billing_events") }}
    WHERE TRUE
        AND "product_type" = 'PASS' 
        AND "message_type" = 'BILLING_PRODUCT_PURCHASE'
        AND "event_status" = 'SUCCEEDED'
   QUALIFY ROW_NUMBER() OVER (PARTITION BY "billing_product_id" ORDER BY "event_timestamp" DESC)=1
)

,cancelled AS (
SELECT * FROM {{ ref("staging__dmp_billing_events") }}
    WHERE TRUE
    AND "product_type" = 'PASS' 
    AND "message_type" = 'BILLING_PRODUCT_CANCELATION'
    AND "event_status" = 'SUCCEEDED'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "billing_product_id" ORDER BY "event_timestamp" DESC)=1
)

,country AS (
  SELECT
        "join_key"
        ,max("country")  AS "country"
    FROM {{ ref("region_dimension") }}
   GROUP BY 1
)

,account_mapping AS (
    SELECT * FROM {{ ref("staging__account_current") }}
)

SELECT 
    purchase."billing_product_id" AS "purchase_id"
    ,DATE_TRUNC('second', purchase."event_timestamp"::timestamp) AS "purchase_timestamp"
    ,purchase."dazn_id" AS "dazn_id"
    ,COALESCE(account_mapping."billing_account_id",purchase."dazn_id") AS "billing_account_id"
    ,purchase."catalog_product_id" AS "catalog_product_id"
    ,purchase."entitlement_set_id" AS "entitlement_set_id"
    ,purchase."source_system_derived" AS "source_system"
    ,purchase."tracking_id_derived" AS "tracking_id"
    ,purchase."is_3pp_exit" as "is_3pp_exit"
    ,purchase."product_group" AS "product_group"
    --,billing_events."billing_country" AS "billing_country"
    ,country."country" AS "country"
    ,purchase."validity_period_unit" AS "validity_period_unit"
    ,purchase."validity_period_duration" AS "validity_period_duration"
    ,to_timestamp(purchase."validity_start_date") AS "start_date"
    --,billing_events.validity_end_date AS "v_end_date"
    ,COALESCE(cancelled."validity_end_date",purchase."validity_end_date") AS "end_date"
  --  ,billing_events.message_type AS "purchase_type"
    ,INITCAP(COALESCE(cancelled."product_status",purchase."product_status")) AS "status"
    ,purchase."currency" AS "currency"
    --,billing_events.gross_price AS "gross_price"
    ,purchase."charge_amount" AS "charge_amount"
    ,COALESCE(cancelled."churn_type",purchase."churn_type") AS "churn_type"
FROM purchase
LEFT JOIN cancelled USING ("billing_product_id")
LEFT JOIN country ON purchase."billing_country"=country."join_key"
LEFT JOIN account_mapping ON purchase."dazn_id"=account_mapping."dazn_user_id"
