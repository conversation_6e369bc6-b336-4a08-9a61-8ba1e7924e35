version: 2

models:
  - name: fct_pass_purchases
    description: "TBC"
    columns:
      - name: purchase_id
        description: "purchase id"
        quote: true

      - name: purchase_timestamp
        description: "purchase timestamp"
        quote: true

      - name: dazn_id
        description: "DAZN user ID which is used to link purchases to user entitlements"
        quote: true

      - name: billing_account_id
        description: "Account ID"
        quote: true

      - name: catalog_product_id
        description: "TBC"
        quote: true

      - name: entitlement_set_id
        description: "Entitlement Set ID"
        quote: true

      - name: source_system
        description: "Entitlement Set ID"
        quote: true

      - name: tracking_id
        description: "Front End Partnerships Tracking ID usually for use in commercial partnership tracking"
        quote: true

      - name: is_3pp_exit
        description: "TBC"
        quote: true

      - name: product_group
        description: "TBC"
        quote: true

      - name: country
        description: "the billing country for the linked subscription"
        quote: true

      - name: validity_period_unit
        description: "TBC"
        quote: true

      - name: validity_period_duration
        description: "TBC"
        quote: true

      - name: start_date
        description: "TBC"
        quote: true

      - name: end_date
        description: "TBC"
        quote: true

      - name: status
        description: "TBC"
        quote: true

      - name: currency
        description: "TBC"
        quote: true

      - name: charge_amount
        description: "TBC"
        quote: true

      - name: churn_type
        description: "TBC"
        quote: true
