version: 2

models:
  - name: staging__fanatics__order_headers_current
    description: "Orders information of merchandise purchased by customers"
    columns:
      - name: batch_date
        description: "Date of Data Source"
        quote: true

      - name: order_id
        description: "Unique Order ID, for each Order"
        quote: true

      - name: dazn_user_id
        description: "Unique Customer ID, for each Customer"
        quote: true

      - name: order_date
        description: "Date of Order"
        quote: true

      - name: despatched_date
        description: "Date of Order Despatched"
        quote: true

      - name: transaction_type
        description: "Type of the Transaction"
        quote: true

      - name: sales_channel
        description: "Device through which the order made"
        quote: true

      - name: goods_net_gbp
        description: "tbc"
        quote: true

      - name: goods_vat_gbp
        description: "tbc"
        quote: true

      - name: shipping_net_gbp
        description: "tbc"
        quote: true

      - name: shipping_vat_gbp
        description: "tbc"
        quote: true

      - name: currency
        description: "currency used to make order"
        quote: true

      - name: conversion_rate
        description: "percent of total visitors who place an order"
        quote: true

      - name: shipping_country
        description: "country to which the order has to be shipped"
        quote: true

      - name: reason
        description: "tbc"
        quote: true

      - name: payment_type
        description: "Mode of Payment"
        quote: true

      - name: promo_code
        description: "Source System of the Data Source"
        quote: true

  - name: staging__fanatics__order_details_current
    description: "Order Detail information of merchandise purchased by customers"
    columns:
      - name: batch_date
        description: "Gives the path of the file where data is stored"
        quote: true

      - name: order_id
        description: "Unique Order ID, for each Order"
        quote: true

      - name: line_number
        description: "tbc"
        quote: true

      - name: product_id
        description: "gives the id of the product purchased"
        quote: true

      - name: variation_id
        description: "tbc"
        quote: true

      - name: unit_price_gbp
        description: "tbc"
        quote: true

      - name: unit_vat_gbp
        description: "tbc"
        quote: true

      - name: quantity
        description: "tbc"
        quote: true

  - name: staging__fanatics__product_feed_current
    description: "Product information of merchandise"
    columns:
      - name: batch_date
        description: "Date of Data Source"
        quote: true

      - name: product_id
        description: "id of the product purchased"
        quote: true

      - name: item_id
        description: "id of the product purchased"
        quote: true

      - name: product_name
        description: "name of the item purchased"
        quote: true

      - name: product_size
        description: "size of the item purchased"
        quote: true

      - name: product_brand
        description: "brand of the item purchased"
        quote: true

      - name: merchclass_level_1
        description: "type of the item purchased"
        quote: true

      - name: merchclass_level_2
        description: "type of the item purchased"
        quote: true

      - name: merchclass_level_3
        description: "type of the item purchased"
        quote: true

      - name: merchclass_level_4
        description: "type of the item purchased"
        quote: true

      - name: merchclass_level_5
        description: "type of the item purchased"
        quote: true

      - name: merchclass_level_6
        description: "type of the item purchased"
        quote: true

      - name: color_style
        description: "type of the item purchased"
        quote: true

      - name: team
        description: "item of the team purchased"
        quote: true

      - name: team_list
        description: "item of the teams purchased"
        quote: true

      - name: color
        description: "colour of the item purchased"
        quote: true

      - name: gender_age_group
        description: "age group of the customer who purchased"
        quote: true

      - name: vendor
        description: "details of the vendor"
        quote: true

      - name: product_url
        description: "URL for the product "
        quote: true

      - name: retail_price
        description: "retail price of the product"
        quote: true

      - name: sale_price
        description: "Sale price of the product"
        quote: true

      - name: player_name
        description: "name of the player"
        quote: true

      - name: vendor_product_code
        description: "vendor product code"
        quote: true

      - name: = vendor_item_code
        description: "vendor item code"
        quote: true

      - name: upc
        description: "tbc"
        quote: true

      - name: gender
        description: "Gender of the customer"
        quote: true

      - name: qty_on_hand
        description: "tbc"
        quote: true

  - name: staging__fanatics__customers_current
    description: "customer related information"
    columns:
      - name: batch_date
        description: "Date of Data Source"
        quote: true

      - name: customer_id
        description: "Unique Customer ID, for each Customer"
        quote: true

      - name: country
        description: "Country name"
        quote: true

      - name: language
        description: "Language code of the display value to customer"
        quote: true

      - name: currency
        description: "currency used to make order"
        quote: true

      - name: registration_timestamp
        description: "Date of Registration"
        quote: true

      - name: club_username
        description: "TBC"
        quote: true

      - name: archived
        description: "TBC"
        quote: true

      - name: newsletter_account
        description: "TBC"
        quote: true

      - name: is_guest
        description: "TBC"
        quote: true

      - name: retail_phone
        description: "TBC"
        quote: true

      - name: retail_email
        description: "TBC"
        quote: true

      - name: retail_sms
        description: "TBC"
        quote: true

      - name: retail_post
        description: "TBC"
        quote: true

      - name: 3rd_party_phone
        description: "TBC"
        quote: true

      - name: 3rd_party_email
        description: "TBC"
        quote: true

      - name: 3rd_party_sms
        description: "TBC"
        quote: true

      - name: 3rd_party_post
        description: "TBC"
        quote: true

      - name: club_phone
        description: "TBC"
        quote: true

      - name: club_email
        description: "TBC"
        quote: true

      - name: club_sms
        description: "TBC"
        quote: true

      - name: club_post
        description: "TBC"
        quote: true

      - name: change_date
        description: "TBC"
        quote: true
