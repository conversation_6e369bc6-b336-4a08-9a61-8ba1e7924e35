WITH prail_served AS (
    SELECT * FROM {{ source('CURATED__AMAZON_PERSONALIZE', 'CURATED__AMAZON_PERSONALIZE__PRAIL_SERVED_RECS_CURRENT') }}
)

,prail_served_recs AS (
    SELECT
        "BATCH_DATE" AS "batch_date",
        "userId" AS "dazn_user_id", 
        "userAgent" AS "user_agent",
        "date" AS "personalization_date",
        "articleId" AS "article_id",
        "fixtureId" AS "fixture_id",
        "index" AS "personalization_order",
        "railId" AS "rail_id",
        "recommendationId" AS "recommendation_id",
        "fromCache" AS "from_cache",
        "language",
        "country" 
    FROM prail_served
)

    SELECT * FROM prail_served_recs
