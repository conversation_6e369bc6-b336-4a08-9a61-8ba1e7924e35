version: 2

models:
  - name: staging__hyper_personalisation__dds_what_cluster_v3_latest
    description: "Gives the sports related preferences of the users"
    columns:
      - name: model_run_date
        description: "date the model was run."
        quote: true

      - name: dazn_user_id
        description: "This uniquely identifies each user within the DAZN platform."
        quote: true

      - name: sport_name_1
        description: "Primary favourite sport of the customer."
        quote: true

      - name: sport_name_2
        description: "Secondary favourite sport of the customer."
        quote: true

      - name: sport_name_3
        description: "Third favourite sport of the customer."
        quote: true

      - name: sport_name_4
        description: "Fourth favourite sport of the customer."
        quote: true

      - name: sport_name_5
        description: "Fifth favourite sport of the customer."
        quote: true

      - name: comp_name_1
        description: "Primary favourite competition of the customer."
        quote: true

      - name: comp_name_2
        description: "Secondary favourite competition of the customer."
        quote: true

      - name: comp_name_3
        description: "Third favourite competition of the customer."
        quote: true

      - name: comp_name_4
        description: "Fourth favourite competition of the customer."
        quote: true

      - name: comp_name_5
        description: "Fifth favourite competition of the customer."
        quote: true

      - name: team_name_1
        description: "Primary favourite team of the customer."
        quote: true

      - name: team_name_2
        description: "econdary favourite team of the customer."
        quote: true

      - name: team_name_3
        description: "Third favourite team of the customer."
        quote: true

      - name: team_name_4
        description: "Fourth favourite team of the customer."
        quote: true

      - name: team_name_5
        description: "Fifth favourite team of the customer."
        quote: true

      - name: playback_duration_in_mins
        description: "time the customer viewed content"
        quote: true

      - name: change_in_preference
        description: "gives the change in preference"
        quote: true

  - name: staging__hyper_personalisation__others_like_you
    columns:
      - name: source_updated_timestamp
        description: "The dds build timestamp."
        quote: true

      - name: dazn_user_id
        description: "This uniquely identifies each user within the DAZN platform."
        quote: true

      - name: recommended_fixture
        description: "This gives recommended fixture."
        quote: true

      - name: fixture_name
        description: "This gives the fixture name."
        quote: true

      - name: territory
        description: "This gives the territory."
        quote: true

      - name: competition_id
        description: "This gives the competition id."
        quote: true

      - name: competition_name
        description: "This gives the competition name."
        quote: true

      - name: sport_id
        description: "This gives the sport id."
        quote: true

      - name: sport_name
        description: "This gives the sport name."
        quote: true

      - name: is_freemium
        description: "This gives the freemium flag."
        quote: true

      - name: META__DBT_INSERT_DTTS
        description: "The insert time by dbt."
        quote: true
