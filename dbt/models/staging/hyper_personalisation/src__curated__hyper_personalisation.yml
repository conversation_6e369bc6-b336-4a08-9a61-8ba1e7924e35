version: 2

sources:
  - name: CURATED_HYPER_PERSONALISATION
    database: TRANSFORMATION_{{ 'UAT' if env_var('AIRFLOW_VAR_ENV') == 'stage' else env_var('AIRFLOW_VAR_ENV') }}
    schema: CURATED
    description: "Gives the sports related preferences of the users"
    tables:
      - name: CURATED__HYPER_PERSONALISATION__DDS_WHAT_CLUSTER_V3_LATEST
        description: "Gives the sports related preferences of the users"
        columns:
          - name: model_run_date
            description: "date the model was run."
            quote: true

          - name: dazn_user_id
            description: "This uniquely identifies each user within the DAZN platform."
            quote: true

          - name: cust_sport1_name
            description: "Primary favourite sport of the customer."
            quote: true

          - name: cust_sport2_name
            description: "Secondary favourite sport of the customer."
            quote: true

          - name: cust_sport3_name
            description: "Third favourite sport of the customer."
            quote: true

          - name: cust_sport4_name
            description: "Fourth favourite sport of the customer."
            quote: true

          - name: cust_sport5_name
            description: "Fifth favourite sport of the customer."
            quote: true

          - name: cust_comp1_name
            description: "Primary favourite competition of the customer."
            quote: true

          - name: cust_comp2_name
            description: "Secondary favourite competition of the customer."
            quote: true

          - name: cust_comp3_name
            description: "Third favourite competition of the customer."
            quote: true

          - name: cust_comp4_name
            description: "Fourth favourite competition of the customer."
            quote: true

          - name: cust_comp5_name
            description: "Fifth favourite competition of the customer."
            quote: true

          - name: cust_team1_name
            description: "Primary favourite team of the customer."
            quote: true

          - name: cust_team2_name
            description: "Secondary favourite team of the customer."
            quote: true

          - name: cust_team3_name
            description: "Third favourite team of the customer."
            quote: true

          - name: cust_team4_name
            description: "Fourth favourite team of the customer."
            quote: true

          - name: cust_team5_name
            description: "Fifth favourite team of the customer."
            quote: true

          - name: cust_play_mins
            description: "time the customer viewed content"
            quote: true

          - name: change_in_preference
            description: "gives the change in preference"
            quote: true

      - name: CURATED__HYPER_PERSONALISATION__OTHERS_LIKE_YOU
        columns:
          - name: dds_build_date
            description: "The dds build timestamp."
            quote: true

          - name: dazn_user_id
            description: "This uniquely identifies each user within the DAZN platform."
            quote: true

          - name: recommended_fixture
            description: "This gives recommended fixture."
            quote: true

          - name: fixture_name
            description: "This gives the fixture name."
            quote: true

          - name: str_territory
            description: "This gives the territory."
            quote: true

          - name: str_competition_id
            description: "This gives the competition id."
            quote: true

          - name: str_competition_name
            description: "This gives the competition name."
            quote: true

          - name: str_sport_id
            description: "This gives the sport id."
            quote: true

          - name: str_sport_name
            description: "This gives the sport name."
            quote: true

          - name: is_freemium
            description: "This gives the freemium flag."
            quote: true

          - name: META__DBT_INSERT_DTTS
            description: "The insert time by dbt."
            quote: true
