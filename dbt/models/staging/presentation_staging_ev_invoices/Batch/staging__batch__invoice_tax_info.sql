{{
	config(
		materialized = 'table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_M_WH',
		schema = 'STAGING',
		tags =  ['presentation-staging-ev-invoices']
    )
}}

WITH legacy_invoices_data AS (
    SELECT * FROM {{ ref_env('staging__batch__legacy_invoice_mapping') }}
)

,invoice_item_tax_data AS (
    SELECT * FROM {{ source_env('BATCH_INVOICES', 'INVOICE_DETAILS') }}
    QUALIFY TRUE
        AND ROW_NUMBER() OVER (PARTITION BY payment_id, ord_prod_id ORDER BY payment_date NULLS LAST) = 1
)

,invoice_item_payments_data AS (
    SELECT
        payment_id
        ,UPPER(currency_code) AS currency_code
    FROM {{ source_env('BATCH_INVOICES', 'PAYMENTS') }}
    QUALIFY TRUE
        AND ROW_NUMBER() OVER (PARTITION BY payment_id ORDER BY modified_ts DESC NULLS LAST) = 1
)

,final_data_invoice AS (
    SELECT
        invoice_item_tax_data.payment_id::STRING AS "invoice_payment_id"
        ,invoice_item_payments_data.currency_code AS "invoice_item_tax_currency_code"
        ,CONCAT_WS('-', payment_id, ord_prod_id) AS "invoice_item_id"
        ,SUM(invoice_item_tax_data.gross_amount) AS "invoice_item_gross_amount"
        ,SUM(invoice_item_tax_data.net_amount) AS "invoice_item_net_amount"
        ,SUM(invoice_item_tax_data.tax::NUMBER(38, 5)) AS "invoice_item_tax_amount"
        ,SUM(invoice_item_tax_data.charge_amount) AS "invoice_item_charge_amount"
        ,SUM(invoice_item_tax_data.discount_amount) AS "invoice_item_discount_amount"
        ,SUM(invoice_item_tax_data.reversal_amount) AS "invoice_item_reversal_amount"
        ,SUM(invoice_item_tax_data.reversal_tax) AS "invoice_item_reversal_tax"
    FROM invoice_item_tax_data
    INNER JOIN invoice_item_payments_data
        USING(payment_id)
    GROUP BY ALL
)

SELECT
    final_data_invoice.* EXCLUDE("invoice_payment_id", "invoice_item_id")
    ,COALESCE(legacy_invoices_data."legacy_invoice_id", final_data_invoice."invoice_payment_id") AS "invoice_payment_id"
    ,CASE
        WHEN legacy_invoices_data."invoice_id" IS NULL THEN final_data_invoice."invoice_item_id"
        ELSE REPLACE(final_data_invoice."invoice_item_id", final_data_invoice."invoice_payment_id", legacy_invoices_data."legacy_invoice_id")
    END AS "invoice_item_id"
FROM final_data_invoice
LEFT JOIN legacy_invoices_data
    ON final_data_invoice."invoice_payment_id" = legacy_invoices_data."invoice_id"
