{{
	config(
		materialized = 'table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
		schema = 'STAGING',
		tags =  ['presentation-staging-ev-invoices']
    )
}}

WITH legacy_invoices_data AS (
    SELECT * FROM TRANSFORMATION_PROD.CURATED.CURATED__ZOURA_EV_LEGACY_PAYMENTS
--                  {{ source_env('BATCH_INVOICES', 'LEGACY_PAYMENTS') }} -- OPEN__SUBSCRIPTIONS__PROD.EV_BATCH_FEED.legacy_payments
)

,legacy_invoices_data_renamed AS (
    SELECT
        payment_id::STRING AS "invoice_id"
        ,legacy_id::STRING AS "legacy_invoice_id"
    FROM legacy_invoices_data
)

,zr_invoices_data AS (
    SELECT
        "invoice_id"
        ,"invoice_created_timestamp"
        ,"billing_account_id"
        ,"invoice_number"
    FROM {{ ref_env('staging__zuora__invoice_current') }}
    GROUP BY ALL
)

,final_data AS (
    SELECT
        legacy_invoices_data_renamed."invoice_id"
        ,legacy_invoices_data_renamed."legacy_invoice_id"
        ,zr_invoices_data."invoice_created_timestamp" AS "legacy_invoice_created_timestamp"
        ,zr_invoices_data."billing_account_id" AS "legacy_billing_account_id"
        ,zr_invoices_data."invoice_number" AS "legacy_invoice_number"
    FROM legacy_invoices_data_renamed
    LEFT JOIN zr_invoices_data
        ON legacy_invoices_data_renamed."legacy_invoice_id" = zr_invoices_data."invoice_id"
)

SELECT * FROM final_data
