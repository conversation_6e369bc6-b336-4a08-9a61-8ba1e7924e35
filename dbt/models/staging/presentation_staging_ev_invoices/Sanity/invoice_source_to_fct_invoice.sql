-- Checking counts from source to Fct Invoice
SELECT COUNT(*), count(distinct invoice_id) FROM TRANSFORMATION_PROD.CURATED.CURATED__DMP_BILLING_EVENTS; -- 2067899,1145726

select count(*) as "cnt" from TRANSFORMATION_PROD.curated.curated__dmp__invoice; -- 1540351
select count(*) as "cnt" from TRANSFORMATION_PROD.curated.curated__dmp__invoice_current; -- 1139797

select count(*) as "cnt" from TRANSFORMATION_PROD.STAGING.staging__dmp__invoice_current; -- 1139797

select * from TRANSFORMATION_PROD.STAGING.staging__dmp__invoice_current; -- 192

select "invoice_id",count(*) as "cnt"
from TRANSFORMATION_PROD.STAGING.staging__dmp__invoice_current
group by all
having "cnt">1; -- 0

select count(*) as "cnt" from TRANSFORMATION_PROD.STAGING.staging__zuora__invoice_current; -- 444728574

select "invoice_id",count(*) as "cnt"
from TRANSFORMATION_PROD.STAGING.staging__zuora__invoice_current
group by all
having "cnt">1; -- 0

select count(*) as "cnt"
from TRANSFORMATION_PROD.STAGING.staging__dmp__invoice_current as invoice_source
inner join OPEN__SUBSCRIPTIONS__PROD.EV_BATCH_FEED.legacy_payments as legacy
on invoice_source."invoice_id" = legacy.LEGACY_ID; --0

select 444728574+192; -- 444728766

select count(*) as "cnt" from TRANSFORMATION_PROD.STAGING.staging__invoice_current; -- 444728766

------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------------------
--     to validate data with fct invoice we are checking for the past 1 month
select count(*) as "cnt" from TRANSFORMATION_PROD.PRESENTATION.FCT_INVOICE
where "invoice_created_timestamp" >= current_date-30; -- 5820741

select count(*) as "cnt" from TRANSFORMATION_PROD.STAGING.staging__invoice_current
where "invoice_created_timestamp" >= current_date-30; -- 5820741

select count(*) as "cnt" from TRANSFORMATION_PROD.curated.curated__dmp__invoice_current
where invoice_created_timestamp >= current_date-30; -- 1134409

select count(*) as "cnt" from TRANSFORMATION_PROD.staging.staging__dmp__invoice_current
where "invoice_created_timestamp" >= current_date-30; -- 1115159

-- excluding zuora data
select count(*) as "cnt" from TRANSFORMATION_PROD.staging.staging__dmp__invoice_current
where "invoice_created_timestamp" >= current_date-30
and try_to_numeric("invoice_id") is not null
; -- 1083100

select count(*) as "cnt" from TRANSFORMATION_prod.staging.staging__zuora__invoice_current
where "invoice_created_timestamp" >= current_date-30; --4737641

select count(*) as "cnt" from TRANSFORMATION_prod.staging.staging__invoice_current
where "invoice_created_timestamp" >= current_date-30; --5820741

select  1083100+4737641;  --5820741

    -- data is perfectly in sync -- as on 9th April'2025

------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------------------
use role TRANSFORMATION_DEV_ROLE;
use warehouse  TRANSFORMATION_DEV_M_WH;


select count(*) as "cnt",count(distinct invoice_item_id) as "dist_inv_cnt" from TRANSFORMATION_PROD.CURATED.curated__dmp__invoice_item; -- 1550696,1150335

select count(*) as "cnt",count(distinct invoice_item_id) as "dist_inv_cnt" from TRANSFORMATION_PROD.CURATED.curated__dmp__invoice_item_current; --1150318,1150318


select invoice_id,invoice_item_id,count(*) as "cnt"
from TRANSFORMATION_PROD.curated.curated__dmp__invoice_item_current
group by all
having "cnt">1; --0

select count(*) as "cnt", count(distinct "invoice_item_id") as "dist_inv_cnt" from TRANSFORMATION_PROD.STAGING.staging__dmp__invoice_item_current; --1150318,1150318

select "invoice_id","invoice_item_id",count(*) as "cnt"
from TRANSFORMATION_PROD.STAGING.staging__dmp__invoice_item_current
group by all
having "cnt">1; --0


select count(*) as "cnt",count(distinct "invoice_item_id") as "dist_inv_cnt" from TRANSFORMATION_PROD.STAGING.STAGING__ZUORA__INVOICE_ITEM_CURRENT; -- 834697611,834697611


select count(*) as "cnt",count(distinct "invoice_item_id") as "dist_inv_cnt" from TRANSFORMATION_PROD.transient.invoice_items__invoice_item_join; --835788605,835788605

select 1150318+834697611; --835847929

select "invoice_id", "invoice_item_id",counT(*) as "cnt" from TRANSFORMATION_PROD.transient.invoice_items__invoice_item_join
group by all
having "cnt" >1;

-- These are near values-- we cant to apple to apple mapping in this models as invoice items invoice item join is a combination of rateplan and oli related information as well

------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------------------


select count(*) from TRANSFORMATION_PROD.CURATED.curated__dmp__payment_method; --1681187
select count(*) from TRANSFORMATION_PROD.CURATED.curated__dmp__payment_method_current; --1119835

select payment_method_id,count(*) as "cnt"
from TRANSFORMATION_PROD.curated.curated__dmp__payment_method_current
group by all
having "cnt">1; --0

select count(*) as "cnt" from TRANSFORMATION_PROD.STAGING.staging__dmp__payment_method_current; --1119835

select "payment_method_id",count(*) as "cnt"
from TRANSFORMATION_PROD.STAGING.staging__dmp__payment_method_current
group by all
having "cnt">1; --0

select count(*) from TRANSFORMATION_PROD.STAGING.staging__zuora__payment_method_current; -- 41834380

select 41834380+1119835; --42954215

select count(*) from TRANSFORMATION_PROD.STAGING.staging__payment_method_current; --42954215

select "payment_method_id",count(*) as "cnt"
from TRANSFORMATION_PROD.STAGING.staging__payment_method_current
group by all
having "cnt">1; --0
-- data is in sync
-------

select count(*) as "cnt" from TRANSFORMATION_PROD.STAGING.staging__invoice_refund_current; -- 3225523

select "refund_invoice_payment_invoice_payment_id",count(*) as "cnt"
from TRANSFORMATION_PROD.STAGING.staging__invoice_refund_current
group by all
having "cnt">1; -- this is expected behaviour to have records

-- 8a128266826b65f601826e25df394ea7
-- 8a1294c1828e259401829152d0a22aff
-- 8a1295258282181d018288a953c40262
-- 8a128cfd899d0ae20189b2e6ae263864
-- 8a1289668286bd0a01828d16bab067dc
-- 8a1291128282180901828c1e290532ba
-- 8a129112828e25950182927163ac15e1

select * from TRANSFORMATION_PROD.STAGING.staging__invoice_refund_current
where "refund_invoice_payment_invoice_payment_id" = '8a128266826b65f601826e25df394ea7';

select * from TRANSFORMATION_PROD.STAGING.staging__invoice_refund_current
where "refund_invoice_payment_invoice_payment_id" = '8a12978887ec1b850187ec42fce46531';


select top 5 * from TRANSFORMATION_PROD.STAGING.staging__zuora__invoice_refund_current
where "refund_invoice_payment_invoice_payment_id" = '8a12978887ec1b850187ec42fce46531';

-- this is not expected to have single record though its mentioned as refund current - this is unique at refund id level


select "refund_id",count(*) as "cnt"
from TRANSFORMATION_PROD.STAGING.staging__dmp__invoice_refund_current
group by all
having "cnt">1; -- this is expected behaviour to have records

-- "source_of_data" = 'zr'

select "refund_id",count(*) as "cnt"
from TRANSFORMATION_PROD.STAGING.staging__invoice_refund_current
where "source_of_data" = 'zr'
group by all
having "cnt">1;



select * from TRANSFORMATION_PROD.STAGING.STAGING__INVOICE_REFUND_CURRENT
where "refund_id" = '2c92a0af671dda90016725a2ced250e8';

-- 2c92a0af671dda90016725a2ced250e8
-- 102296806
-- 102752591
-- 102205915
-- 100185907
-- 102901392
-- 98902296
-- 101394282

select * from TRANSFORMATION_PROD.STAGING.STAGING__ZUORA__REFUND
where "refund_id" = '2c92a0af671dda90016725a2ced250e8';

select * from TRANSFORMATION_PROD.STAGING.staging__zuora__refund_invoice_payment
where "refund_invoice_payment_refund_id" = '2c92a0af671dda90016725a2ced250e8';




------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------------------

select count(*) from TRANSFORMATION_PROD.CURATED.curated__dmp__payment; --216
select count(*) from TRANSFORMATION_PROD.CURATED.curated__dmp__payment_current; -- 185

select psp_reference,count(*) as "cnt"
from TRANSFORMATION_PROD.curated.curated__dmp__payment_current
group by all
having "cnt">1; --0

select count(*) as "cnt" from TRANSFORMATION_PROD.STAGING.staging__dmp__payment_current; --185

select "payment_id",count(*) as "cnt"
from TRANSFORMATION_PROD.STAGING.staging__dmp__payment_current
group by all
having "cnt">1;

select count(*) from TRANSFORMATION_PROD.STAGING.staging__zuora__payment_current; -- 375182488

select 375182488+185; --375182673


select count(*) from TRANSFORMATION_PROD.STAGING.staging__payment_current; -- 375182767

select "invoice_payment_id",count(*) as "cnt"
from TRANSFORMATION_PROD.STAGING.staging__payment_current
group by all
having "cnt">1;

select count(*) from TRANSFORMATION_PROD.CURATED.curated__dmp__payment; -- 228
-- Note
82 cases are getting duplicated -- as single payments was done across multiple invoices
query id - 01ba52e5-0203-fa8c-0000-11357f22622e


-- Analysing the reason behind the duplication

select * from TRANSFORMATION_PROD.STAGING.STAGING__ZUORA__PAYMENT_CURRENT
where "payment_id" = '2c92a0fe66206969016639df67d25e31';

select * from TRANSFORMATION_PROD.STAGING.STAGING__dmp__PAYMENT_CURRENT
where "payment_id" = '2c92a0fe66206969016639df67d25e31';

select * from TRANSFORMATION_PROD.STAGING.STAGING__PAYMENT_CURRENT
where "payment_id" = '2c92a0fe66206969016639df67d25e31';

select top 5  * from TRANSFORMATION_PROD.STAGING.staging__dmp__legacy_invoice_mapping
where "legacy_invoice_id" = '2c92a0fe66206969016639df67d25e31';
-------
-- dbt run --select staging__batch__invoice_tax_info.sql staging__batch__legacy_invoice_mapping.sql staging__dmp__billing_account.sql staging__dmp__billing_product_id_to_sub_id_linking.sql staging__dmp__billing_product_id_to_sub_id_linking_current.sql staging__dmp__invoice_current.sql staging__dmp__invoice_item_current.sql staging__dmp__invoice_refund_current.sql staging__dmp__payment_current.sql staging__dmp__payment_method_current.sql staging__invoice_current.sql staging__invoice_refund_current.sql staging__payment_current.sql staging__payment_method_current.sql dmp__invoice_items__invoice_item_join.sql invoice_items__invoice_item_join.sql fct_invoice_items.sql fct_invoice_addons.sql fct_invoice_invoice_item_adjustment_agg.sql fct_invoice_invoice_items_agg.sql fct_invoice_payments_agg.sql mart_fct_invoice_payments.sql mart_fct_invoice_refund_payments.sql Fct Invoice Payments.sql
-------

-- To Do:

