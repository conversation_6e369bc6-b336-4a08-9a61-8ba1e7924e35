WITH invoice_items_data AS (
    SELECT * FROM {{ ref('staging__dmp__invoice_item_current') }}
)

select
    COUNT(*) AS "rows_cnt"
    ,COUNT(DISTINCT("invoice_id")) AS "unq_invoice_id_cnt"
    ,COUNT(DISTINCT("invoice_item_id")) AS "unq_invoice_item_id_cnt"
    ,"rows_cnt" - COUNT("invoice_item_service_start_date") AS "missing_invoice_item_service_start_date_cnt"
    ,"rows_cnt" - COUNT("invoice_item_service_end_date") AS "missing_invoice_item_service_end_date_cnt"
    ,"rows_cnt" - COUNT("billing_account_currency_code") AS "missing_billing_account_currency_code_cnt"
    ,"rows_cnt" - COUNT("entitlement_set_id") AS "missing_entitlement_set_id_cnt"
    ,COUNT_IF("invoice_item_charge_amount" < 0 ) AS "negative_invoice_item_charge_amount_cnt"
    ,COUNT(
        DISTINCT(
            CASE
                WHEN "rateplan_charge_charge_type" NOT IN ('Recurring', 'OneTime') THEN "rateplan_charge_charge_type"
            END
        )
    ) AS "unknown_rateplan_charge_charge_type"
    ,COUNT(
        DISTINCT(
            CASE
                WHEN "rateplan_partner_id" NOT IN ('SKY_IT','TIVUSAT_IT') THEN "rateplan_partner_id"
            END
        )
    ) AS "unknown_rateplan_partner_id"
FROM invoice_items_data
GROUP BY ALL
