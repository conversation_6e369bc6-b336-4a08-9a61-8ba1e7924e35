{{
	config(
		materialized = 'view',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
		schema = 'STAGING',
		tags =  ['presentation-staging-ev-invoices']
    )
}}

WITH name_current AS (
    SELECT * FROM TRANSFORMATION_PROD.STAGING.STAGING__ZUORA__SUBSCRIPTION_NAME_CURRENT
)

,billing_events AS (
    SELECT * FROM TRANSFORMATION_PROD.CURATED.CURATED__DMP_BILLING_EVENTS
)

,fct_ppv_purchases AS (
    SELECT "rateplan_id", "subscription_id" FROM TRANSFORMATION_PROD.PRESENTATION.FCT_PPV_PURCHASES
)

,source_data AS (
    SELECT
        billing_events.billing_product_id AS "billing_product_id"
        ,CASE
            WHEN billing_events.product_type ILIKE 'SUBSCRIPTION' THEN billing_events.billing_product_id -- This covers subscriptions
            WHEN fct_ppv_purchases."rateplan_id" IS NOT NULL THEN fct_ppv_purchases."subscription_id" -- This covers PPV's
            ELSE billing_events.linked_billing_product_id -- This covers Addon's
        END AS "dmp_subscription_id"
        ,name_current."subscription_id" AS "legacy_subscription_id"
        ,MAX(billing_events.event_timestamp) OVER (PARTITION BY billing_events.billing_product_id) AS "subscription_id_valid_until_timestamp"
    FROM billing_events
    LEFT JOIN fct_ppv_purchases
        ON billing_events.billing_product_id = fct_ppv_purchases."rateplan_id"
    LEFT JOIN name_current
        ON billing_events.legacy_subscription_name = name_current."subscription_name"
    WHERE TRUE
        AND billing_events.billing_product_id IS NOT NULL
)

SELECT
    "billing_product_id"
    ,COALESCE("legacy_subscription_id", "dmp_subscription_id") AS "subscription_id"
    ,"subscription_id_valid_until_timestamp"
FROM source_data
WHERE TRUE
    AND "subscription_id" IS NOT NULL
GROUP BY ALL
