{{
	config(
		materialized = 'table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
		schema = 'STAGING',
		tags =  ['presentation-staging-ev-invoices']
    )
}}

WITH payment_method_data AS (
    SELECT * EXCLUDE( event_timestamp, payment_method_created_timestamp, payment_method_last_transaction_timestamp)
        ,TO_TIMESTAMP_TZ(DATE_TRUNC('second', event_timestamp)) AS event_timestamp
        ,TO_TIMESTAMP_TZ(DATE_TRUNC('second', payment_method_created_timestamp)) AS payment_method_created_timestamp
        ,TO_TIMESTAMP_TZ(DATE_TRUNC('second', payment_method_last_transaction_timestamp)) AS payment_method_last_transaction_timestamp
    FROM {{ source_env('DMP_INVOICES', 'CURATED__DMP__PAYMENT_METHOD_CURRENT') }}
)

,payment_data AS (
    SELECT * FROM {{ ref('staging__dmp__payment_current') }}
)

,renamed_payment_method_data AS (
    SELECT
        dazn_id AS "dazn_user_id"
        ,payment_method_id AS "payment_method_id"
        ,dazn_id AS "billing_account_id"
        ,event_timestamp AS "payment_method_transaction_timestamp"
        ,event_timestamp AS "payment_method_updated_timestamp"
        ,payment_method_payment_method_status AS "payment_method_payment_method_status"
        ,created_by AS "payment_method_created_by_id"
        ,created_by AS "payment_method_updated_by_id"
        ,payment_gateway AS "payment_gateway"
        ,payment_method_type AS "payment_method_type"
        ,payment_method_type AS "payment_method_actual_payment_method"
        ,psp_reference AS "payment_id"
        ,payment_method_bank_identification_number AS "payment_method_bank_identification_number"
        ,payment_method_credit_card_type AS "payment_method_credit_card_type"
        ,payment_method_credit_card_expiration_month AS "payment_method_credit_card_expiration_month"
        ,payment_method_credit_card_expiration_year AS "payment_method_credit_card_expiration_year"
        ,payment_method_credit_card_mask_number AS "payment_method_credit_card_mask_number"
        ,payment_method_paypal_email AS "payment_method_paypal_email"
        ,payment_method_paypal_type AS "payment_method_paypal_type"
        ,payment_method_bank_transfer_type AS "payment_method_bank_transfer_type"
        ,payment_method_created_timestamp AS "payment_method_created_timestamp"
        ,payment_method_last_transaction_timestamp AS "payment_method_last_transaction_timestamp"
--         ,payment_method_last_transaction_status AS "payment_method_last_transaction_status" -- excluding here as this is hardcoded in the curated -- to remove at some point in time
    FROM payment_method_data
)

,payment_transaction_info AS (
    SELECT
        "payment_method_id"
        ,CASE "payment_status"
            WHEN 'Processed' THEN 'Approved'
            WHEN 'Error' THEN 'Declined'
        END AS "payment_method_last_transaction_status"
        ,TO_VARCHAR(MAX(CASE WHEN "payment_status" ='Error' THEN "payment_created_timestamp" END ) OVER ( PARTITION BY "payment_method_id"), 'YYYY-MM-DD"T"HH24:MI:SS+0000') AS "payment_method_last_failed_sale_transaction_timestamp"
    FROM payment_data
    QUALIFY TRUE
        AND ROW_NUMBER() OVER (PARTITION BY "payment_method_id" ORDER BY "payment_created_timestamp" DESC )= 1
)

SELECT
    *
    ,NULL AS "payment_method_num_consecutive_failures"
FROM renamed_payment_method_data
LEFT JOIN payment_transaction_info
    USING("payment_method_id")
WHERE TRUE
    AND "payment_method_id" IS NOT NULL
GROUP BY ALL
