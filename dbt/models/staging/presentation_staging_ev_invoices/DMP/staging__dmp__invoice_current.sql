{{
	config(
		materialized = 'table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
		schema = 'STAGING',
		tags =  ['presentation-staging-ev-invoices']
    )
}}

WITH invoice_data_raw AS (
    SELECT
        * EXCLUDE(invoice_created_timestamp, invoice_updated_timestamp, invoice_item_period_start_date, invoice_item_period_end_date)
        ,TO_TIMESTAMP_TZ(DATE_TRUNC('second', invoice_created_timestamp)) AS invoice_created_timestamp
        ,TO_TIMESTAMP_TZ(DATE_TRUNC('second', invoice_updated_timestamp)) AS invoice_updated_timestamp
        ,TO_TIMESTAMP_TZ(DATE_TRUNC('second', invoice_item_period_start_date)) AS invoice_item_period_start_date
        ,TO_TIMESTAMP_TZ(DATE_TRUNC('second', invoice_item_period_end_date)) AS invoice_item_period_end_date
    FROM {{ source_env('DMP_INVOICES', 'CURATED__DMP__INVOICE_CURRENT') }}
)

,legacy_invoices_data AS (
    SELECT * FROM {{ ref_env('staging__batch__legacy_invoice_mapping') }}
)

,tax_data_raw AS (
    SELECT * FROM {{ ref_env('staging__batch__invoice_tax_info') }}
)

,invoice_refunds_data_raw AS (
    SELECT * FROM {{ ref_env('staging__dmp__invoice_refund_current') }}
)

,renamed_invoice_data AS (
    SELECT
        billing_mode AS "billing_mode"
--         ,message_type AS "message_type"
        ,dazn_id AS "dazn_user_id"
        ,invoice_id AS "invoice_id"
        ,psp_reference AS "psp_reference"
        ,invoice_created_timestamp AS "invoice_created_timestamp"
        ,invoice_updated_timestamp AS "invoice_updated_timestamp"
        ,original_invoice_id AS "original_invoice_id"
        ,churn_type AS "invoice_churn_type"
        ,min_billing_product_id AS "min_billing_product_id"
        ,max_billing_product_id AS "max_billing_product_id"
        ,min_message_type AS "min_message_type"
        ,max_message_type AS "max_message_type"
        ,invoice_item_period_start_date AS "invoice_item_period_start_date"
        ,invoice_item_period_end_date AS "invoice_item_period_end_date"
        ,event_status AS "event_status"
        ,payment_method_id AS "payment_method_id"
        ,payment_failed AS "payment_failed"
        ,payment_failure_reason AS "payment_failure_reason"
        ,payment_failure_attempt AS "payment_failure_attempt"
        ,UPPER(currency) AS "currency"
        ,COALESCE(invoice_amount, 0) AS "invoice_amount" --  includes tax'
        ,COALESCE(payment_amount, 0) AS "invoice_payment_amount"
        ,COALESCE(etf_amount, 0) AS "invoice_etf_amount"
        ,CASE
            WHEN churn_type = 'INVOLUNTARY' THEN 0
            ELSE "invoice_amount" - "invoice_payment_amount"
        END AS "invoice_balance"
        ,source_system_derived AS "source_system_derived"
        ,tracking_id_derived AS "tracking_id_derived"
--         ,balance_amount AS "balance_amount"
        ,CASE first_message_type
            WHEN 'BILLING_PRODUCT_PURCHASE' THEN 'API'
            ELSE 'BillRun'
        END AS "invoice_source"
    FROM invoice_data_raw
)

,invoice_data AS (
    SELECT
        "dazn_user_id"
        ,COALESCE(legacy_invoices_data."legacy_invoice_id", renamed_invoice_data."invoice_id") AS "invoice_id"
        ,COALESCE(legacy_invoices_data."legacy_invoice_created_timestamp", renamed_invoice_data."invoice_created_timestamp") AS "invoice_created_timestamp"
        ,COALESCE(legacy_invoices_data."legacy_billing_account_id", renamed_invoice_data."dazn_user_id") AS "billing_account_id"
        ,COALESCE(legacy_invoices_data."legacy_invoice_number", renamed_invoice_data."invoice_id") AS "invoice_number"
        ,"invoice_updated_timestamp"
        ,'Posted' AS "invoice_status"
        ,"currency" AS "billing_account_currency_code"
        ,"invoice_amount" --  includes tax'
        ,"invoice_payment_amount"
        ,"invoice_etf_amount"
        ,"invoice_balance"
        ,"invoice_churn_type"
        ,"invoice_source"
        ,"original_invoice_id"
    FROM renamed_invoice_data
    LEFT JOIN legacy_invoices_data
        ON renamed_invoice_data."invoice_id" = legacy_invoices_data."invoice_id"
    WHERE TRUE
        AND "event_status" IN ('SUCCEEDED', 'FAILED')
    GROUP BY ALL
)

,tax_data AS (
    SELECT
        "invoice_payment_id"
        ,SUM("invoice_item_tax_amount") AS "invoice_tax_amount"
    FROM tax_data_raw
    GROUP BY ALL
)

,invoice_refunds_data AS (
    SELECT
        "original_invoice_id"
        ,MAX("invoice_refund_updated_timestamp") AS "recent_invoice_refund_updated_timestamp"
        ,SUM("refund_amount") AS "refund_amount"
    FROM invoice_refunds_data_raw
    GROUP BY "original_invoice_id"
)

,final_invoice_data AS (
    SELECT
        invoice_data.* EXCLUDE ("invoice_updated_timestamp")
        ,GREATEST_IGNORE_NULLS("invoice_updated_timestamp", "recent_invoice_refund_updated_timestamp") AS "invoice_updated_timestamp"
--         ,IFF(tax_data."invoice_reversal_amount" > 0, 1, 0) AS "invoice_is_reversed"
        ,COALESCE(tax_data."invoice_tax_amount", 0) AS "invoice_tax_amount"
        ,invoice_data."invoice_amount" - COALESCE(tax_data."invoice_tax_amount", 0)  AS "invoice_amount_without_tax"
        ,COALESCE(invoice_refunds_data."refund_amount", 0) AS "invoice_refund_amount"
    FROM invoice_data
    LEFT JOIN tax_data
        ON tax_data."invoice_payment_id" = invoice_data."invoice_id"
    LEFT JOIN invoice_refunds_data
        ON invoice_data."invoice_id" = invoice_refunds_data."original_invoice_id"
)

SELECT
    "dazn_user_id"
    ,"billing_account_id"
    ,"dazn_user_id" AS "crm_account_id"
    ,"invoice_id"
    ,"invoice_number"
    ,"invoice_created_timestamp"
    ,"invoice_created_timestamp" AS "invoice_posted_timestamp"
    ,"invoice_created_timestamp"::DATE AS "invoice_due_date"
    ,"invoice_created_timestamp"::DATE AS "invoice_date"
    ,"invoice_updated_timestamp"
    ,"invoice_status"
    ,"billing_account_currency_code"
    ,"invoice_amount"
    ,"invoice_payment_amount"
    ,"invoice_etf_amount"
    ,"invoice_balance"
    ,"invoice_tax_amount"
    ,"invoice_amount_without_tax"
    ,"invoice_refund_amount"
    ,"invoice_churn_type"
    ,"invoice_source"
    ,0 AS "invoice_tax_exempt_amount"
    ,CASE
        WHEN "invoice_churn_type" = 'INVOLUNTARY' THEN "invoice_amount"
        WHEN "invoice_refund_amount" IS NOT NULL THEN "invoice_refund_amount"
        ELSE 0
    END AS "invoice_credit_balanced_adjustment_amount"
    ,NULL AS "invoice_is_reversed"
    ,NULL AS "invoice_source_id"
    ,"original_invoice_id"
FROM final_invoice_data
GROUP BY ALL
