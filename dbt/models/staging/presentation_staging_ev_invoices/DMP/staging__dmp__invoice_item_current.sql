{{
	config(
		materialized = 'table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
		schema = 'STAGING',
		tags =  ['presentation-staging-ev-invoices']
    )
}}

WITH invoice_data_raw AS (
    SELECT
        * EXCLUDE(event_timestamp, invoice_item_period_start_date, invoice_item_period_end_date, invoice_item_created_timestamp)
        ,TO_TIMESTAMP_TZ(DATE_TRUNC('second', event_timestamp)) AS event_timestamp
        ,TO_TIMESTAMP_TZ(DATE_TRUNC('day', invoice_item_period_start_date)) AS invoice_item_period_start_date
        ,TO_TIMESTAMP_TZ(DATE_TRUNC('day', invoice_item_period_end_date)) AS invoice_item_period_end_date
        ,TO_TIMESTAMP_TZ(DATE_TRUNC('second', invoice_item_created_timestamp)) AS invoice_item_created_timestamp
    FROM {{ source_env('DMP_INVOICES', 'CURATED__DMP__INVOICE_ITEM_CURRENT') }}
)

,legacy_invoices_data AS (
    SELECT * FROM {{ ref_env('staging__batch__legacy_invoice_mapping') }}
)

,tax_data AS (
    SELECT * FROM {{ ref_env('staging__batch__invoice_tax_info') }}
)

,billing_product_id_to_sub_id_linking AS (
    SELECT * FROM {{ ref_env('staging__dmp__billing_product_id_to_sub_id_linking_current')}}
)

,billing_account_data AS (
    SELECT * FROM {{ source_env('DMP_INVOICES', 'CURATED__DMP_BILLING_ACCOUNT') }}
)

,subscriptions_data_raw AS (
    SELECT * FROM {{ ref_env('subscription_id__dim') }}
)

,subscriptions_data AS (
    SELECT * FROM subscriptions_data_raw
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "subscription_id" ORDER BY "subscription_id_updated_timestamp" DESC) = 1
)

,invoice_data AS (
    SELECT
        invoice_data_raw.* EXCLUDE(invoice_id, invoice_item_id)
        ,COALESCE(legacy_invoices_data."legacy_invoice_id", invoice_data_raw.invoice_id) AS invoice_id
        ,CASE
            WHEN legacy_invoices_data."invoice_id" IS NULL THEN invoice_data_raw.invoice_item_id
            ELSE REPLACE(invoice_data_raw.invoice_item_id, invoice_data_raw.invoice_id, legacy_invoices_data."legacy_invoice_id")
        END AS invoice_item_id
        ,COALESCE(legacy_invoices_data."legacy_billing_account_id", invoice_data_raw.dazn_id) AS billing_account_id
    FROM invoice_data_raw
    LEFT JOIN legacy_invoices_data
        ON invoice_data_raw.invoice_id = legacy_invoices_data."invoice_id"
--     QUALIFY TRUE
--         AND ROW_NUMBER() OVER (PARTITION BY invoice_id, invoice_item_id ORDER BY invoice_item_created_timestamp DESC) = 1 -- this logic was updated bcz of the cancelations of renewal invoices.
)

,renamed_account_data AS (
    SELECT
        daznid AS "dazn_user_id"
        ,TO_TIMESTAMP_TZ(DATE_TRUNC('second', MAX(eventtimestamp))) AS "billing_account_last_updated_timestamp"
    FROM billing_account_data
    GROUP BY ALL
)

SELECT
    invoice_data.business_type AS "business_type"
    ,invoice_data.message_type AS "message_type"
    ,invoice_data.event_id AS "event_id"
    ,invoice_data.event_timestamp AS "event_timestamp"
    ,invoice_data.dazn_id AS "dazn_user_id"
    ,invoice_data.billing_account_id AS "billing_account_id"
    ,invoice_data.billing_mode AS "billing_mode"
    ,invoice_data.invoice_item_id AS "invoice_item_id"
    ,invoice_data.invoice_id AS "invoice_id"
    ,invoice_data.invoice_item_period_start_date::DATE AS "invoice_item_service_start_date"
    ,invoice_data.invoice_item_period_end_date::DATE AS "invoice_item_service_end_date"
    ,invoice_data.invoice_item_created_timestamp AS "invoice_item_created_date"
    ,invoice_data.invoice_item_created_timestamp AS "invoice_item_updated_timestamp"
    ,invoice_data.invoice_item_created_timestamp AS "rateplan_charge_updated_timestamp"
    ,invoice_data.product_type AS "product_type"
    ,CASE
        WHEN product_type ILIKE 'ADDON' THEN 'addon'
        WHEN product_type ILIKE 'ONE TIME' THEN 'onetime'
        WHEN product_type ILIKE 'PASS' THEN 'pass'
        WHEN product_type ILIKE 'PPV' THEN 'ppv'
        WHEN product_type ILIKE 'SUBSCRIPTION' THEN 'subscription'
    ELSE LOWER(product_type)
    END AS "rateplan_product_type"
    ,invoice_data.billing_product_id AS "billing_product_id"
    ,invoice_data.billing_product_id AS "rateplan_charge_id"
    ,invoice_data.billing_product_id AS "rateplan_id"
    ,invoice_data.catalog_product_id AS "catalog_product_id"
    ,invoice_data.catalog_product_id AS "rateplan_charge_name"
    ,invoice_data.entitlement_set_id AS "entitlement_set_id"
    ,UPPER(invoice_data.currency) AS "billing_account_currency_code"
    ,COALESCE(tax_data."invoice_item_tax_amount", 0) AS "invoice_item_tax_amount"
    ,COALESCE(invoice_data.invoice_item_charge_amount::NUMBER(38, 5), 0) - COALESCE(tax_data."invoice_item_tax_amount", 0)  AS "invoice_item_charge_amount"
    ,invoice_data.billing_charge_type AS "billing_charge_type"
    ,CASE invoice_data.billing_charge_type
        WHEN 'RENEWABLE' THEN 'Recurring'
        WHEN 'ONE_TIME' THEN 'OneTime'
        ELSE invoice_data.billing_charge_type
    END AS "rateplan_charge_charge_type"
    ,invoice_data.source_system_derived AS "invoice_source_system_name_derived__skey"
    ,invoice_data.tracking_id_derived AS "invoice_tracking_id__skey"
    ,renamed_account_data."billing_account_last_updated_timestamp" AS "billing_account_last_updated_timestamp"
    ,billing_product_id_to_sub_id_linking."subscription_id" AS "subscription_id"
    ,subscriptions_data."subscription_name" AS "subscription_name"
    ,TO_TIMESTAMP_TZ(DATE_TRUNC('second', subscriptions_data."subscription_id_updated_timestamp")) AS "subscription_id_updated_timestamp"
    ,NULL AS "subscription_number"
    ,NULL AS "invoice_item_applied_to_invoice_item_id"
    ,service_provider AS "rateplan_partner_id"
    ,service_provider_user_id AS "rateplan_partner_user_id"
    ,NULL::TIMESTAMP_TZ AS "rateplan_created_timestamp"
    ,NULL AS "rateplan_addon_type"
    ,NULL AS "invoice_item_charge_name"
    ,NULL AS "data_source"
FROM invoice_data
LEFT JOIN tax_data
    ON invoice_data.invoice_id = tax_data."invoice_payment_id"
        AND invoice_data.invoice_item_id = tax_data."invoice_item_id"
LEFT JOIN billing_product_id_to_sub_id_linking
    ON invoice_data.billing_product_id = billing_product_id_to_sub_id_linking."billing_product_id"
LEFT JOIN subscriptions_data
    ON billing_product_id_to_sub_id_linking."subscription_id" = subscriptions_data."subscription_id"
LEFT JOIN renamed_account_data
    ON invoice_data.dazn_id = renamed_account_data."dazn_user_id"
GROUP BY ALL
