{{
    config(
        materialized = 'table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        schema = 'STAGING',
        tags =  ['presentation-staging-ev-invoices']
    )
}}
--- This model is actually at invoice payments level
WITH payments_data AS (
    SELECT
        * EXCLUDE (event_timestamp)
        ,TO_TIMESTAMP_TZ(DATE_TRUNC('second', event_timestamp)) AS event_timestamp
    FROM {{ source_env('DMP_INVOICES', 'CURATED__DMP__PAYMENT_CURRENT') }}
)

,legacy_invoices_data AS (
    SELECT * FROM {{ ref_env('staging__batch__legacy_invoice_mapping') }}
)

,final_payments_data AS (
    SELECT
        invoice_id AS "invoice_id"
        ,psp_reference AS "payment_id"
        ,dazn_id AS "dazn_user_id"
--         ,dazn_id AS "billing_account_id"
        ,payment_method_id AS "payment_method_id"
--          Functionally we cant do partial payments to a specific invoice and so the create date should be different for each payment and product group is not needed.
         ,CASE
             WHEN payment_failed IS NOT DISTINCT FROM TRUE THEN 'Error'
             WHEN event_status = 'SUCCEEDED' AND MESSAGE_TYPE ILIKE '%cancelation%' AND churn_type = 'INVOLUNTARY' THEN 'Error'
             ELSE 'Processed'
         END AS "payment_status"
        ,event_timestamp::DATE AS "payment_effective_timestamp"
        ,event_timestamp AS "payment_created_timestamp"
        ,event_timestamp AS "payment_updated_timestamp"
        ,UPPER(currency) AS "payment_currency"
        ,COALESCE(charge_amount, 0) AS "payment_amount"
        ,COALESCE(refund_amount, 0)::STRING AS "payment_refund_amount"
        ,CASE
            WHEN "payment_status" = 'Error' THEN TO_VARCHAR(TO_TIMESTAMP_TZ(event_timestamp), 'YYYY-MM-DD"T"HH24:MI:SS+0000')
        END AS "payment_cancelled_on"
        ,gateway AS "payment_gateway"
        ,CASE
            WHEN "payment_status" <> 'Processed' AND TRY_TO_NUMBER(REGEXP_SUBSTR(payment_failure_reason, 'errorCode:([A-Za-z0-9 ]+)', 1, 1, 'e', 1)) IS NULL
                THEN REGEXP_SUBSTR(payment_failure_reason, 'errorCode:([A-Za-z0-9 ]+)', 1, 1, 'e', 1)
            WHEN "payment_status" <> 'Processed' THEN SUBSTR(payment_failure_reason,0,POSITION(' - errorCode:' IN payment_failure_reason))
            ELSE 'Approved'
        END AS "payment_gateway_response"
        ,CASE
            WHEN "payment_status" <> 'Processed' THEN COALESCE(
                                                            TRY_TO_NUMBER(REGEXP_SUBSTR(payment_failure_reason, 'errorCode:([A-Za-z0-9 ]+)', 1, 1, 'e', 1))
                                                            ,REGEXP_SUBSTR(payment_failure_reason, '([0-9]+)', 1, 1)
                                                            )::NUMBER::STRING
            ELSE 'Approved'
        END AS "payment_gateway_response_code"
        ,gateway AS "payment_payment_source"
        ,COALESCE(created_by, triggered_by) AS "payment_created_by_id"
        ,CASE
            WHEN created_by = 'DAZN' THEN 'API'
            WHEN created_by IN ('BILLING', 'evsecurity') THEN 'PaymentRun' -- initially classified as 'BillRun' but conformed as 'PaymentRun'
            WHEN created_by IS NOT NULL THEN 'Manually'
        END AS "payment_source"
    FROM payments_data
    WHERE TRUE
        AND event_status IN ('SUCCEEDED', 'FAILED')
    GROUP BY ALL
)

SELECT
    final_payments_data.* EXCLUDE ("invoice_id")
     ,CONCAT_WS('-', COALESCE(legacy_invoices_data."legacy_invoice_id", final_payments_data."invoice_id"), "payment_id") AS "invoice_payment_id"
     ,COALESCE(legacy_invoices_data."legacy_invoice_id", final_payments_data."invoice_id") AS "invoice_id"
     ,COALESCE(legacy_invoices_data."legacy_billing_account_id", final_payments_data."dazn_user_id") AS "billing_account_id"
     ,NULL AS "payment_source_name"
     ,NULL AS "payment_number"
     ,"payment_id" AS "payment_reference_id"
     ,NULL AS "payment_type"
     ,NULL AS "payment_gateway_state"
     ,NULL AS "payment_comment"
FROM final_payments_data
LEFT JOIN legacy_invoices_data
    ON final_payments_data."invoice_id" = legacy_invoices_data."invoice_id"
