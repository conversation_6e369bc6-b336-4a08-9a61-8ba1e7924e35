{{
	config(
		materialized = 'view',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
		schema = 'STAGING',
		tags =  ['presentation-staging-ev-invoices']
    )
}}

WITH source_data AS (
    SELECT * FROM {{ ref('staging__dmp__billing_product_id_to_sub_id_linking') }}
)

SELECT * FROM source_data
QUALIFY TRUE
    AND ROW_NUMBER() OVER ( PARTITION BY "billing_product_id" ORDER BY "subscription_id_valid_until_timestamp" DESC) = 1
