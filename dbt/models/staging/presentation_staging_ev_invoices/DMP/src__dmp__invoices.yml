version: 2

sources:
  - name: DMP_INVOICES
    database: TRANSFORMATION_{{ 'UAT' if env_var('AIRFLOW_VAR_ENV') == 'stage' else env_var('AIRFLOW_VAR_ENV') }}
    schema: CURATED
    quoting:
      identifier: true
    tables:
      - name: CURATED__DMP_BILLING_EVENTS
        description: "Billing events data from Evergent"
        columns:
          - &MESSAGE_TYPE
            name: MESSAGE_TYPE
            description: "The type of message"
            quote: true

          - &EVENT_ID
            name: EVENT_ID
            description: "The unique identifier for the event"
            quote: true

          - &EVENT_TIMESTAMP
            name: EVENT_TIMESTAMP
            description: "Timestamp when the event occurred"
            quote: true

          - &EVENT_STATUS
            name: EVENT_STATUS
            description: "Status of the event"
            quote: true

          - &DAZN_ID
            name: DAZN_ID
            description: "The DAZN ID associated with the event"
            quote: true

          - &BILLING_MODE
            name: BILLING_MODE
            description: "The billing mode used for the event"
            quote: true

          - &DCB_PROVIDER_NAME
            name: DCB_PROVIDER_NAME
            description: "The name of the DCB provider"
            quote: true

          - &SOURCE_SYSTEM_DERIVED
            name: SOURCE_SYSTEM_DERIVED
            description: "Derived source system"
            quote: true

          - &TRACKING_ID_DERIVED
            name: TRACKING_ID_DERIVED
            description: "Derived tracking ID"
            quote: true

          - &IS_3PP_EXIT
            name: IS_3PP_EXIT
            description: "Indicates whether it is a 3rd-party exit"
            quote: true

          - &DEVICE_MANUFACTURER
            name: DEVICE_MANUFACTURER
            description: "Manufacturer of the device"
            quote: true

          - &DEVICE_PLATFORM
            name: DEVICE_PLATFORM
            description: "The platform of the device"
            quote: true

          - &BILLING_COUNTRY
            name: BILLING_COUNTRY
            description: "Country where the billing is processed"
            quote: true

          - &PRODUCT_GROUP
            name: PRODUCT_GROUP
            description: "Product group associated with the event"
            quote: true

          - &PRODUCT_TYPE
            name: PRODUCT_TYPE
            description: "Type of the product involved"
            quote: true

          - &BUSINESS_TYPE
            name: BUSINESS_TYPE
            description: "Type of business associated with the event"
            quote: true

          - &DAZN_ORDER_ID
            name: DAZN_ORDER_ID
            description: "Order ID from DAZN"
            quote: true

          - &BILLING_PRODUCT_ID
            name: BILLING_PRODUCT_ID
            description: "Billing product ID"
            quote: true

          - &PARENT_BILLING_PRODUCT_ID
            name: PARENT_BILLING_PRODUCT_ID
            description: "Parent product ID for this billing product"
            quote: true

          - &LINKED_BILLING_PRODUCT_ID
            name: LINKED_BILLING_PRODUCT_ID
            description: "Linked billing product ID"
            quote: true

          - &CATALOG_PRODUCT_ID
            name: CATALOG_PRODUCT_ID
            description: "Catalog product ID"
            quote: true

          - &CATALOG_PRODUCT_NAME
            name: CATALOG_PRODUCT_NAME
            description: "Name of the catalog product"
            quote: true

          - &SERVICE_PROVIDER
            name: SERVICE_PROVIDER
            description: "Service provider associated with the event"
            quote: true

          - &SERVICE_PROVIDER_USER_ID
            name: SERVICE_PROVIDER_USER_ID
            description: "User ID from the service provider"
            quote: true

          - &LEGACY_SUBSCRIPTION_NAME
            name: LEGACY_SUBSCRIPTION_NAME
            description: "Legacy subscription name"
            quote: true

          - &LEGACY_OLI_ID
            name: LEGACY_OLI_ID
            description: "Legacy OLI ID"
            quote: true

          - &LEGACY_RATEPLAN_ID
            name: LEGACY_RATEPLAN_ID
            description: "Legacy rate plan ID"
            quote: true

          - &VALIDITY_PERIOD_UNIT
            name: VALIDITY_PERIOD_UNIT
            description: "Unit of the validity period"
            quote: true

          - &VALIDITY_PERIOD_DURATION
            name: VALIDITY_PERIOD_DURATION
            description: "Duration of the validity period"
            quote: true

          - &VALIDITY_START_DATE
            name: VALIDITY_START_DATE
            description: "Start date of the validity period"
            quote: true

          - &VALIDITY_END_DATE
            name: VALIDITY_END_DATE
            description: "End date of the validity period"
            quote: true

          - &NEXT_BILLING_DATE
            name: NEXT_BILLING_DATE
            description: "Next billing date"
            quote: true

          - &PURCHASE_DATE
            name: PURCHASE_DATE
            description: "Date of purchase"
            quote: true

          - &RENEWED_DATE
            name: RENEWED_DATE
            description: "Date when the subscription was renewed"
            quote: true

          - &EFFECTIVE_CANCELLATION_DATE
            name: EFFECTIVE_CANCELLATION_DATE
            description: "Effective cancellation date"
            quote: true

          - &CANCEL_REASON
            name: CANCEL_REASON
            description: "Reason for cancellation"
            quote: true

          - &CHURN_TYPE
            name: CHURN_TYPE
            description: "Type of churn"
            quote: true

          - &PRODUCT_CHANGE_TYPE
            name: PRODUCT_CHANGE_TYPE
            description: "Type of product change"
            quote: true

          - &IS_SEASONAL
            name: IS_SEASONAL
            description: "Indicates whether the product is seasonal"
            quote: true

          - &IS_PAID_BY_INSTALLMENTS
            name: IS_PAID_BY_INSTALLMENTS
            description: "Indicates whether the purchase is paid in installments"
            quote: true

          - &NO_OF_INSTALLMENTS
            name: NO_OF_INSTALLMENTS
            description: "Number of installments"
            quote: true

          - &PAID_INSTALLMENTS
            name: PAID_INSTALLMENTS
            description: "Paid installments"
            quote: true

          - &IS_RECONTRACT
            name: IS_RECONTRACT
            description: "Indicates whether it's a recontract"
            quote: true

          - &INSTALLMENT_PAYLOAD
            name: INSTALLMENT_PAYLOAD
            description: "Payload for installment"
            quote: true

          - &IS_FREE_TRIAL
            name: IS_FREE_TRIAL
            description: "Indicates whether it's a free trial"
            quote: true

          - &FREE_TRIAL_PAYLOAD
            name: FREE_TRIAL_PAYLOAD
            description: "Payload for free trial"
            quote: true

          - &BILLING_CHARGE_TYPE
            name: BILLING_CHARGE_TYPE
            description: "Type of billing charge"
            quote: true

          - &CURRENCY
            name: CURRENCY
            description: "Currency used in the billing"
            quote: true

          - &CATALOG_PRICE
            name: CATALOG_PRICE
            description: "Catalog price of the product"
            quote: true

          - &INSTALLMENT_AMOUNT
            name: INSTALLMENT_AMOUNT
            description: "Installment amount"
            quote: true

          - &GROSS_PRICE
            name: GROSS_PRICE
            description: "Gross price of the product"
            quote: true

          - &CHARGE_AMOUNT
            name: CHARGE_AMOUNT
            description: "Amount charged for the event"
            quote: true

          - &ETF_AMOUNT
            name: ETF_AMOUNT
            description: "ETF amount"
            quote: true

          - &REFUND_AMOUNT
            name: REFUND_AMOUNT
            description: "Refund amount"
            quote: true

          - &REFUND_TYPE
            name: REFUND_TYPE
            description: "Refund type"
            quote: true

          - &REFUND_REASON
            name: REFUND_REASON
            description: "Reason for refund"
            quote: true

          - &TRIGGERED_BY
            name: TRIGGERED_BY
            description: "Who triggered the event"
            quote: true

          - &INVOICE_ID
            name: INVOICE_ID
            description: "Invoice ID associated with the event"
            quote: true

          - &LEGACY_INVOICE_ID
            name: LEGACY_INVOICE_ID
            description: "Legacy invoice ID"
            quote: true

          - &ORIGINAL_INVOICE_ID
            name: ORIGINAL_INVOICE_ID
            description: "Original invoice ID"
            quote: true

          - &INVOICE_ITEM_PERIOD_START_DATE
            name: INVOICE_ITEM_PERIOD_START_DATE
            description: "Invoice item period start date"
            quote: true

          - &INVOICE_ITEM_PERIOD_END_DATE
            name: INVOICE_ITEM_PERIOD_END_DATE
            description: "Invoice item period end date"
            quote: true

          - &PSP_REFERENCE
            name: PSP_REFERENCE
            description: "PSP reference"
            quote: true

          - &CREATED_BY
            name: CREATED_BY
            description: "Creator of the record"
            quote: true

          - &PAYMENT_METHOD_ID
            name: PAYMENT_METHOD_ID
            description: "Payment method ID"
            quote: true

          - &PAYMENT_FAILED
            name: PAYMENT_FAILED
            description: "Whether payment failed"
            quote: true

          - &PAYMENT_FAILURE_REASON
            name: PAYMENT_FAILURE_REASON
            description: "Reason for payment failure"
            quote: true

          - &PAYMENT_FAILURE_ATTEMPT
            name: PAYMENT_FAILURE_ATTEMPT
            description: "Payment failure attempt count"
            quote: true

          - &PAYMENT_PAYLOAD
            name: PAYMENT_PAYLOAD
            description: "Payload associated with the payment"
            quote: true

          - &ENTITLEMENT_SET_ID
            name: ENTITLEMENT_SET_ID
            description: "Entitlement set ID"
            quote: true

          - &PRODUCT_STATUS
            name: PRODUCT_STATUS
            description: "Status of the product"
            quote: true

          - &PAUSE_START_DATE
            name: PAUSE_START_DATE
            description: "Pause start date"
            quote: true

          - &PAUSE_END_DATE
            name: PAUSE_END_DATE
            description: "Pause end date"
            quote: true

          - &CHANNEL
            name: CHANNEL
            description: "Channel of the event"
            quote: true

          - &SEGMENT_ID
            name: SEGMENT_ID
            description: "Segment ID associated with the event"
            quote: true

          - &PROMO_PAYLOAD
            name: PROMO_PAYLOAD
            description: "Promo payload"
            quote: true

          - &RETENTION_OFFER_PAYLOAD
            name: RETENTION_OFFER_PAYLOAD
            description: "Retention offer payload"
            quote: true

          - &DEVICEDETAILS
            name: DEVICEDETAILS
            description: "Device details"
            quote: true

          - &VENDOR_ORDER_ID
            name: VENDOR_ORDER_ID
            description: "Vendor order ID"
            quote: true

          - &CORRELATION_ID
            name: CORRELATION_ID
            description: "Correlation ID"
            quote: true

          - &PAYLOAD
            name: PAYLOAD
            description: "General payload associated with the event"
            quote: true

      - name: CURATED__DMP_BILLING_ACCOUNT
        description: "Billing events data from Evergent"
        columns:
          - *BUSINESS_TYPE
          - *MESSAGE_TYPE
          - *EVENT_ID
          - *EVENT_TIMESTAMP
          - *DAZN_ID
          - *BILLING_MODE
          - *EVENT_STATUS

          - name: COUNTRY
            description: "Country associated with the event"
            quote: true

          - name: STATE
            description: "State or region associated with the event"
            quote: true

          - name: FIRST_NAME
            description: "First name of the user associated with the event"
            quote: true

          - name: LAST_NAME
            description: "Last name of the user associated with the event"
            quote: true

          - *TRIGGERED_BY
          - *PAYLOAD

      - name: CURATED__DMP__INVOICE
        description: "Invoice data from Evergent"
        columns:
          - *BILLING_MODE
          - *MESSAGE_TYPE
          - *DAZN_ID
          - *INVOICE_ID
          - *PSP_REFERENCE

          - name: INVOICE_CREATED_TIMESTAMP
            description: "Timestamp when the invoice was created"
            quote: true

          - name: INVOICE_UPDATED_TIMESTAMP
            description: "Timestamp when the invoice was last updated"
            quote: true

          - *ORIGINAL_INVOICE_ID
          - *CHURN_TYPE

          - name: MIN_BILLING_PRODUCT_ID
            description: "Minimum billing product ID for the invoice"
            quote: true

          - name: MAX_BILLING_PRODUCT_ID
            description: "Maximum billing product ID for the invoice"
            quote: true

          - name: MIN_MESSAGE_TYPE
            description: "Minimum message type related to the invoice"
            quote: true

          - name: MAX_MESSAGE_TYPE
            description: "Maximum message type related to the invoice"
            quote: true

          - *INVOICE_ITEM_PERIOD_START_DATE
          - *INVOICE_ITEM_PERIOD_END_DATE
          - *EVENT_STATUS
          - *PAYMENT_METHOD_ID
          - *PAYMENT_FAILED
          - *PAYMENT_FAILURE_REASON
          - *PAYMENT_FAILURE_ATTEMPT
          - *CURRENCY

          - name: INVOICE_AMOUNT
            description: "Total invoice amount"
            quote: true

          - name: PAYMENT_AMOUNT
            description: "Total payment amount for the invoice"
            quote: true

          - *ETF_AMOUNT
          - *SOURCE_SYSTEM_DERIVED
          - *TRACKING_ID_DERIVED

          - name: BALANCE_AMOUNT
            description: "Remaining balance amount after payments"
            quote: true

      - name: CURATED__DMP__INVOICE_CURRENT
        description: "Latest Invoice data from Evergent"
        columns:
          - *BILLING_MODE
          - *MESSAGE_TYPE
          - *DAZN_ID
          - *INVOICE_ID
          - *PSP_REFERENCE

          - name: INVOICE_CREATED_TIMESTAMP
            description: "Timestamp when the invoice was created"
            quote: true

          - name: INVOICE_UPDATED_TIMESTAMP
            description: "Timestamp when the invoice was last updated"
            quote: true

          - *ORIGINAL_INVOICE_ID
          - *CHURN_TYPE

          - name: MIN_BILLING_PRODUCT_ID
            description: "Minimum billing product ID for the invoice"
            quote: true

          - name: MAX_BILLING_PRODUCT_ID
            description: "Maximum billing product ID for the invoice"
            quote: true

          - name: MIN_MESSAGE_TYPE
            description: "Minimum message type related to the invoice"
            quote: true

          - name: MAX_MESSAGE_TYPE
            description: "Maximum message type related to the invoice"
            quote: true

          - *INVOICE_ITEM_PERIOD_START_DATE
          - *INVOICE_ITEM_PERIOD_END_DATE
          - *EVENT_STATUS
          - *PAYMENT_METHOD_ID
          - *PAYMENT_FAILED
          - *PAYMENT_FAILURE_REASON
          - *PAYMENT_FAILURE_ATTEMPT
          - *CURRENCY

          - name: INVOICE_AMOUNT
            description: "Total invoice amount"
            quote: true

          - name: PAYMENT_AMOUNT
            description: "Total payment amount for the invoice"
            quote: true

          - *ETF_AMOUNT
          - *SOURCE_SYSTEM_DERIVED
          - *TRACKING_ID_DERIVED

          - name: BALANCE_AMOUNT
            description: "Remaining balance amount after payments"
            quote: true

      - name: CURATED__DMP__INVOICE_ITEM
        description: "Invoice item details data from Evergent"
        columns:
          - *BUSINESS_TYPE
          - *MESSAGE_TYPE
          - *EVENT_ID
          - *EVENT_TIMESTAMP
          - *DAZN_ID
          - *BILLING_MODE

          - name: INVOICE_ITEM_ID
            description: "Unique identifier for the invoice item"
            quote: true

          - *INVOICE_ID
          - *INVOICE_ITEM_PERIOD_START_DATE
          - *INVOICE_ITEM_PERIOD_END_DATE

          - name: INVOICE_ITEM_CREATED_TIMESTAMP
            description: "Timestamp when the invoice item was created"
            quote: true

          - *PRODUCT_TYPE
          - *BILLING_PRODUCT_ID
          - *CATALOG_PRODUCT_ID
          - *SERVICE_PROVIDER
          - *SERVICE_PROVIDER_USER_ID
          - *ENTITLEMENT_SET_ID
          - *CURRENCY

          - name: INVOICE_ITEM_CHARGE_AMOUNT
            description: "Charge amount for the invoice item"
            quote: true

          - *BILLING_CHARGE_TYPE
          - *SOURCE_SYSTEM_DERIVED
          - *TRACKING_ID_DERIVED
          - *PAYLOAD

      - name: CURATED__DMP__INVOICE_ITEM_CURRENT
        description: "Latest Invoice item details data from Evergent"
        columns:
          - *BUSINESS_TYPE
          - *MESSAGE_TYPE
          - *EVENT_ID
          - *EVENT_TIMESTAMP
          - *DAZN_ID
          - *BILLING_MODE

          - name: INVOICE_ITEM_ID
            description: "Unique identifier for the invoice item"
            quote: true

          - *INVOICE_ID
          - *INVOICE_ITEM_PERIOD_START_DATE
          - *INVOICE_ITEM_PERIOD_END_DATE

          - name: INVOICE_ITEM_CREATED_TIMESTAMP
            description: "Timestamp when the invoice item was created"
            quote: true

          - *PRODUCT_TYPE
          - *BILLING_PRODUCT_ID
          - *CATALOG_PRODUCT_ID
          - *SERVICE_PROVIDER
          - *SERVICE_PROVIDER_USER_ID
          - *ENTITLEMENT_SET_ID
          - *CURRENCY

          - name: INVOICE_ITEM_CHARGE_AMOUNT
            description: "Charge amount for the invoice item"
            quote: true

          - *BILLING_CHARGE_TYPE
          - *SOURCE_SYSTEM_DERIVED
          - *TRACKING_ID_DERIVED
          - *PAYLOAD

      - name: CURATED__DMP__PAYMENT
        description: "Payment-related data from Evergent"
        columns:
          - *MESSAGE_TYPE
          - *BILLING_MODE
          - *BUSINESS_TYPE
          - *EVENT_TIMESTAMP
          - *DAZN_ID
          - *PSP_REFERENCE
          - *INVOICE_ID
          - *EVENT_STATUS
          - *CURRENCY
          - *CHARGE_AMOUNT
          - *REFUND_AMOUNT
          - *ETF_AMOUNT
          - *REFUND_TYPE
          - *CHURN_TYPE
          - *CREATED_BY
          - *TRIGGERED_BY
          - *PAYMENT_METHOD_ID

          - &PAYMENT_METHOD_TYPE
            name: PAYMENT_METHOD_TYPE
            description: "Type of payment method used"
            quote: true

          - name: GATEWAY
            description: "Payment gateway used for processing the transaction"
            quote: true

          - name: CARD_TYPE
            description: "Type of card used for payment (e.g., Credit, Debit)"
            quote: true

          - name: BIN
            description: "Bank Identification Number of the card"
            quote: true

          - name: LAST_DIGITS
            description: "Last few digits of the card number for reference"
            quote: true

          - name: EXPIRY_DATE
            description: "Expiry date of the card used for payment"
            quote: true

          - *PAYMENT_FAILED
          - *PAYMENT_FAILURE_REASON

      - name: CURATED__DMP__PAYMENT_CURRENT
        description: "Latest Payment-related data from Evergent"
        columns:
          - *MESSAGE_TYPE
          - *BILLING_MODE
          - *BUSINESS_TYPE
          - *EVENT_TIMESTAMP
          - *DAZN_ID
          - *PSP_REFERENCE
          - *INVOICE_ID
          - *EVENT_STATUS
          - *CURRENCY
          - *CHARGE_AMOUNT
          - *REFUND_AMOUNT
          - *ETF_AMOUNT
          - *REFUND_TYPE
          - *CHURN_TYPE
          - *CREATED_BY
          - *TRIGGERED_BY
          - *PAYMENT_METHOD_ID
          - *PAYMENT_METHOD_TYPE

          - name: GATEWAY
            description: "Payment gateway used for processing the transaction"
            quote: true

          - name: CARD_TYPE
            description: "Type of card used for payment (e.g., Credit, Debit)"
            quote: true

          - name: BIN
            description: "Bank Identification Number of the card"
            quote: true

          - name: LAST_DIGITS
            description: "Last few digits of the card number for reference"
            quote: true

          - name: EXPIRY_DATE
            description: "Expiry date of the card used for payment"
            quote: true

          - *PAYMENT_FAILED
          - *PAYMENT_FAILURE_REASON

      - name: CURATED__DMP__PAYMENT_METHOD
        description: "Payment method-related data from Evergent"
        columns:
          - *PAYMENT_METHOD_ID
          - name: BILLING_ACCOUNT_ID
            description: "Billing account associated with the payment method"
            quote: true

          - name: PAYMENT_METHOD_TRANSACTION_TIMESTAMP
            description: "Timestamp of the payment method transaction"
            quote: true

          - name: PAYMENT_METHOD_UPDATED_TIMESTAMP
            description: "Timestamp of the last update to the payment method"
            quote: true

          - name: PAYMENT_METHOD_PAYMENT_METHOD_STATUS
            description: "Current status of the payment method"
            quote: true

          - name: PAYMENT_METHOD_CREATED_BY_ID
            description: "ID of the user who created the payment method"
            quote: true

          - name: PAYMENT_METHOD_UPDATED_BY_ID
            description: "ID of the user who last updated the payment method"
            quote: true

          - name: PAYMENT_GATEWAY
            description: "Payment gateway used for processing transactions"
            quote: true

          - *PAYMENT_METHOD_TYPE

          - name: PAYMENT_METHOD_ACTUAL_PAYMENT_METHOD
            description: "Actual payment method used (e.g., Visa, PayPal, Bank Transfer)"
            quote: true

          - name: PAYMENT_ID
            description: "Unique identifier for the payment transaction"
            quote: true

          - name: PAYMENT_METHOD_BANK_IDENTIFICATION_NUMBER
            description: "Bank Identification Number (BIN) of the card"
            quote: true

          - name: PAYMENT_METHOD_CREDIT_CARD_TYPE
            description: "Type of credit card (e.g., Visa, Mastercard)"
            quote: true

          - name: PAYMENT_METHOD_CREDIT_CARD_EXPIRATION_MONTH
            description: "Expiration month of the credit card"
            quote: true

          - name: PAYMENT_METHOD_CREDIT_CARD_EXPIRATION_YEAR
            description: "Expiration year of the credit card"
            quote: true

          - name: PAYMENT_METHOD_CREDIT_CARD_MASK_NUMBER
            description: "Masked credit card number for reference"
            quote: true

          - name: PAYMENT_METHOD_PAYPAL_EMAIL
            description: "PayPal email associated with the payment method"
            quote: true

          - name: PAYMENT_METHOD_PAYPAL_TYPE
            description: "Type of PayPal payment method used"
            quote: true

          - name: PAYMENT_METHOD_BANK_TRANSFER_TYPE
            description: "Type of bank transfer used as a payment method"
            quote: true

          - name: PAYMENT_METHOD_CREATED_TIMESTAMP
            description: "Timestamp when the payment method was created"
            quote: true

          - name: PAYMENT_METHOD_LAST_TRANSACTION_TIMESTAMP
            description: "Timestamp of the last transaction using this payment method"
            quote: true

          - name: PAYMENT_METHOD_LAST_TRANSACTION_STATUS
            description: "Status of the last transaction associated with the payment method"
            quote: true

      - name: CURATED__DMP__PAYMENT_METHOD_CURRENT
        description: "Latest Payment method-related data from Evergent"
        columns:
          - *PAYMENT_METHOD_ID
          - name: BILLING_ACCOUNT_ID
            description: "Billing account associated with the payment method"
            quote: true

          - name: PAYMENT_METHOD_TRANSACTION_TIMESTAMP
            description: "Timestamp of the payment method transaction"
            quote: true

          - name: PAYMENT_METHOD_UPDATED_TIMESTAMP
            description: "Timestamp of the last update to the payment method"
            quote: true

          - name: PAYMENT_METHOD_PAYMENT_METHOD_STATUS
            description: "Current status of the payment method"
            quote: true

          - name: PAYMENT_METHOD_CREATED_BY_ID
            description: "ID of the user who created the payment method"
            quote: true

          - name: PAYMENT_METHOD_UPDATED_BY_ID
            description: "ID of the user who last updated the payment method"
            quote: true

          - name: PAYMENT_GATEWAY
            description: "Payment gateway used for processing transactions"
            quote: true

          - *PAYMENT_METHOD_TYPE

          - name: PAYMENT_METHOD_ACTUAL_PAYMENT_METHOD
            description: "Actual payment method used (e.g., Visa, PayPal, Bank Transfer)"
            quote: true

          - name: PAYMENT_ID
            description: "Unique identifier for the payment transaction"
            quote: true

          - name: PAYMENT_METHOD_BANK_IDENTIFICATION_NUMBER
            description: "Bank Identification Number (BIN) of the card"
            quote: true

          - name: PAYMENT_METHOD_CREDIT_CARD_TYPE
            description: "Type of credit card (e.g., Visa, Mastercard)"
            quote: true

          - name: PAYMENT_METHOD_CREDIT_CARD_EXPIRATION_MONTH
            description: "Expiration month of the credit card"
            quote: true

          - name: PAYMENT_METHOD_CREDIT_CARD_EXPIRATION_YEAR
            description: "Expiration year of the credit card"
            quote: true

          - name: PAYMENT_METHOD_CREDIT_CARD_MASK_NUMBER
            description: "Masked credit card number for reference"
            quote: true

          - name: PAYMENT_METHOD_PAYPAL_EMAIL
            description: "PayPal email associated with the payment method"
            quote: true

          - name: PAYMENT_METHOD_PAYPAL_TYPE
            description: "Type of PayPal payment method used"
            quote: true

          - name: PAYMENT_METHOD_BANK_TRANSFER_TYPE
            description: "Type of bank transfer used as a payment method"
            quote: true

          - name: PAYMENT_METHOD_CREATED_TIMESTAMP
            description: "Timestamp when the payment method was created"
            quote: true

          - name: PAYMENT_METHOD_LAST_TRANSACTION_TIMESTAMP
            description: "Timestamp of the last transaction using this payment method"
            quote: true

          - name: PAYMENT_METHOD_LAST_TRANSACTION_STATUS
            description: "Status of the last transaction associated with the payment method"
            quote: true

      - name: CURATED__DMP__REFUND_INVOICE
        description: "Refund invoice-related data from Evergent"
        columns:
          - *BILLING_MODE
          - *DAZN_ID
          - *INVOICE_ID
          - *PSP_REFERENCE

          - name: ORIGINAL_INVOICE_ID
            description: "Identifier of the original invoice related to the refund"
            quote: true

          - *EVENT_ID
          - *EVENT_TIMESTAMP
          - *EVENT_STATUS
          - name: REFUND_TYPE
            description: "Type of refund processed (e.g., full, partial)"
            quote: true

          - name: REFUND_REASON
            description: "Reason for initiating the refund"
            quote: true

          - name: TRIGGERED_BY
            description: "User or system entity that triggered the refund"
            quote: true

          - *PAYMENT_METHOD_ID

          - name: PAYMENT_FAILED
            description: "Indicator if the payment associated with the refund failed"
            quote: true

          - name: PAYMENT_FAILURE_REASON
            description: "Reason for payment failure, if applicable"
            quote: true

          - name: PAYMENT_FAILURE_ATTEMPT
            description: "Number of attempts made before payment failure"
            quote: true

          - *CURRENCY

          - name: REFUND_AMOUNT
            description: "Amount refunded to the customer"
            quote: true

          - name: REFUND_GATEWAY
            description: "Payment gateway used to process the refund"
            quote: true

          - name: REFUND_PAYMENT_METHOD_TYPE
            description: "Type of payment method used for the refund (e.g., credit card, PayPal)"
            quote: true

      - name: CURATED__DMP__REFUND_INVOICE_CURRENT
        description: "Latest Refund invoice-related data from Evergent"
        columns:
          - *BILLING_MODE
          - *DAZN_ID
          - *INVOICE_ID
          - *PSP_REFERENCE

          - name: ORIGINAL_INVOICE_ID
            description: "Identifier of the original invoice related to the refund"
            quote: true

          - *EVENT_ID
          - *EVENT_TIMESTAMP
          - *EVENT_STATUS
          - name: REFUND_TYPE
            description: "Type of refund processed (e.g., full, partial)"
            quote: true

          - name: REFUND_REASON
            description: "Reason for initiating the refund"
            quote: true

          - name: TRIGGERED_BY
            description: "User or system entity that triggered the refund"
            quote: true

          - *PAYMENT_METHOD_ID

          - name: PAYMENT_FAILED
            description: "Indicator if the payment associated with the refund failed"
            quote: true

          - name: PAYMENT_FAILURE_REASON
            description: "Reason for payment failure, if applicable"
            quote: true

          - name: PAYMENT_FAILURE_ATTEMPT
            description: "Number of attempts made before payment failure"
            quote: true

          - *CURRENCY

          - name: REFUND_AMOUNT
            description: "Amount refunded to the customer"
            quote: true

          - name: REFUND_GATEWAY
            description: "Payment gateway used to process the refund"
            quote: true

          - name: REFUND_PAYMENT_METHOD_TYPE
            description: "Type of payment method used for the refund (e.g., credit card, PayPal)"
            quote: true
