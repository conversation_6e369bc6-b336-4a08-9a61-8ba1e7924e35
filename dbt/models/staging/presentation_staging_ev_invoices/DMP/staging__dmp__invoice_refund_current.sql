{{
	config(
		materialized = 'table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
		schema = 'STAGING',
		tags =  ['presentation-staging-ev-invoices']
    )
}}
-- this model is not unique at refund_invoice_payment_invoice_payment_id because of the ev data source
WITH refund_data AS (
    SELECT
        * EXCLUDE(event_timestamp)
        ,TO_TIMESTAMP_TZ(DATE_TRUNC('second', event_timestamp)) AS event_timestamp
    FROM {{ source_env('DMP_INVOICES', 'CURATED__DMP__REFUND_INVOICE_CURRENT') }}
)

,legacy_invoices_data AS (
    SELECT * FROM {{ ref_env('staging__batch__legacy_invoice_mapping') }}
)

,invoice_payment_data_for_mapping AS (
    SELECT * FROM {{ ref_env('staging__dmp__payment_current') }}
    QUALIFY TRUE
        AND ROW_NUMBER() OVER (PARTITION BY "invoice_id" ORDER BY "payment_updated_timestamp"DESC) = 1
)

,renamed_refund_data AS (
    SELECT
        original_invoice_id AS "original_invoice_id"
        ,event_timestamp AS "invoice_refund_updated_timestamp"
        ,invoice_id AS "invoice_id"
        ,triggered_by AS "refund_created_by_id"
--         ,COALESCE(created_by, triggered_by) AS "refund_created_by_id" -- older discussions
        ,psp_reference AS "payment_id"
        ,psp_reference AS "refund_reference_id"
        ,dazn_id AS "dazn_id"
        ,invoice_id AS "refund_id" -- event_id
        ,event_timestamp::DATE AS "refund_date"
        ,'Processed' AS "refund_status"
        ,refund_type AS "refund_type"
        ,payment_failure_reason AS "refund_gateway_response"
        ,refund_reason AS "refund_reason_code"
        ,COALESCE(refund_amount, 0) AS "refund_amount"
        ,gateway AS "refund_gateway"
        ,payment_method_type AS "refund_method_type"
        ,NULL AS "refund_source_type"
        ,NULL AS "refund_number"
    FROM refund_data
)

,legacy_invoice_conversion AS (
    SELECT
        renamed_refund_data.* EXCLUDE ("original_invoice_id")
        , COALESCE(legacy_invoices_data."legacy_invoice_id", renamed_refund_data."original_invoice_id") AS "original_invoice_id"
    --      ,CONCAT_WS('-', COALESCE(legacy_invoices_data."legacy_invoice_id", renamed_refund_data."original_invoice_id"), renamed_refund_data."payment_id") AS "refund_invoice_payment_invoice_payment_id"
    FROM renamed_refund_data
        LEFT JOIN legacy_invoices_data
    ON renamed_refund_data."original_invoice_id" = legacy_invoices_data."invoice_id"
)


SELECT
    legacy_invoice_conversion.*
    ,invoice_payment_data_for_mapping."invoice_payment_id" AS "refund_invoice_payment_invoice_payment_id"
FROM legacy_invoice_conversion
LEFT JOIN invoice_payment_data_for_mapping
    ON legacy_invoice_conversion."original_invoice_id" = invoice_payment_data_for_mapping."invoice_id"
GROUP BY ALL
