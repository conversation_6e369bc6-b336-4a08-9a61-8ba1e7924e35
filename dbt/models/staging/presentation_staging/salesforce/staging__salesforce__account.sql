WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__SALESFORCE__ACCOUNT') }}
)

,renamed AS (
    SELECT
        "AccountStatus__c" AS "crm_account_status"
        ,"AllEmailPreferences__c" AS "is_opted_in_marketing"
        ,"CampaignId__c" AS "campaign_id"
        ,"CancellationReason__c" AS "cancellation_reason"
        ,"CreatedDate" AS "crm_account_created_timestamp"
        ,"GiftCode__c" AS "gift_code_number"
        ,"GiftCodeEndDate__c" AS "gift_code_end_date"
        ,"GiftCodeStartDate__c" AS "gift_code_start_date"
        ,"GiftCodeStatus__c" AS "gift_code_status"
        ,"Id" AS "crm_account_id"
        ,"LastModifiedDate" AS "crm_last_modified_timestamp"
        ,"NotificationCountryCode__c" AS "notification_country_code"
        ,"PartnerId__c" AS "partner_id"
        ,"PartnerRegistrationStatus__c" AS "partner_registration_status"
        ,"PauseScheduled__c" AS "has_pause_scheduled"
        ,"PauseFrom__c" AS "pause_from_date"
        ,"PauseScheduledTime__c" AS "pause_scheduled_timestamp"
        ,"PauseUntil__c" AS "pause_until_date"
        ,"PortabilityStatus__c" AS "eu_portability_status"
        ,"SubscriptionDate__c" AS "subscription_date"
        ,"TestUser__c" AS "is_test_user"
        ,"Type" AS "crm_account_type"
        ,"PersonContactId" AS "person_contact_id"
        ,"BillingCountryCode" AS "billing_country_code"
        ,IFNULL("DaznId__c", "Id") AS "dazn_user_id"
        ,"IsDoubleOptin__c" AS "is_double_opt_in"
        ,"NotificationLanguageCode__c" AS "notification_language_code"
        ,"PreviousStatus__c" AS "previous_crm_account_status"
        ,"Ads_Personalisation__c" AS "is_opted_in_ads_personalisation"
        ,"NFLMarketing__c" AS "is_opted_in_nfl_marketing"
        ,"NFLOptInChangeDate__c" AS "nfl_opt_in_change_date"
        ,"CancelledAccount__c" AS "is_cancelled_account"
        ,"IsActivePaidSoftCancellation__c" AS "is_active_paid_soft_cancellation"
        ,"IsFreeTrialSoftCancellation__c" AS "is_free_trial_soft_cancellation"
        ,"Viewer_Id__c" AS "viewer_id"
        ,"DFDFirstName__c" AS "dfd_first_name"
        ,"DFDLastName__c" AS "dfd_last_name"
        ,"PersonEmail" AS "person_email"
        ,"FirstName" AS "first_name"
        ,"LastName" AS "last_name"
        ,"PropensityToChurn__c" AS "propensity_to_churn"
        ,"DAZNTVBoxAddedDate__c" AS "dazn_tv_box_added_date"
        ,"DAZNTVBoxSerialNumber__c" AS "dazn_tv_box_serial_number"
        ,"DAZNTVBoxUser__c" AS "is_dazn_tv_box_user"
    FROM source
)

SELECT * FROM renamed
