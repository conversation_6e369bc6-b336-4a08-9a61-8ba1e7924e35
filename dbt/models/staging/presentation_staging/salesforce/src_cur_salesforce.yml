version: 2

sources:
  - name: CURATED
    database: TRANSFORMATION_{{ 'UAT' if env_var('AIRFLOW_VAR_ENV') == 'stage' else env_var('AIRFLOW_VAR_ENV') }}
    tables:
      - name: CURATED__SALESFORCE__ACCOUNT
        description: "Saleforce Account table"
        columns:
          - name: AccountStatus__c
            description: "Account status also known as user status (e.g. Free Trial, Active Paid)"
            quote: true

          - name: AllEmailPreferences__c
            description: "User is opted-in to marketing comms. All market except DE, this field is independent. For DE, it works with the Double opt in. NFL ticket box works independently"
            quote: true

          - name: CampaignId__c
            description: "Deprecated. Was part of Gift code v1 solution."
            quote: true

          - name: CancellationReason__c
            description: "Reason for Cancellation "
            quote: true

          - name: CreatedDate
            description: "Date the account was created in SF (no logic ot counter gut subscribe)"
            quote: true

          - name: GiftCode__c
            description: "Gift code no"
            quote: true

          - name: GiftCodeEndDate__c
            description: "Gift code End date"
            quote: true

          - name: GiftCodeStartDate__c
            description: "Gift Start End date"
            quote: true

          - name: GiftCodeStatus__c
            description: "Gift code status"
            quote: true

          - name: Id
            description: "Unique ID of the record (also known as Salesforce Account ID)"
            quote: true

          - name: LastModifiedDate
            description: "Datetime the record was last modified on (even for events that are not part of person account history)"
            quote: true

          - name: NotificationCountryCode__c
            description: "Set from the same source as BillingCountryCode__c from DAZNUserFeed"
            quote: true

          - name: PartnerId__c
            description: "is the partner ID. Used for Docomo "
            quote: true

          - name: PartnerRegistrationStatus__c
            description: "Comes from User feed. Picklist: Complete, Incomplete, N/A"
            quote: true

          - name: PauseScheduled__c
            description: "Flag that indicates if pause has been scheduled"
            quote: true

          - name: PauseFrom__c
            description: "Start pause date. Drives functionality in My Account"
            quote: true

          - name: PauseScheduledTime__c
            description: "Datetime when pause for scheduled"
            quote: true

          - name: PauseUntil__c
            description: "Pause until date"
            quote: true

          - name: PortabilityStatus__c
            description: "Show EU portability status: Pending, Granted, Denied, n/a, Not determined"
            quote: true

          - name: SubscriptionDate__c
            description: "start date of Subscription"
            quote: true

          - name: TestUser__c
            description: "Flag set to true for test accounts, false to live accounts"
            quote: true

          - name: Type
            description: "Customer type indicating 3PP or special partnership deals Amazon,Google,Sky,Docomo etc"
            quote: true

          - name: PersonContactId
            description: "For person account, user and contact has 1-2-1 relationship. It links contact to account. Its used in SFMC"
            quote: true

          - name: BillingCountryCode
            description: "Country code set by MISL when account is created"
            quote: true

          - name: DaznId__c
            description: "DAZN ID"
            quote: true

          - name: IsDoubleOptin__c
            description: "DE only. Set to true if a customer confirms double opt-in wb page (legal requirement for marketing opt-in in DE)"
            quote: true

          - name: NotificationLanguageCode__c
            description: "Language code for UI, Set by MISL DAZNUserFeed__c table LanguageLocaleKey__c field, user can change via My Account"
            quote: true

          - name: PreviousStatus__c
            description: "Previous status to current account status"
            quote: true

          - name: Ads_Personalisation__c
            description: "Set to true if customer opted out of Personalised Ads"
            quote: true

          - name: NFLMarketing__c
            description: "True/False flag whether a customer opted in to received emails from NFL directly. Used to select recors whose contact details are then weekly passed on to NFL"
            quote: true

          - name: NFLOptInChangeDate__c
            description: "Date on which a customer changed their NFL opt in status, in order to generate the NFL opt in report."
            quote: true

          - name: CancelledAccount__c
            description: "Set to true if Zuora processed the cancellation, but the account is still active (till the end of the billing period)"
            quote: true

          - name: IsActivePaidSoftCancellation__c
            description: "Set to true if user cancelled while Active Paid. Used for flagging accounts for SFMC flow. Added 2019 March(tbc)"
            quote: true

          - name: IsFreeTrialSoftCancellation__c
            description: "Set to true if user cancelled while in Free Trial state. Used for flagging accounts for SFMC flow. Added 2019 March(tbc)"
            quote: true

          - name: Viewer_Id__c
            description: "Viewer ID value copied from User table (combines EXT and non EXT values, whichever is actually used)"
            quote: true

          - name: DFDFirstName__c
            description: "Docomo first name (as the name is 'Docomo user' to prevent CS to see it)"
            quote: true

          - name: DFDLastName__c
            description: "Docomo last name (as the name is 'Docomo user' to prevent CS to see it)"
            quote: true

          - name: PersonEmail
            description: "Email of the customer. It's currently the DAZN user name apart from Docomo"
            quote: true

          - name: FirstName
            description: "First Name of the customer relevant to the account"
            quote: true

          - name: LastName
            description: "Surname of the customer relevant to the account"
            quote: true

          - name: PropensityToChurn__c
            description: "Indicates when a customer has had a poor playback experience and will receive enhanced customer support to improve retention."
            quote: true

          - name: DAZNTVBoxAddedDate__c
            description: "Date that customer was flagged as a DAZN TV Box User"
            quote: true

          - name: DAZNTVBoxSerialNumber__c
            description: "Unique identifier for the DAZN TV Box"
            quote: true

          - name: DAZNTVBoxUser__c
            description: "Tick box indicating if a customer is a DAZN TV Box User (1) or not (0)"
            quote: true

      - name: CURATED__SALESFORCE__ACCOUNT_CURRENT
        description: "Current view Account Object"
        columns:
          - name: AccountStatus__c
            description: "Account status also known as user status (e.g. Free Trial, Active Paid)"
            quote: true

          - name: AllEmailPreferences__c
            description: "User is opted-in to marketing comms. All market except DE, this field is independent. For DE, it works with the Double opt in. NFL ticket box works independently"
            quote: true

          - name: CampaignId__c
            description: "Deprecated. Was part of Gift code v1 solution."
            quote: true

          - name: CancellationReason__c
            description: "Reason for Cancellation "
            quote: true

          - name: CreatedDate
            description: "Date the account was created in SF (no logic ot counter gut subscribe)"
            quote: true

          - name: GiftCode__c
            description: "Gift code no"
            quote: true

          - name: GiftCodeEndDate__c
            description: "Gift code End date"
            quote: true

          - name: GiftCodeStartDate__c
            description: "Gift Start End date"
            quote: true

          - name: GiftCodeStatus__c
            description: "Gift code status"
            quote: true

          - name: Id
            description: "Unique ID of the record (also known as Salesforce Account ID)"
            quote: true

          - name: LastModifiedDate
            description: "Datetime the record was last modified on (even for events that are not part of person account history)"
            quote: true

          - name: NotificationCountryCode__c
            description: "Set from the same source as BillingCountryCode__c from DAZNUserFeed"
            quote: true

          - name: PartnerId__c
            description: "is the partner ID. Used for Docomo "
            quote: true

          - name: PartnerRegistrationStatus__c
            description: "Comes from User feed. Picklist: Complete, Incomplete, N/A"
            quote: true

          - name: PauseScheduled__c
            description: "Flag that indicates if pause has been scheduled"
            quote: true

          - name: PauseFrom__c
            description: "Start pause date. Drives functionality in My Account"
            quote: true

          - name: PauseScheduledTime__c
            description: "Datetime when pause for scheduled"
            quote: true

          - name: PauseUntil__c
            description: "Pause until date"
            quote: true

          - name: PortabilityStatus__c
            description: "Show EU portability status: Pending, Granted, Denied, n/a, Not determined"
            quote: true

          - name: SubscriptionDate__c
            description: "start date of Subscription"
            quote: true

          - name: TestUser__c
            description: "Flag set to true for test accounts, false to live accounts"
            quote: true

          - name: Type
            description: "Customer type indicating 3PP or special partnership deals Amazon,Google,Sky,Docomo etc"
            quote: true

          - name: PersonContactId
            description: "For person account, user and contact has 1-2-1 relationship. It links contact to account. Its used in SFMC"
            quote: true

          - name: BillingCountryCode
            description: "Country code set by MISL when account is created"
            quote: true

          - name: DaznId__c
            description: "DAZN ID"
            quote: true

          - name: IsDoubleOptin__c
            description: "DE only. Set to true if a customer confirms double opt-in wb page (legal requirement for marketing opt-in in DE)"
            quote: true

          - name: NotificationLanguageCode__c
            description: "Language code for UI, Set by MISL DAZNUserFeed__c table LanguageLocaleKey__c field, user can change via My Account"
            quote: true

          - name: PreviousStatus__c
            description: "Previous status to current account status"
            quote: true

          - name: Ads_Personalisation__c
            description: "Set to true if customer opted out of Personalised Ads"
            quote: true

          - name: NFLMarketing__c
            description: "True/False flag whether a customer opted in to received emails from NFL directly. Used to select recors whose contact details are then weekly passed on to NFL"
            quote: true

          - name: NFLOptInChangeDate__c
            description: "Date on which a customer changed their NFL opt in status, in order to generate the NFL opt in report."
            quote: true

          - name: CancelledAccount__c
            description: "Set to true if Zuora processed the cancellation, but the account is still active (till the end of the billing period)"
            quote: true

          - name: IsActivePaidSoftCancellation__c
            description: "Set to true if user cancelled while Active Paid. Used for flagging accounts for SFMC flow. Added 2019 March(tbc)"
            quote: true

          - name: IsFreeTrialSoftCancellation__c
            description: "Set to true if user cancelled while in Free Trial state. Used for flagging accounts for SFMC flow. Added 2019 March(tbc)"
            quote: true

          - name: Viewer_Id__c
            description: "Viewer ID value copied from User table (combines EXT and non EXT values, whichever is actually used)"
            quote: true

          - name: DFDFirstName__c
            description: "Docomo first name (as the name is 'Docomo user' to prevent CS to see it)"
            quote: true

          - name: DFDLastName__c
            description: "Docomo last name (as the name is 'Docomo user' to prevent CS to see it)"
            quote: true

          - name: PersonEmail
            description: "Email of the customer. It's currently the DAZN user name apart from Docomo"
            quote: true

          - name: FirstName
            description: "First Name of the customer relevant to the account"
            quote: true

          - name: LastName
            description: "Surname of the customer relevant to the account"
            quote: true

          - name: PropensityToChurn__c
            description: "Indicates when a customer has had a poor playback experience and will receive enhanced customer support to improve retention."
            quote: true

          - name: DAZNTVBoxAddedDate__c
            description: "Date that customer was flagged as a DAZN TV Box User"
            quote: true

          - name: DAZNTVBoxSerialNumber__c
            description: "Unique identifier for the DAZN TV Box"
            quote: true

          - name: DAZNTVBoxUser__c
            description: "Tick box indicating if a customer is a DAZN TV Box User (1) or not (0)"
            quote: true

      - name: CURATED__SALESFORCE__ACCOUNT_HISTORY
        description: "Saleforce AccountHistory table"
        columns:
          - name: AccountId
            description: "Unique Account ID"
            quote: true

          - name: CreatedById
            description: "Unique ID of the record (also known as Salesforce Account ID)"
            quote: true

          - name: CreatedDate
            description: "Date Time of record creation"
            quote: true

          - name: Field
            description: "Field that has been updated"
            quote: true

          - name: Id
            description: "Standard field, ID of the record"
            quote: true

          - name: IsDeleted
            description: "Standard field, not in use"
            quote: true

          - name: NewValue
            description: "New value of the updated field"
            quote: true

          - name: OldValue
            description: "Old value of the updated field"
            quote: true

      - name: CURATED__SALESFORCE__CANCELLATION_REASON_TRANSLATIONS
        description: "Saleforce Cancellation_Reason__c table"
        columns:
          - name: CancellationReason__c
            description: "Reason for Cancel the subscription"
            quote: true

          - name: Country__c
            description: "Country Name"
            quote: true

          - name: CreatedDate
            description: "creation date  of the record"
            quote: true

          - name: DisplayValueCS__c
            description: "The value shown to Customer (might be different from the one for CS) in local language"
            quote: true

          - name: DisplayValueCustomer__c
            description: "Value that is shown to the CS Agent"
            quote: true

          - name: Id
            description: "Standard field, ID of the record"
            quote: true

          - name: IsDeleted
            description: "Standard SFDC field. Indicates if the record has been deleted - We do not delete these records so there should not be any"
            quote: true

          - name: IsVisibleToEndCustomer__c
            description: "If FALSE, only CS can see it"
            quote: true

          - name: IsActive__c
            description: "If TRUE then its available (both CS and users) If FALSE reason is hidden"
            quote: true

          - name: Language__c
            description: "Language code of the display value to customer"
            quote: true

          - name: LastModifiedDate
            description: "Date record was last modified on"
            quote: true

      - name: CURATED__SALESFORCE__CANCELLATION_TASKS
        description: "Saleforce Task table"
        columns:
          - name: CreatedDate
            description: "Date Time of record creation"
            quote: true

          - name: Description
            description: "Cancellation reason"
            quote: true

          - name: WhatId
            description: "Record that the task is related to"
            quote: true

          - name: Description_Other_reason__c
            description: "Description of the task, Used to report on cancellation reason"
            quote: true

          - name: Id
            description: "Activity Id relating to the cancellation task"
            quote: true

      - name: CURATED__SALESFORCE__CASE
        description: "Saleforce Case table"
        columns:
          - name: AccountId
            description: "At the end of the trigger, it picks it up from the newly created Account"
            quote: true

          - name: CaseNumber
            description: "Number of the case. Autogenerated"
            quote: true

          - name: ClosedDate
            description: "Date when the case is closed? It updates if case is reopened and closed again"
            quote: true

          - name: CreatedDate
            description: "Date records was created"
            quote: true

          - name: NotificationCountryCode__c
            description: "Country Code"
            quote: true

          - name: Origin
            description: "Contact type used to create a case"
            quote: true

          - name: Priority
            description: "Priority ex: low"
            quote: true

          - name: Status
            description: "Case Status: Open, Responded, Closed"
            quote: true

          - name: SubType__c
            description: "Sub-type/Topic ex: Poor video playback quality"
            quote: true

          - name: SubType2__c
            description: "End-type/Query ex :I have more than one issue"
            quote: true

          - name: Type
            description: "Type/Subject ex : Live streaming issue"
            quote: true

          - name: Id
            description: "Autogenerated Salesforce Id of the record in this table. Standard field. The Id of the Case, representing a customer issue or problem."
            quote: true

          - name: Auto_Response__c
            description: "Indicates whether the case matched an active auto response criteria, triggering an automated response."
            quote: true

          - name: Bot_Chat_Key__c
            description: "The session ID of the chat before it is persisted."
            quote: true

          - name: Churn_Save_Attempted__c
            description: "Indicates whether the agent has made an attempt to save the customer from churning."
            quote: true

          - name: CreatedById
            description: "The user who created the transcript, including creation date and time (Read only)"
            quote: true

          - name: Description__c
            description: "[PII Data, this is a hashed value] Case description"
            quote: true

          - name: Eligible_To_Send_Surveys__c
            description: "Indicates whether the case is eligible for the sending of surveys."
            quote: true

          - name: GC_Eligible__c
            description: "Giftcode eligibility. As of 02-08-2021, the only non-null value in this column is '1m Offer Eligible (IT)'."
            quote: true

          - name: GC_Required__c
            description: "Gift code requested."
            quote: true

          - name: LastModifiedById
            description: "The user who last modified the transcript, including date and time (Read only)"
            quote: true

          - name: LastModifiedDate
            description: "The date and time the transcript was last modified (Read only)"
            quote: true

          - name: OwnerId
            description: "Owner"
            quote: true

          - name: ParentId
            description: "Id of the parent case"
            quote: true

          - name: Priority__c
            description: "Priority assigned to the case, expressed as an integer value between 0 and 6."
            quote: true

          - name: Propensity_To_Churn__c
            description: "Lists the reason why the customer is considered at risk of churn."
            quote: true

          - name: SLA_New__c
            description: "SLA time in number of hours, ranging between 1 and 120."
            quote: true

          - name: Device_Id__c
            description: "Unique identifier for the customer device used to contact CS"
            quote: true

      - name: CURATED__SALESFORCE__GET_FEEDBACK_SURVEYS
        description: "Salesforce Survey Feedback"
        columns:
          - name: AgentName__c
            description: "System ID of the agent who handled the case"
            quote: true

          - name: Case__c
            description: "System ID of the case the survey relates to"
            quote: true

          - name: ChatKey__c
            description: "System ID of the bot record the survey relates to"
            quote: true

          - name: ChatTranscript__c
            description: "System ID of the chat the survey relates to"
            quote: true

          - name: CountryCode__c
            description: "Country the survey relates to"
            quote: true

          - name: CreatedDate
            description: "Date the survey was created"
            quote: true

          - name: Id
            description: "Unique ID of the survey"
            quote: true

          - name: LanguageCode__c
            description: "Language of survey"
            quote: true

          - name: LastModifiedDate
            description: "Timestamp of last change to the survey"
            quote: true

          - name: Name
            description: "Unique ref of the survey (front end)"
            quote: true

          - name: OwnerId
            description: "Unique identifier of the internal owner"
            quote: true

          - name: Question1__c
            description: "Survey question 1"
            quote: true

          - name: Question2__c
            description: "Survey question 2"
            quote: true

          - name: Question3__c
            description: "Survey question 3"
            quote: true

          - name: Question4__c
            description: "Survey question 4"
            quote: true

          - name: Response1__c
            description: "Survey response 1"
            quote: true

          - name: Response2__c
            description: "Survey response 2"
            quote: true

          - name: Response3__c
            description: "Survey response 3"
            quote: true

          - name: Response4__c
            description: "Survey response 4"
            quote: true

          - name: Sentiment__c
            description: "Sentiment of the survey response (numerical value but text in SF for some reason)"
            quote: true

          - name: Status__c
            description: "Status of the survey (sent, responded, etc)"
            quote: true

      - name: CURATED__SALESFORCE__GIFT_CODE
        description: "Saleforce Gift_Code_History__c table"
        columns:
          - name: Account__c
            description: "Account Id"
            quote: true

          - name: AppliedOn__c
            description: "Applied on date"
            quote: true

          - name: AccountStatus__c
            description: "Account status "
            quote: true

          - name: CampaignId__c
            description: "Campaign name"
            quote: true

          - name: CreatedById
            description: "Standard field, not used"
            quote: true

          - name: CreatedDate
            description: "Date of record created"
            quote: true

          - name: GiftCodeEndDate__c
            description: "VoucherCodeEffectiveDate + number of periods"
            quote: true

          - name: GiftCodeStartDate__c
            description: "VoucherCodeEffectiveDate"
            quote: true

          - name: GiftCode__c
            description: "Gift Code"
            quote: true

          - name: GiftCodeStatus__c
            description: "Status: Applied, In Use, Expired"
            quote: true

          - name: Id
            description: "SFDC ID"
            quote: true

          - name: IsInvalid__c
            description: "If subscription cancelled, GC is not longer valid"
            quote: true

          - name: LastModifiedById
            description: "Standard field, not used"
            quote: true

          - name: LastModifiedDate
            description: "Last Modified Date of the record "
            quote: true

          - name: Name
            description: "Autogenerated number"
            quote: true

          - name: NumberOfPeriods__c
            description: "Number of Gift Code periods"
            quote: true

          - name: Source__c
            description: "GC applied on Signup or Post signup"
            quote: true

          - name: UniqueGiftCodeIdentifier__c
            description: "unique GC + account ID"
            quote: true

      - name: CURATED__SALESFORCE__IN_GAGE_SURVEY_C
        description: "Used to store all the surveys"
        columns:
          - name: CreatedById
            description: "The user who created the transcript, including creation date and time (Read only)"
            quote: true

          - name: CreatedDate
            description: "The date and time the transcript was created (Read only)"
            quote: true

          - name: Id
            description: "Autogenerated Salesforce Id of the record in this table. Standard field."
            quote: true

          - name: In_Gage__Background_Image_URL__c
            description: "Background splash page"
            quote: true

          - name: In_Gage__Language__c
            description: "language identifier"
            quote: true

          - name: In_Gage__Submit_Button_Label__c
            description: "submit button string"
            quote: true

          - name: In_Gage__Submit_Response__c
            description: "Submit response string"
            quote: true

          - name: LastModifiedById
            description: "The user who last modified the transcript, including date and time (Read only)"
            quote: true

          - name: LastModifiedDate
            description: "The date and time the transcript was last modified (Read only)"
            quote: true

          - name: Name
            description: "Uniquie survey name"
            quote: true

      - name: CURATED__SALESFORCE__IN_GAGE_SURVEY_QUESTION_C
        description: "Used to store all the survey questions for each survey submitted by a customer."
        columns:
          - name: CreatedById
            description: "The user who created the transcript, including creation date and time (Read only)"
            quote: true

          - name: CreatedDate
            description: "The date and time the transcript was created (Read only)"
            quote: true

          - name: Id
            description: "Autogenerated Salesforce Id of the record in this table. Standard field."
            quote: true

          - name: In_Gage__Survey__c
            description: "Master-Detail (Survey)"
            quote: true

          - name: LastModifiedById
            description: "The user who last modified the transcript, including date and time (Read only)"
            quote: true

          - name: LastModifiedDate
            description: "The date and time the transcript was last modified (Read only)"
            quote: true

          - name: Name
            description: "English language text for each question in the survey"
            quote: true

      - name: CURATED__SALESFORCE__IN_GAGE_SURVEY_QUESTION_RESPONSE
        description: "used to store all the survey question responses for each survey submitted by a customer"
        columns:
          - name: CreatedById
            description: "The user who created the transcript, including creation date and time (Read only)"
            quote: true

          - name: CreatedDate
            description: "The date and time the transcript was created (Read only)"
            quote: true

          - name: Id
            description: "Autogenerated Salesforce Id of the record in this table. Standard field."
            quote: true

          - name: In_Gage__Of_Time_Question_was_Responded_To__c
            description: "Indicates the number of times a question was responded to in a survey."
            quote: true

          - name: In_Gage__Response_Value__c
            description: "Value used to group similar responses, that are originally recorded in local languages."
            quote: true

          - name: In_Gage__Response__c
            description: "[PII Data, this is a hashed value] Response from customer"
            quote: true

          - name: In_Gage__SurveyTaker__c
            description: "Each questions is attached to a unique survey submitted by customer"
            quote: true

          - name: In_Gage__Survey_Question__c
            description: "ID of the survey question matching values listed in column \"Id\" from table InGageSurveyQuestionC."
            quote: true

          - name: LastModifiedById
            description: "The user who last modified the transcript, including date and time (Read only)"
            quote: true

          - name: LastModifiedDate
            description: "The date and time the transcript was last modified (Read only)"
            quote: true

          - name: Name
            description: "Unique identifier for response to single question"
            quote: true

          - name: Survey_Question_Name__c
            description: "Survey question"
            quote: true

      - name: CURATED__SALESFORCE__IN_GAGE_SURVEY_TAKER_C
        description: "Used to store the unique survey submitted by a customer. it is attached to a case closed"
        columns:
          - name: CreatedById
            description: "The user who created the transcript, including creation date and time (Read only)"
            quote: true

          - name: CreatedDate
            description: "The date and time the transcript was created (Read only)"
            quote: true

          - name: Id
            description: "Autogenerated Salesforce Id of the record in this table. Standard field."
            quote: true

          - name: In_Gage__Case__c
            description: "Case attached"
            quote: true

          - name: In_Gage__Employee__c
            description: "Employee (Agent who dealt with the case)"
            quote: true

          - name: In_Gage__Language__c
            description: "language identifier"
            quote: true

          - name: In_Gage__Survey__c
            description: "Survey used"
            quote: true

          - name: LastModifiedById
            description: "The user who last modified the transcript, including date and time (Read only)"
            quote: true

          - name: LastModifiedDate
            description: "The date and time the transcript was last modified (Read only)"
            quote: true

          - name: Name
            description: "Unique identifier for full survey response"
            quote: true

      - name: CURATED__SALESFORCE__LIVE_CHAT_TRANSCRIPT
        description: "Information help track information about your agents’ chats with customers."
        columns:
          - name: Abandoned
            description: "The amount of time in seconds before the unanswered chat request was disconnected"
            quote: true

          - name: AccountId
            description: "The name of the account associated with the transcript"
            quote: true

          - name: AHT__c
            description: "Calculate"
            quote: true

          - name: AverageResponseTimeOperator
            description: "The average time that it took an agent to respond to a chat visitor’s message"
            quote: true

          - name: AverageResponseTimeVisitor
            description: "The average time that it took a visitor to respond to an agent comment"
            quote: true

          - name: Body
            description: "[PII Data, this is a hashed value] The transcribed chat between an agent and a visitor"
            quote: true

          - name: Browser
            description: "The type and version of the browser used by the visitor"
            quote: true

          - name: BrowserLanguage
            description: "The visitor's browser language selection"
            quote: true

          - name: CaseId
            description: "The case associated with the chat"
            quote: true

          - name: Case_Origin__c
            description: "The source of the case e.g. Web, Email, Chatbot etc."
            quote: true

          - name: ChatDuration
            description: "The total duration of the chat in seconds"
            quote: true

          - name: ContactId
            description: "[PII Data, this is a hashed value] The name of the contact that participated in the chat"
            quote: true

          - name: CreatedById
            description: "The user who created the transcript, including creation date and time (Read only)"
            quote: true

          - name: CreatedDate
            description: "The date and time the transcript was created (Read only)"
            quote: true

          - name: EndedBy
            description: "Indicates whether the visitor or the agent ended the chat"
            quote: true

          - name: EndTime
            description: "The time the chat ended"
            quote: true

          - name: Id
            description: "Autogenerated Salesforce Id of the record in this table. Standard field."
            quote: true

          - name: LastModifiedById
            description: "The user who last modified the transcript, including date and time (Read only)"
            quote: true

          - name: LastModifiedDate
            description: "The date and time the transcript was last modified (Read only)"
            quote: true

          - name: LeadId
            description: "The name of the lead that was generated by the chat or discussed during the chat"
            quote: true

          - name: LiveChatButtonId
            description: "The chat button that the visitor clicked to initiate the chat"
            quote: true

          - name: LiveChatDeploymentId
            description: "The deployment from which the visitor initiated the chat"
            quote: true

          - name: LiveChatVisitorId
            description: "Unique, numerical identifier automatically assigned to the visitor."
            quote: true

          - name: Live_Chat_Role__c
            description: "Role of agent handling the chat"
            quote: true

          - name: Location
            description: "The visitor's geographic location. Either the city and state, or city and country (if the visitor is located outside of the United States)."
            quote: true

          - name: MaxResponseTimeOperator
            description: "The maximum time it took an agent to respond to a chat visitor’s message"
            quote: true

          - name: MaxResponseTimeVisitor
            description: "The maximum time it took a customer to respond to an agent’s message"
            quote: true

          - name: Name
            description: "Unique identifier for chat record"
            quote: true

          - name: Notification_Country_Code__c
            description: "ISO country code used as market differentiator for Cases."
            quote: true

          - name: Notification_Language_Code__c
            description: "Language code, dependant on the notification country code."
            quote: true

          - name: Open_Time__c
            description: "The hour component of the transcript creation time, listed as a numeric value between 0 and 23."
            quote: true

          - name: OperatorMessageCount
            description: "The number of messages an agent sent during the chat"
            quote: true

          - name: OwnerId
            description: "The name of the transcript owner. By default, the owner is the user who originally created the transcript (for example, the agent who answered the chat)."
            quote: true

          - name: Platform
            description: "The user's operating system"
            quote: true

          - name: Priority__c
            description: "Priority assigned to the case, expressed as an integer value between 0 and 6."
            quote: true

          - name: ReferrerUri
            description: "Site the visitor was on before they came to your website. For example, if the visitor used Google to search for your support organization’s website, the referring site is Google."
            quote: true

          - name: RequestTime
            description: "The time that the visitor initially requested the chat"
            quote: true

          - name: SF_Bot_Agent_EndTime__c
            description: "The time when the angent transfer ended."
            quote: true

          - name: SF_Bot_Agent_Interaction__c
            description: "Indicates whether the customer interacted with the bot, the agent or both e.g. 'Bot only', 'Agent only', 'Hybrid'."
            quote: true

          - name: SF_Bot_Agent_StartTime__c
            description: "The time when the Agent accepted the transfer."
            quote: true

          - name: SF_Bot_Agent_TransferTime__c
            description: ""
            quote: true

          - name: SF_Bot_Bot_Name__c
            description: "The API name of the Bot used in the Chat."
            quote: true

          - name: SF_Bot_Bot_User__c
            description: "As of 02-08-2021, column contans null values only."
            quote: true

          - name: SF_Bot_Escalated_No_Agent__c
            description: "Agent Transfer requested but either no agent available or abandoned while waiting for the transfer."
            quote: true

          - name: SF_Bot_Escalated__c
            description: "Indicates if the Chat was escalated."
            quote: true

          - name: SF_Bot_FirstOwnerId__c
            description: "The ID of the first owner of the Chat Transcript."
            quote: true

          - name: SF_Bot_Goals_Before_Succes__c
            description: "The number of Goals that the customer went through before rating with a“Success”."
            quote: true

          - name: SF_Bot_Resolution__c
            description: "Indicates if at least one Goal has reached completion."
            quote: true

          - name: SF_Bot_Success__c
            description: "Indicates if at least one Goal of the Chat was rated with a “Success” by the customer."
            quote: true

          - name: SF_Bot_WaitTime__c
            description: "Wait time including Bot Transfer"
            quote: true

          - name: SkillId
            description: "Primary Skill is not used for chat and can be empty."
            quote: true

          - name: SkillLevel__c
            description: "Skill levels from the associated requirement"
            quote: true

          - name: Skills__c
            description: "Skill names from the associated requirement"
            quote: true

          - name: SLA__c
            description: "SLA time in number of hours, ranging between 1 and 120."
            quote: true

          - name: StartTime
            description: "The time the chat started."
            quote: true

          - name: Status
            description: "Completed or Missed. A missed chat was requested but not answered"
            quote: true

          - name: SupervisorTranscriptBody
            description: "Contains the whisper messages from supervisors"
            quote: true

          - name: UserAgent
            description: "A string that identifies the type of browser and operating system the visitor used"
            quote: true

          - name: VisitorMessageCount
            description: "The number of messages a visitor sent during the chat"
            quote: true

          - name: WaitTime
            description: "The total amount of time a chat request was waiting to be accepted by an agent"
            quote: true

          - name: Creation_Time__c
            description: "The time when the transcript was created, listed in HH:MM:SS format."
            quote: true

          - name: Viewer_Id__c
            description: "Viewer ID value copied from User table (combines EXT and non EXT values, whichever is actually used)"
            quote: true

          - name: Bot_Handling_Time__c
            description: "Duration of a customer interaction with the chatbot"
            quote: true

          - name: Queue_Time__c
            description: "Duration of time that a customer waits in queue to chat with an agent after escalation from the chatbot"
            quote: true

          - name: Device_Id__c
            description: "Unique identifier for the customer device used to contact CS"
            quote: true

      - name: CURATED__SALESFORCE__PAUSE_HISTORY
        description: "Saleforce Pause_History__c table"
        columns:
          - name: Account__c
            description: "Unique Account ID"
            quote: true

          - name: ActionPerformed__c
            description: "What action perfomed like Cancel subscription,Set Pause, Early resume etc"
            quote: true

          - name: CreatedDate
            description: "when the record created "
            quote: true

          - name: LastModifiedDate
            description: "when the record was modified "
            quote: true

          - name: Name
            description: "SFDC ID"
            quote: true

          - name: PauseFrom__c
            description: "Pause From date"
            quote: true

          - name: PauseUntil__c
            description: "Pause From until date"
            quote: true

          - name: Status__c
            description: "Status: Success, Unknown"
            quote: true

          - name: WorkflowId__c
            description: "Zuora WF reference"
            quote: true

          - name: Id
            description: "Autogenerated Salesforce Id of the record in this table. Standard field."
            quote: true

      - name: CURATED__SALESFORCE__SF_BOT_DIALOG_INSTANCE_C
        description: "Stores information about customer interaction with a bot, at both dialog and goal level."
        columns:
          - name: Chat_Transcript__c
            description: "Reference of related chat transcript"
            quote: true

          - name: CreatedById
            description: "The user who created the transcript, including creation date and time (Read only)"
            quote: true

          - name: CreatedDate
            description: "The date and time the transcript was created (Read only)"
            quote: true

          - name: Dialog_Name__c
            description: "The Dialog API name e.g. Transfer_To_Agent, Identification."
            quote: true

          - name: First_Dialog_Of_Goal__c
            description: "The API Name of the first Dialog the Goal had."
            quote: true

          - name: Goal_Escalated__c
            description: "Indicates whether the Goal escalated to an Agent or not."
            quote: true

          - name: Goal_Name__c
            description: "Inherited Goal API Name from the Bot Dialog object this Instance belongs to."
            quote: true

          - name: Id
            description: "Autogenerated Salesforce Id of the record in this table. Standard field."
            quote: true

          - name: Label__c
            description: "Dialog user-friendly name."
            quote: true

          - name: LastModifiedById
            description: "The user who last modified the transcript, including date and time (Read only)"
            quote: true

          - name: LastModifiedDate
            description: "The date and time the transcript was last modified (Read only)"
            quote: true

          - name: Last_Customer_Input__c
            description: "The last input of the customer in this Dialog. Populated by Bot’s Context variable \"Last Customer Input\"."
            quote: true

          - name: Last_Step__c
            description: "Keeps track of the customer’s progress in a Dialog."
            quote: true

          - name: Name
            description: "The API name of the Bot that this Dialog belongs to."
            quote: true

          - name: Origin__c
            description: "How this dialog was reached by the customer e.g. Navigation, Redirect, NLP."
            quote: true

          - name: OwnerId
            description: "References the user who owns the object."
            quote: true

          - name: Rating__c
            description: "Field populated only for \"Rating\" type Dialog, with values Success or Failure."
            quote: true

          - name: RecordTypeId
            description: "The ID of the RecordType record that is associated with this object i.e. Goal or Dialog."
            quote: true

          - name: Routable_ID__c
            description: "Context variable, the value of which is used to link to the LiveChatTranscript record."
            quote: true

          - name: Status__c
            description: "The status of the Dialog or Goal e.g. Escalated, Completed, Abandoned etc."
            quote: true

          - name: Triggering_Customer_Input__c
            description: "The customer input that triggered the NLP redirection."
            quote: true

          - name: Type__c
            description: "The Dialog type inherited from the Bot Dialog object that this instance belongs to e.g. Intermediate, Rating, Navigation."
            quote: true

      - name: CURATED__SALESFORCE__STATUS_OWNER_CAPTURE
        description: "Captures status changes of the related Case entity for Customer Sevice Cases"
        columns:
          - name: Action__c
            description: "Action triggering the case ownership change"
            quote: true

          - name: Active__c
            description: "The last status owner record is set to true. Set to false for previous ones. Stays true for 'Status closed'"
            quote: true

          - name: Agent__c
            description: "Salesforce user ID of the CS agent"
            quote: true

          - name: Case__c
            description: "Case ID from Case table"
            quote: true

          - name: CreatedById
            description: "Standard field. Id of the user (can be a user related to internal systems) who created the record in the table."
            quote: true

          - name: CreatedDate
            description: "Standard field. Date & time when record is created in the object"
            quote: true

          - name: CurrencyIsoCode
            description: "Standard field. Not in use"
            quote: true

          - name: CurrentStatus__c
            description: "Current status of the case"
            quote: true

          - name: EntryTime__c
            description: "Date & time the current action started"
            quote: true

          - name: EntryTimeHourGMT1__c
            description: "Used for reporting purposes. GMT + 1 hour"
            quote: true

          - name: EntryTimeHourGMT2__c
            description: "Used for reporting purposes. GMT + 2 hour"
            quote: true

          - name: ExitTime__c
            description: "Date & time the current action ended"
            quote: true

          - name: HandlingNumber__c
            description: "Used for reporting purposes"
            quote: true

          - name: Id
            description: "Autogenerated Salesforce Id of the record in this table. Standard field."
            quote: true

          - name: IsDeleted
            description: "Standard field, present in each object. Set to tre if record is in recycle bin. Not actively used."
            quote: true

          - name: LastModifiedById
            description: "Id of the user modifying the record. Standard field. Not used"
            quote: true

          - name: LastModifiedDate
            description: "Date record was last modified on. Standard field, not used"
            quote: true

          - name: Name
            description: "Autogenerated number"
            quote: true

          - name: Owner__c
            description: "Name of the CS agent in the given status owner"
            quote: true

          - name: OwnerType__c
            description: "Agent or Queue"
            quote: true

          - name: PreviousStatus__c
            description: "Previous status of the case"
            quote: true

          - name: Reason__c
            description: "Used by agents to add notes on status capture change"
            quote: true

          - name: ResponseCount__c
            description: "Counter for every time an agent sends an email to a customer."
            quote: true

          - name: SystemModstamp
            description: "Standard field. Not in use"
            quote: true

          - name: TotalTime__c
            description: "Total time ownership"
            quote: true

          - name: TotalTimeInMins__c
            description: "Duration in minutes of ownership"
            quote: true

          - name: TotalTimeInSeconds__c
            description: "Duration in seconds of ownership"
            quote: true

          - name: Type__c
            description: "Total time based on action"
            quote: true

      - name: CURATED__SALESFORCE__USER
        description: "Saleforce User table"
        columns:
          - name: AccountId
            description: "Unique Account ID, Account reference"
            quote: true

          - name: ViewerId__c
            description: "if ViewerID__c is null then ExternalID__c else ViewerID__c."
            quote: true

          - name: Viewer_Id__c
            description: "Viewer ID from Massive"
            quote: true

          - name: Id
            description: "Autogenerated Salesforce Id of the record in this table. Standard field."
            quote: true

          - name: Country
            description: "Country"
            quote: true

          - name: FirstName
            description: "First Name"
            quote: true

          - name: LastName
            description: "Last Name"
            quote: true

          - name: Email
            description: "Email"
            quote: true

          - name: UserRoleId
            description: "ID of Role applied to the account"
            quote: true

          - name: ProfileId
            description: "ID of Profile applied to the account"
            quote: true

          - name: Division
            description: "Division applied to the account"
            quote: true

          - name: UserRoleName
            description: "Name of Role applied to the account"
            quote: true

          - name: ProfileText__c
            description: "Name of Profile applied to the account"
            quote: true

      - name: CURATED__IDENTITY__MAPPING
        description: "Mapping Table which contains the realtionship between DAZN_ID and CRM_ID"
        columns:
          - name: DAZN_ID
            description: "User Id of all DAZN players"
            quote: true

          - name: CRM_ID
            description: "Unique Salesforce Id"
            quote: true

          - name: VIEWER_ID
            description: "TBD"
            quote: true
