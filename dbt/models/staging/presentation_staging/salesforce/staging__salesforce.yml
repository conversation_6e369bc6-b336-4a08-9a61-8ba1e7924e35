version: 2

models:
  - name: staging__salesforce__account
    description: "Staging data of the Open Salesforce Account table"
    columns:
      - name: crm_account_status
        description: "Account status also known as user status (e.g. Free Trial, Active Paid)"
        quote: true

      - name: is_opted_in_marketing
        description: "User is opted-in to marketing comms. All market except DE, this field is independent. For DE, it works with the Double opt in. NFL ticket box works independently"
        quote: true

      - name: campaign_id
        description: "Deprecated. Was part of Gift code v1 solution."
        quote: true

      - name: cancellation_reason
        description: "Reason for Cancellation "
        quote: true

      - name: crm_account_created_timestamp
        description: "Date the account was created in SF (no logic ot counter gut subscribe)"
        quote: true

      - name: gift_code_number
        description: "Gift code no"
        quote: true

      - name: gift_code_end_date
        description: "Gift code End date"
        quote: true

      - name: gift_code_start_date
        description: "Gift Start End date"
        quote: true

      - name: gift_code_status
        description: "Gift code status"
        quote: True

      - name: crm_account_id
        description: "Unique ID of the record (also known as Salesforce Account ID)"
        quote: true

      - name: crm_last_modified_timestamp
        description: "Datetime the record was last modified on (even for events that are not part of person account history)"
        quote: true

      - name: notification_country_code
        description: "Set from the same source as BillingCountryCode__c from DAZNUserFeed"
        quote: true

      - name: partner_id
        description: "is the partner ID. Used for Docomo "
        quote: true

      - name: partner_registration_status
        description: "Comes from User feed. Picklist: Complete, Incomplete, N/A"
        quote: true

      - name: has_pause_scheduled
        description: "Flag that indicates if pause has been scheduled"
        quote: true

      - name: pause_from_date
        description: "Start pause date. Drives functionality in My Account"
        quote: true

      - name: pause_scheduled_timestamp
        description: "Datetime when pause for scheduled"
        quote: true

      - name: pause_until_date
        description: "Pause until date"
        quote: true

      - name: eu_portability_status
        description: "Show EU portability status: Pending, Granted, Denied, n/a, Not determined"
        quote: true

      - name: subscription_date
        description: "start date of Subscription"
        quote: true

      - name: is_test_user
        description: "Flag set to true for test accounts, false to live accounts"
        quote: true

      - name: crm_account_type
        description: "Customer type indicating 3PP or special partnership deals Amazon,Google,Sky,Docomo etc"
        quote: true

      - name: person_contact_id
        description: "For person account, user and contact has 1-2-1 relationship. It links contact to account. Its used in SFMC"
        quote: true

      - name: billing_country_code
        description: "Country code set by MISL when account is created"
        quote: true

      - name: dazn_user_id
        description: "DAZN ID"
        quote: true

      - name: is_double_opt_in
        description: "DE only. Set to true if a customer confirms double opt-in wb page (legal requirement for marketing opt-in in DE)"
        quote: true

      - name: notification_language_code
        description: "Language code for UI, Set by MISL DAZNUserFeed__c table LanguageLocaleKey__c field, user can change via My Account"
        quote: true

      - name: previous_crm_account_status
        description: "Previous status to current account status"
        quote: true

      - name: is_opted_in_ads_personalisation
        description: "Set to true if customer opted out of Personalised Ads"
        quote: true

      - name: is_opted_in_nfl_marketing
        description: "True/False flag whether a customer opted in to received emails from NFL directly. Used to select recors whose contact details are then weekly passed on to NFL"
        quote: true

      - name: nfl_opt_in_change_date
        description: "Date on which a customer changed their NFL opt in status, in order to generate the NFL opt in report."
        quote: true

      - name: is_cancelled_account
        description: "Set to true if Zuora processed the cancellation, but the account is still active (till the end of the billing period)"
        quote: true

      - name: is_active_paid_soft_cancellation
        description: "Set to true if user cancelled while Active Paid. Used for flagging accounts for SFMC flow. Added 2019 March(tbc)"
        quote: true

      - name: is_free_trial_soft_cancellation
        description: "Set to true if user cancelled while in Free Trial state. Used for flagging accounts for SFMC flow. Added 2019 March(tbc)"
        quote: true

      - name: viewer_id
        description: "Viewer ID value copied from User table (combines EXT and non EXT values, whichever is actually used)"
        quote: true

      - name: dfd_first_name
        description: "Docomo first name (as the name is 'Docomo user' to prevent CS to see it)"
        quote: true

      - name: dfd_last_name
        description: "Docomo last name (as the name is 'Docomo user' to prevent CS to see it)"
        quote: true

      - name: person_email
        description: "Email of the customer. It's currently the DAZN user name apart from Docomo"
        quote: true

      - name: first_name
        description: "First Name of the customer relevant to the account"
        quote: true

      - name: last_name
        description: "Surname of the customer relevant to the account"
        quote: true

      - name: propensity_to_churn
        description: "Indicates when a customer has had a poor playback experience and will receive enhanced customer support to improve retention."
        quote: true

      - name: dazn_tv_box_added_date
        description: "Date that customer was flagged as a DAZN TV Box User"
        quote: true

      - name: dazn_tv_box_serial_number
        description: "Unique identifier for the DAZN TV Box"
        quote: true

      - name: is_dazn_tv_box_user
        description: "Tick box indicating if a customer is a DAZN TV Box User (1) or not (0)"
        quote: true

  - name: staging__salesforce__account_current
    description: "Current view Account Object"
    columns:
      - name: crm_account_status
        description: "Account status also known as user status (e.g. Free Trial, Active Paid)"
        quote: true

      - name: is_opted_in_marketing
        description: "User is opted-in to marketing comms. All market except DE, this field is independent. For DE, it works with the Double opt in. NFL ticket box works independently"
        quote: true

      - name: campaign_id
        description: "Deprecated. Was part of Gift code v1 solution."
        quote: true

      - name: cancellation_reason
        description: "Reason for Cancellation "
        quote: true

      - name: crm_account_created_timestamp
        description: "Date the account was created in SF (no logic ot counter gut subscribe)"
        quote: true

      - name: gift_code_number
        description: "Gift code no"
        quote: true

      - name: gift_code_end_date
        description: "Gift code End date"
        quote: true

      - name: gift_code_start_date
        description: "Gift Start End date"
        quote: true

      - name: gift_code_status
        description: "Gift code status"
        quote: True

      - name: crm_account_id
        description: "Unique ID of the record (also known as Salesforce Account ID)"
        quote: true

      - name: crm_last_modified_timestamp
        description: "Datetime the record was last modified on (even for events that are not part of person account history)"
        quote: true

      - name: notification_country_code
        description: "Set from the same source as BillingCountryCode__c from DAZNUserFeed"
        quote: true

      - name: partner_id
        description: "is the partner ID. Used for Docomo "
        quote: true

      - name: partner_registration_status
        description: "Comes from User feed. Picklist: Complete, Incomplete, N/A"
        quote: true

      - name: has_pause_scheduled
        description: "Flag that indicates if pause has been scheduled"
        quote: true

      - name: pause_from_date
        description: "Start pause date. Drives functionality in My Account"
        quote: true

      - name: pause_scheduled_timestamp
        description: "Datetime when pause for scheduled"
        quote: true

      - name: pause_until_date
        description: "Pause until date"
        quote: true

      - name: eu_portability_status
        description: "Show EU portability status: Pending, Granted, Denied, n/a, Not determined"
        quote: true

      - name: subscription_date
        description: "start date of Subscription"
        quote: true

      - name: is_test_user
        description: "Flag set to true for test accounts, false to live accounts"
        quote: true

      - name: crm_account_type
        description: "Customer type indicating 3PP or special partnership deals Amazon,Google,Sky,Docomo etc"
        quote: true

      - name: person_contact_id
        description: "For person account, user and contact has 1-2-1 relationship. It links contact to account. Its used in SFMC"
        quote: true

      - name: billing_country_code
        description: "Country code set by MISL when account is created"
        quote: true

      - name: dazn_user_id
        description: "DAZN ID"
        quote: true

      - name: is_double_opt_in
        description: "DE only. Set to true if a customer confirms double opt-in wb page (legal requirement for marketing opt-in in DE)"
        quote: true

      - name: notification_language_code
        description: "Language code for UI, Set by MISL DAZNUserFeed__c table LanguageLocaleKey__c field, user can change via My Account"
        quote: true

      - name: previous_crm_account_status
        description: "Previous status to current account status"
        quote: true

      - name: is_opted_in_ads_personalisation
        description: "Set to true if customer opted out of Personalised Ads"
        quote: true

      - name: is_opted_in_nfl_marketing
        description: "True/False flag whether a customer opted in to received emails from NFL directly. Used to select recors whose contact details are then weekly passed on to NFL"
        quote: true

      - name: nfl_opt_in_change_date
        description: "Date on which a customer changed their NFL opt in status, in order to generate the NFL opt in report."
        quote: true

      - name: is_cancelled_account
        description: "Set to true if Zuora processed the cancellation, but the account is still active (till the end of the billing period)"
        quote: true

      - name: is_active_paid_soft_cancellation
        description: "Set to true if user cancelled while Active Paid. Used for flagging accounts for SFMC flow. Added 2019 March(tbc)"
        quote: true

      - name: is_free_trial_soft_cancellation
        description: "Set to true if user cancelled while in Free Trial state. Used for flagging accounts for SFMC flow. Added 2019 March(tbc)"
        quote: true

      - name: viewer_id
        description: "Viewer ID value copied from User table (combines EXT and non EXT values, whichever is actually used)"
        quote: true

      - name: dfd_first_name
        description: "Docomo first name (as the name is 'Docomo user' to prevent CS to see it)"
        quote: true

      - name: dfd_last_name
        description: "Docomo last name (as the name is 'Docomo user' to prevent CS to see it)"
        quote: true

      - name: person_email
        description: "Email of the customer. It's currently the DAZN user name apart from Docomo"
        quote: true

      - name: first_name
        description: "First Name of the customer relevant to the account"
        quote: true

      - name: last_name
        description: "Surname of the customer relevant to the account"
        quote: true

      - name: propensity_to_churn
        description: "Indicates when a customer has had a poor playback experience and will receive enhanced customer support to improve retention."
        quote: true

      - name: dazn_tv_box_added_date
        description: "Date that customer was flagged as a DAZN TV Box User"
        quote: true

      - name: dazn_tv_box_serial_number
        description: "Unique identifier for the DAZN TV Box"
        quote: true

      - name: is_dazn_tv_box_user
        description: "Tick box indicating if a customer is a DAZN TV Box User (1) or not (0)"
        quote: true

  - name: staging__identity__mapping
    description: "Mapping Table which contains the realtionship between DAZN_ID and CRM_ID"
    columns:
      - name: dazn_user_id
        description: "User Id of all DAZN players"
        quote: true

      - name: crm_account_id
        description: "Unique Salesforce Id"
        quote: true

      - name: viewer_id
        description: "TBD"
        quote: true
