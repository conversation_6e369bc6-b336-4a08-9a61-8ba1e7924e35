
WITH source AS (
    SELECT * FROM {{ source('CURATED','curated__dots__gmr_article_current' )}}
)

, renamed AS (
    SELECT
        "DBT_INSERT_DTTS" AS "DBT_INSERT_DTTS"
        ,"EDM_INSERT_DTTS" AS "EDM_INSERT_DTTS"
        ,"payloadId" AS "payload_id"
        ,"payloadType" AS "payload_type"
        ,"payloadState" AS "payload_state"
        ,"payload" AS "payload"
        ,"lastUpdatedTime" AS "last_updated_timestamp"
        ,"isTest" AS "is_test"
        ,"envelopeSchemaVersion" AS "envelope_schema_version"
        ,"guid" AS "article_id"
        ,"entity" AS "article_entity"
        ,"action" AS "article_action"
        ,"updated__timestamp" AS "article_updated_timestamp"
        ,"title" AS "article_title"
        ,"startTime__timestamp" AS "article_start_timestamp"
        ,"endTime__timestamp" AS "article_end_timestamp"
        ,"aggregatedStartTime__timestamp" AS "article_aggregated_start_timestamp"
        ,"publishDate__timestamp" AS "article_publish_timestamp"
        ,"unpublishDate__timestamp" AS "article_unpublish_timestamp"
        ,"territory" AS "article_territory"
        ,"country" AS "article_country"
        ,"country_titleLocalised" AS "article_country_title_localised"
        ,"description" AS "article_description"
        ,"articleType" AS "article_type"
        ,"articleType_guid" AS "article_type_id"
        ,"articleType_name" AS "article_type_name"
        ,"competition" AS "competition"
        ,"competition_guid" AS "competition_id"
        ,"competition_titleLocalised" AS "competition_title_localised"
        ,"contestants" AS "contestants"
        ,"contestant_0_guid" AS "contestant_0_id"
        ,"contestant_0_titleLocalised" AS "contestant_0_title_localised"
        ,"contestant_1_guid" AS "contestant_1_id"
        ,"contestant_1_titleLocalised" AS "contestant_1_title_localised"
        ,"contestant_2_guid" AS "contestant_2_id"
        ,"contestant_2_titleLocalised" AS "contestant_2_title_localised"
        ,"fixture" AS "fixture"
        ,"fixture_guid" AS "fixture_id"
        ,"fixture_startTime__timestamp" AS "fixture_start_timestamp"
        ,"fixture_status" AS "fixture_status"
        ,"fixture_titleLocalised" AS "fixture_title_localised"
        ,"sport" AS "sport"
        ,"sport_guid" AS "sport_id"
        ,"sport_titleLocalised" AS "sport_title_localised"
        ,"ruleset" AS "ruleset"
        ,"ruleset_guid" AS "ruleset_id"
        ,"ruleset_titleLocalised" AS "ruleset_title_localised"
        ,"tournament" AS "tournament"
        ,"tournament_guid" AS "tournament_id"
        ,"tournament_titleLocalised" AS "tournament_title_localised"
        ,"tournament_startDate__timestamp" AS "tournament_start_timestamp"
        ,"tournament_endDate__timestamp" AS "tournament_end_timestamp"
        ,"venue" AS "venue"
        ,"venue_guid" AS "venue_id"
        ,"venue_titleLocalised" AS "venue_title_localised"
        ,"stage" AS "stage"
        ,"stage_guid" AS "stage_id"
        ,"stage_titleLocalised" AS "stage_title_localised"
        ,"stage_startDate__timestamp" AS "stage_start_timestamp"
        ,"stage_endDate__timestamp" AS "stage_end_timestamp"
        ,"liveMedia_daznChannelId" AS "live_media_dazn_channel_id"
        ,"liveMedia_fixture" AS "live_media_fixture"
        ,"liveMedia_liveStream_start__timestamp" AS "live_media_live_stream_start_timestamp"
        ,"liveMedia_liveStream_end__timestamp" AS "live_media_live_stream_end_timestamp"
        ,"liveMedia_outlet_id" AS "live_media_outlet_id"
        ,"liveMedia_title" AS "live_media_title"
        ,"liveMedia_txEventTypeName" AS "live_media_tx_event_type_name"
        ,"liveMedia_type" AS "live_media_type"
        ,"liveMedia_videoType" AS "live_media_video_type"
        ,"productValue" AS "product_value"
        ,"productValue_weight" AS "product_value_weight"
        ,"entitlementIds" AS "article_entitlement_ids"
        ,"language" AS "article_language"
        ,"categories" AS "article_categories"
        ,"countryAvailability" AS "article_country_availability"
        ,"relatedArticleGuid" AS "related_article_id"
        ,"ageRestricted" AS "article_is_age_restricted"
        ,"hideInUi" AS "article_is_hide_in_ui"
        ,"images" AS "article_images"
        ,"article_media_id" AS "article_media_id" 
        ,"is_content_freemium" AS "is_content_freemium"
        ,"is_content_b2b" AS "is_content_b2b"
        ,"is_content_b2c" AS "is_content_b2c"
        ,"is_content_linear" AS "is_content_linear"
        ,"article_format" AS "article_format"
        ,CASE 
            WHEN "payload":"categories" ILIKE '%Play4Free%' THEN TRUE
            ELSE FALSE
        END AS "is_play4free"
    FROM source
)

select * from renamed
