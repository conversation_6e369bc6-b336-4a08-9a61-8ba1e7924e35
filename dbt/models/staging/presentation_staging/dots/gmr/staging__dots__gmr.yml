version: 2
models:
  - name: staging__dots__gmr_article_current
    description: ""
    columns:
      - name: DBT_INSERT_DTTS
        description: "Date/Timestamp the record was inserted by dbt to the curated layer"
        quote: true

      - name: EDM_INSERT_DTTS
        description: "Date/Timestamp the record was inserted by the ingest process into the open layer"
        quote: true

      - name: payload_id
        description: "A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message."
        quote: true

      - name: payload_type
        description: "A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers."
        quote: true

      - name: payload_state
        description: "An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?"
        quote: true

      - name: payload
        description: "An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer."
        quote: true

      - name: last_updated_timestamp
        description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
        quote: true

      - name: is_test
        description: "This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false."
        quote: true

      - name: envelope_schema_version
        description: "The version of the envelope schema. This version must be 2."
        quote: true

      - name: article_id
        description: "The ID of the article"
        quote: true

      - name: article_entity
        description: "TBC"
        quote: true

      - name: article_action
        description: "TBC"
        quote: true

      - name: article_updated_timestamp
        description: "The last time the article was updated (converted to a TIMESTAMP)"
        quote: true

      - name: article_title
        description: "The title of the article"
        quote: true

      - name: article_start_timestamp
        description: "The start time of the article (converted to a TIMESTAMP)"
        quote: true

      - name: article_end_timestamp
        description: "The end time of the article (converted to a TIMESTAMP)"
        quote: true

      - name: article_aggregated_start_timestamp
        description: "TBC"
        quote: true

      - name: article_publish_timestamp
        description: "TBC"
        quote: true

      - name: article_unpublish_timestamp
        description: "TBC"
        quote: true

      - name: article_territory
        description: "The territory of the article"
        quote: true

      - name: article_country
        description: "TBC"
        quote: true

      - name: article_country_title_localised
        description: "TBC"
        quote: true

      - name: article_description
        description: "The description of the article"
        quote: true

      - name: article_type
        description: "Array containing metadata about the type of the article"
        quote: true

      - name: article_type_id
        description: "The ID of the type of the article"
        quote: true

      - name: article_type_name
        description: "The name of the type of the article"
        quote: true

      - name: competition
        description: "An array containing metadata about the competition directly relating to the article, if any"
        quote: true

      - name: competition_id
        description: "The ID of the competition directly relating to the article, if any"
        quote: true

      - name: competition_title_localised
        description: "The localised title of the competition directly relating to the article, if any"
        quote: true

      - name: contestants
        description: "An array containing metadata about the contestants directly relating to the article, if any"
        quote: true

      - name: contestant_0_id
        description: "The ID of the first contestant in the contestants array, if any"
        quote: true

      - name: contestant_0_title_localised
        description: "The localised title of the first contestant in the contestants array, if any"
        quote: true

      - name: contestant_1_id
        description: "The ID of the second contestant in the contestants array, if any"
        quote: true

      - name: contestant_1_title_localised
        description: "The localised second of the first contestant in the contestants array, if any"
        quote: true

      - name: contestant_2_id
        description: "The ID of the third contestant in the contestants array, if any"
        quote: true

      - name: contestant_2_title_localised
        description: "The localised third of the first contestant in the contestants array, if any"
        quote: true

      - name: fixture
        description: "An array containing metadata about the fixture directly relating to the article, if any"
        quote: true

      - name: fixture_id
        description: "The ID of the fixture directly relating to the article, if any (will match to the MFL Fixture ID)"
        quote: true

      - name: fixture_start_timestamp
        description: "The unixtimestamp of the start of the fixture directly relating to the article, if any"
        quote: true

      - name: fixture_status
        description: "The status of the fixture directly relating to the article, if any"
        quote: true

      - name: fixture_title_localised
        description: "The localised title of the fixture directly relating to the article, if any"
        quote: true

      - name: sport
        description: "An array containing metadata about the sport directly relating to the article, if any"
        quote: true

      - name: sport_id
        description: "The ID of the sport directly relating to the article, if any (will match to the MFL Sport ID)"
        quote: true

      - name: sport_title_localised
        description: "The localised title of the sport directly relating to the article, if any"
        quote: true

      - name: ruleset
        description: "An array containing metadata about the ruleset directly relating to the article, if any"
        quote: true

      - name: ruleset_id
        description: "The ID of the ruleset directly relating to the article, if any (will match to the MFL Ruleset ID)"
        quote: true

      - name: ruleset_title_localised
        description: "The localised title of the ruleset directly relating to the article, if any"
        quote: true

      - name: tournament
        description: "An array containing metadata about the tournament directly relating to the article, if any"
        quote: true

      - name: tournament_id
        description: "The ID of the tournament directly relating to the article, if any (will match to the MFL Tournament Calendar ID)"
        quote: true

      - name: tournament_title_localised
        description: "The localised title of the tournament directly relating to the article, if any"
        quote: true

      - name: tournament_start_timestamp
        description: "The converted timestamp of the start of the tournament directly relating to the article, if any"
        quote: true

      - name: tournament_end_timestamp
        description: "The converted timestamp of the end of the tournament directly relating to the article, if any"
        quote: true

      - name: venue
        description: "An array containing metadata about the venue directly relating to the article, if any"
        quote: true

      - name: venue_id
        description: "The ID of the venue directly relating to the article, if any (will match to the MFL Venue ID)"
        quote: true

      - name: venue_title_localised
        description: "The localised title of the venue directly relating to the article, if any"
        quote: true

      - name: stage
        description: "An array containing metadata about the stage directly relating to the article, if any"
        quote: true

      - name: stage_id
        description: "The ID of the stage directly relating to the article, if any (will match to the MFL Stage ID)"
        quote: true

      - name: stage_title_localised
        description: "The localised title of the stage directly relating to the article, if any"
        quote: true

      - name: stage_start_timestamp
        description: "The timestamp of the start of the stage directly relating to the article, if any"
        quote: true

      - name: stage_end_timestamp
        description: "The timestamp of the start of the stage directly relating to the article, if any"
        quote: true

      - name: live_media_dazn_channel_id
        description: "TBC"
        quote: true

      - name: live_media_fixture
        description: "TBC"
        quote: true

      - name: live_media_live_stream_start_timestamp
        description: "TBC"
        quote: true

      - name: live_media_live_stream_end_timestamp
        description: "TBC"
        quote: true

      - name: live_media_outlet_id
        description: "TBC"
        quote: true

      - name: live_media_title
        description: "TBC"
        quote: true

      - name: live_media_tx_event_type_name
        description: "TBC"
        quote: true

      - name: live_media_type
        description: "TBC"
        quote: true

      - name: live_media_video_type
        description: "TBC"
        quote: true

      - name: product_value
        description: "TBC"
        quote: true

      - name: product_value_weight
        description: "TBC"
        quote: true

      - name: article_entitlement_ids
        description: "The entitlement IDs needed to watch the article"
        quote: true

      - name: article_language
        description: "TBC"
        quote: true

      - name: article_categories
        description: "TBC"
        quote: true

      - name: article_country_availability
        description: "An array of countries this article is available to watch in"
        quote: true

      - name: related_article_id
        description: "TBC"
        quote: true

      - name: article_is_age_restricted
        description: "Boolean, true if the article is age restricted"
        quote: true

      - name: article_is_hide_in_ui
        description: "Boolean, true if the article is hidden in the UI/platform"
        quote: true

      - name: article_images
        description: "An array containing metadata about the images on the article"
        quote: true

      - name: article_media_id
        description: "Article media id"
        quote: true

      - name: is_content_freemium
        description: "Boolean, true if the article is freemium"
        quote: true

      - name: is_content_b2b
        description: "Boolean, true if the article is b2b"
        quote: true

      - name: is_content_b2c
        description: "Boolean, true if the article is b2c"
        quote: true

      - name: is_content_linear
        description: "Boolean, true if the article is linear"
        quote: true

      - name: article_format
        description: "article formate wethere it's Short Highlights or Condensed or NA"
        quote: true

      - name: is_play4free
        description: "Boolean, true if the article is play4free"
        quote: true
