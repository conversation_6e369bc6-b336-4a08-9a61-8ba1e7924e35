version: 2
sources:
  - name: CURATED
    database: TRANSFORMATION_{{ env_var('SNOWFLAKE_VAR_ENV') }}
    tables:
      - name: curated__dots__gmr_article_current
        description: "GMR article dimension. From DOTS data source"
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by dbt to the curated layer"
            quote: true
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by the ingest process into the open layer"
            quote: true
          - name: payloadId
            description: "A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message."
            quote: true
          - name: payloadType
            description: "A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers."
            quote: true
          - name: payloadState
            description: "An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?"
            quote: true
          - name: payload
            description: "An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer."
            quote: true
          - name: lastUpdatedTime
            description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
            quote: true
          - name: isTest
            description: "This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false."
            quote: true
          - name: envelopeSchemaVersion
            description: "The version of the envelope schema. This version must be 2."
            quote: true
          - name: guid
            description: "The ID of the article"
            quote: true
          - name: entity
            description: "TBC"
            quote: true
          - name: action
            description: "TBC"
            quote: true
          - name: updated
            description: "The last time the article was updated (unixtimestamp)"
            quote: true
          - name: updated__timestamp
            description: "The last time the article was updated (converted to a TIMESTAMP)"
            quote: true
          - name: title
            description: "The title of the article"
            quote: true
          - name: startTime
            description: "The start time of the article (unixtimestamp)"
            quote: true
          - name: startTime__timestamp
            description: "The start time of the article (converted to a TIMESTAMP)"
            quote: true
          - name: endTime
            description: "The end time of the article (unixtimestamp)"
            quote: true
          - name: endTime__timestamp
            description: "The end time of the article (converted to a TIMESTAMP)"
            quote: true
          - name: aggregatedStartTime
            description: "TBC"
            quote: true
          - name: aggregatedStartTime__timestamp
            description: "TBC"
            quote: true
          - name: publishDate
            description: "TBC"
            quote: true
          - name: publishDate__timestamp
            description: "TBC"
            quote: true
          - name: unpublishDate
            description: "TBC"
            quote: true
          - name: unpublishDate__timestamp
            description: "TBC"
            quote: true
          - name: territory
            description: "The territory of the article"
            quote: true
          - name: country
            description: "TBC"
            quote: true
          - name: country_titleLocalised
            description: "TBC"
            quote: true
          - name: description
            description: "The description of the article"
            quote: true
          - name: articleType
            description: "Array containing metadata about the type of the article"
            quote: true
          - name: articleType_guid
            description: "The guid of the type of the article"
            quote: true
          - name: articleType_name
            description: "The name of the type of the article"
            quote: true
          - name: competition
            description: "An array containing metadata about the competition directly relating to the article, if any"
            quote: true
          - name: competition_guid
            description: "The ID of the competition directly relating to the article, if any"
            quote: true
          - name: competition_titleLocalised
            description: "The localised title of the competition directly relating to the article, if any"
            quote: true
          - name: contestants
            description: "An array containing metadata about the contestants directly relating to the article, if any"
            quote: true
          - name: contestant_0_guid
            description: "The ID of the first contestant in the contestants array, if any"
            quote: true
          - name: contestant_0_titleLocalised
            description: "The localised title of the first contestant in the contestants array, if any"
            quote: true
          - name: contestant_1_guid
            description: "The ID of the second contestant in the contestants array, if any"
            quote: true
          - name: contestant_1_titleLocalised
            description: "The localised second of the first contestant in the contestants array, if any"
            quote: true
          - name: contestant_2_guid
            description: "The ID of the third contestant in the contestants array, if any"
            quote: true
          - name: contestant_2_titleLocalised
            description: "The localised third of the first contestant in the contestants array, if any"
            quote: true
          - name: fixture
            description: "An array containing metadata about the fixture directly relating to the article, if any"
            quote: true
          - name: fixture_guid
            description: "The ID of the fixture directly relating to the article, if any (will match to the MFL Fixture ID)"
            quote: true
          - name: fixture_startTime
            description: "The unixtimestamp of the start of the fixture directly relating to the article, if any"
            quote: true
          - name: fixture_startTime__timestamp
            description: "The converted timestamp of the start of the fixture directly relating to the article, if any"
            quote: true
          - name: fixture_status
            description: "The status of the fixture directly relating to the article, if any"
            quote: true
          - name: fixture_titleLocalised
            description: "The localised title of the fixture directly relating to the article, if any"
            quote: true
          - name: sport
            description: "An array containing metadata about the sport directly relating to the article, if any"
            quote: true
          - name: sport_guid
            description: "The ID of the sport directly relating to the article, if any (will match to the MFL Sport ID)"
            quote: true
          - name: sport_titleLocalised
            description: "The localised title of the sport directly relating to the article, if any"
            quote: true
          - name: ruleset
            description: "An array containing metadata about the ruleset directly relating to the article, if any"
            quote: true
          - name: ruleset_guid
            description: "The ID of the ruleset directly relating to the article, if any (will match to the MFL Ruleset ID)"
            quote: true
          - name: ruleset_titleLocalised
            description: "The localised title of the ruleset directly relating to the article, if any"
            quote: true
          - name: tournament
            description: "An array containing metadata about the tournament directly relating to the article, if any"
            quote: true
          - name: tournament_guid
            description: "The ID of the tournament directly relating to the article, if any (will match to the MFL Tournament Calendar ID)"
            quote: true
          - name: tournament_titleLocalised
            description: "The localised title of the tournament directly relating to the article, if any"
            quote: true
          - name: tournament_startDate
            description: "The unixtimestamp of the start of the tournament directly relating to the article, if any"
            quote: true
          - name: tournament_startDate__timestamp
            description: "The converted timestamp of the start of the tournament directly relating to the article, if any"
            quote: true
          - name: tournament_endDate
            description: "The unixtimestamp of the end of the tournament directly relating to the article, if any"
            quote: true
          - name: tournament_endDate__timestamp
            description: "The converted timestamp of the end of the tournament directly relating to the article, if any"
            quote: true
          - name: venue
            description: "An array containing metadata about the venue directly relating to the article, if any"
            quote: true
          - name: venue_guid
            description: "The ID of the venue directly relating to the article, if any (will match to the MFL Venue ID)"
            quote: true
          - name: venue_titleLocalised
            description: "The localised title of the venue directly relating to the article, if any"
            quote: true
          - name: stage
            description: "An array containing metadata about the stage directly relating to the article, if any"
            quote: true
          - name: stage_guid
            description: "The ID of the stage directly relating to the article, if any (will match to the MFL Stage ID)"
            quote: true
          - name: stage_titleLocalised
            description: "The localised title of the stage directly relating to the article, if any"
            quote: true
          - name: stage_startDate
            description: "The unixtimestamp of the start of the stage directly relating to the article, if any"
            quote: true
          - name: stage_startDate__timestamp
            description: "The timestamp of the start of the stage directly relating to the article, if any"
            quote: true
          - name: stage_endDate
            description: "The unixtimestamp of the end of the stage directly relating to the article, if any"
            quote: true
          - name: stage_endDate__timestamp
            description: "The timestamp of the start of the stage directly relating to the article, if any"
            quote: true
          - name: liveMedia_daznChannelId
            description: "TBC"
            quote: true
          - name: liveMedia_fixture
            description: "TBC"
            quote: true
          - name: liveMedia_liveStream_start
            description: "TBC"
            quote: true
          - name: liveMedia_liveStream_start__timestamp
            description: "TBC"
            quote: true
          - name: liveMedia_liveStream_end
            description: "TBC"
            quote: true
          - name: liveMedia_liveStream_end__timestamp
            description: "TBC"
            quote: true
          - name: liveMedia_outlet_id
            description: "TBC"
            quote: true
          - name: liveMedia_title
            description: "TBC"
            quote: true
          - name: liveMedia_txEventTypeName
            description: "TBC"
            quote: true
          - name: liveMedia_type
            description: "TBC"
            quote: true
          - name: liveMedia_videoType
            description: "TBC"
            quote: true
          - name: productValue
            description: "TBC"
            quote: true
          - name: productValue_weight
            description: "TBC"
            quote: true
          - name: entitlementIds
            description: "The entitlement IDs needed to watch the article"
            quote: true
          - name: language
            description: "TBC"
            quote: true
          - name: categories
            description: "TBC"
            quote: true
          - name: countryAvailability
            description: "An array of countries this article is available to watch in"
            quote: true
          - name: relatedArticleGuid
            description: "TBC"
            quote: true
          - name: ageRestricted
            description: "Boolean, true if the article is age restricted"
            quote: true
          - name: hideInUi
            description: "Boolean, true if the article is hidden in the UI/platform"
            quote: true
          - name: images
            description: "An array containing metadata about the images on the article"
            quote: true

          - name: article_media_id
            description: "Article media id"
            quote: true

          - name: is_content_freemium
            description: "Boolean, true if the article is freemium"
            quote: true

          - name: is_content_b2b
            description: "Boolean, true if the article is b2b"
            quote: true

          - name: is_content_b2c
            description: "Boolean, true if the article is b2c"
            quote: true

          - name: is_content_linear
            description: "Boolean, true if the article is linear"
            quote: true

          - name: article_format
            description: "article formate wethere it's Short Highlights or Condensed or NA"
            quote: true
