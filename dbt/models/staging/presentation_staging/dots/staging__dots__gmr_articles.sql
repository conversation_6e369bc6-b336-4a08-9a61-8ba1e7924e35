WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__DOTS__GMR_ARTICLES') }}
)

, renamed AS (
    SELECT
        "payloadId" AS "payload_id"
        ,"lastUpdatedTime" AS "record_last_updated_timestamp"
        ,"payloadType" AS "payload_type"
        ,"payloadState" AS "payload_is_active_state"
        ,"payload" AS "payload"
        ,"ttl" AS "ttl"
        ,"isTest" AS "is_test"
        ,"envelopeSchemaVersion" AS "envelope_schema_version"
        ,"payload__action" AS "gmr_articles_action"
        ,"payload__entitlement_ids" AS "gmr_articles_entitlement_ids"
        ,"payload__entity" AS "gmr_articles_entity"
        ,"payload__guid" AS "gmr_articles_article_id"
        ,"payload__updated" AS "gmr_articles_article_updated_timestamp"
    FROM source
)

SELECT * FROM renamed
