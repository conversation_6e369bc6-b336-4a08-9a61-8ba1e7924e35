
WITH source AS (
    SELECT * FROM {{ source('CURATED','curated__dots__won_liveencoding_current' )}}
)

, renamed AS (
    SELECT
        -- The macro is defined in dbt/macros/Dots/won_payload_columns.sql
        {{ won_payload_columns() }}
        ,"liveEncodingId" AS "live_encoding_id"
        ,"fixtureUuid" AS "fixture_id"
        ,"title" AS "live_encoding_title"
        ,"status" AS "live_encoding_status"
        ,"eventStartUTC__timestamp" AS "live_encoding_event_start_timestamp"
        ,"eventEndUTC__timestamp" AS "live_encoding_event_end_timestamp"
        ,"timeAllocations" AS "live_encoding_time_allocations"
    FROM source
)

select * from renamed
