
WITH source AS (
    SELECT * FROM {{ source('CURATED','curated__dots__won_vodproduct_current' )}}
)

, renamed AS (
    SELECT
        -- The macro is defined in dbt/macros/Dots/won_payload_columns.sql
        {{ won_payload_columns() }}
        ,"vodProductId" AS "vod_product_id"
        ,"title" AS "vod_product_title"
        ,"productType" AS "vod_product_type"
        ,"fixtureUuid" AS "fixture_id"
        ,"productVersion" AS "vod_product_version"
        ,"competition" AS "competition"
        ,"competitionUuid__coalesce" AS "competition_id"
        ,"competitionName__coalesce" AS "competition_name"
        ,"rulesetName" AS "vod_product_ruleset_name"
        ,"location" AS "vod_product_location"
        ,"tournamentCalendarUuid" AS "tournament_calendar_id"
        ,"contestantUuids" AS "contestant_ids"
        ,"sportUuid__coalesce" AS "sport_id"
        ,"sportName__coalesce" AS "sport_name"
        ,"contentDistinctionUuid__coalesce" AS "vod_product_content_distinction_id"
        ,"contentDistinction_name__coalesce" AS "vod_product_content_distinction_name"
        ,"sourceLanguage" AS "vod_product_source_language"
        ,"closedCaptionsAvailable" AS "vod_product_has_closed_captions_available"
        ,"closedCaptionsLanguage" AS "vod_product_closed_captions_language"
        ,"arrivalMethod" AS "vod_product_arrival_method"
        ,"synopsis" AS "vod_product_synopsis"
        ,"expectedDuration" AS "vod_product_expected_duration"
        ,"contractualCompliance" AS "vod_product_contractual_compliance"
    FROM source
)

select * from renamed
