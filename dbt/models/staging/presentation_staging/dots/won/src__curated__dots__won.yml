version: 2
sources:
  - name: CURATED
    database: TRANSFORMATION_PROD
    tables:
      - name: curated__dots__won_liveproduct_current
        description: "The currrent version of the live Product envelope contains the meta data describing the full-game product. The product contains generic info that is re-used for the relevant scheduled full-game events"
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by dbt to the curated layer"
            quote: true
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by the ingest process into the open layer"
            quote: true
          - name: lastUpdatedTime
            description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
            quote: true
          - name: isTest
            description: "This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false."
            quote: true
          - name: envelopeSchemaVersion
            description: "The version of the envelope schema. This version must be 2."
            quote: true
          - name: payloadId
            description: "A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message."
            quote: true
          - name: payloadType
            description: "A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers."
            quote: true
          - name: payloadState
            description: "An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?"
            quote: true
          - name: payload
            description: "An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer."
            quote: true
          - name: changeTime
            description: "Last change time when updated from WON (STRING version)"
            quote: true
          - name: changeTime__timestamp
            description: "Last change time when updated from WON (Converted to TIMESTAMP when possible)"
            quote: true
          - name: changeOwner
            description: "Source specific email of the user who stimulated the change"
            quote: true
          - name: changeSource
            description: "Defines what stimulated the update"
            quote: true
          - name: liveProductId
            description: "Unique ID of the WON product"
            quote: true
          - name: title
            description: "Description of Product"
            quote: true
          - name: productType
            description: "The type of product (E.g. Series, Episode, ...)"
            quote: true
          - name: fixtureUuid
            description: "ID of the related MFL fixture"
            quote: true
          - name: competitionUuid
            description: "ID of the related MFL competition"
            quote: true
          - name: rulesetName
            description: "Name of the related MFL ruleset"
            quote: true
          - name: tournamentCalendarUuid
            description: "ID of the related MFL tournament calendar"
            quote: true
          - name: worldFeedLanguages
            description: "Worls feed languages on the source video received by DAZN"
            quote: true
          - name: worldFeedLanguages_code
            description: "The first value from the worldFeedLanguages array representing the primary Worls feed language on the source video received by DAZN"
            quote: true
          - name: sportUuid
            description: "ID of the related MFL sport"
            quote: true
          - name: sportName
            description: "Name of the related MFL sport"
            quote: true
          - name: supportTier
            description: "Support Tier from WON"
            quote: true
          - name: orderOfPlay
            description: "Order of Play from WON"
            quote: true
          - name: expectedDuration
            description: "Expected duration of the live event"
            quote: true
          - name: contractualCompliance
            description: "List of keys of prohibited ad types defined by rights compliance"
            quote: true
      - name: curated__dots__won_vodright_current
        description: ""
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by dbt to the curated layer"
            quote: true
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by the ingest process into the open layer"
            quote: true
          - name: lastUpdatedTime
            description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
            quote: true
          - name: isTest
            description: "This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false."
            quote: true
          - name: envelopeSchemaVersion
            description: "The version of the envelope schema. This version must be 2."
            quote: true
          - name: payloadId
            description: "A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message."
            quote: true
          - name: payloadType
            description: "A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers."
            quote: true
          - name: payloadState
            description: "An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?"
            quote: true
          - name: payload
            description: "An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer."
            quote: true
          - name: changeTime
            description: "Last change time when updated from WON (STRING version)"
            quote: true
          - name: changeTime__timestamp
            description: "Last change time when updated from WON (Converted to TIMESTAMP when possible)"
            quote: true
          - name: changeOwner
            description: "Source specific email of the user who stimulated the change"
            quote: true
          - name: changeSource
            description: "Defines what stimulated the update"
            quote: true
          - name: rightId
            description: "Unique id of this right in WON i.e. External Reference"
            quote: true
          - name: title
            description: "Title of the linked product"
            quote: true
          - name: startDate
            description: "Start date of the exploitation right"
            quote: true
          - name: endDate
            description: "End date of the exploitation right"
            quote: true
          - name: type
            description: "The type of the right E.g. OTT, ..."
            quote: true
          - name: regionUuid
            description: "ID that represents the DAZN region and MCS Product"
            quote: true
          - name: productId
            description: "Reference ID to the associated WON product"
            quote: true
          - name: productVersion
            description: "Indicates if the rights are relevant to Non-Live and/or Highlights"
            quote: true
          - name: productVersions
            description: "Indicates if the rights are relevant to Non-Live and/or Highlights"
            quote: true
          - name: productVersions__coalesce
            description: "Indicates if the rights are relevant to Non-Live and/or Highlights, created using the COALESCE of the two productVersion and productVersions fields, needed as there was a change of the field name"
            quote: true
          - name: runCount
            description: "Number of runs (aka commitments) allowed for the given exploitation right"
            quote: true
          - name: status
            description: "The status of the exploitation right"
            quote: true
          - name: tournamentCalendarUuid
            description: "ID of the related MFL tournament calendar"
            quote: true
          - name: competitionUuid
            description: "ID of the related MFL competition"
            quote: true
          - name: contractId
            description: "Reference ID for exploitation right's parent contract"
            quote: true
          - name: ftvAllowed
            description: "Boolean value that indicates whether live events for the given right can be offered as 'free to view'"
            quote: true
          - name: downloadAllowed
            description: "Boolean value that indicates whether live events for the given right can be offered as 'downloadable'"
            quote: true
          - name: b2bAllowed
            description: "Boolean value indicating whether the rights are allowed for DAZN's B2B product"
            quote: true
          - name: b2cAllowed
            description: "Boolean value indicating whether the rights are allowed for DAZN's B2C product"
            quote: true
          - name: b2bCommercialPremises
            description: "An array of commercial premise types that are allowed to receive these rights"
            quote: true
          - name: CommercialPremises
            description: "An array of commercial premise types that are allowed to receive these rights"
            quote: true
          - name: clearedForDaznPlayer
            description: "Boolean value that indicates if the content can be re-used on the DAZN Player product"
            quote: true
          - name: clearedForSocial
            description: "Boolean value that indicates if the content can be re-used on social media"
            quote: true
          - name: preRecorded
            description: "Boolean value that indicates if the content has been pre-recorded"
            quote: true
          - name: disallowedCountryCodes
            description: "An array of countries blocked from consuming events for the given rights"
            quote: true
          - name: allowedAudioLanguages
            description: "An array of audio languages allowed to be applied against the event"
            quote: true
          - name: allowedCountryCodes
            description: "A list of countries allowed to consume events for the given rights"
            quote: true
          - name: allowedCountryCodes_0
            description: "The first value of the array in field allowedCountryCodes representing countries allowed to consume events for the given rights"
            quote: true
          - name: allowedCountryCodes_1
            description: "The second value of the array in field allowedCountryCodes representing countries allowed to consume events for the given rights"
            quote: true
          - name: allowedCountryCodes_2
            description: "The third value of the array in field allowedCountryCodes representing countries allowed to consume events for the given rights"
            quote: true
          - name: nonExclusiveRegions
            description: "A list of non exclusive regions for the given right"
            quote: true
          - name: nonExclusiveRegions_0
            description: "The first value of the array in field nonExclusiveRegions representing non exclusive regions for the given right"
            quote: true
          - name: nonExclusiveRegions_1
            description: "The second value of the array in field nonExclusiveRegions representing non exclusive regions for the given right"
            quote: true
          - name: nonExclusiveRegions_2
            description: "The third value of the array in field nonExclusiveRegions representing non exclusive regions for the given right"
            quote: true
      - name: curated__dots__won_liveevent_current
        description: "The current version of the WON meta data describing a scheduled live event in a designated DAZN Region"
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by dbt to the curated layer"
            quote: true
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by the ingest process into the open layer"
            quote: true
          - name: lastUpdatedTime
            description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
            quote: true
          - name: isTest
            description: "This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false."
            quote: true
          - name: envelopeSchemaVersion
            description: "The version of the envelope schema. This version must be 2."
            quote: true
          - name: payloadId
            description: "A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message."
            quote: true
          - name: payloadType
            description: "A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers."
            quote: true
          - name: payloadState
            description: "An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?"
            quote: true
          - name: payload
            description: "An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer."
            quote: true
          - name: changeTime
            description: "Last change time when updated from WON (STRING version)"
            quote: true
          - name: changeTime__timestamp
            description: "Last change time when updated from WON (Converted to TIMESTAMP when possible)"
            quote: true
          - name: changeOwner
            description: "Source specific email of the user who stimulated the change"
            quote: true
          - name: changeSource
            description: "Defines what stimulated the update"
            quote: true
          - name: liveEventId
            description: "Unique id of this event in WON i.e. External Reference"
            quote: true
          - name: eventTitle
            description: "The title of the event"
            quote: true
          - name: eventStartUTC
            description: "The UTC time of the Pre-KO of the event (String version that we use to parse into another field)"
            quote: true
          - name: eventStartUTC__timestamp
            description: "The UTC time of the Pre-KO of the event"
            quote: true
          - name: eventEndUTC
            description: "The UTC time of the event end (String version that we use to parse into another field)"
            quote: true
          - name: eventEndUTC__timestamp
            description: "The UTC time of the event end"
            quote: true
          - name: scheduleVersion
            description: "Schedule version of Transmission from WON (Active, Alternative) - Alternative is used for pre-planning"
            quote: true
          - name: status
            description: "The status of the regional transmission in WON"
            quote: true
          - name: fixtureUuid
            description: "ID that represents the master fixture associated to the scheduled transmission"
            quote: true
          - name: mflDaznDummyFixtureId
            description: "ID that represents the dummy fixture if one is relevant for the scheduled transmission"
            quote: true
          - name: dummyFixtureUuid
            description: "ID that represents the dummy fixture if one is relevant for the scheduled transmission"
            quote: true
          - name: dummyFixtureUuid__coalesce
            description: "ID that represents the dummy fixture if one is relevant for the scheduled transmission, created using the COALESCE of the two mflDaznDummyFixtureId and dummyFixtureUuid fields, needed as there was a change of the field name"
            quote: true
          - name: rightsId
            description: "Reference ID to the WON exploitation rights used to schedule this event"
            quote: true
          - name: rightId
            description: "Reference ID to the WON exploitation rights used to schedule this event"
            quote: true
          - name: rightId__coalesce
            description: "Reference ID to the WON exploitation rights used to schedule this event, created using the COALESCE of the two rightsId and rightId fields, needed as there was a change of the field name"
            quote: true
          - name: productId
            description: "Reference to the  Live Product associated with this live event"
            quote: true
          - name: groupUuid
            description: "ID used to group separate events together as a single shared live stream"
            quote: true
          - name: liveEncodingId
            description: "Reference ID to the associated Live Encoding data"
            quote: true
          - name: regionUuid
            description: "ID that represents the DAZN region and MCS Product"
            quote: true
          - name: eventType_name
            description: "Represents the TX Event Type used by Bookings for appropriate channel assignment E.g. TX50, PBB50, LTD50, OND59, TX59, DCI 50, DCI 59"
            quote: true
          - name: eventType_uuid
            description: "External reference ID of Event type field"
            quote: true
          - name: broadcastTier
            description: "The broadcast tier set against the event to indicate the level of production to applied by DAZN"
            quote: true
          - name: catchUpAllowedStartUTC
            description: "The UTC time the catch up tile can be published or made available on the DAZN app"
            quote: true
          - name: catchUpTakedownUTC
            description: "The UTC time the catch up tile can be expired or removed from the DAZN app"
            quote: true
          - name: gfxKey
            description: "Represents the graphics resources that will be required for the event"
            quote: true
          - name: vobKey
            description: "Represents the voice over booth resources will be required for the event"
            quote: true
          - name: bestVobKey
            description: "Represents the best vobKey value for shared events calculated by WON"
            quote: true
          - name: frameRate
            description: "Indicates the frame rate in which the event will be delivered on the OTT product"
            quote: true
          - name: type
            description: "Used to distinguish a scheduled event as LIVE or ASLIVE (Delayed)"
            quote: true
          - name: platform
            description: "Used to distinguish between scheduled events that are for the OTT platform and others"
            quote: true
          - name: workflowType
            description: "Represents the Alternative Workflow used by Planning to indicate a special scenario"
            quote: true
          - name: gallery_name
            description: "Indicates whether it is a gallery event requiring specific Gallery resources"
            quote: true
          - name: gallery_uuid
            description: "ID of the Gallery object"
            quote: true
          - name: bestGallery
            description: "Represents the best Gallery value for shared events calculated by WON"
            quote: true
          - name: txPod
            description: "Represents the actual TX Pod resource allocated to the event"
            quote: true
          - name: obligated
            description: "Surfaces whether transmission is obligated to be played"
            quote: true
          - name: daznExclusive
            description: "Boolean represnting if the live event is exclusive to DAZN"
            quote: true
          - name: coExclusive
            description: "Boolean represnting if DAZN shares exclusivity for the live event"
            quote: true
          - name: ftvAllowed
            description: "Boolean value that indicates whether the live event can be offered as 'free to view'"
            quote: true
          - name: b2bContentTiering
            description: "List of objects that contain b2bContentTiering name and it’s WON external reference ID"
            quote: true
          - name: ftvCatchupAllowed
            description: "Boolean value that indicates whether the catchup asset of the live event can be offered as 'free to view'"
            quote: true
          - name: dci
            description: "Boolean value if event is dci or not"
            quote: true
          - name: dciGroup_name
            description: "Represents the DCI group that the event forms"
            quote: true
          - name: worldFeed
            description: "Indicates whether the World Feed commentary can be used or not"
            quote: true
          - name: worldFeedLanguages
            description: "A map of one or more world feed commentary languages made available for this event"
            quote: true
          - name: worldFeedLanguages_code
            description: "The first value from the worldFeedLanguages array representing the primary world feed commentary language made available for this event"
            quote: true
          - name: commentaryLangs
            description: "A map of one or more commentary languages made available for this event"
            quote: true
          - name: commentaryLangs_code
            description: "The first value from the commentaryLangs array representing the primary commentary language made available for this event"
            quote: true
          - name: closedCaptionLangs
            description: "A map of one or more closed caption languages made available for this event keyed by ISO two-letter language codes"
            quote: true
          - name: closedCaptionLangs_code
            description: "The first value from the closedCaptionLangs array representing the primary caption language made available for this event keyed by ISO two-letter language codes"
            quote: true
          - name: advertisingComplianceRules
            description: "Array representing the advertising compliance rules of the live event"
            quote: true
          - name: announcedStartUTC
            description: "Announced time in UTC dropdown on transmission"
            quote: true
          - name: sponsoring
            description: "Represents the sponsoring field from Transmission"
            quote: true
          - name: htFiller
            description: "Represents the HT Filler that needs to be planned for"
            quote: true
          - name: regionalSupplier
            description: "Indicates that within a certain territory the content is actually being supplied by another rights holder"
            quote: true
          - name: regionalSupplierId
            description: "Indicates that within a certain territory the content is actually being supplied by another rights holder - only ID"
            quote: true
          - name: closedCaptions
            description: "A map of one or more closed caption languages made available for this event keyed by ISO two-letter language codes along with the supplier Details"
            quote: true
      - name: curated__dots__won_liveencoding_current
        description: "The current version of the WON meta data describing a scheduled live encoding. Can be linked to the live event via liveEncodingId"
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by dbt to the curated layer"
            quote: true
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by the ingest process into the open layer"
            quote: true
          - name: lastUpdatedTime
            description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
            quote: true
          - name: isTest
            description: "This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false."
            quote: true
          - name: envelopeSchemaVersion
            description: "The version of the envelope schema. This version must be 2."
            quote: true
          - name: payloadId
            description: "A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message."
            quote: true
          - name: payloadType
            description: "A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers."
            quote: true
          - name: payloadState
            description: "An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?"
            quote: true
          - name: payload
            description: "An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer."
            quote: true
          - name: changeTime
            description: "Last change time when updated from WON (STRING version)"
            quote: true
          - name: changeTime__timestamp
            description: "Last change time when updated from WON (Converted to TIMESTAMP when possible)"
            quote: true
          - name: changeOwner
            description: "Source specific email of the user who stimulated the change"
            quote: true
          - name: changeSource
            description: "Defines what stimulated the update"
            quote: true
          - name: liveEncodingId
            description: "Live encoding ID. unique per payload but not for allocation type. Used to map to live event table"
            quote: true
          - name: fixtureUuid
            description: "ID that represents the fixture associated to the scheduled transmission"
            quote: true
          - name: title
            description: "Title of the fixture id"
            quote: true
          - name: status
            description: "Status of the payload"
            quote: true
          - name: eventStartUTC
            description: "The UTC time of the Pre-KO of the event (String version that we use to parse into another field)"
            quote: true
          - name: eventStartUTC__timestamp
            description: "The UTC time of the Pre-KO of the event"
            quote: true
          - name: eventEndUTC
            description: "The UTC time of the event end (String version that we use to parse into another field)"
            quote: true
          - name: eventEndUTC__timestamp
            description: "The UTC time of the event end"
            quote: true
          - name: timeAllocations
            description: "List of dictionary containing information about allocation"
            quote: true
      - name: curated__dots__won_timeallocations_current
        description: "Derived from curated__dots__won_liveencoding_timeallocations_current. The time_allocation nested values exploded, the other fields are the same. Primary key on AllocId"
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by dbt to the curated layer"
            quote: true
          - name: liveEncodingId
            description: "Live encoding ID. unique per payload but not for allocation type. Used to map to live event table"
            quote: true
          - name: timeAllocations_allocId
            description: "Flattened from timeAllocations, This is the ID of the allocation sub-record"
            quote: true
          - name: timeAllocations_duration
            description: "Flattened from timeAllocations, This is the duration of the allocation sub-record"
            quote: true
          - name: timeAllocations_name
            description: "Flattened from timeAllocations, This is the name of the allocation sub-record"
            quote: true
          - name: timeAllocations_secondaryEvents
            description: "Flattened from timeAllocations, This is a list of secondary events of the allocation sub-record"
            quote: true
          - name: timeAllocations_secondaryEvents_spliceEventId_0
            description: "Flattened from timeAllocations, eventId extracted from the 1st record of the secondary events."
            quote: true
          - name: timeAllocations_secondaryEvents_type_0
            description: "Flattened from timeAllocations, type extracted from the 1st record of the secondary events."
            quote: true
          - name: timeAllocations_secondaryEvents_duration_0
            description: "Flattened from timeAllocations, duration extracted from the 1st record of the secondary events."
            quote: true
          - name: timeAllocations_secondaryEvents_spliceEventId_1
            description: "Flattened from timeAllocations, eventId extracted from the 2nd record of the secondary events."
            quote: true
          - name: timeAllocations_secondaryEvents_type_1
            description: "Flattened from timeAllocations, type extracted from the 2nd record of the secondary events."
            quote: true
          - name: timeAllocations_secondaryEvents_duration_1
            description: "Flattened from timeAllocations, duration extracted from the 2nd record of the secondary events."
            quote: true
          - name: timeAllocations_source
            description: "Flattened from timeAllocations, This is the source of the allocation sub-record"
            quote: true
          - name: timeAllocations_startDateTimeUTC
            description: "Flattened from timeAllocations, This is the start time of the allocation sub-record"
            quote: true
          - name: timeAllocations_title
            description: "Flattened from timeAllocations, This is the title of the allocation sub-record"
            quote: true
          - name: timeAllocations_txEvents
            description: "Flattened from timeAllocations, this is a list of tx records inside the allocation sub-record. Can be up to a 10"
            quote: true
          - name: timeAllocations_type
            description: "Flattened from timeAllocations, This is the type of the allocation sub-record"
            quote: true
          - name: timeAllocations_uuid
            description: "Flattened from timeAllocations, This is the uuid of the allocation sub-record"
            quote: true
      - name: curated__dots__won_contract_current
        description: "The current version of the WON meta data describing the parent contract we have with a given rights holder"
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by dbt to the curated layer"
            quote: true
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by the ingest process into the open layer"
            quote: true
          - name: lastUpdatedTime
            description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
            quote: true
          - name: isTest
            description: "This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false."
            quote: true
          - name: envelopeSchemaVersion
            description: "The version of the envelope schema. This version must be 2."
            quote: true
          - name: payloadId
            description: "A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message."
            quote: true
          - name: payloadType
            description: "A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers."
            quote: true
          - name: payloadState
            description: "An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?"
            quote: true
          - name: payload
            description: "An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer."
            quote: true
          - name: changeTime
            description: "Last change time when updated from WON (STRING version)"
            quote: true
          - name: changeTime__timestamp
            description: "Last change time when updated from WON (Converted to TIMESTAMP when possible)"
            quote: true
          - name: changeOwner
            description: "Source specific email of the user who stimulated the change"
            quote: true
          - name: changeSource
            description: "Defines what stimulated the update"
            quote: true
          - name: contractId
            description: "Unique id of this event in WON i.e. External Reference"
            quote: true
          - name: contractName
            description: "Name of the contract"
            quote: true
          - name: name
            description: "Name of the contract"
            quote: true
          - name: contractName_COALESCE
            description: "Name of the contract, created using the COALESCE of the two contractName and name fields, needed as there was a change of the field name"
            quote: true
          - name: status
            description: "Status of the contract"
            quote: true
          - name: startDate_string
            description: "Global start date of the contract (String version that we use to parse into another field)"
            quote: true
          - name: startDate
            description: "Global start date of the contract"
            quote: true
          - name: endDate_string
            description: "Global end date of the contract (String version that we use to parse into another field)"
            quote: true
          - name: endDate
            description: "Global end date of the contract"
            quote: true
          - name: rightsHolderId
            description: "Reference ID of the contract's rights holder"
            quote: true
      - name: curated__dots__won_vodevent_current
        description: ""
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by dbt to the curated layer"
            quote: true
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by the ingest process into the open layer"
            quote: true
          - name: lastUpdatedTime
            description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
            quote: true
          - name: isTest
            description: "This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false."
            quote: true
          - name: envelopeSchemaVersion
            description: "The version of the envelope schema. This version must be 2."
            quote: true
          - name: payloadId
            description: "A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message."
            quote: true
          - name: payloadType
            description: "A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers."
            quote: true
          - name: payloadState
            description: "An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?"
            quote: true
          - name: payload
            description: "An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer."
            quote: true
          - name: changeTime
            description: "Last change time when updated from WON (STRING version)"
            quote: true
          - name: changeTime__timestamp
            description: "Last change time when updated from WON (Converted to TIMESTAMP when possible)"
            quote: true
          - name: changeOwner
            description: "Source specific email of the user who stimulated the change"
            quote: true
          - name: changeSource
            description: "Defines what stimulated the update"
            quote: true
          - name: vodEventId
            description: "Unique id of this non-live event in WON i.e. External Reference"
            quote: true
          - name: title
            description: "The title of the event"
            quote: true
          - name: status
            description: "The status of the regional transmission in WON"
            quote: true
          - name: scheduleVersion
            description: "Schedule version of Transmission from WON (Active, Alternative) - Alternative is used for pre-planning"
            quote: true
          - name: scheduledUploadDate
            description: "The scheduled date the event will upload to the platform"
            quote: true
          - name: scheduledUploadTime
            description: "The scheduled time the event will upload to the platform"
            quote: true
          - name: scheduledUploadDateTime__timestamp
            description: "The scheduled datetime the event will upload to the platform, created by combining the scheduledUploadDate and scheduledUploadTime"
            quote: true
          - name: scheduledUploadDateTimeUTC
            description: "The scheduled time the event will upload to the platform, coming straight from the payload as there was a field change"
            quote: true
          - name: eventStartUTC
            description: "The date/time that the VOD content will be available on DAZN"
            quote: true
          - name: vodEventStartDateTime__timestamp
            description: "The date/time that the VOD content will be available on DAZN, created as the combination of all fields that have been populated for this data point"
            quote: true
          - name: killDate
            description: "The scheduled date the event will be taken down from the platform"
            quote: true
          - name: killTime
            description: "The scheduled time the event will be taken down from the platform"
            quote: true
          - name: killDateTime__timestamp
            description: "The scheduled datetime the event will be taken down from the platform, created by combining the scheduledUploadDate and scheduledUploadTime"
            quote: true
          - name: killDateTimeUTC
            description: "The scheduled time the event will be taken down from the platform, coming straight from the payload as there was a field change"
            quote: true
          - name: eventEndUTC
            description: "The UTC time the event end"
            quote: true
          - name: vodEventEndDateTime__timestamp
            description: "The UTC time the event end, created as the combination of all fields that have been populated for this data point"
            quote: true
          - name: rightId
            description: "Reference ID to the WON exploitation rights used to schedule this non-live event"
            quote: true
          - name: rightsId
            description: "Reference ID to the WON exploitation rights used to schedule this non-live event"
            quote: true
          - name: rightsId__coalesce
            description: "Reference ID to the WON exploitation rights used to schedule this non-live event, created using the COALESCE of the two rightId and rightsId fields, needed as there was a change of the field name"
            quote: true
          - name: vodMediaAssetId
            description: "Reference ID to the associated WON media asset"
            quote: true
          - name: vodMediaId
            description: "Reference ID to the associated WON media asset"
            quote: true
          - name: vodMediaId__coalesce
            description: "Reference ID to the associated WON media asset, created using the COALESCE of the two rightId and rightsId fields, needed as there was a change of the field name"
            quote: true
          - name: productId
            description: "Reference ID to the associated WON product"
            quote: true
          - name: regionUuid
            description: "ID that represents the DAZN region and MCS Product"
            quote: true
          - name: additionalProductionElements
            description: "Notes provided to Production"
            quote: true
          - name: obligated
            description: "mustPlay field from transmission"
            quote: true
          - name: ftvAllowed
            description: "Boolean value that indicates whether the non-live event can be offered as 'free to view'"
            quote: true
          - name: advertising
            description: "Represents the advertising field from Transmission"
            quote: true
          - name: duration
            description: "Expected duration of the live event"
            quote: true
          - name: b2bContentTiering
            description: "Array of objects that contain b2bContentTiering name and its WON external reference ID"
            quote: true
      - name: curated__dots__won_liveright_current
        description: "The current version of the WON meta data describing the rights available to exploit for a given live event"
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by dbt to the curated layer"
            quote: true
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by the ingest process into the open layer"
            quote: true
          - name: lastUpdatedTime
            description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
            quote: true
          - name: isTest
            description: "This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false."
            quote: true
          - name: envelopeSchemaVersion
            description: "The version of the envelope schema. This version must be 2."
            quote: true
          - name: payloadId
            description: "A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message."
            quote: true
          - name: payloadType
            description: "A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers."
            quote: true
          - name: payloadState
            description: "An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?"
            quote: true
          - name: payload
            description: "An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer."
            quote: true
          - name: changeTime
            description: "Last change time when updated from WON (STRING version)"
            quote: true
          - name: changeTime__timestamp
            description: "Last change time when updated from WON (Converted to TIMESTAMP when possible)"
            quote: true
          - name: changeOwner
            description: "Source specific email of the user who stimulated the change"
            quote: true
          - name: changeSource
            description: "Defines what stimulated the update"
            quote: true
          - name: rightId
            description: "Unique id of this right in WON i.e. External Reference"
            quote: true
          - name: title
            description: "Title of the linked product"
            quote: true
          - name: startDate
            description: "Start date of the exploitation right"
            quote: true
          - name: startDateFormula
            description: "Start date of the exploitation right"
            quote: true
          - name: startDate__coalesce
            description: "Start date of the exploitation right, created using the COALESCE of the two startDate and startDateFormula fields, needed as there was a change of the field name"
            quote: true
          - name: endDate
            description: "End date of the exploitation right"
            quote: true
          - name: endDateFormula
            description: "End date of the exploitation right"
            quote: true
          - name: endDate__coalesce
            description: "End date of the exploitation right, created using the COALESCE of the two endDate and endDateFormula fields, needed as there was a change of the field name"
            quote: true
          - name: type
            description: "The type of the right E.g. OTT, ..."
            quote: true
          - name: productType
            description: "The type of the linked product E.g. Series, Episode"
            quote: true
          - name: regionUuid
            description: "ID that represents the DAZN region and MCS Product"
            quote: true
          - name: plannedFixtures
            description: "Number of runs (aka commitments) allowed for the given exploitation right"
            quote: true
          - name: status
            description: "The status of the exploitation right"
            quote: true
          - name: live
            description: "Boolean value which indicates if the exploitation right applies to LIVE events"
            quote: true
          - name: asLive
            description: "Boolean value which indicates if the exploitation right applies to ASLIVE (Delayed) events"
            quote: true
          - name: tournamentCalendarUuid
            description: "ID of the related MFL tournament calendar"
            quote: true
          - name: contractId
            description: "Reference ID for exploitation right's parent contract"
            quote: true
          - name: ftvAllowed
            description: "Boolean value that indicates whether live events for the given right can be offered as 'free to view'"
            quote: true
          - name: downloadAllowed
            description: "Boolean value that indicates whether live events for the given right can be offered as 'downloadable'"
            quote: true
          - name: b2cAllowed
            description: "Boolean value indicating whether the rights are allowed for DAZN's B2C product"
            quote: true
          - name: b2bAllowed
            description: "Boolean value indicating whether the rights are allowed for DAZN's B2B product"
            quote: true
          - name: b2bCatchupAllowed
            description: "Boolean value indicating whether the rights are allowed on DAZN's B2B product as catchup content"
            quote: true
          - name: contentDistinctionReference
            description: "The ID of the Content Distinction"
            quote: true
          - name: contentDistinction
            description: "The content distinction name breaking out the content type E.g. Highlights, ..."
            quote: true
          - name: allowedCountryCodes
            description: "A array of countries allowed to consume events for the given rights"
            quote: true
          - name: allowedCountryCodes_0
            description: "The first value of the array in field allowedCountryCodes representing countries allowed to consume events for the given rights"
            quote: true
          - name: allowedCountryCodes_1
            description: "The second value of the array in field allowedCountryCodes representing countries allowed to consume events for the given rights"
            quote: true
          - name: allowedCountryCodes_2
            description: "The third value of the array in field allowedCountryCodes representing countries allowed to consume events for the given rights"
            quote: true
          - name: disallowedCountryCodes
            description: "An array of countries blocked from consuming events for the given rights"
            quote: true
          - name: nonExclusiveRegions
            description: "An array of non exclusive regions for the given right"
            quote: true
          - name: nonExclusiveRegions_0
            description: "The first value of the array in field nonExclusiveRegions representing non exclusive regions for the given right"
            quote: true
          - name: nonExclusiveRegions_1
            description: "The second value of the array in field nonExclusiveRegions representing non exclusive regions for the given right"
            quote: true
          - name: nonExclusiveRegions_2
            description: "The third value of the array in field nonExclusiveRegions representing non exclusive regions for the given right"
            quote: true
          - name: allowedAudioLanguages
            description: "An array of audio languages allowed to be applied against the event"
            quote: true
          - name: commercialPremises
            description: "An array of commercial premise types that are allowed to receive these rights"
            quote: true
      - name: curated__dots__won_region_current
        description: "The current versio of the meta data describing a DAN Region and its usage within WON, and how it correlates to legacy entities such as DAZN CMS Organisations and DAZN Feed Outlets"
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by dbt to the curated layer"
            quote: true
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by the ingest process into the open layer"
            quote: true
          - name: lastUpdatedTime
            description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
            quote: true
          - name: isTest
            description: "This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false."
            quote: true
          - name: envelopeSchemaVersion
            description: "The version of the envelope schema. This version must be 2."
            quote: true
          - name: payloadId
            description: "A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message."
            quote: true
          - name: payloadType
            description: "A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers."
            quote: true
          - name: payloadState
            description: "An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?"
            quote: true
          - name: payload
            description: "An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer."
            quote: true
          - name: changeTime
            description: "Last change time when updated from WON (STRING version)"
            quote: true
          - name: changeTime__timestamp
            description: "Last change time when updated from WON (Converted to TIMESTAMP when possible)"
            quote: true
          - name: changeOwner
            description: "Source specific email of the user who stimulated the change"
            quote: true
          - name: changeSource
            description: "Defines what stimulated the update"
            quote: true
          - name: regionUuid
            description: "ID that represents the DAZN region and MCS Product"
            quote: true
          - name: regionName
            description: "Name of the DAZN region"
            quote: true
          - name: defaultTimezone
            description: "The default timezone of the region"
            quote: true
          - name: adMarket
            description: "Region identifier uniquely used for advertising purposes"
            quote: true
          - name: organisationUuid
            description: "ID that represents the related DCMS organisation"
            quote: true
          - name: outletKey
            description: "The authentication key used for the relevant DAZN Feed outlet for given DAZN region"
            quote: true
          - name: m2aRegionKey
            description: "Region identifier uniquely used by M2A"
            quote: true
          - name: countries
            description: "An array of countries associated with the region"
            quote: true
      - name: curated__dots__won_vodproduct_current
        description: ""
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by dbt to the curated layer"
            quote: true
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by the ingest process into the open layer"
            quote: true
          - name: lastUpdatedTime
            description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
            quote: true
          - name: isTest
            description: "This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false."
            quote: true
          - name: envelopeSchemaVersion
            description: "The version of the envelope schema. This version must be 2."
            quote: true
          - name: payloadId
            description: "A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message."
            quote: true
          - name: payloadType
            description: "A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers."
            quote: true
          - name: payloadState
            description: "An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?"
            quote: true
          - name: payload
            description: "An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer."
            quote: true
          - name: changeTime
            description: "Last change time when updated from WON (STRING version)"
            quote: true
          - name: changeTime__timestamp
            description: "Last change time when updated from WON (Converted to TIMESTAMP when possible)"
            quote: true
          - name: changeOwner
            description: "Source specific email of the user who stimulated the change"
            quote: true
          - name: changeSource
            description: "Defines what stimulated the update"
            quote: true
          - name: vodProductId
            description: "Unique ID of the WON product"
            quote: true
          - name: title
            description: "Title used as reference to the original product title"
            quote: true
          - name: productType
            description: "The type of product"
            quote: true
          - name: fixtureUuid
            description: "ID of the related MFL fixture"
            quote: true
          - name: productVersion
            description: "Version of the product"
            quote: true
          - name: competition
            description: "Array containing metadata relating to the competition of the product"
            quote: true
          - name: competition_uuid
            description: "ID of the related MFL competition"
            quote: true
          - name: competitionUuid
            description: "ID of the related MFL competition"
            quote: true
          - name: competitionUuid_COALESCE
            description: "ID of the related MFL competition, created using the COALESCE of the two competition_uuid and competitionUuid fields, needed as there was a change of the field name"
            quote: true
          - name: competition_name
            description: "Name of the related MFL competition"
            quote: true
          - name: competitionName
            description: "Name of the related MFL competition"
            quote: true
          - name: competitionName__coalesce
            description: "Name of the related MFL competition, created using the COALESCE of the two competition_name and competitionName fields, needed as there was a change of the field name"
            quote: true
          - name: rulesetName
            description: "Name of the related MFL ruleset"
            quote: true
          - name: location
            description: "Location of the related MFL Competition"
            quote: true
          - name: tournamentCalendarUuid
            description: "ID of the related MFL tournament calendar"
            quote: true
          - name: contestantUuids
            description: "Array of IDs of the related MFL contestants"
            quote: true
          - name: sport
            description: "Array containing metadata relating to the sport of the product"
            quote: true
          - name: sport_uuid
            description: "ID of the related MFL sport"
            quote: true
          - name: sportUuid
            description: "ID of the related MFL sport"
            quote: true
          - name: sportUuid__coalesce
            description: "ID of the related MFL sport, created using the COALESCE of the two sport_uuid and sportUuid fields, needed as there was a change of the field name"
            quote: true
          - name: sport_name
            description: "Name of the related MFL sport"
            quote: true
          - name: sportName
            description: "Name of the related MFL sport"
            quote: true
          - name: sportName__coalesce
            description: "Name of the related MFL sport, created using the COALESCE of the two sport_name and sportName fields, needed as there was a change of the field name"
            quote: true
          - name: contentDistinction
            description: "Array containing metadata relating to the contentDistinction of the product"
            quote: true
          - name: contentDistinction_uuid
            description: "External reference of the relevant Content Distinction (aka Video Type)"
            quote: true
          - name: contentDistinctionReference
            description: "External reference of the relevant Content Distinction (aka Video Type)"
            quote: true
          - name: contentDistinctionUuid__coalesce
            description: "External reference of the relevant Content Distinction (aka Video Type), created using the COALESCE of the two contentDistinction_uuid and contentDistinctionReference fields, needed as there was a change of the field name"
            quote: true
          - name: contentDistinction_name
            description: "Name of the relevant Content Distinction (aka Video Type)"
            quote: true
          - name: contentDistinction_name__coalesce
            description: "Name of the relevant Content Distinction (aka Video Type), created using the COALESCE of the two contentDistinction_name and contentDistinction fields, needed as there was a change of the field name"
            quote: true
          - name: sourceLanguage
            description: "Audio language on the source video received by DAZN"
            quote: true
          - name: closedCaptionsAvailable
            description: "Boolean value indicated if there are closed captions on the source video received by DAZN"
            quote: true
          - name: closedCaptionsLanguage
            description: "Language of the closed captions provided on the source video received by DAZN"
            quote: true
          - name: arrivalMethod
            description: "Method in which DAZN received the source video"
            quote: true
          - name: synopsis
            description: "Short description of the non-live event"
            quote: true
          - name: expectedDuration
            description: "Expected duration of the live event"
            quote: true
          - name: contractualCompliance
            description: "List of keys of prohibited ad types defined by rights compliance"
            quote: true
      - name: curated__dots__won_rightsholder_current
        description: "The current version of the WON meta data describing the rights holder entity"
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by dbt to the curated layer"
            quote: true
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by the ingest process into the open layer"
            quote: true
          - name: lastUpdatedTime
            description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
            quote: true
          - name: isTest
            description: "This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false."
            quote: true
          - name: envelopeSchemaVersion
            description: "The version of the envelope schema. This version must be 2."
            quote: true
          - name: payloadId
            description: "A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message."
            quote: true
          - name: payloadType
            description: "A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers."
            quote: true
          - name: payloadState
            description: "An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?"
            quote: true
          - name: payload
            description: "An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer."
            quote: true
          - name: changeTime
            description: "Last change time when updated from WON (STRING version)"
            quote: true
          - name: changeTime__timestamp
            description: "Last change time when updated from WON (Converted to TIMESTAMP when possible)"
            quote: true
          - name: changeOwner
            description: "Source specific email of the user who stimulated the change"
            quote: true
          - name: changeSource
            description: "Defines what stimulated the update"
            quote: true
          - name: rightsHolderId
            description: "ID that represents the rights holder"
            quote: true
          - name: rightsHolderName
            description: "Name of the rights holder"
            quote: true
          - name: name
            description: "Name of the rights holder"
            quote: true
          - name: rightsHolderName__coalesce
            description: "Name of the rights holder, created using the COALESCE of the two rightsHolderName and name fields, needed as there was a change of the field name"
            quote: true
      - name: curated__dots__won_prelimliveevent_current
        description: "The current version of the WON meta data describing a live event in the preliminary schedule for a designated DAZN Region"
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by dbt to the curated layer"
            quote: true
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by the ingest process into the open layer"
            quote: true
          - name: lastUpdatedTime
            description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
            quote: true
          - name: isTest
            description: "This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false."
            quote: true
          - name: envelopeSchemaVersion
            description: "The version of the envelope schema. This version must be 2."
            quote: true
          - name: payloadId
            description: "A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message."
            quote: true
          - name: payloadType
            description: "A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers."
            quote: true
          - name: payloadState
            description: "An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?"
            quote: true
          - name: payload
            description: "An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer."
            quote: true
          - name: changeTime
            description: "Last change time when updated from WON (STRING version)"
            quote: true
          - name: changeTime__timestamp
            description: "Last change time when updated from WON (Converted to TIMESTAMP when possible)"
            quote: true
          - name: changeOwner
            description: "Source specific email of the user who stimulated the change"
            quote: true
          - name: changeSource
            description: "Defines what stimulated the update"
            quote: true
          - name: prelimLiveEventId
            description: "Unique id of this event in WON i.e. External Reference"
            quote: true
          - name: eventTitle
            description: "The title of the event"
            quote: true
          - name: eventStartUTC
            description: "The UTC time of the Pre-KO of the event"
            quote: true
          - name: eventEndUTC
            description: "The UTC time the event end"
            quote: true
          - name: scheduleVersion
            description: "Schedule version of Transmission from WON (Active, Alternative) - Alternative is used for pre-planning"
            quote: true
          - name: status
            description: "The status of the regional transmission in WON"
            quote: true
          - name: fixtureUuid
            description: "ID that represents the master fixture associated to the scheduled transmission"
            quote: true
          - name: dummyFixtureUuid
            description: "ID that represents the dummy fixture if one is relevant for the scheduled transmission"
            quote: true
          - name: rightId
            description: "Reference ID to the WON exploitation rights used to schedule this event"
            quote: true
          - name: productId
            description: "Reference ID to the WON product used to schedule this event"
            quote: true
          - name: regionUuid
            description: "ID that represents the DAZN region and MCS Product"
            quote: true
          - name: eventType_name
            description: "Represents the TX Event Type used by Bookings for appropriate channel assignment E.g. TX50, PBB50, LTD50, OND59, TX59, DCI 50, DCI 59"
            quote: true
          - name: eventType_id
            description: "External reference ID of Event type field"
            quote: true
          - name: broadcastTier
            description: "The broadcast tier set against the event to indicate the level of production to applied by DAZN"
            quote: true
          - name: vobKey
            description: "Represents the voice over booth resources will be required for the event"
            quote: true
          - name: frameRate
            description: "Indicates the frame rate in which the event will be delivered on the OTT product"
            quote: true
          - name: type
            description: "Used to distinguish a scheduled event as LIVE or ASLIVE (Delayed)"
            quote: true
          - name: platform
            description: "Used to distinguish between scheduled events that are for the OTT platform and others"
            quote: true
          - name: workflowType
            description: "Represents the Alternative Workflow used by Planning to indicate a special scenario"
            quote: true
          - name: worldFeed
            description: "Indicates whether the World Feed commentary can be used or not"
            quote: true
          - name: worldFeedLanguages
            description: "A map of one or more world feed commentary languages made available for this event"
            quote: true
          - name: worldFeedLanguages_code
            description: "The first value from the worldFeedLanguages array representing the primary world feed commentary language made available for this event"
            quote: true
          - name: commentaryLangs
            description: "A map of one or more commentary languages made available for this event"
            quote: true
          - name: commentaryLangs_code
            description: "The first value from the commentaryLangs array representing the primary commentary language made available for this event"
            quote: true
          - name: gallery_name
            description: "Indicates whether it is a gallery event requiring specific Gallery resources"
            quote: true
          - name: gallery_id
            description: "ID of the Gallery object"
            quote: true
          - name: txPod
            description: "Represents the actual TX Pod resource allocated to the event"
            quote: true
          - name: coExclusive
            description: "Exclusive to DAZN field from transmission"
            quote: true
          - name: ftvAllowed
            description: "Boolean value that indicates whether the live event can be offered as 'free to view'"
            quote: true
          - name: ftwCatchupAllowed
            description: "Boolean value that indicates whether the catchup asset of the live event can be offered as 'free to view'"
            quote: true
          - name: b2bContentTierings
            description: "List of objects that contain b2bContentTiering name and it’s WON external reference ID"
            quote: true
          - name: dci
            description: "Boolean value if event is dci or not"
            quote: true
          - name: dciGroup_name
            description: "Represents the DCI group that the event forms"
            quote: true
          - name: obligated
            description: "mustPlay field from transmission"
            quote: true
          - name: droppedReason
            description: "Reason for not selecting"
            quote: true
      - name: curated__dots__won_vodmedia_current
        description: ""
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by dbt to the curated layer"
            quote: true
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by the ingest process into the open layer"
            quote: true
          - name: lastUpdatedTime
            description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
            quote: true
          - name: isTest
            description: "This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false."
            quote: true
          - name: envelopeSchemaVersion
            description: "The version of the envelope schema. This version must be 2."
            quote: true
          - name: payloadId
            description: "A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message."
            quote: true
          - name: payloadType
            description: "A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers."
            quote: true
          - name: payloadState
            description: "An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?"
            quote: true
          - name: payload
            description: "An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer."
            quote: true
          - name: changeTime
            description: "Last change time when updated from WON (STRING version)"
            quote: true
          - name: changeTime__timestamp
            description: "Last change time when updated from WON (Converted to TIMESTAMP when possible)"
            quote: true
          - name: changeOwner
            description: "Source specific email of the user who stimulated the change"
            quote: true
          - name: changeSource
            description: "Defines what stimulated the update"
            quote: true
          - name: vodMediaId
            description: "Unique ID to the WON media asset"
            quote: true
          - name: wonMediaLabel
            description: "Label used to identify the purpose of the media asset"
            quote: true
          - name: label
            description: "Label used to identify the purpose of the media asset"
            quote: true
          - name: label__coalesce
            description: "Label used to identify the purpose of the media asset, created using the COALESCE of the two wonMediaLabel and label fields, needed as there was a change of the field name"
            quote: true
          - name: vodProductId
            description: "External reference (ID) of linked Vod Product"
            quote: true
          - name: oldFileName
            description: "Filename required for the produced media asset"
            quote: true
          - name: teamAssigned
            description: "The team assigned to product the media asset"
            quote: true
          - name: reversionRequired
            description: "Boolean value indicating whether the media asset needs to be re-versioned into another media asset with different audio language"
            quote: true
          - name: closedCaptionsRequired
            description: "Boolean value indicating if closed captions need to be added to produced media asset"
            quote: true
          - name: audioLanguages
            description: "Audio Languages used within the Media"
            quote: true
          - name: audioLanguages_value
            description: "The first value from the audioLanguages array representing the primary audio Languages used within the Media"
            quote: true
