
WITH source AS (
    SELECT * FROM {{ source('CURATED','curated__dots__won_liveproduct_current' )}}
)

, renamed AS (
    SELECT
        -- The macro is defined in dbt/macros/Dots/won_payload_columns.sql
        {{ won_payload_columns() }}
        ,"liveProductId" AS "live_product_id"
        ,"title" AS "live_product_title"
        ,"productType" AS "live_product_type"
        ,"fixtureUuid" AS "fixture_id"
        ,"competitionUuid" AS "competition_id"
        ,"rulesetName" AS "live_product_ruleset_name"
        ,"tournamentCalendarUuid" AS "tournament_calendar_id"
        ,"worldFeedLanguages" AS "live_product_world_feed_languages"
        ,"worldFeedLanguages_code" AS "live_product_world_feed_languages_code"
        ,"sportUuid" AS "sport_id"
        ,"sportName" AS "sport_name"
        ,"supportTier" AS "live_product_support_tier"
        ,"orderOfPlay" AS "live_product_order_of_play"
        ,"expectedDuration" AS "live_product_expected_duration"
        ,"contractualCompliance" AS "live_product_contractual_compliance"
    FROM source
)

select * from renamed
