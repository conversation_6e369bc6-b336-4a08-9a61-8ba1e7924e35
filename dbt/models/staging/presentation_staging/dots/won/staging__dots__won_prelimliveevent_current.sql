/*
    Null only fields we removed:
        - prelim_live_event_ftw_catchup_allowed
        - prelim_live_event_tx_pod
*/

WITH source AS (
    SELECT * FROM {{ source('CURATED','curated__dots__won_prelimliveevent_current' )}}
)

, renamed AS (
    SELECT
        -- The macro is defined in dbt/macros/Dots/won_payload_columns.sql
        {{ won_payload_columns() }}
        ,"prelimLiveEventId" AS "prelim_live_event_id"
        ,"eventTitle" AS "prelim_live_event_title"
        ,"eventStartUTC" AS "prelim_live_event_start_timestamp"
        ,"eventEndUTC" AS "prelim_live_event_end_timestamp"
        ,"scheduleVersion" AS "prelim_live_event_schedule_version"
        ,"status" AS "prelim_live_event_status"
        ,"fixtureUuid" AS "fixture_id"
        ,"dummyFixtureUuid" AS "dummy_fixture_id"
        ,"rightId" AS "live_right_id"
        ,"productId" AS "live_product_id"
        ,"regionUuid" AS "region_id"
        ,"eventType_name" AS "prelim_live_event_type_name"
        ,"eventType_id" AS "prelim_live_event_type_id"
        ,"broadcastTier" AS "prelim_live_event_broadcast_tier"
        ,"vobKey" AS "prelim_live_event_vob_key"
        ,"frameRate" AS "prelim_live_event_frame_rate"
        ,"type" AS "prelim_live_event_type"
        ,"platform" AS "prelim_live_event_platform"
        ,"workflowType" AS "prelim_live_event_workflow_type"
        ,"worldFeed" AS "prelim_live_event_is_world_feed"
        ,"worldFeedLanguages" AS "prelim_live_event_world_feed_languages"
        ,"worldFeedLanguages_code" AS "prelim_live_event_world_feed_languages_code"
        ,"commentaryLangs" AS "prelim_live_event_commentary_languages"
        ,"commentaryLangs_code" AS "prelim_live_event_commentary_languages_code"
        ,"gallery_name" AS "prelim_live_event_gallery_name"
        ,"gallery_id" AS "prelim_live_event_gallery_id"
        ,"coExclusive" AS "prelim_live_event_is_co_exclusive"
        ,"ftvAllowed" AS "prelim_live_event_has_ftv_allowed"
        ,"b2bContentTierings" AS "prelim_live_event_b2b_content_tierings"
        ,"dci" AS "prelim_live_event_is_dci"
        ,"dciGroup_name" AS "prelim_live_event_dci_group_name"
        ,"obligated" AS "prelim_live_event_is_obligated"
        ,"droppedReason" AS "prelim_live_event_dropped_reason"
    FROM source
)

select * from renamed
