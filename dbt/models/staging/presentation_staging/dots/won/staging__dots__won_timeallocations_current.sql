
WITH source AS (
    SELECT * FROM {{ source('CURATED','curated__dots__won_timeallocations_current' )}}
)

, renamed AS (
    SELECT
        -- The macro is defined in dbt/macros/Dots/won_payload_columns.sql
        {{ won_payload_columns() }}
        ,"liveEncodingId" AS "live_encoding_id"
        ,"fixtureUuid" AS "fixture_id"
        ,"title" as "live_encoding_title"
        ,"status" as "live_encoding_status"
        ,"eventStartUTC__timestamp" as "live_encoding_event_start_timestamp"
        ,"eventEndUTC__timestamp" as "live_encoding_event_end_timestamp"
        ,"timeAllocations" AS "live_encoding_time_allocations"
        ,"timeAllocations_allocId" AS "time_allocations_allocation_id"
        ,"timeAllocations_duration" AS "time_allocations_duration"
        ,"timeAllocations_name" AS "time_allocations_name"
        ,"timeAllocations_secondaryEvents" AS "time_allocations_secondary_events"
        ,"timeAllocations_secondaryEvents_spliceEventId_0" AS "time_allocations_secondary_events_splice_event_id_0"
        ,"timeAllocations_secondaryEvents_type_0" AS "time_allocations_secondary_events_type_0"
        ,"timeAllocations_secondaryEvents_duration_0" AS "time_allocations_secondary_events_duration_0"
        ,"timeAllocations_secondaryEvents_spliceEventId_1" AS "time_allocations_secondary_events_splice_event_id_1"
        ,"timeAllocations_secondaryEvents_type_1" AS "time_allocations_secondary_events_type_1"
        ,"timeAllocations_secondaryEvents_duration_1" AS "time_allocations_secondary_events_duration_1"
        ,"timeAllocations_source" AS "time_allocations_source"
        ,"timeAllocations_startDateTimeUTC"::TIMESTAMP AS "time_allocations_start_timestamp"
        ,"timeAllocations_title" AS "time_allocations_title"
        ,"timeAllocations_txEvents" AS "time_allocations_tx_events"
        ,"timeAllocations_type" AS "time_allocations_type"
        ,"timeAllocations_uuid" AS "time_allocations_id"
    FROM source
)

select * from renamed
