/*
    Null only fields we removed:
        - live_event_dazn_exclusive
        - live_event_gfx_key
        - live_event_catch_up_takedown_timestamp
        - live_event_catch_up_allowed_start_timestamp
*/

WITH source AS (
    SELECT * FROM {{ source('CURATED','curated__dots__won_liveevent_current' )}}
)

, renamed AS (
    SELECT
        -- The macro is defined in dbt/macros/Dots/won_payload_columns.sql
        {{ won_payload_columns() }}
        ,"liveEventId" AS "live_event_id"
        ,"eventTitle" AS "live_event_title"
        ,"eventStartUTC__timestamp" AS "live_event_start_timestamp"
        ,"eventEndUTC__timestamp" AS "live_event_end_timestamp"
        ,"scheduleVersion" AS "live_event_schedule_version"
        ,"status" AS "live_event_status"
        ,"fixtureUuid" AS "fixture_id"
        ,"dummyFixtureUuid__coalesce" AS "dummy_fixture_id"
        ,"rightId__coalesce" AS "live_right_id"
        ,"productId" AS "live_product_id"
        ,"groupUuid" AS "group_id"
        ,"liveEncodingId" AS "live_encoding_id"
        ,"regionUuid" AS "region_id"
        ,"eventType_name" AS "live_event_type_name"
        ,"eventType_uuid" AS "live_event_type_id"
        ,"broadcastTier" AS "live_event_broadcast_tier"
        ,"vobKey" AS "live_event_vob_key"
        ,"bestVobKey" AS "live_event_best_vob_key"
        ,"frameRate" AS "live_event_frame_rate"
        ,"type" AS "live_event_type"
        ,"platform" AS "live_event_platform"
        ,"workflowType" AS "live_event_workflow_type"
        ,"gallery_name" AS "live_event_gallery_name"
        ,"gallery_uuid" AS "live_event_gallery_id"
        ,"bestGallery" AS "live_event_best_gallery"
        ,"txPod" AS "live_event_tx_pod"
        ,"obligated" AS "live_event_is_obligated"
        ,"coExclusive" AS "live_event_is_co_exclusive"
        ,"ftvAllowed" AS "live_event_has_ftv_allowed"
        ,"b2bContentTiering" AS "live_event_b2b_content_tiering"
        ,"ftvCatchupAllowed" AS "live_event_has_ftv_catchup_allowed"
        ,"dci" AS "live_event_is_dci"
        ,"dciGroup_name" AS "live_event_dci_group_name"
        ,"worldFeed" AS "live_event_is_world_feed"
        ,"worldFeedLanguages" AS "live_event_world_feed_languages"
        ,"worldFeedLanguages_code" AS "live_event_world_feed_languages_code"
        ,"commentaryLangs" AS "live_event_commentary_languages"
        ,"commentaryLangs_code" AS "live_event_commentary_languages_code"
        ,"closedCaptionLangs" AS "live_event_closed_caption_languages"
        ,"closedCaptionLangs_code" AS "live_event_closed_caption_languages_code"
        ,"advertisingComplianceRules" AS "live_event_advertising_compliance_rules"
        ,"announcedStartUTC" AS "live_event_announced_start_timestamp"
        ,"sponsoring" AS "live_event_sponsoring"
        ,"htFiller" AS "live_event_ht_filler"
        ,"regionalSupplier" AS "live_event_regional_supplier"
        ,"regionalSupplierId" AS "live_event_regional_supplier_id"
        ,"closedCaptions" AS "live_event_closed_captions"
    FROM source
)

select * from renamed
