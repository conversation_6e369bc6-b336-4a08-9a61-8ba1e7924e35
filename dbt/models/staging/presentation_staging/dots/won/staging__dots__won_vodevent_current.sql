/*
    Null only fields we removed:
        - vod_event_additional_production_elements
        - vod_event_b2b_content_tiering
*/


WITH source AS (
    SELECT * FROM {{ source('CURATED','curated__dots__won_vodevent_current' )}}
)

, renamed AS (
    SELECT
        -- The macro is defined in dbt/macros/Dots/won_payload_columns.sql
        {{ won_payload_columns() }}
        ,"vodEventId" AS "vod_event_id"
        ,"title" AS "vod_event_title"
        ,"status" AS "vod_event_status"
        ,"scheduleVersion" AS "vod_event_schedule_version"
        ,"vodEventStartDateTime__timestamp" AS "vod_event_start_timestamp"
        ,"vodEventEndDateTime__timestamp" AS "vod_event_end_timestamp"
        ,"rightsId__coalesce" AS "vod_right_id"
        ,"vodMediaId__coalesce" AS "vod_media_id"
        ,"productId" AS "vod_product_id"
        ,"regionUuid" AS "region_id"
        ,"obligated" AS "vod_event_is_obligated"
        ,"ftvAllowed" AS "vod_event_has_ftv_allowed"
        ,"advertising" AS "vod_event_advertising"
        ,"duration" AS "vod_event_duration"
    FROM source
)

select * from renamed
