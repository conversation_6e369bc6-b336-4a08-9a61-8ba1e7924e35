
WITH source AS (
    SELECT * FROM {{ source('CURATED','curated__dots__won_liveright_current' )}}
)

, renamed AS (
    SELECT
        -- The macro is defined in dbt/macros/Dots/won_payload_columns.sql
        {{ won_payload_columns() }}
        ,"rightId" AS "live_right_id"
        ,"title" AS "live_right_title"
        -- there are two types of date formats + a few invalid values. This way we process what we can and nullify the rest.
        ,COALESCE(TRY_TO_DATE("startDate__coalesce",'dd/mm/yy'),TRY_TO_DATE("startDate__coalesce")) AS "live_right_start_date"
        ,COALESCE(TRY_TO_DATE("endDate__coalesce",'dd/mm/yy'),TRY_TO_DATE("endDate__coalesce")) as "live_right_end_date"
        ,"type" AS "live_right_type"
        ,"productType" AS "live_right_product_type"
        ,"regionUuid" AS "region_id"
        ,"plannedFixtures" AS "live_right_planned_fixtures"
        ,"status" AS "live_right_status"
        ,"live" AS "live_right_is_live"
        ,"asLive" AS "live_right_is_as_live"
        ,"tournamentCalendarUuid" AS "tournament_calendar_id"
        ,"contractId" AS "contract_id"
        ,"ftvAllowed" AS "live_right_has_ftv_allowed"
        ,"downloadAllowed" AS "live_right_has_download_allowed"
        ,"b2cAllowed" AS "live_right_has_b2c_allowed"
        ,"b2bAllowed" AS "live_right_has_b2b_allowed"
        ,"b2bCatchupAllowed" AS "live_right_has_b2b_catchup_allowed"
        ,"contentDistinctionReference" AS "live_right_content_distinction_reference"
        ,"contentDistinction" AS "live_right_content_distinction"
        ,"allowedCountryCodes" AS "live_right_allowed_country_codes"
        ,"allowedCountryCodes_0" AS "live_right_allowed_country_codes_0"
        ,"allowedCountryCodes_1" AS "live_right_allowed_country_codes_1"
        ,"allowedCountryCodes_2" AS "live_right_allowed_country_codes_2"
        ,"disallowedCountryCodes" AS "live_right_disallowed_country_codes"
        ,"nonExclusiveRegions" AS "live_right_non_exclusive_regions"
        ,"nonExclusiveRegions_0" AS "live_right_non_exclusive_regions_0"
        ,"nonExclusiveRegions_1" AS "live_right_non_exclusive_regions_1"
        ,"nonExclusiveRegions_2" AS "live_right_non_exclusive_regions_2"
        ,"allowedAudioLanguages" AS "live_right_allowed_audio_languages"
        ,"commercialPremises" AS "live_right_commercial_premises"
    FROM source
)

select * from renamed
