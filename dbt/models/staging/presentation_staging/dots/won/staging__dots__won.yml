version: 2
models:
  - name: staging__dots__won_contract_current
    description: "The current version of the WON meta data describing the parent contract we have with a given rights holder"
    columns:
      - &DBT_INSERT_DTTS
        name: DBT_INSERT_DTTS
        description: "Date/Timestamp the record was inserted by dbt to the curated layer"
        quote: true

      - &EDM_INSERT_DTTS
        name: EDM_INSERT_DTTS
        description: "Date/Timestamp the record was inserted by the ingest process into the open layer"
        quote: true

      - &last_updated_timestamp
        name: last_updated_timestamp
        description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
        quote: true

      - &is_test
        name: is_test
        description: "This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false."
        quote: true

      - &envelope_schema_version
        name: envelope_schema_version
        description: "The version of the envelope schema. This version must be 2."
        quote: true

      - &payload_id
        name: payload_id
        description: "A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message."
        quote: true

      - &payload_type
        name: payload_type
        description: "A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers."
        quote: true

      - &payload_state
        name: payload_state
        description: "An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?"
        quote: true

      - &payload
        name: payload
        description: "An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer."
        quote: true

      - &change_timestamp
        name: change_timestamp
        description: "Last change time when updated from WON (Converted to TIMESTAMP when possible)"
        quote: true

      - &change_owner
        name: change_owner
        description: "Source specific email of the user who stimulated the change"
        quote: true

      - &change_source
        name: change_source
        description: "Defines what stimulated the update"
        quote: true

      - name: contract_id
        description: "Unique id of this event in WON i.e. External Reference"
        quote: true

      - name: contract_name
        description: "Name of the contract, created using the COALESCE of the two contractName and name fields, needed as there was a change of the field name"
        quote: true

      - name: contract_status
        description: "Status of the contract"
        quote: true

      - name: contract_start_date
        description: "Global start date of the contract"
        quote: true

      - name: contract_end_date
        description: "Global end date of the contract"
        quote: true

      - name: rights_holder_id
        description: "Reference ID of the contract's rights holder"
        quote: true

  - name: staging__dots__won_liveencoding_current
    description: "The current version of the WON meta data describing a scheduled live encoding. Can be linked to the live event via liveEncodingId"
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload
      - *change_timestamp
      - *change_owner
      - *change_source

      - name: live_encoding_id
        description: "Live encoding ID. unique per payload but not for allocation type. Used to map to live event table"
        quote: true

      - name: fixture_id
        description: "ID that represents the fixture associated to the scheduled transmission"
        quote: true

      - name: live_encoding_title
        description: "Title of the fixture id"
        quote: true

      - name: live_encoding_status
        description: "Status of the payload"
        quote: true

      - name: live_encoding_event_start_timestamp
        description: "The UTC time of the Pre-KO of the event"
        quote: true

      - name: live_encoding_event_end_timestamp
        description: "The UTC time of the event end"
        quote: true

      - name: live_encoding_time_allocations
        description: "List of dictionary containing information about allocation"
        quote: true

  - name: staging__dots__won_liveevent_current
    description: "The current version of the WON meta data describing a scheduled live event in a designated DAZN Region"
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload
      - *change_timestamp
      - *change_owner
      - *change_source

      - name: live_event_id
        description: "Unique id of this event in WON i.e. External Reference"
        quote: true

      - name: live_event_title
        description: "The title of the event"
        quote: true

      - name: live_event_start_timestamp
        description: "The UTC time of the Pre-KO of the event"
        quote: true

      - name: live_event_end_timestamp
        description: "The UTC time of the event end"
        quote: true

      - name: live_event_schedule_version
        description: "Schedule version of Transmission from WON (Active, Alternative) - Alternative is used for pre-planning"
        quote: true

      - name: live_event_status
        description: "The status of the regional transmission in WON"
        quote: true

      - name: fixture_id
        description: "ID that represents the master fixture associated to the scheduled transmission"
        quote: true

      - name: dummy_fixture_id
        description: "ID that represents the dummy fixture if one is relevant for the scheduled transmission, created using the COALESCE of the two mflDaznDummyFixtureId and dummyFixtureUuid fields, needed as there was a change of the field name"
        quote: true

      - name: live_right_id
        description: "Reference ID to the WON exploitation rights used to schedule this event, created using the COALESCE of the two rightsId and rightId fields, needed as there was a change of the field name"
        quote: true

      - name: live_product_id
        description: "Reference to the  Live Product associated with this live event"
        quote: true

      - name: group_id
        description: "ID used to group separate events together as a single shared live stream"
        quote: true

      - name: live_encoding_id
        description: "Reference ID to the associated Live Encoding data"
        quote: true

      - name: region_id
        description: "ID that represents the DAZN region and MCS Product"
        quote: true

      - name: live_event_type_name
        description: "Represents the TX Event Type used by Bookings for appropriate channel assignment E.g. TX50, PBB50, LTD50, OND59, TX59, DCI 50, DCI 59"
        quote: true

      - name: live_event_type_id
        description: "External reference ID of Event type field"
        quote: true

      - name: live_event_broadcast_tier
        description: "The broadcast tier set against the event to indicate the level of production to applied by DAZN"
        quote: true

      - name: live_event_vob_key
        description: "Represents the voice over booth resources will be required for the event"
        quote: true

      - name: live_event_best_vob_key
        description: "Represents the best vobKey value for shared events calculated by WON"
        quote: true

      - name: live_event_frame_rate
        description: "Indicates the frame rate in which the event will be delivered on the OTT product"
        quote: true

      - name: live_event_type
        description: "Used to distinguish a scheduled event as LIVE or ASLIVE (Delayed)"
        quote: true

      - name: live_event_platform
        description: "Used to distinguish between scheduled events that are for the OTT platform and others"
        quote: true

      - name: live_event_workflow_type
        description: "Represents the Alternative Workflow used by Planning to indicate a special scenario"
        quote: true

      - name: live_event_gallery_name
        description: "Indicates whether it is a gallery event requiring specific Gallery resources"
        quote: true

      - name: live_event_gallery_id
        description: "ID of the Gallery object"
        quote: true

      - name: live_event_best_gallery
        description: "Represents the best Gallery value for shared events calculated by WON"
        quote: true

      - name: live_event_tx_pod
        description: "Represents the actual TX Pod resource allocated to the event"
        quote: true

      - name: live_event_is_obligated
        description: "Surfaces whether transmission is obligated to be played"
        quote: true

      - name: live_event_is_co_exclusive
        description: "Boolean represnting if DAZN shares exclusivity for the live event"
        quote: true

      - name: live_event_has_ftv_allowed
        description: "Boolean value that indicates whether the live event can be offered as 'free to view"
        quote: true

      - name: live_event_b2b_content_tiering
        description: "List of objects that contain b2bContentTiering name and it’s WON external reference ID"
        quote: true

      - name: live_event_has_ftv_catchup_allowed
        description: "Boolean value that indicates whether the catchup asset of the live event can be offered as 'free to view"
        quote: true

      - name: live_event_is_dci
        description: "Boolean value if event is dci or not"
        quote: true

      - name: live_event_dci_group_name
        description: "Represents the DCI group that the event forms"
        quote: true

      - name: live_event_is_world_feed
        description: "Indicates whether the World Feed commentary can be used or not"
        quote: true

      - name: live_event_world_feed_languages
        description: "A map of one or more world feed commentary languages made available for this event"
        quote: true

      - name: live_event_world_feed_languages_code
        description: "The first value from the worldFeedLanguages array representing the primary world feed commentary language made available for this event"
        quote: true

      - name: live_event_commentary_languages
        description: "A map of one or more commentary languages made available for this event"
        quote: true

      - name: live_event_commentary_languages_code
        description: "The first value from the commentaryLangs array representing the primary commentary language made available for this event"
        quote: true

      - name: live_event_closed_caption_languages
        description: "A map of one or more closed caption languages made available for this event keyed by ISO two-letter language codes"
        quote: true

      - name: live_event_closed_caption_languages_code
        description: "The first value from the closedCaptionLangs array representing the primary caption language made available for this event keyed by ISO two-letter language codes"
        quote: true

      - name: live_event_advertising_compliance_rules
        description: "Array representing the advertising compliance rules of the live event"
        quote: true

      - name: live_event_announced_start_timestamp
        description: "Announced time in UTC dropdown on transmission"
        quote: true

      - name: live_event_sponsoring
        description: "Represents the sponsoring field from Transmission"
        quote: true

      - name: live_event_ht_filler
        description: "Represents the HT Filler that needs to be planned for"
        quote: true

      - name: live_event_regional_supplier
        description: "Indicates that within a certain territory the content is actually being supplied by another rights holder"
        quote: true

      - name: live_event_regional_supplier_id
        description: "Indicates that within a certain territory the content is actually being supplied by another rights holder - only ID"
        quote: true

      - name: live_event_closed_captions
        description: "A map of one or more closed caption languages made available for this event keyed by ISO two-letter language codes along with the supplier Details"
        quote: true
        
  - name: staging__dots__won_liveproduct_current
    description: "The currrent version of the live Product envelope contains the meta data describing the full-game product. The product contains generic info that is re-used for the relevant scheduled full-game events"
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload
      - *change_timestamp
      - *change_owner
      - *change_source

      - name: live_product_id
        description: "Unique ID of the WON product"
        quote: true

      - name: live_product_title
        description: "Description of Product"
        quote: true

      - name: live_product_type
        description: "The type of product (E.g. Series, Episode, ...)"
        quote: true

      - name: fixture_id
        description: "ID of the related MFL fixture"
        quote: true

      - name: competition_id
        description: "ID of the related MFL competition"
        quote: true

      - name: live_product_ruleset_name
        description: "Name of the related MFL ruleset"
        quote: true

      - name: tournament_calendar_id
        description: "ID of the related MFL tournament calendar"
        quote: true

      - name: live_product_world_feed_languages
        description: "Worls feed languages on the source video received by DAZN"
        quote: true

      - name: live_product_world_feed_languages_code
        description: "The first value from the worldFeedLanguages array representing the primary Worls feed language on the source video received by DAZN"
        quote: true

      - name: sport_id
        description: "ID of the related MFL sport"
        quote: true

      - name: sport_name
        description: "Name of the related MFL sport"
        quote: true

      - name: live_product_support_tier
        description: "Support Tier from WON"
        quote: true

      - name: live_product_order_of_play
        description: "Order of Play from WON"
        quote: true

      - name: live_product_expected_duration
        description: "Expected duration of the live event"
        quote: true

      - name: live_product_contractual_compliance
        description: "List of keys of prohibited ad types defined by rights compliance"
        quote: true

  - name: staging__dots__won_liveright_current
    description: "The current version of the WON meta data describing the rights available to exploit for a given live event"
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload
      - *change_timestamp
      - *change_owner
      - *change_source

      - name: live_right_id
        description: "Unique id of this right in WON i.e. External Reference"
        quote: true

      - name: live_right_title
        description: "Title of the linked product"
        quote: true

      - name: live_right_start_date
        description: "Start date of the exploitation right, created using the COALESCE of the two startDate and startDateFormula fields, needed as there was a change of the field name"
        quote: true

      - name: live_right_end_date
        description: "End date of the exploitation right, created using the COALESCE of the two endDate and endDateFormula fields, needed as there was a change of the field name"
        quote: true

      - name: live_right_type
        description: "The type of the right E.g. OTT, ..."
        quote: true

      - name: live_right_product_type
        description: "The type of the linked product E.g. Series, Episode"
        quote: true

      - name: region_id
        description: "ID that represents the DAZN region and MCS Product"
        quote: true

      - name: live_right_planned_fixtures
        description: "Number of runs (aka commitments) allowed for the given exploitation right"
        quote: true

      - name: live_right_status
        description: "The status of the exploitation right "
        quote: true

      - name: live_right_is_live
        description: "Boolean value which indicates if the exploitation right applies to LIVE events"
        quote: true

      - name: live_right_is_as_live
        description: "Boolean value which indicates if the exploitation right applies to ASLIVE (Delayed) events"
        quote: true

      - name: tournament_calendar_id
        description: "ID of the related MFL tournament calendar"
        quote: true

      - name: contract_id
        description: "Reference ID for exploitation right's parent contract"
        quote: true

      - name: live_right_has_ftv_allowed
        description: "Boolean value that indicates whether live events for the given right can be offered as 'free to view"
        quote: true

      - name: live_right_has_download_allowed
        description: "Boolean value that indicates whether live events for the given right can be offered as 'downloadable"
        quote: true

      - name: live_right_has_b2c_allowed
        description: "Boolean value indicating whether the rights are allowed for DAZN's B2C product"
        quote: true

      - name: live_right_has_b2b_allowed
        description: "Boolean value indicating whether the rights are allowed for DAZN's B2B product"
        quote: true

      - name: live_right_has_b2b_catchup_allowed
        description: "Boolean value indicating whether the rights are allowed on DAZN's B2B product as catchup content"
        quote: true

      - name: live_right_content_distinction_reference
        description: "The ID of the Content Distinction"
        quote: true

      - name: live_right_content_distinction
        description: "The content distinction name breaking out the content type E.g. Highlights, ..."
        quote: true

      - name: live_right_allowed_country_codes
        description: "A array of countries allowed to consume events for the given rights"
        quote: true

      - name: live_right_allowed_country_codes_0
        description: "The first value of the array in field allowedCountryCodes representing countries allowed to consume events for the given rights"
        quote: true

      - name: live_right_allowed_country_codes_1
        description: "The second value of the array in field allowedCountryCodes representing countries allowed to consume events for the given rights"
        quote: true

      - name: live_right_allowed_country_codes_2
        description: "The third value of the array in field allowedCountryCodes representing countries allowed to consume events for the given rights"
        quote: true

      - name: live_right_disallowed_country_codes
        description: "An array of countries blocked from consuming events for the given rights"
        quote: true

      - name: live_right_non_exclusive_regions
        description: "An array of non exclusive regions for the given right"
        quote: true

      - name: live_right_non_exclusive_regions_0
        description: "The first value of the array in field nonExclusiveRegions representing non exclusive regions for the given right"
        quote: true

      - name: live_right_non_exclusive_regions_1
        description: "The second value of the array in field nonExclusiveRegions representing non exclusive regions for the given right"
        quote: true

      - name: live_right_non_exclusive_regions_2
        description: "The third value of the array in field nonExclusiveRegions representing non exclusive regions for the given right"
        quote: true

      - name: live_right_allowed_audio_languages
        description: "An array of audio languages allowed to be applied against the event"
        quote: true

      - name: live_right_commercial_premises
        description: "An array of commercial premise types that are allowed to receive these rights"
        quote: true

  - name: staging__dots__won_prelimliveevent_current
    description: "The current version of the WON meta data describing a live event in the preliminary schedule for a designated DAZN Region"
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload
      - *change_timestamp
      - *change_owner
      - *change_source

      - name: prelim_live_event_id
        description: "Unique id of this event in WON i.e. External Reference"
        quote: true

      - name: prelim_live_event_title
        description: "The title of the event"
        quote: true

      - name: prelim_live_event_start_timestamp
        description: "The UTC time of the Pre-KO of the event"
        quote: true

      - name: prelim_live_event_end_timestamp
        description: "The UTC time the event end"
        quote: true

      - name: prelim_live_event_schedule_version
        description: "Schedule version of Transmission from WON (Active, Alternative) - Alternative is used for pre-planning"
        quote: true

      - name: prelim_live_event_status
        description: "The status of the regional transmission in WON"
        quote: true

      - name: fixture_id
        description: "ID that represents the master fixture associated to the scheduled transmission"
        quote: true

      - name: dummy_fixture_id
        description: "ID that represents the dummy fixture if one is relevant for the scheduled transmission"
        quote: true

      - name: live_right_id
        description: "Reference ID to the WON exploitation rights used to schedule this event"
        quote: true

      - name: live_product_id
        description: "Reference ID to the WON product used to schedule this event"
        quote: true

      - name: region_id
        description: "ID that represents the DAZN region and MCS Product"
        quote: true

      - name: prelim_live_event_type_name
        description: "Represents the TX Event Type used by Bookings for appropriate channel assignment E.g. TX50, PBB50, LTD50, OND59, TX59, DCI 50, DCI 59"
        quote: true

      - name: prelim_live_event_type_id
        description: "External reference ID of Event type field"
        quote: true

      - name: prelim_live_event_broadcast_tier
        description: "The broadcast tier set against the event to indicate the level of production to applied by DAZN"
        quote: true

      - name: prelim_live_event_vob_key
        description: "Represents the voice over booth resources will be required for the event"
        quote: true

      - name: prelim_live_event_frame_rate
        description: "Indicates the frame rate in which the event will be delivered on the OTT product"
        quote: true

      - name: prelim_live_event_type
        description: "Used to distinguish a scheduled event as LIVE or ASLIVE (Delayed)"
        quote: true

      - name: prelim_live_event_platform
        description: "Used to distinguish between scheduled events that are for the OTT platform and others"
        quote: true

      - name: prelim_live_event_workflow_type
        description: "Represents the Alternative Workflow used by Planning to indicate a special scenario"
        quote: true

      - name: prelim_live_event_is_world_feed
        description: "Indicates whether the World Feed commentary can be used or not"
        quote: true

      - name: prelim_live_event_world_feed_languages
        description: "A map of one or more world feed commentary languages made available for this event"
        quote: true

      - name: prelim_live_event_world_feed_languages_code
        description: "The first value from the worldFeedLanguages array representing the primary world feed commentary language made available for this event"
        quote: true

      - name: prelim_live_event_commentary_languages
        description: "A map of one or more commentary languages made available for this event"
        quote: true

      - name: prelim_live_event_commentary_languages_code
        description: "The first value from the commentaryLangs array representing the primary commentary language made available for this event"
        quote: true

      - name: prelim_live_event_gallery_name
        description: "Indicates whether it is a gallery event requiring specific Gallery resources"
        quote: true

      - name: prelim_live_event_gallery_id
        description: "ID of the Gallery object"
        quote: true

      - name: prelim_live_event_is_co_exclusive
        description: "Exclusive to DAZN field from transmission"
        quote: true

      - name: prelim_live_event_has_ftv_allowed
        description: "Boolean value that indicates whether the live event can be offered as 'free to view"
        quote: true

      - name: prelim_live_event_b2b_content_tierings
        description: "List of objects that contain b2bContentTiering name and it’s WON external reference ID"
        quote: true

      - name: prelim_live_event_is_dci
        description: "Boolean value if event is dci or not"
        quote: true

      - name: prelim_live_event_dci_group_name
        description: "Represents the DCI group that the event forms"
        quote: true

      - name: prelim_live_event_is_obligated
        description: "mustPlay field from transmission"
        quote: true

      - name: prelim_live_event_dropped_reason
        description: "Reason for not selecting"
        quote: true

  - name: staging__dots__won_region_current
    description: "The current versio of the meta data describing a DAN Region and its usage within WON, and how it correlates to legacy entities such as DAZN CMS Organisations and DAZN Feed Outlets"
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload
      - *change_timestamp
      - *change_owner
      - *change_source

      - name: region_id
        description: "ID that represents the DAZN region and MCS Product"
        quote: true

      - name: region_name
        description: "Name of the DAZN region"
        quote: true

      - name: region_default_timezone
        description: "The default timezone of the region"
        quote: true

      - name: region_ad_market
        description: "Region identifier uniquely used for advertising purposes"
        quote: true

      - name: region_organisation_id
        description: "ID that represents the related DCMS organisation"
        quote: true

      - name: region_outlet_key
        description: "The authentication key used for the relevant DAZN Feed outlet for given DAZN region"
        quote: true

      - name: region_m2a_key
        description: "Region identifier uniquely used by M2A"
        quote: true

      - name: region_countries
        description: "An array of countries associated with the region"
        quote: true

  - name: staging__dots__won_rightsholder_current
    description: "The current versio of the WON meta data describing the rights holder entity"
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload
      - *change_timestamp
      - *change_owner
      - *change_source

      - name: rights_holder_id
        description: "ID that represents the rights holder"
        quote: true

      - name: rights_holder_name
        description: "Name of the rights holder, created using the COALESCE of the two rightsHolderName and name fields, needed as there was a change of the field name"
        quote: true

  - name: staging__dots__won_timeallocations_current
    description: "Derived from curated__dots__won_liveencoding_timeallocations_current. The time_allocation nested values exploded, the other fields are the same. Primary key on AllocId"
    columns:
      - name: live_encoding_id
        description: "Live encoding ID. unique per payload but not for allocation type. Used to map to live event table"
        quote: true

      - name: time_allocations_allocation_id
        description: "Flattened from timeAllocations, This is the ID of the allocation sub-record"
        quote: true

      - name: time_allocations_duration
        description: "Flattened from timeAllocations, This is the duration of the allocation sub-record"
        quote: true

      - name: time_allocations_name
        description: "Flattened from timeAllocations, This is the name of the allocation sub-record"
        quote: true

      - name: time_allocations_secondary_events
        description: "Flattened from timeAllocations, This is a list of secondary events of the allocation sub-record"
        quote: true

      - name: time_allocations_secondary_events_splice_event_id_0
        description: "Flattened from timeAllocations, eventId extracted from the 1st record of the secondary events."
        quote: true

      - name: time_allocations_secondary_events_type_0
        description: "Flattened from timeAllocations, type extracted from the 1st record of the secondary events."
        quote: true

      - name: time_allocations_secondary_events_duration_0
        description: "Flattened from timeAllocations, duration extracted from the 1st record of the secondary events."
        quote: true

      - name: time_allocations_secondary_events_splice_event_id_1
        description: "Flattened from timeAllocations, eventId extracted from the 2nd record of the secondary events."
        quote: true

      - name: time_allocations_secondary_events_type_1
        description: "Flattened from timeAllocations, type extracted from the 2nd record of the secondary events."
        quote: true

      - name: time_allocations_secondary_events_duration_1
        description: "Flattened from timeAllocations, duration extracted from the 2nd record of the secondary events."
        quote: true

      - name: time_allocations_source
        description: "Flattened from timeAllocations, This is the source of the allocation sub-record"
        quote: true

      - name: time_allocations_start_timestamp
        description: "Flattened from timeAllocations, This is the start time of the allocation sub-record"
        quote: true

      - name: time_allocations_title
        description: "Flattened from timeAllocations, This is the title of the allocation sub-record"
        quote: true

      - name: time_allocations_tx_events
        description: "Flattened from timeAllocations, this is a list of tx records inside the allocation sub-record. Can be up to a 10"
        quote: true

      - name: time_allocations_type
        description: "Flattened from timeAllocations, This is the type of the allocation sub-record"
        quote: true

      - name: time_allocations_id
        description: "Flattened from timeAllocations, This is the uuid of the allocation sub-record"
        quote: true

  - name: staging__dots__won_vodevent_current
    description: ""
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload
      - *change_timestamp
      - *change_owner
      - *change_source

      - name: vod_event_id
        description: "Unique id of this non-live event in WON i.e. External Reference"
        quote: true

      - name: vod_event_title
        description: "The title of the event"
        quote: true

      - name: vod_event_status
        description: "The status of the regional transmission in WON"
        quote: true

      - name: vod_event_schedule_version
        description: "Schedule version of Transmission from WON (Active, Alternative) - Alternative is used for pre-planning"
        quote: true

      - name: vod_event_start_timestamp
        description: "The date/time that the VOD content will be available on DAZN, created as the combination of all fields that have been populated for this data point"
        quote: true

      - name: vod_event_end_timestamp
        description: "The UTC time the event end, created as the combination of all fields that have been populated for this data point"
        quote: true

      - name: vod_right_id
        description: "Reference ID to the WON exploitation rights used to schedule this non-live event, created using the COALESCE of the two rightId and rightsId fields, needed as there was a change of the field name"
        quote: true

      - name: vod_media_id
        description: "Reference ID to the associated WON media asset, created using the COALESCE of the two rightId and rightsId fields, needed as there was a change of the field name"
        quote: true

      - name: vod_product_id
        description: "Reference ID to the associated WON product "
        quote: true

      - name: region_id
        description: "ID that represents the DAZN region and MCS Product"
        quote: true

      - name: vod_event_is_obligated
        description: "mustPlay field from transmission"
        quote: true

      - name: vod_event_has_ftv_allowed
        description: "Boolean value that indicates whether the non-live event can be offered as 'free to view"
        quote: true

      - name: vod_event_advertising
        description: "Represents the advertising field from Transmission"
        quote: true

      - name: vod_event_duration
        description: "Expected duration of the live event"
        quote: true

  - name: staging__dots__won_vodmedia_current
    description: ""
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload
      - *change_timestamp
      - *change_owner
      - *change_source

      - name: vod_media_id
        description: "Unique ID to the WON media asset "
        quote: true

      - name: vod_media_label
        description: "Label used to identify the purpose of the media asset, created using the COALESCE of the two wonMediaLabel and label fields, needed as there was a change of the field name"
        quote: true

      - name: vod_product_id
        description: "External reference (ID) of linked Vod Product"
        quote: true

      - name: vod_media_old_file_name
        description: "Filename required for the produced media asset"
        quote: true

      - name: vod_media_team_assigned
        description: "The team assigned to product the media asset"
        quote: true

      - name: vod_media_has_reversion_required
        description: "Boolean value indicating whether the media asset needs to be re-versioned into another media asset with different audio language"
        quote: true

      - name: vod_media_has_closed_captions_required
        description: "Boolean value indicating if closed captions need to be added to produced media asset"
        quote: true

      - name: vod_media_audio_languages
        description: "Audio Languages used within the Media"
        quote: true

      - name: vod_media_audio_languages_value
        description: "The first value from the audioLanguages array representing the primary audio Languages used within the Media"
        quote: true

  - name: staging__dots__won_vodproduct_current
    description: ""
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload
      - *change_timestamp
      - *change_owner
      - *change_source

      - name: vod_product_id
        description: "Unique ID of the WON product "
        quote: true

      - name: vod_product_title
        description: "Title used as reference to the original product title"
        quote: true

      - name: vod_product_type
        description: "The type of product"
        quote: true

      - name: fixture_id
        description: "ID of the related MFL fixture"
        quote: true

      - name: vod_product_version
        description: "Version of the product"
        quote: true

      - name: competition
        description: "Array containing metadata relating to the competition of the product"
        quote: true

      - name: competition_id
        description: "ID of the related MFL competition, created using the COALESCE of the two competition_uuid and competitionUuid fields, needed as there was a change of the field name"
        quote: true

      - name: competition_name
        description: "Name of the related MFL competition, created using the COALESCE of the two competition_name and competitionName fields, needed as there was a change of the field name"
        quote: true

      - name: vod_product_ruleset_name
        description: "Name of the related MFL ruleset"
        quote: true

      - name: vod_product_location
        description: "Location of the related MFL Competition"
        quote: true

      - name: tournament_calendar_id
        description: "ID of the related MFL tournament calendar"
        quote: true

      - name: contestant_ids
        description: "Array of IDs of the related MFL contestants"
        quote: true

      - name: sport_id
        description: "ID of the related MFL sport, created using the COALESCE of the two sport_uuid and sportUuid fields, needed as there was a change of the field name"
        quote: true

      - name: sport_name
        description: "Name of the related MFL sport, created using the COALESCE of the two sport_name and sportName fields, needed as there was a change of the field name"
        quote: true

      - name: vod_product_content_distinction_id
        description: "External reference of the relevant Content Distinction (aka Video Type), created using the COALESCE of the two contentDistinction_uuid and contentDistinctionReference fields, needed as there was a change of the field name"
        quote: true

      - name: vod_product_content_distinction_name
        description: "Name of the relevant Content Distinction (aka Video Type), created using the COALESCE of the two contentDistinction_name and contentDistinction fields, needed as there was a change of the field name"
        quote: true

      - name: vod_product_source_language
        description: "Audio language on the source video received by DAZN"
        quote: true

      - name: vod_product_has_closed_captions_available
        description: "Boolean value indicated if there are closed captions on the source video received by DAZN"
        quote: true

      - name: vod_product_closed_captions_language
        description: "Language of the closed captions provided on the source video received by DAZN"
        quote: true

      - name: vod_product_arrival_method
        description: "Method in which DAZN received the source video"
        quote: true

      - name: vod_product_synopsis
        description: "Short description of the non-live event"
        quote: true

      - name: vod_product_expected_duration
        description: "Expected duration of the live event"
        quote: true

      - name: vod_product_contractual_compliance
        description: "List of keys of prohibited ad types defined by rights compliance"
        quote: true

  - name: staging__dots__won_vodright_current
    description: ""
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload
      - *change_timestamp
      - *change_owner
      - *change_source

      - name: vod_right_id
        description: "Unique id of this right in WON i.e. External Reference"
        quote: true

      - name: vod_right_title
        description: "Title of the linked product"
        quote: true

      - name: vod_right_start_date
        description: "Start date of the exploitation right"
        quote: true

      - name: vod_right_end_date
        description: "End date of the exploitation right"
        quote: true

      - name: vod_right_type
        description: "The type of the right E.g. OTT, ..."
        quote: true

      - name: region_id
        description: "ID that represents the DAZN region and MCS Product"
        quote: true

      - name: vod_product_id
        description: "Reference ID to the associated WON product "
        quote: true

      - name: vod_right_product_versions
        description: "Indicates if the rights are relevant to Non-Live and/or Highlights, created using the COALESCE of the two productVersion and productVersions fields, needed as there was a change of the field name"
        quote: true

      - name: vod_right_run_count
        description: "Number of runs (aka commitments) allowed for the given exploitation right"
        quote: true

      - name: vod_right_status
        description: "The status of the exploitation right "
        quote: true

      - name: tournament_calendar_id
        description: "ID of the related MFL tournament calendar"
        quote: true

      - name: competition_id
        description: "ID of the related MFL competition"
        quote: true

      - name: contract_id
        description: "Reference ID for exploitation right's parent contract"
        quote: true

      - name: vod_right_has_ftv_allowed
        description: "Boolean value that indicates whether live events for the given right can be offered as 'free to view"
        quote: true

      - name: vod_right_has_download_allowed
        description: "Boolean value that indicates whether live events for the given right can be offered as 'downloadable"
        quote: true

      - name: vod_right_has_b2b_allowed
        description: "Boolean value indicating whether the rights are allowed for DAZN's B2B product"
        quote: true

      - name: vod_right_has_b2c_allowed
        description: "Boolean value indicating whether the rights are allowed for DAZN's B2C product"
        quote: true

      - name: vod_right_is_cleared_for_dazn_player
        description: "Boolean value that indicates if the content can be re-used on the DAZN Player product"
        quote: true

      - name: vod_right_is_cleared_for_social
        description: "Boolean value that indicates if the content can be re-used on social media"
        quote: true

      - name: vod_right_is_pre_recorded
        description: "Boolean value that indicates if the content has been pre-recorded"
        quote: true

      - name: vod_right_disallowed_country_codes
        description: "An array of countries blocked from consuming events for the given rights"
        quote: true

      - name: vod_right_allowed_audio_languages
        description: "An array of audio languages allowed to be applied against the event"
        quote: true

      - name: vod_right_allowed_country_codes
        description: "A list of countries allowed to consume events for the given rights"
        quote: true

      - name: vod_right_allowed_country_codes_0
        description: "The first value of the array in field allowedCountryCodes representing countries allowed to consume events for the given rights"
        quote: true

      - name: vod_right_allowed_country_codes_1
        description: "The second value of the array in field allowedCountryCodes representing countries allowed to consume events for the given rights"
        quote: true

      - name: vod_right_allowed_country_codes_2
        description: "The third value of the array in field allowedCountryCodes representing countries allowed to consume events for the given rights"
        quote: true

      - name: vod_right_non_exclusive_regions
        description: "A list of non exclusive regions for the given right"
        quote: true

      - name: vod_right_non_exclusive_regions_0
        description: "The first value of the array in field nonExclusiveRegions representing non exclusive regions for the given right"
        quote: true

      - name: vod_right_non_exclusive_regions_1
        description: "The second value of the array in field nonExclusiveRegions representing non exclusive regions for the given right"
        quote: true

      - name: vod_right_non_exclusive_regions_2
        description: "The third value of the array in field nonExclusiveRegions representing non exclusive regions for the given right"
        quote: true
