/*
    Null only fields we removed:
        - vod_right_b2b_commercial_premises
        - vod_right_commercial_premises
*/



WITH source AS (
    SELECT * FROM {{ source('CURATED','curated__dots__won_vodright_current' )}}
)

, renamed AS (
    SELECT
        -- The macro is defined in dbt/macros/Dots/won_payload_columns.sql
        {{ won_payload_columns() }}
        ,"rightId" AS "vod_right_id"
        ,"title" AS "vod_right_title"
        ,"startDate" AS "vod_right_start_date"
        ,"endDate" AS "vod_right_end_date"
        ,"type" AS "vod_right_type"
        ,"regionUuid" AS "region_id"
        ,"productId" AS "vod_product_id"
        ,"productVersions__coalesce" AS "vod_right_product_versions"
        ,"runCount" AS "vod_right_run_count"
        ,"status" AS "vod_right_status"
        ,"tournamentCalendarUuid" AS "tournament_calendar_id"
        ,"competitionUuid" AS "competition_id"
        ,"contractId" AS "contract_id"
        ,"ftvAllowed" AS "vod_right_has_ftv_allowed"
        ,"downloadAllowed" AS "vod_right_has_download_allowed"
        ,"b2bAllowed" AS "vod_right_has_b2b_allowed"
        ,"b2cAllowed" AS "vod_right_has_b2c_allowed"
        ,"clearedForDaznPlayer" AS "vod_right_is_cleared_for_dazn_player"
        ,"clearedForSocial" AS "vod_right_is_cleared_for_social"
        ,"preRecorded" AS "vod_right_is_pre_recorded"
        ,"disallowedCountryCodes" AS "vod_right_disallowed_country_codes"
        ,"allowedAudioLanguages" AS "vod_right_allowed_audio_languages"
        ,"allowedCountryCodes" AS "vod_right_allowed_country_codes"
        ,"allowedCountryCodes_0" AS "vod_right_allowed_country_codes_0"
        ,"allowedCountryCodes_1" AS "vod_right_allowed_country_codes_1"
        ,"allowedCountryCodes_2" AS "vod_right_allowed_country_codes_2"
        ,"nonExclusiveRegions" AS "vod_right_non_exclusive_regions"
        ,"nonExclusiveRegions_0" AS "vod_right_non_exclusive_regions_0"
        ,"nonExclusiveRegions_1" AS "vod_right_non_exclusive_regions_1"
        ,"nonExclusiveRegions_2" AS "vod_right_non_exclusive_regions_2"
    FROM source
)

select * from renamed
