
WITH source AS (
    SELECT * FROM {{ source('CURATED','curated__dots__won_vodmedia_current' )}}
)

, renamed AS (
    SELECT
        -- The macro is defined in dbt/macros/Dots/won_payload_columns.sql
        {{ won_payload_columns() }}
        ,"vodMediaId" AS "vod_media_id"
        ,"label__coalesce" AS "vod_media_label"
        ,"vodProductId" AS "vod_product_id"
        ,"oldFileName" AS "vod_media_old_file_name"
        ,"teamAssigned" AS "vod_media_team_assigned"
        ,"reversionRequired" AS "vod_media_has_reversion_required"
        ,"closedCaptionsRequired" AS "vod_media_has_closed_captions_required"
        ,"audioLanguages" AS "vod_media_audio_languages"
        ,"audioLanguages_value" AS "vod_media_audio_languages_value"
    FROM source
)

select * from renamed
