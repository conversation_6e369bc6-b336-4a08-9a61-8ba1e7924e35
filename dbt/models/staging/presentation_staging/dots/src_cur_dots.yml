version: 2

sources:
  - name: CURATED
    database: TRANSFORMATION_PROD
    tables:
      - name: CURATED__DOTS__PCMS_LIVESTREAM
        description: "PCMS livestream data with channel information"
        columns:
          - name: payloadId
            description: "Unique identifier for the payload"
            quote: true
          - name: fixture_id
            description: "MFL fixture identifier"
            quote: true
          - name: region
            description: "Region/territory for the stream"
            quote: true
          - name: daznChannelId
            description: "DAZN channel identifier from PCMS"
            quote: true
          - name: feedStartTime
            description: "Feed start timestamp"
            quote: true
          - name: feedEndTime
            description: "Feed end timestamp"
            quote: true
          - name: streamStartTime
            description: "Stream start timestamp"
            quote: true
          - name: streamEndTime
            description: "Stream end timestamp"
            quote: true
          - name: lastModTime
            description: "Last modification timestamp"
            quote: true
          - name: EDM_INSERT_DTTS
            description: "EDM insert timestamp"
            quote: true

      - name: CURATED__DOTS__GMR_ARTICLES
        description: "DOTs GMR Articles messges with payload variant flattened"
        columns:
          - name: payloadId
            description: A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message.
            quote: true

          - name: payloadType
            description: A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers.
            quote: true

          - name: payloadState
            description: An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?
            quote: true

          - name: payload
            description: An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer.
            quote: true

          - name: lastUpdatedTime
            description: An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored.
            quote: true

          - name: ttl
            description: An ISO Date formatted string defining when the message expires.
            quote: true

          - name: isTest
            description: This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false.
            quote: true

          - name: envelopeSchemaVersion
            description: "The version of the envelope schema. This version must be 2."
            quote: true

          - name: payload__action
            description: The action message from within the payload
            quote: true

          - name: payload__entity
            description: The entity message from within the payload
            quote: true

          - name: payload__guid
            description: The guid message from within the payload, this details the article ID.
            quote: true

          - name: payload__updated
            description: The updated date message from within the payload, details when the update last happened, epoch.
            quote: true

          - name: payload__entitlement_ids
            description: The entitlement IDs from within the payload.
            quote: true
