
WITH source AS (
    SELECT * FROM {{ source('CURATED','curated__dots__mfl_stage_current' )}}
)

, renamed AS (
    SELECT
        -- The macro is defined in dbt/macros/Content/mfl_payload_columns.sql
        {{ mfl_payload_columns() }}
        ,"stageUUID" AS "stage_id"
        ,"stageName" AS "stage_name"
        ,"stageDateStart__timestamp"::DATE AS "stage_start_date"
        ,"stageDateEnd__timestamp"::DATE AS "stage_end_date"
        ,"extraTime" AS "stage_has_extra_time"
        ,"penaltyShootout" AS "stage_has_penalty_shootout"
        ,"tournamentCalUUID" AS "tournament_calendar_id"
    FROM source
)

select * from renamed
