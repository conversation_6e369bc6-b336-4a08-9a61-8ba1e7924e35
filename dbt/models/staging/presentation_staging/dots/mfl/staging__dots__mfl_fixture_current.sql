
WITH source AS (
    SELECT * FROM {{ source('CURATED','curated__dots__mfl_fixture_current' )}}
)

, renamed AS (
    SELECT
        -- The macro is defined in dbt/macros/Content/mfl_payload_columns.sql
        {{ mfl_payload_columns() }}
        ,"uuid" AS "fixture_id"
        ,"description" AS "fixture_description"
        ,"timeKnown" AS "fixture_time_known"
        ,"datetime__timestamp" AS "fixture_start_timestamp"
        ,"status" AS "fixture_status"
        ,"competitionUUID" AS "competition_id"
        ,"contestantUUIDs__coalesce" AS "contestant_ids"
        ,"contestantUUID_0" AS "contestant_id_0"
        ,"contestantUUID_1" AS "contestant_id_1"
        ,"contestantUUID_2" AS "contestant_id_2"
        ,"contestantUUID_3" AS "contestant_id_3"
        ,"contestantUUID_4" AS "contestant_id_4"
        ,"contestantUUID_5" AS "contestant_id_5"
        ,"rulesetUUID" AS "ruleset_id"
        ,"sportUUID" AS "sport_id"
        ,"stageUUID" AS "stage_id"
        ,"tournamentCalUUID" AS "tournament_calendar_id"
        ,"venueUUID" AS "venue_id"
    FROM source
)

select * from renamed
