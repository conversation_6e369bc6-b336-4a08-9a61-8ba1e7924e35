version: 2
sources:
  - name: CURATED
    database: TRANSFORMATION_PROD
    tables:
      - name: curated__dots__mfl_competition_current
        description: "Table containing the current version of all metadata relating to every MFL Competition"
        columns:
          - &DBT_INSERT_DTTS
            name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by dbt to the curated layer"
            quote: true

          - &EDM_INSERT_DTTS
            name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted by the ingest process into the open layer"
            quote: true

          - &lastUpdatedTime
            name: lastUpdatedTime
            description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
            quote: true

          - &isTest
            name: isTest
            description: "This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false."
            quote: true

          - &envelopeSchemaVersion
            name: envelopeSchemaVersion
            description: "The version of the envelope schema. This version must be 2."
            quote: true

          - &payloadId
            name: payloadId
            description: "A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message."
            quote: true

          - &payloadType
            name: payloadType
            description: "A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers."
            quote: true

          - &payloadState
            name: payloadState
            description: "An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?"
            quote: true

          - &payload
            name: payload
            description: "An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer."
            quote: true

          - name: competitionUUID
            description: "The UUID of the Competition in MFL"
            quote: true

          - name: competitionName
            description: "The name of the competition in MFL"
            quote: true

          - name: sportUUID
            description: "The UUID of the sport directly relating to the MFL Competition"
            quote: true

          - name: rulesetUUID
            description: "The UUID of the ruleset directly relating to the MFL Competition"
            quote: true

          - name: countryUUID
            description: "The UUID of the country directly relating to the MFL Competition"
            quote: true

          - name: suppliers
            description: "An array containing UUID and Entity ID values of the suppliers relating to the competition in MFL"
            quote: true

      - name: curated__dots__mfl_contestant_current
        description: "Table containing the current version of all metadata relating to every MFL Contestant"
        columns:
          - *DBT_INSERT_DTTS
          - *EDM_INSERT_DTTS
          - *lastUpdatedTime
          - *isTest
          - *envelopeSchemaVersion
          - *payloadId
          - *payloadType
          - *payloadState
          - *payload

          - name: contestantUUID
            description: "The UUID of the Contestant in MFL"
            quote: true

          - name: contestantName
            description: "The name of the Contestant in MFL"
            quote: true

          - name: countryUUID
            description: "The country of the contestant"
            quote: true

          - name: rulesetUUIDs
            description: "An array containing the ruleset UUID values the contestant relates to"
            quote: true

      - name: curated__dots__mfl_country_current
        description: "Table containing the current version of all metadata relating to every MFL Country"
        columns:
          - *DBT_INSERT_DTTS
          - *EDM_INSERT_DTTS
          - *lastUpdatedTime
          - *isTest
          - *envelopeSchemaVersion
          - *payloadId
          - *payloadType
          - *payloadState
          - *payload

          - name: countryUUID
            description: "The UUID of the Country in MFL"
            quote: true

          - name: name
            description: "The name of the country in MFL"
            quote: true

          - name: locations
            description: "An array containing location details of the country"
            quote: true

      - name: curated__dots__mfl_fixture_current
        description: "Table containing the current version of all metadata relating to every MFL Fixture ever (including non-DAZN fixtures)"
        columns:
          - *DBT_INSERT_DTTS
          - *EDM_INSERT_DTTS
          - *lastUpdatedTime
          - *isTest
          - *envelopeSchemaVersion
          - *payloadId
          - *payloadType
          - *payloadState
          - *payload

          - name: uuid
            description: "The UUID of the MFL Fixture"
            quote: true

          - name: description
            description: "The description of the MFL Fixture"
            quote: true

          - name: date
            description: "The start date the MFL Fixture"
            quote: true

          - name: time
            description: "The start time of the MFL Fixture"
            quote: true

          - name: timeKnown
            description: "Boolean, True if the start time if known"
            quote: true

          - name: datetime__timestamp
            description: "The timestamp of the start of the MFL Fixture, constructed using the combincation of the date and time fields"
            quote: true

          - name: status
            description: "The status of the MFL Fixture"
            quote: true

          - name: competitionUUID
            description: "The UUID of the Competition relating directly to the MFL Fixture"
            quote: true

          - name: contestantUUIDs
            description: "An array containing the UUID values of none, two or more Contestants relating directly to the MFL Fixture"
            quote: true

          - name: contestantsUUIDs
            description: "An array containing the UUID values of none, two or more Contestants relating directly to the MFL Fixture"
            quote: true

          - name: contestantUUIDs__coalesce
            description: "COALESCE of the two contestantUUIDs and contestantsUUIDs fields, needed as there was a change of the field name"
            quote: true

          - name: contestantUUID_0
            description: "The first value from the contestantUUIDs_COALESCE array representing the first, if any, contestant of the MFL Fixture"
            quote: true

          - name: contestantUUID_1
            description: "The second value from the contestantUUIDs_COALESCE array representing the second, if any, contestant of the MFL Fixture"
            quote: true

          - name: contestantUUID_2
            description: "The third value from the contestantUUIDs_COALESCE array representing the third, if any, contestant of the MFL Fixture"
            quote: true

          - name: contestantUUID_3
            description: "The fourth value from the contestantUUIDs_COALESCE array representing the fourth, if any, contestant of the MFL Fixture"
            quote: true

          - name: contestantUUID_4
            description: "The fifth value from the contestantUUIDs_COALESCE array representing the fifth, if any, contestant of the MFL Fixture"
            quote: true

          - name: contestantUUID_5
            description: "The sixth value from the contestantUUIDs_COALESCE array representing the sixth, if any, contestant of the MFL Fixture"
            quote: true

          - name: rulesetUUID
            description: "The UUID of the Ruleset relating directly to the MFL Fixture"
            quote: true

          - name: sportUUID
            description: "The UUID of the Sport relating directly to the MFL Fixture"
            quote: true

          - name: stageUUID
            description: "The UUID of the Stage relating directly to the MFL Fixture"
            quote: true

          - name: tournamentCalUUID
            description: "The UUID of the Tournament Calendar relating directly to the MFL Fixture"
            quote: true

          - name: venueUUID
            description: "The UUID of the Venue relating directly to the MFL Fixture"
            quote: true

      - name: curated__dots__mfl_ruleset_current
        description: "Table containing the current version of all metadata relating to every MFL Ruleset"
        columns:
          - *DBT_INSERT_DTTS
          - *EDM_INSERT_DTTS
          - *lastUpdatedTime
          - *isTest
          - *envelopeSchemaVersion
          - *payloadId
          - *payloadType
          - *payloadState
          - *payload

          - name: rulesetUUID
            description: "The UUID of the Ruleset in MFL"
            quote: true

          - name: rulesetName
            description: "The name of the ruleset in MFL E.g. Mens, Womens, Juniors, ..."
            quote: true

          - name: sportUUID
            description: "The UUID of the Sport directly relating to the Ruleset"
            quote: true

      - name: curated__dots__mfl_sport_current
        description: "Table containing the current version of all metadata relating to every MFL Sport"
        columns:
          - *DBT_INSERT_DTTS
          - *EDM_INSERT_DTTS
          - *lastUpdatedTime
          - *isTest
          - *envelopeSchemaVersion
          - *payloadId
          - *payloadType
          - *payloadState
          - *payload

          - name: sportUUID
            description: "The UUID of the Sport in MFL"
            quote: true

          - name: sportName
            description: "The name of the Sport in MFL"
            quote: true

          - name: suppliers
            description: "An array containing UUID and Entity ID values of the suppliers relating to the sport in MFL"
            quote: true

      - name: curated__dots__mfl_stage_current
        description: "Table containing the current version of all metadata relating to every MFL Stage"
        columns:
          - *DBT_INSERT_DTTS
          - *EDM_INSERT_DTTS
          - *lastUpdatedTime
          - *isTest
          - *envelopeSchemaVersion
          - *payloadId
          - *payloadType
          - *payloadState
          - *payload

          - name: stageUUID
            description: "The UUID of the Stage in MFL"
            quote: true

          - name: stageName
            description: "The name of the Stage in MFL E.g. Final, Semi-final, Group, ..."
            quote: true

          - name: stageDateStart
            description: "The unixtimestamp of the start of the Stage"
            quote: true

          - name: stageDateStart__timestamp
            description: "The converted timestamp of the start of the Stage"
            quote: true

          - name: stageDateEnd
            description: "The unixtimestamp of the end of the Stage"
            quote: true

          - name: stageDateEnd__timestamp
            description: "The converted timestamp of the end of the Stage"
            quote: true

          - name: extraTime
            description: "Boolean, True if extra time is possible at this stage"
            quote: true

          - name: penaltyShootout
            description: "Boolean, True if a penalty shootout is possible at this stage"
            quote: true

          - name: tournamentCalUUID
            description: "The UUID of the Tournament Calendar relating directly to the Stage"
            quote: true

      - name: curated__dots__mfl_tournamentcalendar_current
        description: "Table containing the current version of all metadata relating to every MFL Tournament Calendar"
        columns:
          - *DBT_INSERT_DTTS
          - *EDM_INSERT_DTTS
          - *lastUpdatedTime
          - *isTest
          - *envelopeSchemaVersion
          - *payloadId
          - *payloadType
          - *payloadState
          - *payload

          - name: tournamentCalUUID
            description: "The UUID of the Tournament Calendar in MFL"
            quote: true

          - name: tournamentCalName
            description: "The name of the Tournament Calendar"
            quote: true

          - name: tournamentCalDateStart
            description: "The unixtimestamp of the start of the Tournament Calendar"
            quote: true

          - name: tournamentCalDateStart__timestamp
            description: "The timestamp of the start of the Tournament Calendar"
            quote: true

          - name: tournamentCalDateEnd
            description: "The unixtimestamp of the end of the Tournament Calendar"
            quote: true

          - name: tournamentCalDateEnd__timestamp
            description: "The timestamp of the end of the Tournament Calendar"
            quote: true

          - name: sportUUID
            description: "The UUID of the Sport directly relating to the Tournament Calendar"
            quote: true

          - name: rulesetUUID
            description: "The UUID of the Ruleset directly relating to the Tournament Calendar"
            quote: true

          - name: competitionUUID
            description: "The UUID of the Competition directly relating to the Tournament Calendar"
            quote: true

          - name: countryUUID
            description: "The UUID of the Country directly relating to the Tournament Calendar"
            quote: true

          - name: suppliers
            description: "An array containing UUID and Entity ID values of the suppliers relating to the competition in MFL"
            quote: true

      - name: curated__dots__mfl_venue_current
        description: "Table containing the current version of all metadata relating to every MFL Venue"
        columns:
          - *DBT_INSERT_DTTS
          - *EDM_INSERT_DTTS
          - *lastUpdatedTime
          - *isTest
          - *envelopeSchemaVersion
          - *payloadId
          - *payloadType
          - *payloadState
          - *payload

          - name: venueUUID
            description: "The UUID of the Venue in MFL"
            quote: true

          - name: venueNameShort
            description: "The short name of the venue in MFL"
            quote: true

          - name: venueNameLong
            description: "The long name of the venue in MFL"
            quote: true

          - name: countryUUID
            description: "The UUID of the country directly relating to the Venue"
            quote: true

      - name: curated__dots__competition_group_current
        description: "Table containing the current version of all metadata relating to every MFL Venue"
        columns:
          - *DBT_INSERT_DTTS
          - *EDM_INSERT_DTTS
          - *lastUpdatedTime
          - *isTest
          - *envelopeSchemaVersion
          - *payloadId
          - *payloadType
          - *payloadState
          - *payload

          - name: competitions
            description: "The list of competitions belonging to the group"
            quote: true

          - name: groupId
            description: "The group id for each set of competitions"
            quote: true

          - name: groupName
            description: "The group name (product group) for each set of competitions"
            quote: true
