version: 2
models:
  - name: staging__dots__mfl_competition_current
    description: "Table containing the current version of all metadata relating to every MFL Competition"
    columns:
      - &DBT_INSERT_DTTS
        name: DBT_INSERT_DTTS
        description: "Date/Timestamp the record was inserted by dbt to the curated layer"
        quote: true

      - &EDM_INSERT_DTTS
        name: EDM_INSERT_DTTS
        description: "Date/Timestamp the record was inserted by the ingest process into the open layer"
        quote: true

      - &last_updated_timestamp
        name: last_updated_timestamp
        description: "An epoch timestamp defining when the message data was last updated. Used to ensure that stale messages are ignored."
        quote: true

      - &is_test
        name: is_test
        description: "This flag should be set to true if the message originates from a test. Allows searching for the message in the stream test message store. Defaults to false."
        quote: true

      - &envelope_schema_version
        name: envelope_schema_version
        description: "The version of the envelope schema. This version must be 2."
        quote: true

      - &payload_id
        name: payload_id
        description: "A unique identifier for a message of a particular type. If two messages are received with the same id, this signifies an update to a particular message."
        quote: true

      - &payload_type
        name: payload_type
        description: "A string defining what type of message this is. Takes the format source/type/subType. Used for routing messages to consumers."
        quote: true

      - &payload_state
        name: payload_state
        description: "An enum describing the current state of the payload. I.e. does the data exist in the source at update time or has it been archived?"
        quote: true

      - &payload
        name: payload
        description: "An object containing the message payload. There are no restrictions around what this object can contain (except a maximum size), and is defined by the producer."
        quote: true

      - name: competition_id
        description: "The UUID of the Competition in MFL"
        quote: true

      - name: competition_name
        description: "The name of the competition in MFL"
        quote: true

      - name: sport_id
        description: "The UUID of the sport directly relating to the MFL Competition"
        quote: true

      - name: ruleset_id
        description: "The UUID of the ruleset directly relating to the MFL Competition"
        quote: true

      - name: country_id
        description: "The UUID of the country directly relating to the MFL Competition"
        quote: true

      - name: competition_suppliers
        description: "An array containing UUID and Entity ID values of the suppliers relating to the competition in MFL"
        quote: true

  - name: staging__dots__mfl_sport_current
    description: "Table containing the current version of all metadata relating to every MFL Sport"
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload

      - name: sport_id
        description: "The UUID of the Sport in MFL"
        quote: true

      - name: sport_name
        description: "The name of the Sport in MFL"
        quote: true

      - name: sport_suppliers
        description: "An array containing UUID and Entity ID values of the suppliers relating to the sport in MFL"
        quote: true

  - name: staging__dots__mfl_venue_current
    description: "Table containing the current version of all metadata relating to every MFL Venue"
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload

      - name: venue_id
        description: "The UUID of the Venue in MFL"
        quote: true

      - name: venue_name_short
        description: "The short name of the venue in MFL"
        quote: true

      - name: venue_name_long
        description: "The long name of the venue in MFL"
        quote: true

      - name: country_id
        description: "The UUID of the country directly relating to the Venue"
        quote: true

  - name: staging__dots__mfl_tournamentcalendar_current
    description: "Table containing the current version of all metadata relating to every MFL Tournament Calendar"
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload

      - name: tournament_calendar_id
        description: "The UUID of the Tournament Calendar in MFL"
        quote: true

      - name: tournament_calendar_name
        description: "The name of the Tournament Calendar"
        quote: true

      - name: tournament_calendar_start_date
        description: "The date of the start of the Tournament Calendar"
        quote: true

      - name: tournament_calendar_end_date
        description: "The date of the end of the Tournament Calendar"
        quote: true

      - name: sport_id
        description: "The UUID of the Sport directly relating to the Tournament Calendar"
        quote: true

      - name: ruleset_id
        description: "The UUID of the Ruleset directly relating to the Tournament Calendar"
        quote: true

      - name: competition_id
        description: "The UUID of the Competition directly relating to the Tournament Calendar"
        quote: true

      - name: country_id
        description: "The UUID of the Country directly relating to the Tournament Calendar"
        quote: true

      - name: tournament_calendar_suppliers
        description: "An array containing UUID and Entity ID values of the suppliers relating to the competition in MFL"
        quote: true

  - name: staging__dots__mfl_stage_current
    description: "Table containing the current version of all metadata relating to every MFL Stage"
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload

      - name: stage_id
        description: "The UUID of the Stage in MFL"
        quote: true

      - name: stage_name
        description: "The name of the Stage in MFL E.g. Final, Semi-final, Group, ..."
        quote: true

      - name: stage_start_date
        description: "The date of the start of the Stage"
        quote: true

      - name: stage_end_date
        description: "The date of the end of the Stage"
        quote: true

      - name: stage_has_extra_time
        description: "Boolean, True if extra time is possible at this stage"
        quote: true

      - name: stage_has_penalty_shootout
        description: "Boolean, True if a penalty shootout is possible at this stage"
        quote: true

      - name: tournament_calendar_id
        description: "The UUID of the Tournament Calendar relating directly to the Stage"
        quote: true

  - name: staging__dots__mfl_country_current
    description: "Table containing the current version of all metadata relating to every MFL Country"
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload

      - name: country_id
        description: "The UUID of the Country in MFL"
        quote: true

      - name: country_name
        description: "The name of the country in MFL"
        quote: true

      - name: country_locations
        description: "An array containing location details of the country"
        quote: true

  - name: staging__dots__mfl_ruleset_current
    description: "Table containing the current version of all metadata relating to every MFL Ruleset"
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload

      - name: ruleset_id
        description: "The UUID of the Ruleset in MFL"
        quote: true

      - name: ruleset_name
        description: "The name of the ruleset in MFL E.g. Mens, Womens, Juniors, ..."
        quote: true

      - name: sport_id
        description: "The UUID of the Sport directly relating to the Ruleset"
        quote: true

  - name: staging__dots__mfl_fixture_current
    description: "Table containing the current version of all metadata relating to every MFL Fixture ever (including non-DAZN fixtures)"
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload

      - name: fixture_id
        description: "The UUID of the MFL Fixture"
        quote: true

      - name: fixture_description
        description: "The description of the MFL Fixture"
        quote: true

      - name: fixture_time_known
        description: "Boolean, True if the start time if known"
        quote: true

      - name: fixture_start_timestamp
        description: "The timestamp of the start of the MFL Fixture, constructed using the combincation of the date and time fields"
        quote: true

      - name: fixture_status
        description: "The status of the MFL Fixture"
        quote: true

      - name: competition_id
        description: "The UUID of the Competition relating directly to the MFL Fixture"
        quote: true

      - name: contestant_ids
        description: "COALESCE of the two contestantUUIDs and contestantsUUIDs fields, needed as there was a change of the field name"
        quote: true

      - name: contestant_id_0
        description: "The first value from the contestantUUIDs_COALESCE array representing the first, if any, contestant of the MFL Fixture"
        quote: true

      - name: contestant_id_1
        description: "The second value from the contestantUUIDs_COALESCE array representing the second, if any, contestant of the MFL Fixture"
        quote: true

      - name: contestant_id_2
        description: "The third value from the contestantUUIDs_COALESCE array representing the third, if any, contestant of the MFL Fixture"
        quote: true

      - name: contestant_id_3
        description: "The fourth value from the contestantUUIDs_COALESCE array representing the fourth, if any, contestant of the MFL Fixture"
        quote: true

      - name: contestant_id_4
        description: "The fifth value from the contestantUUIDs_COALESCE array representing the fifth, if any, contestant of the MFL Fixture"
        quote: true

      - name: contestant_id_5
        description: "The sixth value from the contestantUUIDs_COALESCE array representing the sixth, if any, contestant of the MFL Fixture"
        quote: true

      - name: ruleset_id
        description: "The UUID of the Ruleset relating directly to the MFL Fixture"
        quote: true

      - name: sport_id
        description: "The UUID of the Sport relating directly to the MFL Fixture"
        quote: true

      - name: stage_id
        description: "The UUID of the Stage relating directly to the MFL Fixture"
        quote: true

      - name: tournament_calendar_id
        description: "The UUID of the Tournament Calendar relating directly to the MFL Fixture"
        quote: true

      - name: venue_id
        description: "The UUID of the Venue relating directly to the MFL Fixture"
        quote: true

  - name: staging__dots__mfl_contestant_current
    description: "Table containing the current version of all metadata relating to every MFL Contestant"
    columns:
      - *DBT_INSERT_DTTS
      - *EDM_INSERT_DTTS
      - *last_updated_timestamp
      - *is_test
      - *envelope_schema_version
      - *payload_id
      - *payload_type
      - *payload_state
      - *payload

      - name: contestant_id
        description: "The UUID of the Contestant in MFL"
        quote: true

      - name: contestant_name
        description: "The name of the Contestant in MFL"
        quote: true

      - name: country_id
        description: "The country of the contestant"
        quote: true

      - name: ruleset_ids
        description: "An array containing the ruleset UUID values the contestant relates to"
        quote: true
