
WITH source AS (
    SELECT * FROM {{ source('CURATED','curated__dots__competition_group_current' )}}
)

, renamed AS (
    SELECT
        -- The macro is defined in dbt/macros/Dots/mfl_payload_columns.sql
        {{ mfl_payload_columns() }}
        ,competitions_flatten.value AS "competition_id"
        ,"groupId" AS "group_id"
        ,"groupName" AS "group_name"
    FROM source , lateral flatten(input=>"competitions") competitions_flatten 
)

select * from renamed
