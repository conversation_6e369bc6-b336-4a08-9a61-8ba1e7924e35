
WITH source AS (
    SELECT * FROM {{ source('CURATED','curated__dots__mfl_tournamentcalendar_current' )}}
)

, renamed AS (
    SELECT
        -- The macro is defined in dbt/macros/Content/mfl_payload_columns.sql
        {{ mfl_payload_columns() }}
        ,"tournamentCalUUID" AS "tournament_calendar_id"
        ,"tournamentCalName" AS "tournament_calendar_name"
        ,"tournamentCalDateStart__timestamp"::DATE AS "tournament_calendar_start_date"
        ,"tournamentCalDateEnd__timestamp"::DATE AS "tournament_calendar_end_date"
        ,"sportUUID" AS "sport_id"
        ,"rulesetUUID" AS "ruleset_id"
        ,"competitionUUID" AS "competition_id"
        ,"countryUUID" AS "country_id"
        ,"suppliers" AS "tournament_calendar_suppliers"
    FROM source
)

select * from renamed
