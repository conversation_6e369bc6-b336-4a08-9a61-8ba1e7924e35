
WITH source AS (
    SELECT * FROM {{ source('CURATED','curated__dots__mfl_venue_current' )}}
)

, renamed AS (
    SELECT
        -- The macro is defined in dbt/macros/Content/mfl_payload_columns.sql
        {{ mfl_payload_columns() }}
        ,"venueUUID" AS "venue_id"
        ,"venueNameShort" AS "venue_name_short"
        ,"venueNameLong" AS "venue_name_long"
        ,"countryUUID" AS "country_id"
    FROM source
)

select * from renamed
