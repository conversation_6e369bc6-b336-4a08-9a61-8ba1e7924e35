
WITH source AS (
    SELECT * FROM {{ source('CURATED','curated__dots__mfl_contestant_current' )}}
)

, renamed AS (
    SELECT
        -- The macro is defined in dbt/macros/Content/mfl_payload_columns.sql
        {{ mfl_payload_columns() }}
        ,"contestantUUID" AS "contestant_id"
        ,"contestantName" AS "contestant_name"
        ,"countryUUID" AS "country_id"
        ,"rulesetUUIDs" AS "ruleset_ids"
    FROM source
)

select * from renamed
