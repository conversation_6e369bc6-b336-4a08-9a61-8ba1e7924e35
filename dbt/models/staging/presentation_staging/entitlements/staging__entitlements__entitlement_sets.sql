WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ENTITLEMENT_SETS') }}
)

, renamed AS (
    SELECT
        "entitlement_sets_scd_primary_key"
        ,"messageId" AS "message_id"
        ,"messageTimestamp" AS "message_timestamp"
        ,"entitlementSetId" AS "entitlement_set_id"
        ,"eventStartTimestamp" AS "event_start_timestamp"
        ,"entitlementIds" AS "entitlement_ids"
        ,TO_TIMESTAMP_NTZ("updatedTimestamp",3) AS "updated_timestamp"
    FROM source
)

SELECT * FROM renamed
