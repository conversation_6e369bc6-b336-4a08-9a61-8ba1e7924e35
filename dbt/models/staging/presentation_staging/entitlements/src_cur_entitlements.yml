version: 2

sources:
  - name: CURATED
    database: TRANSFORMATION_PROD
    tables:
      - name: CURATED__ENTITLEMENT_SETS
        description: Curated Layer - Entitlement Sets
        columns:
          - name: entitlement_sets_scd_primary_key
            description: "MD5 hashed key of the entitlement_set_id and updatedTimestamp (aka effective_from), used in the merge statement as a unique identifier in the entitlement sets scd"
            quote: true

          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: messageId
            description: "Unique identifier for the SNS message."
            quote: true

          - name: messageTimestamp
            description: "Timestamp indicating when the message was published to SNS."
            quote: true

          - name: entitlementSetId
            description: "Id of the entitlement set, as a human readable string. The PPV code used in purchase flow, which is stored in Zuora's RatePlan custom field."
            quote: true

          - name: eventStartTimestamp
            description: "UTC timestamp indicating the start of the event to which the entitlement set is related, represented in ISO 8601 format."
            quote: true

          - name: entitlementIds
            description: "List of entitlement ids (alternatively known as article tags) that are part of the entitlement set."
            quote: true

          - name: updatedTimestamp
            description: "Unix timestamp indicating when a change in configuration occurred across the collection of entitlement sets."
            quote: true

      - name: CURATED__USER_ENTITLEMENT_EVENT
        description: "User Entitlement Events from EDM OPEN_ENTITLEMENTSERVICE"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: messageId
            description: "Unique identifier for the SNS message."
            quote: true

          - name: messageTimestamp
            description: "Timestamp indicating when the message was published to SNS."
            quote: true

          - name: userId
            description: "DAZN User ID used to identify users across a number of tables incl. Zuora and Salesforce"
            quote: true

          - name: entitlementSetId
            description: "Id of the entitlement set, as a human readable string. The PPV code used in purchase flow, which is stored in Zuora's RatePlan custom field."
            quote: true

          - name: eventTimestamp
            description: "UTC timestamp indicating when the entitlement set was assigned to the user or when the user purchased the entitlement set."
            quote: true

          - name: expirationTimestamp
            description: "Unix timestamp derived from the value of TTL (time to live), indicating when the user entitlement becomes obsolete i.e. the user will no longer have the entitlement after this date. This will blank for PPV Purchases."
            quote: true

          - name: eventType
            description: "The type of event being recorded in relation to the user's ownership of the entitlement set i.e. create or delete. This covers all scenarios except for removals determined by time to live within the replay service (TTL)."
            quote: true

          - name: previousEntitlementSetId
            description: "For update messages, shows the previous entitlement set value being updated"
            quote: true

          - name: productType
            description: "The type of the entitlement set, either ppv or tier"
            quote: true

          - name: source
            description: "The origin of modification. Can be as purchase (will have rate plan) or SF, if changes were executed directly from Salesforce API"
            quote: true

          - name: tableVersion
            description: "Version number of the backend user entitltment service, v1 or v2. v2 includes subscriptionName"
            quote: true

          - name: subscriptionName
            description: "Zuora Subscription name associated with the entitlement"
            quote: true

          - name: messagePayload
            description: "Full payload of user entitlement service message, containing all attributes"
            quote: true
