WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__USER_ENTITLEMENT_EVENT') }}
)

, renamed AS (
    SELECT
        "messageId" AS "message_id"
        ,"messageTimestamp" AS "message_timestamp"
        ,"userId" AS "dazn_user_id"
        ,"entitlementSetId" AS "entitlement_set_id"
        ,"eventTimestamp" AS "entitlement_change_timestamp"
        ,"expirationTimestamp" AS "expiration_timestamp"
        ,"eventType" AS "entitlement_change_type"
        ,"previousEntitlementSetId" AS "previous_entitlement_set_id"
        ,"productType" AS "product_type"
        ,"source"
        ,"tableVersion" AS "table_version"
        ,"subscriptionName" AS "subscription_name"
        ,"messagePayload" AS "message_payload"
    FROM source
)

SELECT * FROM renamed
