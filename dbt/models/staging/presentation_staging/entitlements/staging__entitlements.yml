version: 2

models:
  - name: staging__entitlements__entitlement_sets
    description: "Curated Layer - Entitlement Sets"
    columns:
      - name: entitlement_sets_scd_primary_key
        description: "MD5 hashed key of the entitlement_set_id and updatedTimestamp (aka effective_from), used in the merge statement as a unique identifier in the entitlement sets scd"
        quote: true

      - name: message_id
        description: "Unique identifier for the SNS message."
        quote: true

      - name: message_timestamp
        description: "Timestamp indicating when the message was published to SNS."
        quote: true

      - name: entitlement_set_id
        description: "Id of the entitlement set, as a human readable string. The PPV code used in purchase flow, which is stored in Zuora's RatePlan custom field."
        quote: true

      - name: event_start_timestamp
        description: "UTC timestamp indicating the start of the event to which the entitlement set is related, represented in ISO 8601 format."
        quote: true

      - name: entitlement_ids
        description: "List of entitlement ids (alternatively known as article tags) that are part of the entitlement set."
        quote: true

      - name: updated_timestamp
        description: "Unix timestamp indicating when a change in configuration occurred across the collection of entitlement sets."
        quote: true

  - name: staging__entitlements__user_entitlement_service
    description: "User Entitlement Events from EDM OPEN_ENTITLEMENTSERVICE"
    columns:
      - name: message_id
        description: "Unique identifier for the SNS message."
        quote: true

      - name: message_timestamp
        description: "Timestamp indicating when the message was published to SNS."
        quote: true

      - name: dazn_user_id
        description: "DAZN User ID used to identify users across a number of tables incl. Zuora and Salesforce"
        quote: true

      - name: entitlement_set_id
        description: "Id of the entitlement set, as a human readable string. The PPV code used in purchase flow, which is stored in Zuora's RatePlan custom field."
        quote: true

      - name: entitlement_change_timestamp
        description: "UTC timestamp indicating when the entitlement set was assigned to the user or when the user purchased the entitlement set."
        quote: true

      - name: expiration_timestamp
        description: "UTC timestamp derived from the value of TTL (time to live), indicating when the user entitlement becomes obsolete i.e. the user will no longer have the entitlement after this date. This will blank for PPV Purchases."
        quote: true

      - name: entitlement_change_type
        description: "The type of event being recorded in relation to the user's ownership of the entitlement set i.e. create or delete. This covers all scenarios except for removals determined by time to live within the replay service (TTL)."
        quote: true

      - name: previous_entitlement_set_id
        description: "For update messages, shows the previous entitlement set value being updated"
        quote: true

      - name: product_type
        description: "The type of the entitlement set, either ppv or tier"
        quote: true

      - name: source
        description: "The origin of modification. Can be as purchase (will have rate plan) or SF, if changes were executed directly from Salesforce API"
        quote: true

      - name: table_version
        description: "Version number of the backend user entitltment service, v1 or v2. v2 includes subscription_name"
        quote: true

      - name: subscription_name
        description: "Zuora Subscription name associated with the entitlement"
        quote: true

      - name: message_payload
        description: "Full payload of user entitlement service message, containing all attributes"
        quote: true
