{{
	config(
		materialized = 'view',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
		schema = 'STAGING',
		tags =  ['presentation-staging']
    )
}}

WITH ev_payment_methods_data AS (
    SELECT * FROM {{ ref_env('staging__dmp__payment_method_current') }}
)

,zr_payment_methods_data AS (
    SELECT * FROM {{ ref_env('staging__zuora__payment_method_current') }}
)

,zr_billing_account_mapping_data AS (
    SELECT
        "dazn_user_id"
        ,"billing_account_id"
    FROM {{ ref_env('staging__account_current') }}
)

,ev_payment_methods_data_enriched_with_legacy_billing_acc_data AS (
    SELECT
        ev_payment_methods_data.* EXCLUDE ("billing_account_id")
        ,COALESCE(zr_billing_account_mapping_data."billing_account_id", zr_billing_account_mapping_data."billing_account_id") AS "billing_account_id"
    FROM ev_payment_methods_data
    LEFT JOIN zr_billing_account_mapping_data
        USING("dazn_user_id")
    GROUP BY ALL
)

,final_payment_methods_data AS (
    SELECT
        "payment_method_id"
        ,"billing_account_id"
        ,"payment_method_updated_timestamp"
        ,"payment_method_payment_method_status"
        ,"payment_method_created_by_id"
        ,"payment_method_updated_by_id"
        ,"payment_method_type"
        ,"payment_method_bank_identification_number"
        ,"payment_method_credit_card_type"
        ,"payment_method_credit_card_expiration_month"
        ,"payment_method_credit_card_expiration_year"
        ,"payment_method_credit_card_mask_number"
        ,"payment_method_paypal_email"
        ,"payment_method_paypal_type"
        ,"payment_method_bank_transfer_type"
        ,"payment_method_created_timestamp"
        ,"payment_method_actual_payment_method"
        ,"payment_method_last_transaction_timestamp"
        ,"payment_method_last_transaction_status"
        ,"payment_method_last_failed_sale_transaction_timestamp"
        ,"payment_method_num_consecutive_failures"
        ,'ev' AS "payment_method_source_of_data"
    FROM ev_payment_methods_data_enriched_with_legacy_billing_acc_data
    UNION ALL
    SELECT
        "payment_method_id"
        ,"billing_account_id"
        ,"payment_method_updated_timestamp"
        ,"payment_method_payment_method_status"
        ,"payment_method_created_by_id"
        ,"payment_method_updated_by_id"
        ,"payment_method_type"
        ,"payment_method_bank_identification_number"
        ,"payment_method_credit_card_type"
        ,"payment_method_credit_card_expiration_month"
        ,"payment_method_credit_card_expiration_year"
        ,"payment_method_credit_card_mask_number"
        ,"payment_method_paypal_email"
        ,"payment_method_paypal_type"
        ,"payment_method_bank_transfer_type"
        ,"payment_method_created_timestamp"
        ,"payment_method_actual_payment_method"
        ,"payment_method_last_transaction_timestamp"
        ,"payment_method_last_transaction_status"
        ,"payment_method_last_failed_sale_transaction_timestamp"
        ,"payment_method_num_consecutive_failures"
        ,'zr' AS "payment_method_source_of_data"
    FROM zr_payment_methods_data
)

SELECT * FROM final_payment_methods_data
