{{
	config(
		materialized = 'table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XL_WH',
		schema = 'STAGING',
		tags =  ['presentation-staging']
    )
}}
--- This model is actually at invoice payments level
WITH ev_payments_data AS (
    SELECT * FROM {{ ref_env('staging__dmp__payment_current') }}
)
--
-- ,ev_refund_data AS (
--     SELECT * FROM {{ ref('staging__dmp__invoice_refund_current') }}
-- )

,zr_accounts_data AS (
    SELECT * FROM {{ ref_env('staging__zuora__account_current') }}
)

,zr_invoice_payments_data AS (
    SELECT * FROM {{ ref_env('staging__zuora__invoice_payment_current') }}
)

,zr_billing_account_mapping_data AS (
    SELECT
        "dazn_user_id"
        ,"billing_account_id"
    FROM {{ ref_env('staging__account_current') }}
)

,zr_payments_data AS (
    SELECT * FROM {{ ref_env('staging__zuora__payment_current') }}
    {% if is_incremental() %}
    WHERE "payment_updated_timestamp"::DATE >= (SELECT MAX("payment_updated_timestamp"::DATE) FROM {{ this }})
    {% endif %}
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "payment_id" ORDER BY "payment_updated_timestamp"::DATE DESC NULLS LAST) = 1
)

,final_zr_payments_data AS (
    SELECT
        zr_invoice_payments_data."invoice_payment_id"
        ,zr_invoice_payments_data."invoice_id"
        ,zr_payments_data."payment_id" AS "payment_id"
        ,zr_accounts_data."dazn_user_id"
        ,zr_payments_data."payment_number"
        ,zr_payments_data."payment_reference_id"
        ,zr_payments_data."payment_method_id"
        ,zr_payments_data."payment_created_by_id"
        ,zr_payments_data."payment_source"
        ,zr_payments_data."payment_source_name"
        ,zr_payments_data."payment_type"
        ,zr_payments_data."payment_status"
        ,zr_payments_data."payment_effective_timestamp" AS "payment_effective_timestamp"
        ,zr_payments_data."payment_created_timestamp"::DATE AS "payment_created_date"
        ,zr_payments_data."payment_created_timestamp"
        ,zr_payments_data."payment_updated_timestamp"
        ,zr_invoice_payments_data."invoice_payment_updated_timestamp"
        ,zr_payments_data."payment_currency"
        ,zr_payments_data."payment_amount"
        ,zr_payments_data."payment_refund_amount"
        ,zr_payments_data."payment_cancelled_on"
        ,zr_payments_data."payment_gateway"
        ,zr_payments_data."payment_gateway_response"
        ,zr_payments_data."payment_gateway_response_code"
        ,zr_payments_data."payment_gateway_state"
        ,zr_payments_data."payment_payment_source" AS "payment_payment_source"
        ,zr_payments_data."payment_comment"
    FROM zr_payments_data
    LEFT JOIN zr_invoice_payments_data
        ON zr_payments_data."payment_id" = zr_invoice_payments_data."payment_id"
    LEFT JOIN zr_accounts_data
        ON zr_payments_data."billing_account_id"=zr_accounts_data."billing_account_id"
    GROUP BY ALL
)

,final_ev_payments_data AS (
    SELECT
        "invoice_payment_id"
        ,"invoice_id"
        ,"payment_id"
        ,"dazn_user_id"
        ,"payment_number"
        ,"payment_reference_id"
        ,"payment_method_id"
        ,"payment_created_by_id"
        ,"payment_source"
        ,"payment_source_name"
        ,"payment_type"
        ,"payment_status"
        ,"payment_effective_timestamp" AS "payment_effective_timestamp"
        ,"payment_created_timestamp"::DATE AS "payment_created_date"
        ,"payment_created_timestamp"
        ,"payment_updated_timestamp"
        ,"payment_updated_timestamp" AS "invoice_payment_updated_timestamp"
        ,"payment_currency"
        ,"payment_amount"
        ,"payment_refund_amount"
        ,"payment_cancelled_on"
        ,"payment_gateway"
        ,"payment_gateway_response"
        ,"payment_gateway_response_code"
        ,"payment_gateway_state"
        ,"payment_payment_source"
        ,"payment_comment"
    FROM ev_payments_data
--     WHERE NOT EXISTS (SELECT 1 FROM ev_refund_data WHERE ev_refund_data."invoice_id" = ev_payments_data."invoice_id")
    GROUP BY ALL
)

,final_payments_data AS (
    SELECT
        *
        ,'zr' AS "payment_source_of_data"
    FROM final_zr_payments_data
    UNION ALL
    SELECT
        *
        ,'ev' AS "payment_source_of_data"
    FROM final_ev_payments_data
)

,updated_account_mapping AS (
    SELECT
        final_payments_data.*
        ,COALESCE(zr_billing_account_mapping_data."billing_account_id", final_payments_data."dazn_user_id") AS "billing_account_id"
    FROM final_payments_data
    LEFT JOIN zr_billing_account_mapping_data
        ON final_payments_data."dazn_user_id" = zr_billing_account_mapping_data."dazn_user_id"
    GROUP BY ALL
)

SELECT * FROM updated_account_mapping
-- As a single payment can be happen to multiple invoices in zuora and as we can have migrated invoice, we are handling it with below qualify statement
QUALIFY TRUE
    AND ROW_NUMBER() OVER (PARTITION BY "invoice_payment_id" ORDER BY "invoice_payment_updated_timestamp" DESC) = 1
