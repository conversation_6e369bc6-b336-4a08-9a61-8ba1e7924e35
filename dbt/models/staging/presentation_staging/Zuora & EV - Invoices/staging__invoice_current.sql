{{
	config(
		materialized = 'table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XL_WH',
		schema = 'STAGING',
		tags =  ['presentation-staging']
    )
}}

WITH ev_invoices_raw_data AS (
    SELECT * FROM {{ ref_env('staging__dmp__invoice_current') }}
)

,zr_invoices_raw_data AS (
    SELECT * FROM {{ ref_env('staging__zuora__invoice_current') }}
)

,zr_accounts_data AS (
    SELECT * FROM {{ ref_env('staging__zuora__account_current') }}
)

,zr_billing_account_mapping_data AS (
    SELECT
        "dazn_user_id"
        ,"billing_account_id"
    FROM {{ ref_env('staging__account_current') }}
)

-- handling migrated invoices
,zr_refunds_from_ev_data_raw AS (
    SELECT * FROM {{ ref_env('staging__dmp__invoice_refund_current') }}
)

,zr_refunds_from_ev_final AS (
    SELECT
        "original_invoice_id"
        ,MAX("invoice_refund_updated_timestamp") AS "recent_invoice_refund_updated_timestamp"
        ,SUM("refund_amount") AS "refund_amount"
    FROM zr_refunds_from_ev_data_raw
    WHERE TRUE
        AND TRY_TO_NUMERIC("original_invoice_id") IS NULL
    GROUP BY "original_invoice_id"
)

,zr_invoice_data AS (
    SELECT
        zr_accounts_data."dazn_user_id" AS "dazn_user_id"
        ,zr_accounts_data."crm_account_id" AS "crm_account_id"
        ,"invoice_id"
        ,NULL AS "invoice_churn_type"
        ,"invoice_number"
        ,"invoice_created_timestamp"
        ,"invoice_due_date"
        ,"invoice_date"
        ,"invoice_posted_timestamp"
        ,"invoice_updated_timestamp"
        ,"invoice_status"
        ,zr_accounts_data."billing_account_currency_code" AS "billing_account_currency_code"
        ,"invoice_amount"
        ,"invoice_payment_amount"
        ,0 AS "invoice_etf_amount"
        ,"invoice_balance"
        ,"invoice_tax_amount"
        ,"invoice_amount_without_tax"
        ,"invoice_tax_exempt_amount"
        ,"invoice_credit_balanced_adjustment_amount"
        ,"invoice_is_reversed"
        ,"invoice_source_id"
        ,"invoice_source"
        ,COALESCE(zr_refunds_from_ev_final."refund_amount", zr_invoices_raw_data."invoice_refund_amount") AS "invoice_refund_amount"
    FROM zr_invoices_raw_data
    LEFT JOIN zr_accounts_data
        USING("billing_account_id")
    LEFT JOIN zr_refunds_from_ev_final
        ON zr_invoices_raw_data."invoice_id" = zr_refunds_from_ev_final."original_invoice_id"
    GROUP BY ALL
)

,ev_invoice_data AS (
    SELECT
        "dazn_user_id"
        ,"crm_account_id"
        ,"invoice_id"
        ,"invoice_churn_type"
        ,"invoice_number"
        ,"invoice_created_timestamp"
        ,"invoice_due_date"
        ,"invoice_date"
        ,"invoice_posted_timestamp"
        ,"invoice_updated_timestamp"
        ,"invoice_status"
        ,"billing_account_currency_code"
        ,"invoice_amount"
        ,"invoice_payment_amount"
        ,"invoice_etf_amount"
        ,"invoice_balance"
        ,"invoice_tax_amount"
        ,"invoice_amount_without_tax"
        ,"invoice_tax_exempt_amount"
        ,"invoice_credit_balanced_adjustment_amount"
        ,"invoice_is_reversed"
        ,"invoice_source_id"
        ,"invoice_source"
        ,"invoice_refund_amount"
    FROM ev_invoices_raw_data
)

,union_data AS (
    SELECT
        'zr' AS "invoice_source_of_data"
        ,*
    FROM zr_invoice_data
    UNION
    SELECT
        'ev' AS "invoice_source_of_data"
        ,*
    FROM ev_invoice_data
)

,updated_account_mapping AS (
    SELECT
        union_data.*
        ,COALESCE(zr_billing_account_mapping_data."billing_account_id", union_data."dazn_user_id") AS "billing_account_id"
    FROM union_data
    LEFT JOIN zr_billing_account_mapping_data
        ON union_data."dazn_user_id" = zr_billing_account_mapping_data."dazn_user_id"
    GROUP BY ALL
)

,final_data AS (
    SELECT * FROM updated_account_mapping
    QUALIFY TRUE
--     When a particular invoice got generated in Zuora and continued payments in EV then we have to take the ev invoice only based on the invoice updated timestamp
        AND ROW_NUMBER() OVER (PARTITION BY "invoice_id" ORDER BY "invoice_updated_timestamp" DESC NULLS LAST) = 1
)

SELECT
    * EXCLUDE("invoice_source_of_data")
    ,"invoice_source_of_data" AS "recent_invoice_source_of_data"
FROM final_data
GROUP BY ALL


