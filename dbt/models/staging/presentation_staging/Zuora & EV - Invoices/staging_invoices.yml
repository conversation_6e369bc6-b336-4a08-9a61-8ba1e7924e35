version: 2

models:

  - name: staging__invoice_current
    description: "current view of invoice"
    columns:
      - name: dazn_user_id
        description: "Unique ID of the DAZN User"
        quote: true

      - name: crm_account_id
        description: "Unique ID of the record (also known as Salesforce Account ID)"
        quote: true

      - name: invoice_id
        description: "Id of the Invoice"
        quote: true
        tests:
          - unique

      - name: invoice_churn_type
        description: "Type of churn"
        quote: true

      - name: invoice_number
        description: "Number associated to the invoice"
        quote: true

      - name: invoice_created_timestamp
        description: "Timestamp the invoice was created"
        quote: true

      - name: invoice_due_date
        description: "Date the invoice is Due"
        quote: true

      - name: invoice_date
        description: "Date of the Invoice"
        quote: true

      - name: invoice_posted_timestamp
        description: "Timestamp the Invoice was Posted"
        quote: true

      - name: invoice_updated_timestamp
        description: "Timestamp the invoice was updated"
        quote: true

      - name: invoice_status
        description: "Current status of the Invoice"
        quote: true

      - name: billing_account_currency_code
        description: "Currency of invoice"
        quote: true

      - name: invoice_amount
        description: "Amount of the invoice"
        quote: true

      - name: invoice_payment_amount
        description: "Payment Amount of the Invoice"
        quote: true

      - name: invoice_etf_amount
        description: "ETF amount"
        quote: true

      - name: invoice_balance
        description: "Balance of the Invoice"
        quote: true

      - name: invoice_tax_amount
        description: "Tax amount of the invoice"
        quote: true

      - name: invoice_amount_without_tax
        description: "Amount of the invoice without tax"
        quote: true

      - name: invoice_tax_exempt_amount
        description: "Tax exempt amount of the invoice"
        quote: true

      - name: invoice_credit_balanced_adjustment_amount
        description: "Credit balance adjustment amount of the invoice"
        quote: true

      - name: invoice_is_reversed
        description: "Flag, 1 if the Invoice is Reversed"
        quote: true

      - name: invoice_source_id
        description: "Source Id of the Invoice"
        quote: true

      - name: invoice_source
        description: "Source of the invoice"
        quote: true

      - name: invoice_refund_amount
        description: "Refund Amount of the invoice, in the currency of the invoice"
        quote: true

      - name: billing_account_id
        description: "Id of the account"
        quote: true

      - name: recent_invoice_source_of_data
        description: "Source of Data"
        quote: true

  - name: staging__invoice_refund_current
    description: "current view of invoice refund"
    columns:
      - name: source_of_data
        description: "Source of Data"
        quote: true

      - name: refund_invoice_payment_invoice_payment_id
        description: "Refund invoice paymnet of invoice payment id"
        quote: true

      - name: refund_id
        description: "Id of the refund"
        quote: true

      - name: refund_amount
        description: "Refund amount"
        quote: true

      - name: refund_gateway_response
        description: "Refund gateway response"
        quote: true

      - name: refund_gateway_response_code
        description: "Refund gateway response code"
        quote: true

      - name: refund_reason_code
        description: "Reason for refund"
        quote: true

      - name: refund_date
        description: "Date of refund"
        quote: true

      - name: refund_status
        description: "Status of refund"
        quote: true

      - name: refund_gateway
        description: "Payment gateway used to process the refund"
        quote: true

      - name: refund_method_type
        description: "Type of payment method used for the refund (e.g., credit card, PayPal)"
        quote: true

      - name: refund_created_by_id
        description: "This is the UserID / API that created that refund record"
        quote: true

      - name: refund_source_type
        description: "Source type of refund"
        quote: true

      - name: refund_reference_id
        description: "Reference ID from the refund service provider for the event"
        quote: true

      - name: refund_number
        description: "Number associated to the refund"
        quote: true

  - name: staging__payment_current
    description: "current view of payment"
    columns:
      - name: invoice_payment_id
        description: "Payment Id of the Invoice"
        quote: true

      - name: invoice_id
        description: "Id of the Invoice"
        quote: true

      - name: payment_id
        description: "Id of the paymen"
        quote: true

      - name: dazn_user_id
        description: "Unique ID of the DAZN User"
        quote: true

      - name: payment_number
        description: "Number associated to the payment"
        quote: true

      - name: payment_reference_id
        description: "Reference Id associated to the payment"
        quote: true

      - name: payment_method_id
        description: "Method Id associated to the payment"
        quote: true

      - name: payment_created_by_id
        description: "Id associated to the payment creation"
        quote: true

      - name: payment_source
        description: "Source of the payment"
        quote: true

      - name: payment_source_name
        description: "Source Name associated to the payment"
        quote: true

      - name: payment_type
        description: "Type/Mode of payment"
        quote: true

      - name: payment_status
        description: "State of payment if approved or error"
        quote: true

      - name: payment_effective_timestamp
        description: "payment effective timestamp"
        quote: true

      - name: payment_created_date
        description: "Date when payment is initiated"
        quote: true

      - name: payment_created_timestamp
        description: "Timestamp when payment is initiated"
        quote: true

      - name: payment_updated_timestamp
        description: "Timestamp when payment is updated"
        quote: true

      - name: invoice_payment_updated_timestamp
        description: "Timestamp when invoice payment is updated"
        quote: true

      - name: payment_currency
        description: "Currency associated with payment"
        quote: true

      - name: payment_amount
        description: "Amount paid"
        quote: true

      - name: payment_refund_amount
        description: "Refund amount availed for payment"
        quote: true

      - name: payment_cancelled_on
        description: "Date of payment cancellation"
        quote: true

      - name: payment_gateway
        description: "electronic financial transaction's technology platform"
        quote: true

      - name: payment_gateway_response
        description: "payment transaction's response details"
        quote: true

      - name: payment_gateway_response_code
        description: "payment transaction's response code"
        quote: true

      - name: payment_gateway_state
        description: "state of payment transactions"
        quote: true

      - name: payment_payment_source
        description: "Source of paymeent"
        quote: true

      - name: payment_comment
        description: "Payment comment"
        quote: true

      - name: payment_source_of_data
        description: "Source of the data"
        quote: true

      - name: billing_account_id
        description: "Id of the account"
        quote: true

  - name: staging__payment_method_current
    description: "current view of payment method"
    columns:
      - name: payment_method_id
        description: "Method Id associated to the payment"
        quote: true
        tests:
          - unique
          - not_null

      - name: billing_account_id
        description: "Id of the account"
        quote: true

      - name: payment_method_updated_timestamp
        description: "Timestamp of the last update to the payment method"
        quote: true

      - name: payment_method_payment_method_status
        description: "Current status of the payment method"
        quote: true

      - name: payment_method_created_by_id
        description: "ID of the user who created the payment method"
        quote: true

      - name: payment_method_updated_by_id
        description: "ID of the user who last updated the payment method"
        quote: true

      - name: payment_method_type
        description: "Type of payment method used"
        quote: true

      - name: payment_method_bank_identification_number
        description: "Bank Identification Number (BIN) of the card"
        quote: true

      - name: payment_method_credit_card_type
        description: "Type of credit card (e.g., Visa, Mastercard)"
        quote: true

      - name: payment_method_credit_card_expiration_month
        description: "Expiration month of the credit card"
        quote: true

      - name: payment_method_credit_card_expiration_year
        description: "Expiration year of the credit card"
        quote: true

      - name: payment_method_credit_card_mask_number
        description: "Masked credit card number for reference"
        quote: true

      - name: payment_method_paypal_email
        description: "PayPal email associated with the payment method"
        quote: true

      - name: payment_method_paypal_type
        description: "Type of PayPal payment method used"
        quote: true

      - name: payment_method_bank_transfer_type
        description: "Type of bank transfer used as a payment method"
        quote: true

      - name: payment_method_created_timestamp
        description: "Timestamp when the payment method was created"
        quote: true

      - name: payment_method_actual_payment_method
        description: "Actual payment method used (e.g., Visa, PayPal, Bank Transfer)"
        quote: true

      - name: payment_method_last_transaction_timestamp
        description: "Timestamp of the last transaction using this payment method"
        quote: true

      - name: payment_method_last_transaction_status
        description: "Status of the last transaction associated with the payment method"
        quote: true

      - name: payment_method_last_failed_sale_transaction_timestamp
        description: "Timestamp of the last failed sale transaction using this payment method"
        quote: true

      - name: payment_method_num_consecutive_failures
        description: "payment method num consecutive failures"
        quote: true

      - name: payment_method_source_of_data
        description: "source of data"
        quote: true
