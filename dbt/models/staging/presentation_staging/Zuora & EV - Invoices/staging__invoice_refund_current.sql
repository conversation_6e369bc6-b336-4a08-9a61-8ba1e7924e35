{{
	config(
		materialized = 'table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
		schema = 'STAGING',
		tags =  ['presentation-staging']
    )
}}
 -- this model is not unique at refund_invoice_payment_invoice_payment_id because of the ev data source
WITH ev_refund_raw_data AS (
    SELECT * FROM {{ ref_env('staging__dmp__invoice_refund_current') }}
)

,zr_refund_invoice_payments_data AS (
    SELECT * FROM {{ ref_env('staging__zuora__refund_invoice_payment') }}
    QUALIFY TRUE
        AND ROW_NUMBER() OVER (PARTITION BY "refund_invoice_payment_id" ORDER BY "refund_invoice_payment_updated_date":: DATE DESC NULLS LAST) = 1
)

,zr_refund_invoice_data AS (
    SELECT * FROM {{ ref_env('staging__zuora__refund') }}
    QUALIFY TRUE
        AND ROW_NUMBER() OVER (PARTITION BY "refund_id" ORDER BY "refund_updated_date":: DATE DESC NULLS LAST) = 1
)

,zr_refund_data AS (
    SELECT
        zr_refund_invoice_payments_data."refund_invoice_payment_invoice_payment_id"
        ,"refund_id"
        ,"refund_amount"
        ,COALESCE("refund_gateway_response", '') AS "refund_gateway_response"
        ,COALESCE("refund_gateway_response", '') AS "refund_gateway_response_code"
        ,"refund_reason_code"
        ,"refund_date"
        ,"refund_status"
        ,"refund_gateway"
        ,"refund_method_type"
        ,"refund_created_by_id"
        ,"refund_source_type"
        ,COALESCE("refund_reference_id", "refund_reference_id_2") AS "refund_reference_id"
        ,"refund_number"
    FROM zr_refund_invoice_data
    INNER JOIN zr_refund_invoice_payments_data
        ON zr_refund_invoice_payments_data."refund_invoice_payment_refund_id" = zr_refund_invoice_data."refund_id"
    GROUP BY ALL
)

,ev_refund_data AS (
    SELECT
        "refund_invoice_payment_invoice_payment_id"
        ,"refund_id"
        ,"refund_amount"
        ,COALESCE("refund_gateway_response", '') AS "refund_gateway_response"
        ,COALESCE("refund_gateway_response", '') AS "refund_gateway_response_code"
        ,"refund_reason_code"
        ,"refund_date"
        ,"refund_status"
        ,"refund_gateway"
        ,"refund_method_type"
        ,"refund_created_by_id"
        ,"refund_source_type"
        ,"refund_reference_id"
        ,"refund_number"
    FROM ev_refund_raw_data
    GROUP BY ALL
)

,final_data AS (
    SELECT
        'zr' AS "source_of_data"
        ,*
    FROM zr_refund_data
    UNION ALL
    SELECT
        'ev' AS "source_of_data"
        ,*
    FROM ev_refund_data
)

SELECT * FROM final_data
