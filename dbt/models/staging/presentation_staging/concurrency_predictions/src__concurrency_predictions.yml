version: 2
sources:
  - name: CONCURRENCY_PREDICTIONS
    schema: PREDICTIONS
    database: CONCURRENCY_PREDICTIONS__B2C__{{ 'UAT' if env_var('AIRFLOW_VAR_ENV') == 'stage' else env_var('AIRFLOW_VAR_ENV') }}
    tables:
      - name: OPS_PREDICTION_UPLOADS
        description: "Table holding predictions modified and uploaded by ops."
        columns:
          - name: fixture_id
            description: "fixture id"
            quote: true
          - name: territory
            description: "territory"
            quote: true
          - name: ops_prediction
            description: "prediction made by ops"
            quote: true
          - name: upload_timestamp
            description: "when this prediction was uploaded"
            quote: true

  - name: DS
    schema: FIXTURE_VIEWERSHIP_PREDICTION
    database: DS_PROD
    quoting:
      identifier: true
      schema: true
    tables:
      - name: fvp_final_predictions
        quote: true
        description: "Table hold predictions made by the data science team."
