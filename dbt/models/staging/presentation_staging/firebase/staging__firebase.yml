version: 2

models:
  - name: staging__firebase__flat_event
    description: "Staging data of the Open Firebase FlatEvent Table"
    columns:
      - name: firebase_primary_key
        description: "Unique identifier for the event. Obtained by concatenating userPseudoId and eventTimestamp"
        quote: true

      - name: app_info_id
        description: "The package name or bundle ID of the app."
        quote: true

      - name: app_info_firebase_app_id
        description: "The Firebase App ID associated with the app"
        quote: true

      - name: app_info_install_source
        description: "The store that installed the app."
        quote: true

      - name: app_info_version
        description: "The apps versionName (Android) or short bundle version."
        quote: true

      - name: device_category
        description: "The device category (mobile, tablet, desktop)."
        quote: true

      - name: device_mobile_brand_name
        description: "The device brand name."
        quote: true

      - name: device_mobile_model_name
        description: "The device model name."
        quote: true

      - name: device_mobile_marketing_name
        description: "The device marketing name."
        quote: true

      - name: device_mobile_os_hardware_model
        description: "The device model information retrieved directly from the operating system."
        quote: true

      - name: device_operating_system
        description: "The operating system of the device."
        quote: true

      - name: device_operating_system_version
        description: "The OS version."
        quote: true

      - name: device_vendor_id
        description: "IDFV (present only if IDFA is not collected)."
        quote: true

      - name: device_advertising_id
        description: "Advertising ID/IDFA."
        quote: true

      - name: device_language
        description: "The OS language."
        quote: true

      - name: device_time_zone_offset_seconds
        description: "The offset from GMT in seconds."
        quote: true

      - name: device_is_limited_ad_tracking
        description: "The devices Limit Ad Tracking setting."
        quote: true

      - name: stream_id
        description: "The numeric ID of the stream."
        quote: true

      - name: platform
        description: "The platform on which the app was built."
        quote: true

      - name: user_first_touch_timestamp
        description: "The time (in microseconds) at which the user first opened the app."
        quote: true

      - name: user_id
        description: "The user ID set via the setUserId API."
        quote: true

      - name: user_pseudo_id
        description: "The pseudonymous id (e.g., app instance ID) for the user."
        quote: true

      - name: user_ltv_revenue
        description: "The Lifetime Value (revenue) of the user. This field is not populated in intraday tables."
        quote: true

      - name: user_ltv_currency
        description: "The Lifetime Value (currency) of the user. This field is not populated in intraday tables."
        quote: true

      - name: traffic_source_name
        description: "Name of the marketing campaign that first acquired the user. This field is not populated in intraday tables."
        quote: true

      - name: traffic_source_medium
        description: "Name of the medium (paid search, organic search, email, etc.) that first acquired the user. This field is not populated in intraday tables."
        quote: true

      - name: traffic_source_source
        description: "Name of the network that first acquired the user. This field is not populated in intraday tables."
        quote: true

      - name: geo_continent
        description: "The continent from which events were reported, based on IP address."
        quote: true

      - name: geo_country
        description: "The country from which events were reported, based on IP address."
        quote: true

      - name: geo_region
        description: "The region from which events were reported, based on IP address."
        quote: true

      - name: geo_city
        description: "The city from which events were reported, based on IP address."
        quote: true

      - name: event_date
        description: "The date on which the event was logged (YYYYMMDD format in the registered timezone of your app)."
        quote: true

      - name: event_timestamp
        description: "The time (in microseconds, UTC) at which the event was logged on the client."
        quote: true

      - name: event_timestamp_seconds
        description: "The time (in seconds, UTC) at which the event was logged on the client."
        quote: true

      - name: event_previous_timestamp
        description: "The time (in microseconds, UTC) at which the event was previously logged on the client."
        quote: true

      - name: event_name
        description: "The name of the event."
        quote: true

      - name: event_value_in_usd
        description: "The currency-converted value (in USD) of the events 'value' parameter."
        quote: true

      - name: event_bundle_sequence_id
        description: "The sequential ID of the bundle in which these events were uploaded."
        quote: true

      - name: event_param_action_category
        description: "Describes what part of the app was clicked, possible values are: Button, Rail, Signup"
        quote: true

      - name: event_param_action_label
        description: "Additional label sent with events, for buttons describes what type button was clicked, for interactions with tiles its format is ${tile.railId}:${tile.eventId}, when switching between game types contains the type of video (condensed, catchup,...)"
        quote: true

      - name: event_param_action_name
        description: "Another description of user actions, can be Customer Signup|Click"
        quote: true

      - name: event_param_action_origin
        description: "related to reminders and favourites - not only; available values: tile | playback | schedule | reminders | favourites | dazn | appleTVapp | deeplink | favourites_manage | category_page | rails_page | schedule_page | sports_page | more_page | create_favourite | favourites_list"
        quote: true

      - name: age_restricted
        description: "sent when a tile is clicked, indicates is streaming requires age verification"
        quote: true

      - name: article_id
        description: "ASSET_ID_PARAM_NAME, article ID (PCMS)"
        quote: true

      - name: asset_id
        description: "PlaybackResponse low level logs"
        quote: true

      - name: event_param_bitrate
        description: "describes what bitrate is being downloaded and player in offline, contains all bitrates (audio+video) separated with semi-colon"
        quote: true

      - name: event_param_button_title
        description: "button title"
        quote: true

      - name: event_param_campaign
        description: "Install/Re-engagement Campaign Name"
        quote: true

      - name: campaign_info_source
        description: "Sent when the app is launched with campaign parameters via Firebase."
        quote: true

      - name: event_param_cdn
        description: "cdn url"
        quote: true

      - name: event_param_channel
        description: "iOS (deprecated, these events are no longer sent)"
        quote: true

      - name: event_param_competition_id
        description: "COMPETITION_ID_PARAM_NAME, competition ID (PCMS)"
        quote: true

      - name: event_param_competition_name
        description: "name of the competition"
        quote: true

      - name: competitor_id
        description: "can contain competitor or competition id, depending on what type the user interaction was with"
        quote: true

      - name: content_campaign
        description: "Install/Re-engagement Campaign Content, eg. TCL"
        quote: true

      - name: event_param_customer_id
        description: "Redundant, duplicated user_id"
        quote: true

      - name: event_param_data_cap_cellular_on
        description: "Data capping functionality (reducing stream quality on slow cellular connection)"
        quote: true

      - name: event_param_data_cap_on
        description: "Data capping functionality (if it is on or not)"
        quote: true

      - name: event_param_data_cap_wifi_on
        description: "Data capping functionality (reducing stream quality on slow wifi connection)"
        quote: true

      - name: event_param_device_id
        description: "Sent to Airship when migrating users"
        quote: true

      - name: event_param_download_status
        description: "Possible values: idle | preparing | waiting for wifi | downloading | paused | completed | error"
        quote: true

      - name: event_param_engagement_time_msec
        description: "The additional engagement time (ms) since the last user_engagement event"
        quote: true

      - name: event_param_error_code_cat
        description: "CATEGORY_PARAM_NAME, INTEGER XX part from XX-YYY-ZZZ"
        quote: true

      - name: event_param_error_code_response
        description: "RESPONSE_PARAM_NAME,INTEGER ZZZ part from XX-YYY-ZZZ"
        quote: true

      - name: event_param_error_code_type
        description: "TYPE_PARAM_NAME, INTEGER YYY part from XX-YYY-ZZZ"
        quote: true

      - name: event_param_error_http_code
        description: "http response code"
        quote: true

      - name: event_param_error_internal_code
        description: "ERROR_CODE_PARAM_NAME, If error is non-Massive format"
        quote: true

      - name: event_param_error_internal_msg
        description: "ERROR_MESSAGE_PARAM_NAME, If error is non-Massive format"
        quote: true

      - name: event_param_error_misl_code
        description: "MISL error code"
        quote: true

      - name: event_param_error_misl_message
        description: "MISL error message"
        quote: true

      - name: event_param_error_user_message_key
        description: "Deprecated, User error message key"
        quote: true

      - name: event_param_error_value
        description: "Not sent anymore, contains error code representation shown to the user"
        quote: true

      - name: event_param_eventid
        description: "PLAYBACK_EVENT_ID/EVENT_ID_PARAM_NAME"
        quote: true

      - name: event_param_fa_event_action
        description: "EVENT_ACTION_PARAM_NAME"
        quote: true

      - name: event_param_fa_event_desc
        description: "EVENT_DESCRIPTION_PARAM_NAME"
        quote: true

      - name: event_param_fa_event_object
        description: "EVENT_OBJECT_PARAM_NAME, Object impacted by the event"
        quote: true

      - name: event_param_factory_error
        description: "Possible values: network | noURLResponse | notHTTPResponse | noData | mapping | expectedJSONDictionary | expectedJSONArray | expectedJSONArrayOfObjects | mapperError"
        quote: true

      - name: event_param_failed_cdn
        description: "Failed CDN url"
        quote: true

      - name: favourite_id
        description: "Not sent in the new versions of the app, can be competitor id, competition id, event id"
        quote: true

      - name: event_param_firebase_event_origin
        description: "Denotes the SDK origin of the event"
        quote: true

      - name: event_param_firebase_previous_class
        description: "Previous screen class (Activity/UIViewController) on which this event occurred"
        quote: true

      - name: event_param_firebase_previous_id
        description: "Some id generated by firebase"
        quote: true

      - name: event_param_firebase_previous_screen
        description: "Previous screen name (manually supplied) on which this event occurred"
        quote: true

      - name: event_param_firebase_screen
        description: "The screen name (manually supplied) on which this event occurred"
        quote: true

      - name: event_param_firebase_screen_class
        description: "The screen class (Activity/UIViewController) on which this event occurred"
        quote: true

      - name: event_param_firebase_screen_id
        description: "Some id generated by firebase"
        quote: true

      - name: ga_session_id_event_parameter
        description: "Unique session identifier (based on the timestamp of the session_start event) associated with each event that occurs within a session"
        quote: true

      - name: event_param_interaction_type
        description: "collected with dazn_fullscreen_playback_resized, keep, reuse for other actions (possible values: click | swipe | tap_button | double_tap)"
        quote: true

      - name: event_param_languagecode
        description: "Language code that is used to present content to user. Can either come from startup (for non-signed-in users) or from user profile (for signed in users)"
        quote: true

      - name: event_param_launch_origin
        description: "there are places where the same event is triggered and this param is used to distinguish place where the event was called from (e.g from dazn app, from deeplink, from tile)"
        quote: true

      - name: event_param_medium
        description: "Install/Re-engagement Campaign Medium"
        quote: true

      - name: event_param_min_bitrate
        description: "minimum bitrate"
        quote: true

      - name: event_param_notification_type
        description: "Possible values: download_completed | download_not_enough_space, on Android can also be download_failed"
        quote: true

      - name: event_param_currency
        description: "tiered pricing currency code"
        quote: true

      - name: event_param_offer_type
        description: "free trial|hard offer, set on subscription completed. Reuse for plan selector (plan rendering), This is only monthly_plan | annual_plan"
        quote: true

      - name: event_param_offline_playback_position_in_millis
        description: "playback position"
        quote: true

      - name: event_param_page_index
        description: "Downloads onbording, ex 1, 2"
        quote: true

      - name: event_param_play_origin
        description: "origin of play action"
        quote: true

      - name: event_param_previous_app_version
        description: "Signifies the previous application version"
        quote: true

      - name: event_param_previous_first_open_count
        description: "The number of times first_open was logged before this occurrence"
        quote: true

      - name: event_param_previous_os_version
        description: "Signifies the previous OS version"
        quote: true

      - name: event_param_price
        description: "PAYMENT_PRODUCT_PRICE/IAP price"
        quote: true

      - name: event_param_product_id
        description: "IAP product id, auto sent by firebase"
        quote: true

      - name: event_param_product_name
        description: "Product Name"
        quote: true

      - name: event_param_rail_offset
        description: "Index in rail when a tile is selected"
        quote: true

      - name: search_result_category
        description: "category id"
        quote: true

      - name: event_param_screen_name
        description: "Screen Name"
        quote: true

      - name: search_item_selected
        description: "On older versions of the app, this is a param that duplicates event name. On newer versions this in not a param."
        quote: true

      - name: search_term
        description: "On older versions of the app, this is a param that duplicates event name. On newer versions this in not a param."
        quote: true

      - name: event_param_source
        description: "Install/Re-engagement Campaign Source"
        quote: true

      - name: event_param_sport_id
        description: "SPORT_ID_PARAM_NAME, sport ID (PCMS)"
        quote: true

      - name: event_param_sport_name
        description: "sport name"
        quote: true

      - name: event_param_update_with_analytics
        description: "Signifies that the first_open was logged as a result of an update to a new version of the app which integrates Google Analytics"
        quote: true

      - name: event_param_view_mode
        description: "View Mode, Possible values: fit_screen | fill_screen"
        quote: true

      - name: open_browse
        description: "available|unavailable|active|inactive, contains the current status of open browse"
        quote: true

      - name: reminders_feature
        description: "Reminders feature status, true|false"
        quote: true

      - name: firebase_experiment_parameters
        description: "FB auto, experiments numbers"
        quote: true

      - name: share_page
        description: "The exact screen page name that was shared through the share link. Example values are home, schedule, category, standings."
        quote: true

      - name: share_origin
        description: "The platform from where the share link was created and shared. Default value is 'Web' but can also be 'ios' and 'android'."
        quote: true

      - name: experiment_id
        description: "ID of Experiment"
        quote: true

      - name: experiment_key
        description: "Name of Experiment"
        quote: true

      - name: variation_id
        description: "ID of Variation"
        quote: true

      - name: variation_key
        description: "Name of Variation"
        quote: true

      - name: dazn_device_id
        description: "DAZN Device ID"
        quote: true

      - name: article_name
        description: "Report article name"
        quote: true

      - name: navigate_to
        description: "Report navigateTo value from backend response"
        quote: true

      - name: tile_title
        description: "Report tile title"
        quote: true

      - name: rail_position_of_loaded
        description: "Report position of rail in loaded list"
        quote: true

      - name: rail_position_in_view
        description: "Report position of rail in visible part of the screen"
        quote: true

      - name: rail_position_of_tile_start
        description: "Report start position of tiles in rail (to which tile rail should be scrolled before user will see rail)"
        quote: true

      - name: rail_name
        description: "Report Rail id"
        quote: true

      - name: rail_title
        description: "Report rail title"
        quote: true

      - name: rail_length
        description: "Report  number of tiles in rail"
        quote: true

      - name: coming_up
        description: "Report teaser when tile has teaser or otherwise none"
        quote: true

      - name: tile_position_of_loaded
        description: "Report position of tile on the  list"
        quote: true

      - name: tile_position_in_view
        description: "Report position of tile in visible part of the screen"
        quote: true

      - name: status
        description: "Report tile status"
        quote: true

      - name: dazn_session_id
        description: "DAZN user session identifier, that is generated when a user opens a player. Its also sent to both Conviva and Total Rekall."
        quote: true

      - name: dazn_event_date_dt
        description: ""
        quote: true

      - name: dazn_event_timestamp_ts
        description: ""
        quote: true

      - name: dazn_event_previous_timestamp_ts
        description: ""
        quote: true

      - name: event_param_article_id
        description: ""
        quote: true
