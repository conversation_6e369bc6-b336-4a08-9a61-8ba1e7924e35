version: 2

sources:
  - name: CURATED
    database: TRANSFORMATION_PROD
    quoting:
      identifier: true
    tables:
      - name: CURATED__M2A__CURRENT
        description: "Logs coming from the M2A source, this step ensures we have only one record per primary key."
        columns:
          - name: event_id
            description: "DAZN MFL Id of the fixture."

          - name: client_content_id
            description: "M2A Event ID"

          - name: outlet
            description: "Regional outlet"

          - name: territory
            description: "territory derived from outlet"

          - name: ad_date
            description: "The date the asset played out on"

          - name: ad_start_timestamp
            description: "Start time of asset playout based on stream time."

          - name: ad_end_timestamp
            description: "End time of the assest playout. Derived from StartTime plus Duration"

          - name: duration
            description: "Duration in seconds of the inserted asset - ie the amount played out, not asset duration, or the scheduled duration."

          - name: break_id
            description: "The ID of the break as identified from splice_event_id."

          - name: content_id
            description: "Id of the content played. Some of the values also refers to Google Ad Manager VAST creatives. Creative name should be exactly what it is in WON"

          - name: gam_id
            description: "This refers to the Google Ad Manager line item Id. This is the numeric ID for the delivered Ad Manager line item."

          - name: streaming_protocol
            description: "The streaming protocol used to deliver the asset"

          - name: origin_data_centre
            description: "The data centre used to deliver the asset"

          - name: origin_server
            description: "Derived from DateCentre"

          - name: primary_key
            description: "A combination of fields to uniquely define a Log. This is composed of client_content_id(fixture_id), Outlet and StartTime."
