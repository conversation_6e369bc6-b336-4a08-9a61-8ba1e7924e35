WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__SEGMENT__USER_UPDATE_PROFILE_CURRENT') }}
)

SELECT
    "CONTEXT_LIBRARY_VERSION" AS "context_library_version"
    ,"DATE_OF_BIRTH" AS "date_of_birth"
    ,"EVENT" AS "event"
    ,"PROFILE_ID" AS "profile_id"
    ,"RECEIVED_AT" AS "received_at"
    ,"UPDATED_AT" AS "updated_at"
    ,"UUID_TS" AS "uuid_ts"
    ,"NICK_NAME" AS "nick_name"
    ,"CONTEXT_LIBRARY_NAME" AS "context_library_name"
    ,"EMAIL" AS "email"
    ,"GENDER" AS "gender"
    ,"ID" AS "id"
    ,"TIMESTAMP" AS "timestamp"
    ,"USER_ID" AS "user_id"
    ,"CREATED_AT" AS "created_at"
    ,"DAZN_ID" AS "dazn_user_id"
    ,"EVENT_TEXT" AS "event_text"
    ,"MARKETING_PHONE_NUMBER" AS "marketing_phone_number"
    ,"ORIGINAL_TIMESTAMP" AS "original_timestamp"
    ,"SENT_AT" AS "sent_at"
    ,"AGE_RANGE" AS "age_range"
    ,"PHONE_COUNTRY_CODE" AS "phone_country_code"
FROM source
