WITH segment_user_events_identifies_scd AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__SEGMENT__USER_PREFERENCES_IDENTIFIES_SCD') }}
)

SELECT
    "ID" AS "id"
    ,"USER_ID" AS "dazn_user_id"
    ,"DAZN_ID" AS "dazn_user_id_nulls"
    ,"TIMESTAMP" AS "timestamp"
    ,"ORIGINAL_TIMESTAMP" AS "original_timestamp"
    ,"LAST_UPDATED_TIMESTAMP" AS "last_updated_timestamp"
    ,"SENT_AT" AS "sent_at_timestamp"
    ,"RECEIVED_AT" AS "received_at_timestamp"
    ,"UUID_TS" AS "uuid_timestamp"
    ,"ALLOW_MARKETING_EMAILS" AS "has_allowed_dazn_marketing_emails"
    ,"ALLOW_NFL_MARKETING_EMAILS" AS "has_allowed_nfl_marketing_emails"
    ,"PREFERENCES_MULTI_TRACK_AUDIO_LANGUAGE" AS "preferences_multi_track_audio_language"
    ,"PREFERENCES_KEY_MOMENTS_DISABLED" AS "preferences_key_moments_disabled"
    ,"PREFERENCES_OPTED_IN_THIRTY_DAYS_CANCELLATION" AS "preferences_opted_in_thirty_days_cancellation"
    ,"PREFERENCES_OPTED_OUT_FROM_PERSONALISATION" AS "preferences_opted_out_from_personalisation"
    ,"ALLOW_DAZNBET_MARKETING_EMAILS" AS "allow_daznbet_marketing_emails"
    ,"MARKETING_CONSENT_BET_SMS" AS "marketing_consent_bet_sms"
    ,"MARKETING_CONSENT_BET_PUSH" AS "marketing_consent_bet_push"
    ,"NEWS_OFFERS_EMAIL" AS "news_offers_email"
    ,"NEWS_OFFERS_SMS" AS "news_offers_sms"
    ,"NEWS_OFFERS_PUSH" AS "news_offers_push"
    ,"DAZN_OFFERS_SMS" AS "dazn_offers_sms"
    ,"DAZN_OFFERS_PUSH" AS "dazn_offers_push"
    ,"PROMOTION_OFFERS_EMAIL" AS "promotion_offers_email"
    ,"PROMOTION_OFFERS_SMS" AS "promotion_offers_sms"
    ,"PROMOTION_OFFERS_PUSH" AS "promotion_offers_push"
    ,"CONTEXT_LIBRARY_VERSION" AS "context_library_version"
    ,"CONTEXT_LIBRARY_NAME" AS "context_library_name"
    ,"record_valid_from_timestamp"
    ,"record_valid_until_timestamp"
FROM segment_user_events_identifies_scd
