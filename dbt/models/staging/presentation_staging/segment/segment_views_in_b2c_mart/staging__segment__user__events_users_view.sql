{{
    config(
        database='B2C__MART__' + snowflake_env(),
        schema='INTERMEDIATE',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        alias='DAZN_USER'
    )
}}

WITH source AS (
    SELECT * FROM {{ ref('staging__segment__user_events_users') }}
)

SELECT "dazn_user_id"
    ,"salesforce_id"
    ,"first_name"
    ,"last_name"
    ,"email"
    ,"user_type"
    ,"user_account_created_event_timestamp"
    ,"nfl_user"
    ,"viewer_id"
    ,"source_type"
FROM source
