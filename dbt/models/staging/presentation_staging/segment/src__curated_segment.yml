version: 2

sources:
  - name: CURATED
    database: TRANSFORMATION_{{ 'UAT' if env_var('AIRFLOW_VAR_ENV') == 'stage' else env_var('AIRFLOW_VAR_ENV') }}
    tables:
      - name: CURATED__SEGMENT__USER_EVENTS_IDENTIFIES_SCD
        description: "Simple Slowly Changing Dimension (SCD) table of the Segment User Events IDENTIFIES table to show for all Dazn User IDs the exat timestamp certain traits were valid from and until"
        columns:
          - name: ID
            description: "The unique ID of the identify call itself related directly to Segment data."
            quote: true

          - name: USER_ID
            description: "The unique ID of the user. Matching the Dazn User Id."
            quote: true

          - name: DAZN_ID
            description: "The DAZN User Id of the user. Never NULL."
            quote: true

          - name: ZUORA_ID
            description: "The Zuora Account ID of the user. Can be NULL if the user does not have a subscription at the time of the event."
            quote: true

          - name: SALESFORCE_ID
            description: "The Salesforce Account ID of the user. Often NULL mainly for Partial users, as they haven't made it to Salesforce at the exact time of the User created event."
            quote: true

          - name: VIEWER_ID
            description: "The Viewer ID of the user. Very rarely, if ever, NULL."
            quote: true

          - name: PARTNER_ID
            description: "The ID coming from external Partners (like Docomo or NFL)."
            quote: true

          - name: PROVIDER_CUSTOMER_ID
            description: "TBC"
            quote: true

          - name: LOCALE_SID_KEY
            description: "TBC"
            quote: true

          - name: TIMESTAMP
            description: "The UTC-converted timestamp which is set by the Segment library. If you are importing historical events using a server-side library, this is the timestamp you will want to reference in your queries."
            quote: true

          - name: ORIGINAL_TIMESTAMP
            description: "The original timestamp set by the Segment library at the time the event is created. Generally, this timestamp should be ignored in favor of the timestamp column."
            quote: true

          - name: RECEIVED_AT
            description: "The UTC timestamp set by the Segment API when the API receives the payload from client or server. This does not ensure chronology of events. For queries based on event chronology, timestamp should be used."
            quote: true

          - name: SENT_AT
            description: "The UTC timestamp set by library when the Segment API call was sent. This timestamp can be affected by device clock skew."
            quote: true

          - name: UUID_TS
            description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
            quote: true

          - name: FIRST_NAME
            description: "The first name of the user. Masked as is PII."
            quote: true

          - name: LAST_NAME
            description: "The last name of the user. Masked as is PII."
            quote: true

          - name: EMAIL
            description: "The email of the user. Masked as is PII."
            quote: true

          - name: IP_ADDRESS
            description: "The IP Address of the user. Masked as is PII."
            quote: true

          - name: HOME_COUNTRY_CODE
            description: "The two-letter Country Code of the user. E.g. GB, IT, DE, ..."
            quote: true

          - name: LANGUAGE
            description: "The language of the user. E.g. en, es, it, ..."
            quote: true

          - name: LANGUAGE_LOCALE_KEY
            description: "The language locale of the user. E.g. en_US, pt_BR, de, it, ..."
            quote: true

          - name: SOURCE_TYPE
            description: "The source of the event (E.g. NFL)"
            quote: true

          - name: USER_TYPE
            description: "The system type of the user. E.g. Dazn, Dazn2, Docomo, ..."
            quote: true

          - name: NFL_USER
            description: "The type of NFL user (E.g. New/Existing), NULL if Existing"
            quote: true

          - name: NFL_AUTHENTICATED
            description: "Flag for if the user has authenticated their email on the NFL platform"
            quote: true

          - name: PRODUCT_STATUS_DAZN
            description: "The product status for the user on the DAZN platform"
            quote: true

          - name: PRODUCT_STATUS_NFL
            description: "The product status for the user on the NFL platform"
            quote: true

          - name: STATUS
            description: "Currently always NULL."
            quote: true

          - name: MY_ACCOUNT_STATUS
            description: "Currently always NULL."
            quote: true

          - name: SUBSCRIPTION_STATUS
            description: "Subscription Status of the user, probably coming dircetly from Salesforce"
            quote: true

          - name: SUBSCRIPTION_CANCELLED
            description: "TBC"
            quote: true

          - name: SUBCRIPTION_PAUSE_FROM
            description: "TBC"
            quote: true

          - name: LATEST_GIFTCODE_EXPIRES_AT
            description: "TBC"
            quote: true

          - name: EMAIL_VERIFIED_AT
            description: "TBC"
            quote: true

          - name: IS_RESUMING
            description: "TBC"
            quote: true

          - name: SET_PAYMENT_METHOD
            description: "TBC"
            quote: true

          - name: USER_ACCOUNT_CREATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_ACCOUNT_UPDATE_INGESTED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_ACCOUNT_MIGRATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_STATUS_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: SIGN_UP_DETAILS_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: MY_DAZN_DETAILS_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: MARKETING_FLAGS_UPDATED_TIMESTAMP
            description: "TBC"
            quote: true

          - name: HOME_COUNTRY_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: TRACK_CLIENT_STATUS_DAZN_BET
            description: "TBC"
            quote: true

          - name: ALLOW_THIRD_PARTY
            description: "TBC"
            quote: true

          - name: ALLOW_MARKETING_EMAILS
            description: "Flag for if the user has allowed Dazn marketing emails. Will shortly be deprecated for the User Preferences events."
            quote: true

          - name: ALLOW_NFL_MARKETING_EMAILS
            description: "Flag for if the user has allowed NFL marketing emails. Will shortly be deprecated for the User Preferences events."
            quote: true

          - name: CONTEXT_LIBRARY_VERSION
            description: "TBC"
            quote: true

          - name: CONTEXT_LIBRARY_NAME
            description: "TBC"
            quote: true

          - name: record_valid_from_timestamp
            description: "The datetime this data point is valid from (inclusive)"
            quote: true

          - name: record_valid_until_timestamp
            description: "The datetime this data point is valid until (exlusive)"
            quote: true

          - name: EVENT_TYPE
            description: "TBC"
            quote: true

          - name: DOMAIN_NAME
            description: "TBC"
            quote: true

      - name: CURATED__SEGMENT__USER_PREFERENCES_IDENTIFIES_SCD
        description: "Simple Slowly Changing Dimension (SCD) table of the Segment User Preferences IDENTIFIES table to show for all Dazn User IDs the exat timestamp certain traits were valid from and until"
        columns:
          - name: ID
            description: "The unique ID of the identify call itself related directly to Segment data."
            quote: true

          - name: USER_ID
            description: "The unique ID of the user. Matching the Dazn User Id."
            quote: true

          - name: DAZN_ID
            description: "The DAZN User Id of the user. Never NULL."
            quote: true

          - name: TIMESTAMP
            description: "The UTC-converted timestamp which is set by the Segment library. If you are importing historical events using a server-side library, this is the timestamp you will want to reference in your queries."
            quote: true

          - name: ORIGINAL_TIMESTAMP
            description: "The original timestamp set by the Segment library at the time the event is created. Generally, this timestamp should be ignored in favor of the timestamp column."
            quote: true

          - name: LAST_UPDATED_TIMESTAMP
            description: "TBC"
            quote: true

          - name: RECEIVED_AT
            description: "The UTC timestamp set by the Segment API when the API receives the payload from client or server. This does not ensure chronology of events. For queries based on event chronology, timestamp should be used."
            quote: true

          - name: SENT_AT
            description: "The UTC timestamp set by library when the Segment API call was sent. This timestamp can be affected by device clock skew."
            quote: true

          - name: UUID_TS
            description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
            quote: true

          - name: ALLOW_MARKETING_EMAILS
            description: "Flag for if the user has allowed Dazn marketing emails."
            quote: true

          - name: ALLOW_NFL_MARKETING_EMAILS
            description: "Flag for if the user has allowed NFL marketing emails."
            quote: true

          - name: PREFERENCES_MULTI_TRACK_AUDIO_LANGUAGE
            description: "TBC"
            quote: true

          - name: PREFERENCES_KEY_MOMENTS_DISABLED
            description: "TBC"
            quote: true

          - name: PREFERENCES_OPTED_IN_THIRTY_DAYS_CANCELLATION
            description: "TBC"
            quote: true

          - name: PREFERENCES_OPTED_OUT_FROM_PERSONALISATION
            description: "TBC"
            quote: true

          - name: ALLOW_DAZNBET_MARKETING_EMAILS
            description: "TBC"
            quote: true

          - name: MARKETING_CONSENT_BET_SMS
            description: "TBC"
            quote: true

          - name: MARKETING_CONSENT_BET_PUSH
            description: "TBC"
            quote: true

          - name: NEWS_OFFERS_EMAIL
            description: "TBC"
            quote: true

          - name: NEWS_OFFERS_SMS
            description: "TBC"
            quote: true

          - name: NEWS_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: DAZN_OFFERS_SMS
            description: "TBC"
            quote: true

          - name: DAZN_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: PROMOTION_OFFERS_EMAIL
            description: "TBC"
            quote: true

          - name: PROMOTION_OFFERS_SMS
            description: "TBC"
            quote: true

          - name: PROMOTION_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: CONTEXT_LIBRARY_VERSION
            description: "TBC"
            quote: true

          - name: CONTEXT_LIBRARY_NAME
            description: "TBC"
            quote: true

          - name: record_valid_from_timestamp
            description: "The datetime this data point is valid from (inclusive)"
            quote: true

          - name: record_valid_until_timestamp
            description: "The datetime this data point is valid until (exlusive)"
            quote: true

      - name: CURATED__SEGMENT__USER_EVENTS_IDENTIFIES
        description: "IDENTIFIES table from Segment data of the User Events/Service, containing a log of all events with some top-level traits of the users at the time of the event"
        columns:
          - name: ID
            description: "The unique ID of the identify call itself related directly to Segment data."
            quote: true

          - name: USER_ID
            description: "The unique ID of the user. Matching the Dazn User Id."
            quote: true

          - name: DAZN_ID
            description: "The DAZN User Id of the user. Never NULL."
            quote: true

          - name: ZUORA_ID
            description: "The Zuora Account ID of the user. Can be NULL if the user does not have a subscription at the time of the event."
            quote: true

          - name: SALESFORCE_ID
            description: "The Salesforce Account ID of the user. Often NULL mainly for Partial users, as they haven't made it to Salesforce at the exact time of the User created event."
            quote: true

          - name: VIEWER_ID
            description: "The Viewer ID of the user. Very rarely, if ever, NULL."
            quote: true

          - name: PARTNER_ID
            description: "The ID coming from external Partners (like Docomo or NFL)."
            quote: true

          - name: PROVIDER_CUSTOMER_ID
            description: "TBC"
            quote: true

          - name: LOCALE_SID_KEY
            description: "TBC"
            quote: true

          - name: TIMESTAMP
            description: "The UTC-converted timestamp which is set by the Segment library. If you are importing historical events using a server-side library, this is the timestamp you will want to reference in your queries."
            quote: true

          - name: ORIGINAL_TIMESTAMP
            description: "The original timestamp set by the Segment library at the time the event is created. Generally, this timestamp should be ignored in favor of the timestamp column."
            quote: true

          - name: RECEIVED_AT
            description: "The UTC timestamp set by the Segment API when the API receives the payload from client or server. This does not ensure chronology of events. For queries based on event chronology, timestamp should be used."
            quote: true

          - name: SENT_AT
            description: "The UTC timestamp set by library when the Segment API call was sent. This timestamp can be affected by device clock skew."
            quote: true

          - name: UUID_TS
            description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
            quote: true

          - name: FIRST_NAME
            description: "The first name of the user. Masked as is PII."
            quote: true

          - name: LAST_NAME
            description: "The last name of the user. Masked as is PII."
            quote: true

          - name: EMAIL
            description: "The email of the user. Masked as is PII."
            quote: true

          - name: IP_ADDRESS
            description: "The IP Address of the user. Masked as is PII."
            quote: true

          - name: HOME_COUNTRY_CODE
            description: "The two-letter Country Code of the user. E.g. GB, IT, DE, ..."
            quote: true

          - name: LANGUAGE
            description: "The language of the user. E.g. en, es, it, ..."
            quote: true

          - name: LANGUAGE_LOCALE_KEY
            description: "The language locale of the user. E.g. en_US, pt_BR, de, it, ..."
            quote: true

          - name: SOURCE_TYPE
            description: "The source type of the user (E.g. NFL)"
            quote: true

          - name: USER_TYPE
            description: "The system type of the user. E.g. Dazn, Dazn2, Docomo, ..."
            quote: true

          - name: NFL_USER
            description: "The type of NFL user (E.g. New/Existing), NULL if Existing?"
            quote: true

          - name: NFL_AUTHENTICATED
            description: "Flag for if the user has authenticated their email on the NFL platform"
            quote: true

          - name: PRODUCT_STATUS_DAZN
            description: "The product status for the user on the DAZN platform"
            quote: true

          - name: PRODUCT_STATUS_NFL
            description: "The product status for the user on the NFL platform"
            quote: true

          - name: STATUS
            description: "Currently always NULL."
            quote: true

          - name: MY_ACCOUNT_STATUS
            description: "Currently always NULL."
            quote: true

          - name: SUBSCRIPTION_STATUS
            description: "Subscription Status of the user, probably coming dircetly from Salesforce"
            quote: true

          - name: SUBSCRIPTION_CANCELLED
            description: "TBC"
            quote: true

          - name: SUBCRIPTION_PAUSE_FROM
            description: "TBC"
            quote: true

          - name: LATEST_GIFTCODE_EXPIRES_AT
            description: "TBC"
            quote: true

          - name: EMAIL_VERIFIED_AT
            description: "TBC"
            quote: true

          - name: IS_RESUMING
            description: "TBC"
            quote: true

          - name: SET_PAYMENT_METHOD
            description: "TBC"
            quote: true

          - name: USER_ACCOUNT_CREATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_ACCOUNT_UPDATE_INGESTED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_ACCOUNT_MIGRATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_STATUS_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: SIGN_UP_DETAILS_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: MY_DAZN_DETAILS_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: MARKETING_FLAGS_UPDATED_TIMESTAMP
            description: "TBC"
            quote: true

          - name: HOME_COUNTRY_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: TRACK_CLIENT_STATUS_DAZN_BET
            description: "TBC"
            quote: true

          - name: ALLOW_THIRD_PARTY
            description: "TBC"
            quote: true

          - name: ALLOW_MARKETING_EMAILS
            description: "Flag for if the user has allowed Dazn marketing emails. Will shortly be deprecated for the User Preferences events."
            quote: true

          - name: ALLOW_NFL_MARKETING_EMAILS
            description: "Flag for if the user has allowed NFL marketing emails. Will shortly be deprecated for the User Preferences events."
            quote: true

          - name: CONTEXT_LIBRARY_VERSION
            description: "TBC"
            quote: true

          - name: CONTEXT_LIBRARY_NAME
            description: "TBC"
            quote: true

          - name: EVENT_TYPE
            description: "TBC"
            quote: true

          - name: DOMAIN_NAME
            description: "TBC"
            quote: true

      - name: CURATED__SEGMENT__USER_EVENTS_IDENTIFIES_CURRENT
        description: "IDENTIFIES table from Segment data of the User Events/Service, containing a log of all events with some top-level traits of the users at the time of the event"
        columns:
          - name: ID
            description: "The unique ID of the identify call itself related directly to Segment data."
            quote: true

          - name: USER_ID
            description: "The unique ID of the user. Matching the Dazn User Id."
            quote: true

          - name: DAZN_ID
            description: "The DAZN User Id of the user. Never NULL."
            quote: true

          - name: ZUORA_ID
            description: "The Zuora Account ID of the user. Can be NULL if the user does not have a subscription at the time of the event."
            quote: true

          - name: SALESFORCE_ID
            description: "The Salesforce Account ID of the user. Often NULL mainly for Partial users, as they haven't made it to Salesforce at the exact time of the User created event."
            quote: true

          - name: VIEWER_ID
            description: "The Viewer ID of the user. Very rarely, if ever, NULL."
            quote: true

          - name: PARTNER_ID
            description: "The ID coming from external Partners (like Docomo or NFL)."
            quote: true

          - name: PROVIDER_CUSTOMER_ID
            description: "TBC"
            quote: true

          - name: LOCALE_SID_KEY
            description: "TBC"
            quote: true

          - name: TIMESTAMP
            description: "The UTC-converted timestamp which is set by the Segment library. If you are importing historical events using a server-side library, this is the timestamp you will want to reference in your queries."
            quote: true

          - name: ORIGINAL_TIMESTAMP
            description: "The original timestamp set by the Segment library at the time the event is created. Generally, this timestamp should be ignored in favor of the timestamp column."
            quote: true

          - name: RECEIVED_AT
            description: "The UTC timestamp set by the Segment API when the API receives the payload from client or server. This does not ensure chronology of events. For queries based on event chronology, timestamp should be used."
            quote: true

          - name: SENT_AT
            description: "The UTC timestamp set by library when the Segment API call was sent. This timestamp can be affected by device clock skew."
            quote: true

          - name: UUID_TS
            description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
            quote: true

          - name: FIRST_NAME
            description: "The first name of the user. Masked as is PII."
            quote: true

          - name: LAST_NAME
            description: "The last name of the user. Masked as is PII."
            quote: true

          - name: EMAIL
            description: "The email of the user. Masked as is PII."
            quote: true

          - name: IP_ADDRESS
            description: "The IP Address of the user. Masked as is PII."
            quote: true

          - name: HOME_COUNTRY_CODE
            description: "The two-letter Country Code of the user. E.g. GB, IT, DE, ..."
            quote: true

          - name: LANGUAGE
            description: "The language of the user. E.g. en, es, it, ..."
            quote: true

          - name: LANGUAGE_LOCALE_KEY
            description: "The language locale of the user. E.g. en_US, pt_BR, de, it, ..."
            quote: true

          - name: SOURCE_TYPE
            description: "The source type of the user (E.g. NFL)"
            quote: true

          - name: USER_TYPE
            description: "The system type of the user. E.g. Dazn, Dazn2, Docomo, ..."
            quote: true

          - name: NFL_USER
            description: "The type of NFL user (E.g. New/Existing), NULL if Existing?"
            quote: true

          - name: NFL_AUTHENTICATED
            description: "Flag for if the user has authenticated their email on the NFL platform"
            quote: true

          - name: PRODUCT_STATUS_DAZN
            description: "The product status for the user on the DAZN platform"
            quote: true

          - name: PRODUCT_STATUS_NFL
            description: "The product status for the user on the NFL platform"
            quote: true

          - name: STATUS
            description: "Currently always NULL."
            quote: true

          - name: MY_ACCOUNT_STATUS
            description: "Currently always NULL."
            quote: true

          - name: SUBSCRIPTION_STATUS
            description: "Subscription Status of the user, probably coming dircetly from Salesforce"
            quote: true

          - name: SUBSCRIPTION_CANCELLED
            description: "TBC"
            quote: true

          - name: SUBCRIPTION_PAUSE_FROM
            description: "TBC"
            quote: true

          - name: LATEST_GIFTCODE_EXPIRES_AT
            description: "TBC"
            quote: true

          - name: EMAIL_VERIFIED_AT
            description: "TBC"
            quote: true

          - name: IS_RESUMING
            description: "TBC"
            quote: true

          - name: SET_PAYMENT_METHOD
            description: "TBC"
            quote: true

          - name: USER_ACCOUNT_CREATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_ACCOUNT_UPDATE_INGESTED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_ACCOUNT_MIGRATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_STATUS_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: SIGN_UP_DETAILS_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: MY_DAZN_DETAILS_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: MARKETING_FLAGS_UPDATED_TIMESTAMP
            description: "TBC"
            quote: true

          - name: HOME_COUNTRY_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: TRACK_CLIENT_STATUS_DAZN_BET
            description: "TBC"
            quote: true

          - name: ALLOW_THIRD_PARTY
            description: "TBC"
            quote: true

          - name: ALLOW_MARKETING_EMAILS
            description: "Flag for if the user has allowed Dazn marketing emails. Will shortly be deprecated for the User Preferences events."
            quote: true

          - name: ALLOW_NFL_MARKETING_EMAILS
            description: "Flag for if the user has allowed NFL marketing emails. Will shortly be deprecated for the User Preferences events."
            quote: true

          - name: CONTEXT_LIBRARY_VERSION
            description: "TBC"
            quote: true

          - name: CONTEXT_LIBRARY_NAME
            description: "TBC"
            quote: true

          - name: EVENT_TYPE
            description: "TBC"
            quote: true

          - name: DOMAIN_NAME
            description: "TBC"
            quote: true

      - name: CURATED__SEGMENT__USER_EVENTS_USERS
        description: "IDENTIFIES table from Segment data of the User Events/Service, containing a log of all events with some top-level traits of the users at the time of the event"
        columns:
          - name: ID
            description: "The unique ID of the identify call itself related directly to Segment data."
            quote: true

          - name: DAZN_ID
            description: "The DAZN User Id of the user. Never NULL."
            quote: true

          - name: ZUORA_ID
            description: "The Zuora Account ID of the user. Can be NULL if the user does not have a subscription at the time of the event."
            quote: true

          - name: SALESFORCE_ID
            description: "The Salesforce Account ID of the user. Often NULL mainly for Partial users, as they haven't made it to Salesforce at the exact time of the User created event."
            quote: true

          - name: VIEWER_ID
            description: "The Viewer ID of the user. Very rarely, if ever, NULL."
            quote: true

          - name: PARTNER_ID
            description: "The ID coming from external Partners (like Docomo or NFL)."
            quote: true

          - name: PROVIDER_CUSTOMER_ID
            description: "TBC"
            quote: true

          - name: LOCALE_SID_KEY
            description: "TBC"
            quote: true

          - name: RECEIVED_AT
            description: "The UTC timestamp set by the Segment API when the API receives the payload from client or server. This does not ensure chronology of events. For queries based on event chronology, timestamp should be used."
            quote: true

          - name: UUID_TS
            description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
            quote: true

          - name: FIRST_NAME
            description: "The first name of the user. Masked as is PII."
            quote: true

          - name: LAST_NAME
            description: "The last name of the user. Masked as is PII."
            quote: true

          - name: EMAIL
            description: "The email of the user. Masked as is PII."
            quote: true

          - name: IP_ADDRESS
            description: "The IP Address of the user. Masked as is PII."
            quote: true

          - name: HOME_COUNTRY_CODE
            description: "The two-letter Country Code of the user. E.g. GB, IT, DE, ..."
            quote: true

          - name: LANGUAGE
            description: "The language of the user. E.g. en, es, it, ..."
            quote: true

          - name: LANGUAGE_LOCALE_KEY
            description: "The language locale of the user. E.g. en_US, pt_BR, de, it, ..."
            quote: true

          - name: SOURCE_TYPE
            description: "The source type of the user (E.g. NFL)"
            quote: true

          - name: USER_TYPE
            description: "The system type of the user. E.g. Dazn, Dazn2, Docomo, ..."
            quote: true

          - name: NFL_USER
            description: "The type of NFL user (E.g. New/Existing), NULL if Existing?"
            quote: true

          - name: NFL_AUTHENTICATED
            description: "Flag for if the user has authenticated their email on the NFL platform"
            quote: true

          - name: PRODUCT_STATUS_DAZN
            description: "The product status for the user on the DAZN platform"
            quote: true

          - name: PRODUCT_STATUS_NFL
            description: "The product status for the user on the NFL platform"
            quote: true

          - name: PRODUCT_STATUS_LIGA_SEGUNDA
            description: "The product status for the user on the LIGA SEGUNDA platform"
            quote: true

          - name: PRODUCT_STATUS_FIBA
            description: "The product status for the user on the FIBA platform"
            quote: true

          - name: PRODUCT_STATUS_PGA
            description: "The product status for the user on the PGA platform"
            quote: true

          - name: PRODUCT_STATUS_RALLY_TV
            description: "The product status for the user on the RallyTV platform"
            quote: true

          - name: PRODUCT_STATUS_TENNIS_TV
            description: "The product status for the user on the TennisTV platform"
            quote: true

          - name: STATUS
            description: "Currently always NULL."
            quote: true

          - name: MY_ACCOUNT_STATUS
            description: "Currently always NULL."
            quote: true

          - name: SUBSCRIPTION_STATUS
            description: "Subscription Status of the user, probably coming dircetly from Salesforce"
            quote: true

          - name: SUBSCRIPTION_CANCELLED
            description: "TBC"
            quote: true

          - name: SUBCRIPTION_PAUSE_FROM
            description: "TBC"
            quote: true

          - name: LATEST_GIFTCODE_EXPIRES_AT
            description: "TBC"
            quote: true

          - name: EMAIL_VERIFIED_AT
            description: "TBC"
            quote: true

          - name: IS_RESUMING
            description: "TBC"
            quote: true

          - name: SET_PAYMENT_METHOD
            description: "TBC"
            quote: true

          - name: USER_ACCOUNT_CREATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_ACCOUNT_UPDATE_INGESTED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_ACCOUNT_MIGRATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_STATUS_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: SIGN_UP_DETAILS_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: MY_DAZN_DETAILS_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: MARKETING_FLAGS_UPDATED_TIMESTAMP
            description: "TBC"
            quote: true

          - name: HOME_COUNTRY_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: TRACK_CLIENT_STATUS_DAZN_BET
            description: "TBC"
            quote: true

          - name: ALLOW_THIRD_PARTY
            description: "TBC"
            quote: true

          - name: ALLOW_MARKETING_EMAILS
            description: "Flag for if the user has allowed Dazn marketing emails. Will shortly be deprecated for the User Preferences events."
            quote: true

          - name: ALLOW_NFL_MARKETING_EMAILS
            description: "Flag for if the user has allowed NFL marketing emails. Will shortly be deprecated for the User Preferences events."
            quote: true

          - name: CONTEXT_LIBRARY_VERSION
            description: "TBC"
            quote: true

          - name: CONTEXT_LIBRARY_NAME
            description: "TBC"
            quote: true

      - name: CURATED__SEGMENT__NEW_USER_EVENTS_USER_ACCOUNT_CREATED
        description: "table which holds the details of the user_account_created event"
        columns:
          - name: CANCELLED
            description: "tbc"
            quote: true

          - name: COUNTRY
            description: "tbc"
            quote: true

          - name: CREATED_AT
            description: "tbc"
            quote: true

          - name: EMAIL
            description: "tbc"
            quote: true

          - name: EVENT_TYPE
            description: "tbc"
            quote: true

          - name: FIRST_NAME
            description: "tbc"
            quote: true

          - name: DAZN_ID
            description: "tbc"
            quote: true

          - name: IP_ADDRESS
            description: "tbc"
            quote: true

          - name: LANGUAGE_LOCALE_KEY
            description: "tbc"
            quote: true

          - name: LAST_NAME
            description: "tbc"
            quote: true

          - name: LOCALE_SID_KEY
            description: "tbc"
            quote: true

          - name: META_AWS_UPDATED_REGION
            description: "tbc"
            quote: true

          - name: PAUSE_IS_SCHEDULED
            description: "tbc"
            quote: true

          - name: PRODUCT_STATUS_DAZN
            description: "tbc"
            quote: true

          - name: PRODUCT_STATUS_NFL
            description: "tbc"
            quote: true

          - name: PROVIDER_CUSTOMER_ID
            description: "tbc"
            quote: true

          - name: SOURCE_TYPE
            description: "tbc"
            quote: true

          - name: TIMEZONES_SID_KEY
            description: "tbc"
            quote: true

          - name: USER_TYPE
            description: "tbc"
            quote: true

          - name: UPDATED_AT
            description: "tbc"
            quote: true

          - name: ACCOUNT_CREATED_TIMESTAMP
            description: "tbc"
            quote: true

          - name: VIEWER_ID
            description: "tbc"
            quote: true

          - name: S3_FILE_LAST_MODIFIED
            description: "tbc"
            quote: true

          - name: USER_EVENT_PAYLOAD_HASH
            description: "tbc"
            quote: true

          - name: DWH_INSERT_TIMESTAMP
            description: "tbc"
            quote: true

      - name: CURATED__SEGMENT__USER_EVENTS_IDENTIFIES_V1
        description: "IDENTIFIES table from Segment data of the User Events/Service, containing a log of all events with some top-level traits of the users at the time of the event"
        columns:
          - name: ID
            description: "The unique ID of the identify call itself related directly to Segment data."
            quote: true

          - name: USER_ID
            description: "The unique ID of the user. Matching the Dazn User Id."
            quote: true

          - name: DAZN_ID
            description: "The DAZN User Id of the user. Never NULL."
            quote: true

          - name: ZUORA_ID
            description: "The Zuora Account ID of the user. Can be NULL if the user does not have a subscription at the time of the event."
            quote: true

          - name: SALESFORCE_ID
            description: "The Salesforce Account ID of the user. Often NULL mainly for Partial users, as they haven't made it to Salesforce at the exact time of the User created event."
            quote: true

          - name: VIEWER_ID
            description: "The Viewer ID of the user. Very rarely, if ever, NULL."
            quote: true

          - name: PARTNER_ID
            description: "The ID coming from external Partners (like Docomo or NFL)."
            quote: true

          - name: PROVIDER_CUSTOMER_ID
            description: "TBC"
            quote: true

          - name: LOCALE_SID_KEY
            description: "TBC"
            quote: true

          - name: TIMESTAMP
            description: "The UTC-converted timestamp which is set by the Segment library. If you are importing historical events using a server-side library, this is the timestamp you will want to reference in your queries."
            quote: true

          - name: ORIGINAL_TIMESTAMP
            description: "The original timestamp set by the Segment library at the time the event is created. Generally, this timestamp should be ignored in favor of the timestamp column."
            quote: true

          - name: RECEIVED_AT
            description: "The UTC timestamp set by the Segment API when the API receives the payload from client or server. This does not ensure chronology of events. For queries based on event chronology, timestamp should be used."
            quote: true

          - name: SENT_AT
            description: "The UTC timestamp set by library when the Segment API call was sent. This timestamp can be affected by device clock skew."
            quote: true

          - name: UUID_TS
            description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
            quote: true

          - name: FIRST_NAME
            description: "The first name of the user. Masked as is PII."
            quote: true

          - name: LAST_NAME
            description: "The last name of the user. Masked as is PII."
            quote: true

          - name: EMAIL
            description: "The email of the user. Masked as is PII."
            quote: true

          - name: IP_ADDRESS
            description: "The IP Address of the user. Masked as is PII."
            quote: true

          - name: HOME_COUNTRY_CODE
            description: "The two-letter Country Code of the user. E.g. GB, IT, DE, ..."
            quote: true

          - name: LANGUAGE
            description: "The language of the user. E.g. en, es, it, ..."
            quote: true

          - name: LANGUAGE_LOCALE_KEY
            description: "The language locale of the user. E.g. en_US, pt_BR, de, it, ..."
            quote: true

          - name: SOURCE_TYPE
            description: "The source type of the user (E.g. NFL)"
            quote: true

          - name: USER_TYPE
            description: "The system type of the user. E.g. Dazn, Dazn2, Docomo, ..."
            quote: true

          - name: NFL_USER
            description: "The type of NFL user (E.g. New/Existing), NULL if Existing?"
            quote: true

          - name: NFL_AUTHENTICATED
            description: "Flag for if the user has authenticated their email on the NFL platform"
            quote: true

          - name: PRODUCT_STATUS_DAZN
            description: "The product status for the user on the DAZN platform"
            quote: true

          - name: PRODUCT_STATUS_NFL
            description: "The product status for the user on the NFL platform"
            quote: true

          - name: STATUS
            description: "Currently always NULL."
            quote: true

          - name: MY_ACCOUNT_STATUS
            description: "Currently always NULL."
            quote: true

          - name: SUBSCRIPTION_STATUS
            description: "Subscription Status of the user, probably coming dircetly from Salesforce"
            quote: true

          - name: SUBSCRIPTION_CANCELLED
            description: "TBC"
            quote: true

          - name: SUBCRIPTION_PAUSE_FROM
            description: "TBC"
            quote: true

          - name: LATEST_GIFTCODE_EXPIRES_AT
            description: "TBC"
            quote: true

          - name: EMAIL_VERIFIED_AT
            description: "TBC"
            quote: true

          - name: IS_RESUMING
            description: "TBC"
            quote: true

          - name: SET_PAYMENT_METHOD
            description: "TBC"
            quote: true

          - name: USER_ACCOUNT_CREATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_ACCOUNT_UPDATE_INGESTED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_ACCOUNT_MIGRATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_STATUS_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: SIGN_UP_DETAILS_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: MY_DAZN_DETAILS_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: MARKETING_FLAGS_UPDATED_TIMESTAMP
            description: "TBC"
            quote: true

          - name: HOME_COUNTRY_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: TRACK_CLIENT_STATUS_DAZN_BET
            description: "TBC"
            quote: true

          - name: ALLOW_THIRD_PARTY
            description: "TBC"
            quote: true

          - name: ALLOW_MARKETING_EMAILS
            description: "Flag for if the user has allowed Dazn marketing emails. Will shortly be deprecated for the User Preferences events."
            quote: true

          - name: ALLOW_NFL_MARKETING_EMAILS
            description: "Flag for if the user has allowed NFL marketing emails. Will shortly be deprecated for the User Preferences events."
            quote: true

          - name: CONTEXT_LIBRARY_VERSION
            description: "TBC"
            quote: true

          - name: CONTEXT_LIBRARY_NAME
            description: "TBC"
            quote: true

          - name: EVENT_TYPE
            description: "TBC"
            quote: true

          - name: MODEL
            description: "TBC"
            quote: true

          - name: REGISTRATION_DATE
            description: "TBC"
            quote: true

          - name: VENDOR
            description: "TBC"
            quote: true

          - name: OS_VERSION
            description: "TBC"
            quote: true

          - name: OS
            description: "TBC"
            quote: true

          - name: USER_AGENT
            description: "TBC"
            quote: true

          - name: PRIMARY_HARDWARE_TYPE
            description: "TBC"
            quote: true

          - name: DEVICE_NAME
            description: "TBC"
            quote: true

          - name: TYPE
            description: "TBC"
            quote: true

          - name: PRODUCT_STATUS_PGA
            description: "TBC"
            quote: true

          - name: SRC_EVENT_TYPE
            description: "TBC"
            quote: true

          - name: CONTENT_COUNTRY
            description: "TBC"
            quote: true

          - name: CONTENT_COUNTRY_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: DOMAIN_NAME
            description: "TBC"
            quote: true

          - name: CONCURRENCY
            description: "TBC"
            quote: true

          - name: USER_CONCURRENCY_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: RECEIVED_AT_DATE
            description: "TBC"
            quote: true

      - name: CURATED__SEGMENT__USER_EVENTS_IDENTIFIES_SCD_V1
        description: "Simple Slowly Changing Dimension (SCD) table of the Segment User Events IDENTIFIES table to show for all Dazn User IDs the exat timestamp certain traits were valid from and until"
        columns:
          - name: ID
            description: "The unique ID of the identify call itself related directly to Segment data."
            quote: true

          - name: USER_ID
            description: "The unique ID of the user. Matching the Dazn User Id."
            quote: true

          - name: DAZN_ID
            description: "The DAZN User Id of the user. Never NULL."
            quote: true

          - name: ZUORA_ID
            description: "The Zuora Account ID of the user. Can be NULL if the user does not have a subscription at the time of the event."
            quote: true

          - name: SALESFORCE_ID
            description: "The Salesforce Account ID of the user. Often NULL mainly for Partial users, as they haven't made it to Salesforce at the exact time of the User created event."
            quote: true

          - name: VIEWER_ID
            description: "The Viewer ID of the user. Very rarely, if ever, NULL."
            quote: true

          - name: PARTNER_ID
            description: "The ID coming from external Partners (like Docomo or NFL)."
            quote: true

          - name: PROVIDER_CUSTOMER_ID
            description: "TBC"
            quote: true

          - name: LOCALE_SID_KEY
            description: "TBC"
            quote: true

          - name: TIMESTAMP
            description: "The UTC-converted timestamp which is set by the Segment library. If you are importing historical events using a server-side library, this is the timestamp you will want to reference in your queries."
            quote: true

          - name: ORIGINAL_TIMESTAMP
            description: "The original timestamp set by the Segment library at the time the event is created. Generally, this timestamp should be ignored in favor of the timestamp column."
            quote: true

          - name: RECEIVED_AT
            description: "The UTC timestamp set by the Segment API when the API receives the payload from client or server. This does not ensure chronology of events. For queries based on event chronology, timestamp should be used."
            quote: true

          - name: SENT_AT
            description: "The UTC timestamp set by library when the Segment API call was sent. This timestamp can be affected by device clock skew."
            quote: true

          - name: UUID_TS
            description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
            quote: true

          - name: FIRST_NAME
            description: "The first name of the user. Masked as is PII."
            quote: true

          - name: LAST_NAME
            description: "The last name of the user. Masked as is PII."
            quote: true

          - name: EMAIL
            description: "The email of the user. Masked as is PII."
            quote: true

          - name: IP_ADDRESS
            description: "The IP Address of the user. Masked as is PII."
            quote: true

          - name: LANGUAGE
            description: "The language of the user. E.g. en, es, it, ..."
            quote: true

          - name: LANGUAGE_LOCALE_KEY
            description: "The language locale of the user. E.g. en_US, pt_BR, de, it, ..."
            quote: true

          - name: SOURCE_TYPE
            description: "The source of the event (E.g. NFL)"
            quote: true

          - name: USER_TYPE
            description: "The system type of the user. E.g. Dazn, Dazn2, Docomo, ..."
            quote: true

          - name: PRODUCT_STATUS_DAZN
            description: "The product status for the user on the DAZN platform"
            quote: true

          - name: PRODUCT_STATUS_NFL
            description: "The product status for the user on the NFL platform"
            quote: true

          - name: STATUS
            description: "Currently always NULL."
            quote: true

          - name: MY_ACCOUNT_STATUS
            description: "Currently always NULL."
            quote: true

          - name: SUBSCRIPTION_STATUS
            description: "Subscription Status of the user, probably coming dircetly from Salesforce"
            quote: true

          - name: SUBSCRIPTION_CANCELLED
            description: "TBC"
            quote: true

          - name: SUBCRIPTION_PAUSE_FROM
            description: "TBC"
            quote: true

          - name: LATEST_GIFTCODE_EXPIRES_AT
            description: "TBC"
            quote: true

          - name: EMAIL_VERIFIED_AT
            description: "TBC"
            quote: true

          - name: NFL_USER
            description: "TBC"
            quote: true

          - name: NFL_AUTHENTICATED
            description: "TBC"
            quote: true

          - name: IS_RESUMING
            description: "TBC"
            quote: true

          - name: SET_PAYMENT_METHOD
            description: "TBC"
            quote: true

          - name: USER_ACCOUNT_CREATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_ACCOUNT_UPDATE_INGESTED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_ACCOUNT_MIGRATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_STATUS_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: SIGN_UP_DETAILS_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: MY_DAZN_DETAILS_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: MARKETING_FLAGS_UPDATED_TIMESTAMP
            description: "TBC"
            quote: true

          - name: HOME_COUNTRY_UPDATED_EVENT_TIMESTAMP
            description: "TBC"
            quote: true

          - name: TRACK_CLIENT_STATUS_DAZN_BET
            description: "TBC"
            quote: true

          - name: ALLOW_THIRD_PARTY
            description: "TBC"
            quote: true

          - name: ALLOW_MARKETING_EMAILS
            description: "Flag for if the user has allowed Dazn marketing emails. Will shortly be deprecated for the User Preferences events."
            quote: true

          - name: ALLOW_NFL_MARKETING_EMAILS
            description: "Flag for if the user has allowed NFL marketing emails. Will shortly be deprecated for the User Preferences events."
            quote: true

          - name: CONTEXT_LIBRARY_VERSION
            description: "TBC"
            quote: true

          - name: CONTEXT_LIBRARY_NAME
            description: "TBC"
            quote: true

          - name: record_valid_from_timestamp
            description: "The datetime this data point is valid from (inclusive)"
            quote: true

          - name: record_valid_until_timestamp
            description: "The datetime this data point is valid until (exlusive)"
            quote: true

      - name: CURATED__SEGMENT__USER_EVENTS_IDENTIFIES_HOME_COUNTRY_SCD_V1
        columns:
          - name: USER_ID
            description: "The unique ID of the user. Matching the Dazn User Id."
            quote: true

          - name: ORIGINAL_TIMESTAMP
            description: "The original timestamp set by the Segment library at the time the event is created. Generally, this timestamp should be ignored in favor of the timestamp column."
            quote: true

          - name: HOME_COUNTRY_CODE
            description: "The two-letter Country Code of the user. E.g. GB, IT, DE, ..."
            quote: true

          - name: record_valid_from_timestamp
            description: "The datetime this data point is valid from (inclusive)"
            quote: true

          - name: record_valid_until_timestamp
            description: "The datetime this data point is valid until (exlusive)"
            quote: true

      - name: CURATED__SEGMENT__USER_EVENTS_IDENTIFIES_NFL_AUTH
        columns:
          - name: USER_ID
            description: "The unique ID of the user. Matching the Dazn User Id."
            quote: true

          - name: NFL_AUTHENTICATED_AT
            description: " "
            quote: true

          - name: NFL_USER
            description: "The type of NFL user (E.g. New/Existing), NULL if Existing?"
            quote: true

          - name: NFL_AUTHENTICATED
            description: "Flag for if the user has authenticated their email on the NFL platform"
            quote: true

      - name: CURATED__SEGMENT__USER_PREFERENCES_USERS
        description: "USERS table from Segment data of the User Preferences, containing a log of all events with some top-level traits of the users at the time of the event"
        columns:
          - name: ID
            description: "The unique ID of the identify call itself related directly to Segment data."
            quote: true

          - name: DAZN_ID
            description: "The DAZN User Id of the user. Never NULL."
            quote: true

          - name: LAST_UPDATED_TIMESTAMP
            description: "TBC"
            quote: true

          - name: RECEIVED_AT
            description: "The UTC timestamp set by the Segment API when the API receives the payload from client or server."
            quote: true

          - name: UUID_TS
            description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
            quote: true

          - name: ALLOW_MARKETING_EMAILS
            description: "Flag for if the user has allowed Dazn marketing emails."
            quote: true

          - name: ALLOW_NFL_MARKETING_EMAILS
            description: "Flag for if the user has allowed NFL marketing emails."
            quote: true

          - name: PREFERENCES_MULTI_TRACK_AUDIO_LANGUAGE
            description: "TBC"
            quote: true

          - name: PREFERENCES_KEY_MOMENTS_DISABLED
            description: "TBC"
            quote: true

          - name: PREFERENCES_OPTED_IN_THIRTY_DAYS_CANCELLATION
            description: "TBC"
            quote: true

          - name: PREFERENCES_OPTED_OUT_FROM_PERSONALISATION
            description: "TBC"
            quote: true

          - name: ALLOW_DAZNBET_MARKETING_EMAILS
            description: "TBC"
            quote: true

          - name: MARKETING_CONSENT_BET_SMS
            description: "TBC"
            quote: true

          - name: MARKETING_CONSENT_BET_PUSH
            description: "TBC"
            quote: true

          - name: NEWS_OFFERS_EMAIL
            description: "TBC"
            quote: true

          - name: NEWS_OFFERS_SMS
            description: "TBC"
            quote: true

          - name: NEWS_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: DAZN_OFFERS_SMS
            description: "TBC"
            quote: true

          - name: DAZN_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: PROMOTION_OFFERS_EMAIL
            description: "TBC"
            quote: true

          - name: PROMOTION_OFFERS_SMS
            description: "TBC"
            quote: true

          - name: PROMOTION_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: CONTEXT_LIBRARY_VERSION
            description: "TBC"
            quote: true

          - name: CONTEXT_LIBRARY_NAME
            description: "TBC"
            quote: true

          - name: AGE_CONSENT
            description: "TBC"
            quote: true

          - name: SIX_TO_WIN_PUSH
            description: "TBC"
            quote: true

          - name: SIX_TO_WIN_EMAIL
            description: "TBC"
            quote: true

          - name: SIX_TO_WIN_SMS
            description: "TBC"
            quote: true

          - name: FIBA_OFFERS_EMAIL
            description: "TBC"
            quote: true

          - name: FIBA_OFFERS_SMS
            description: "TBC"
            quote: true

          - name: FIBA_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: PGA_OFFERS_EMAIL
            description: "TBC"
            quote: true

          - name: PGA_OFFERS_SMS
            description: "TBC"
            quote: true

          - name: PGA_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: ECOMMERCE_OFFERS_EMAIL
            description: "TBC"
            quote: true

          - name: ECOMMERCE_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: ECOMMERCE_OFFERS_SMS
            description: "TBC"
            quote: true

          - name: TENNISTV_OFFERS_EMAIL
            description: "TBC"
            quote: true

          - name: TENNISTV_OFFERS_SMS
            description: "TBC"
            quote: true

          - name: TENNISTV_OFFERS_PUSH
            description: "TBC"
            quote: true

      - name: CURATED__SEGMENT__USER_PREFERENCES_USER_PREFERENCES_CURRENT
        description: "the current model of curated segment user preferences history"
        columns:
          - name: ID
            description: "The unique ID of the identify call itself related directly to Segment data."
            quote: true

          - name: DAZN_ID
            description: "The DAZN User Id of the user. Never NULL."
            quote: true

          - name: LAST_UPDATED_TIMESTAMP
            description: "last event updated timestamp"
            quote: true

          - name: RECEIVED_AT
            description: "The UTC timestamp set by the Segment API when the API receives the payload from client or server."
            quote: true

          - name: UUID_TS
            description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
            quote: true

          - name: ALLOW_MARKETING_EMAILS
            description: "Flag for if the user has allowed Dazn marketing emails."
            quote: true

          - name: ALLOW_NFL_MARKETING_EMAILS
            description: "Flag for if the user has allowed NFL marketing emails."
            quote: true

          - name: PREFERENCES_MULTI_TRACK_AUDIO_LANGUAGE
            description: "TBC"
            quote: true

          - name: PREFERENCES_KEY_MOMENTS_DISABLED
            description: "TBC"
            quote: true

          - name: PREFERENCES_OPTED_IN_THIRTY_DAYS_CANCELLATION
            description: "TBC"
            quote: true

          - name: PREFERENCES_OPTED_OUT_FROM_PERSONALISATION
            description: "gives the preferences opted out from personalisation"
            quote: true

          - name: ALLOW_DAZNBET_MARKETING_EMAILS
            description: "Flag for if the user has allowed dazn bet marketing emails."
            quote: true

          - name: MARKETING_CONSENT_BET_SMS
            description: "Flag for if the user has allowed dazn bet marketing sms."
            quote: true

          - name: MARKETING_CONSENT_BET_PUSH
            description: "TBC"
            quote: true

          - name: NEWS_OFFERS_EMAIL
            description: "Flag for if the user has allowed news marketing emails."
            quote: true

          - name: NEWS_OFFERS_SMS
            description: "Flag for if the user has allowed news marketing sms."
            quote: true

          - name: NEWS_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: DAZN_OFFERS_SMS
            description: "Flag for if the user has allowed dazn marketing sms."
            quote: true

          - name: DAZN_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: PROMOTION_OFFERS_EMAIL
            description: "Flag for if the user has allowed promotion marketing emails."
            quote: true

          - name: PROMOTION_OFFERS_SMS
            description: "Flag for if the user has allowed promotion marketing sms."
            quote: true

          - name: PROMOTION_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: CONTEXT_LIBRARY_VERSION
            description: "TBC"
            quote: true

          - name: CONTEXT_LIBRARY_NAME
            description: "TBC"
            quote: true

          - name: FIBA_OFFERS_EMAIL
            description: "Flag for if the user has allowed fiba marketing emails."
            quote: true

          - name: FIBA_OFFERS_SMS
            description: "Flag for if the user has allowed fiba marketing sms."
            quote: true

          - name: FIBA_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: PGA_OFFERS_EMAIL
            description: "Flag for if the user has allowed pga marketing emails."
            quote: true

          - name: PGA_OFFERS_SMS
            description: "Flag for if the user has allowed pga marketing sms."
            quote: true

          - name: PGA_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: ECOMMERCE_OFFERS_EMAIL
            description: "Flag for if the user has allowed ecommerce marketing emails."
            quote: true

          - name: ECOMMERCE_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: ECOMMERCE_OFFERS_SMS
            description: "Flag for if the user has allowed ecommerce marketing sms."
            quote: true

          - name: EVENT
            description: "The event for which the ingest occurred."
            quote: true

          - name: EVENT_TEXT
            description: "The event for which the ingest occurred."
            quote: true

          - name: ORIGINAL_TIMESTAMP
            description: "TBC"
            quote: true

          - name: SENT_AT
            description: "TBC"
            quote: true

          - name: TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_ID
            description: "TBC"
            quote: true

          - name: RALLY_OFFERS_EMAIL
            description: "Flag for if the user has allowed rally marketing email."
            quote: true

          - name: RALLY_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: RALLY_OFFERS_SMS
            description: "Flag for if the user has allowed rally marketing sms."
            quote: true

          - name: TENNISTV_OFFERS_EMAIL
            description: "TBC"
            quote: true

          - name: TENNISTV_OFFERS_SMS
            description: "TBC"
            quote: true

          - name: TENNISTV_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: SIX_TO_WIN_SMS
            description: "Flag for if the user has allowed six to win marketing sms."
            quote: true

          - name: SIX_TO_WIN_EMAIL
            description: "Flag for if the user has allowed six to win marketing email."
            quote: true

          - name: SIX_TO_WIN_PUSH
            description: "TBC"
            quote: true

          - name: AGE_CONSENT
            description: "gives the age consent of the user"
            quote: true

          - name: record_valid_from_timestamp
            description: "The datetime this data point is valid from (inclusive)"
            quote: true

          - name: record_valid_until_timestamp
            description: "The datetime this data point is valid until (exlusive)"
            quote: true

          - name: DBT_INSERT_DTTS
            description: "TBC"
            quote: true

      - name: CURATED__SEGMENT__USER_PREFERENCES_USER_PREFERENCES__SCD
        description: "The history model of curated segment user preferences history"
        columns:
          - name: DAZN_ID
            description: "The DAZN User Id of the user. Never NULL."
            quote: true

          - name: LAST_UPDATED_TIMESTAMP
            description: "TBC"
            quote: true

          - name: UUID_TS
            description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
            quote: true

          - name: ALLOW_MARKETING_EMAILS
            description: "Flag for if the user has allowed Dazn marketing emails."
            quote: true

          - name: ALLOW_NFL_MARKETING_EMAILS
            description: "Flag for if the user has allowed NFL marketing emails."
            quote: true

          - name: PREFERENCES_MULTI_TRACK_AUDIO_LANGUAGE
            description: "TBC"
            quote: true

          - name: PREFERENCES_KEY_MOMENTS_DISABLED
            description: "TBC"
            quote: true

          - name: PREFERENCES_OPTED_IN_THIRTY_DAYS_CANCELLATION
            description: "TBC"
            quote: true

          - name: PREFERENCES_OPTED_OUT_FROM_PERSONALISATION
            description: "TBC"
            quote: true

          - name: ALLOW_DAZNBET_MARKETING_EMAILS
            description: "TBC"
            quote: true

          - name: MARKETING_CONSENT_BET_SMS
            description: "TBC"
            quote: true

          - name: MARKETING_CONSENT_BET_PUSH
            description: "TBC"
            quote: true

          - name: NEWS_OFFERS_EMAIL
            description: "TBC"
            quote: true

          - name: NEWS_OFFERS_SMS
            description: "TBC"
            quote: true

          - name: NEWS_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: DAZN_OFFERS_SMS
            description: "TBC"
            quote: true

          - name: DAZN_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: PROMOTION_OFFERS_EMAIL
            description: "TBC"
            quote: true

          - name: PROMOTION_OFFERS_SMS
            description: "TBC"
            quote: true

          - name: PROMOTION_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: CONTEXT_LIBRARY_VERSION
            description: "TBC"
            quote: true

          - name: CONTEXT_LIBRARY_NAME
            description: "TBC"
            quote: true

          - name: FIBA_OFFERS_EMAIL
            description: "TBC"
            quote: true

          - name: FIBA_OFFERS_SMS
            description: "TBC"
            quote: true

          - name: FIBA_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: PGA_OFFERS_EMAIL
            description: "TBC"
            quote: true

          - name: PGA_OFFERS_SMS
            description: "TBC"
            quote: true

          - name: PGA_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: TENNISTV_OFFERS_EMAIL
            description: "TBC"
            quote: true

          - name: TENNISTV_OFFERS_SMS
            description: "TBC"
            quote: true

          - name: TENNISTV_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: ECOMMERCE_OFFERS_EMAIL
            description: "TBC"
            quote: true

          - name: ECOMMERCE_OFFERS_PUSH
            description: "TBC"
            quote: true

          - name: ECOMMERCE_OFFERS_SMS
            description: "TBC"
            quote: true

          - name: EVENT
            description: "TBC"
            quote: true

          - name: EVENT_TEXT
            description: "TBC"
            quote: true

          - name: USER_ID
            description: "TBC"
            quote: true

          - name: record_valid_from_timestamp
            description: "TBC"
            quote: true

          - name: record_valid_until_timestamp
            description: "TBC"
            quote: true

          - name: DBT_INSERT_DTTS
            description: "TBC"
            quote: true

      - name: CURATED__SEGMENT__USER_UPDATE_PROFILE_CURRENT
        description: "TBC"
        columns:
          - name: CONTEXT_LIBRARY_VERSION
            description: "TBC"
            quote: true

          - name: DATE_OF_BIRTH
            description: "TBC"
            quote: true

          - name: EVENT
            description: "TBC"
            quote: true

          - name: PROFILE_ID
            description: "TBC"
            quote: true

          - name: RECEIVED_AT
            description: "TBC"
            quote: true

          - name: UPDATED_AT
            description: "TBC"
            quote: true

          - name: UUID_TS
            description: "TBC"
            quote: true

          - name: NICK_NAME
            description: "TBC"
            quote: true

          - name: CONTEXT_LIBRARY_NAME
            description: "TBC"
            quote: true

          - name: EMAIL
            description: "TBC"
            quote: true

          - name: GENDER
            description: "TBC"
            quote: true

          - name: ID
            description: "TBC"
            quote: true

          - name: TIMESTAMP
            description: "TBC"
            quote: true

          - name: USER_ID
            description: "TBC"
            quote: true

          - name: CREATED_AT
            description: "TBC"
            quote: true

          - name: DAZN_ID
            description: "TBC"
            quote: true

          - name: EVENT_TEXT
            description: "TBC"
            quote: true

          - name: MARKETING_PHONE_NUMBER
            description: "TBC"
            quote: true

          - name: ORIGINAL_TIMESTAMP
            description: "TBC"
            quote: true

          - name: SENT_AT
            description: "TBC"
            quote: true

          - name: AGE_RANGE
            description: "TBC"
            quote: true

          - name: PHONE_COUNTRY_CODE
            description: "TBC"
            quote: true

      - name: curated__segment__user_events_play4free_current
        description: "TBC"
        columns:
          - name: USER_ID
            description: "TBC"
            quote: true

          - name: VIEWER_ID
            description: "TBC"
            quote: true

          - name: EVENT_NAME
            description: "TBC"
            quote: true

          - name: COUNTRY
            description: "TBC"
            quote: true

          - name: DEVICE_ID
            description: "TBC"
            quote: true

          - name: LANGUAGE
            description: "TBC"
            quote: true

          - name: CREATED_AT
            description: "TBC"
            quote: true

          - name: UPDATED_AT
            description: "TBC"
            quote: true
