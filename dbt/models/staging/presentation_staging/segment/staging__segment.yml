version: 2

models:
  - name: staging__segment__user_events_identifies_scd
    description: "Simple Slowly Changing Dimension (SCD) table of the Segment User Events IDENTIFIES table to show for all Dazn User IDs the exat timestamp certain traits were valid from and until"
    columns:
      - name: id
        description: "The unique ID of the identify call itself related directly to Segment data."
        quote: true

      - name: dazn_user_id
        description: "The unique ID of the user. Matching the Dazn User Id. E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: dazn_user_id_nulls
        description: "The DAZN User Id of the user. Which is unfortunately sometimes NULL and therefore unusable. E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: zuora_id
        description: "The Zuora Account ID of the user. Can be NULL if the user does not have a subscription at the time of the event. E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: salesforce_id
        description: "The Salesforce Account ID of the user. Often NULL mainly for Partial users, as they haven't made it to Salesforce at the exact time of the User created event. E.g. 0011o00001pVfx9AAC"
        quote: true

      - name: viewer_id
        description: "The Viewer ID of the user. Very rarely, if ever, NULL. E.g. 3cc19ed76c25"
        quote: true

      - name: partner_id
        description: "The ID coming from external Partners (like Docomo or NFL)."
        quote: true

      - name: provider_customer_id
        description: "TBC"
        quote: true

      - name: locale_sid_key
        description: "TBC"
        quote: true

      - name: timestamp
        description: "The UTC-converted timestamp which is set by the Segment library. If you are importing historical events using a server-side library, this is the timestamp you will want to reference in your queries."
        quote: true

      - name: original_timestamp
        description: "The original timestamp set by the Segment library at the time the event is created. Generally, this timestamp should be ignored in favor of the timestamp column."
        quote: true

      - name: received_at_timestamp
        description: "The UTC timestamp set by the Segment API when the API receives the payload from client or server. This does not ensure chronology of events. For queries based on event chronology, timestamp should be used."
        quote: true

      - name: sent_at_timestamp
        description: "The UTC timestamp set by library when the Segment API call was sent. This timestamp can be affected by device clock skew."
        quote: true

      - name: uuid_timestamp
        description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
        quote: true

      - name: first_name
        description: "The first name of the user. Masked as is PII."
        quote: true

      - name: last_name
        description: "The last name of the user. Masked as is PII."
        quote: true

      - name: email
        description: "The email of the user. Masked as is PII."
        quote: true

      - name: ip_address
        description: "The IP Address of the user. Masked as is PII."
        quote: true

      - name: home_country_code
        description: "The two-letter Country Code of the user. E.g. GB, IT, DE, ..."
        quote: true

      - name: language
        description: "The language of the user. E.g. en, es, it, ..."
        quote: true

      - name: language_locale_key
        description: "The language locale of the user. E.g. en_US, pt_BR, de, it, ..."
        quote: true

      - name: source_type
        description: "The source type of the user (E.g. NFL)"
        quote: true

      - name: user_type
        description: "The system type of the user. E.g. Dazn, Dazn2, Docomo, ..."
        quote: true

      - name: nfl_user
        description: "The type of NFL user (E.g. New/Existing), NULL if Existing"
        quote: true

      - name: is_nfl_authenticated_email
        description: "Flag for if the user has authenticated their email on the NFL platform"
        quote: true

      - name: product_status_dazn
        description: "The product status for the user on the DAZN platform"
        quote: true

      - name: product_status_nfl
        description: "The product status for the user on the NFL platform"
        quote: true

      - name: status
        description: "Currently always NULL."
        quote: true

      - name: my_account_status
        description: "Currently always NULL."
        quote: true

      - name: subscription_status
        description: "Subscription Status of the user, probably coming dircetly from Salesforce"
        quote: true

      - name: subscription_cancelled
        description: "TBC"
        quote: true

      - name: subcription_pause_from
        description: "TBC"
        quote: true

      - name: latest_giftcode_expires_at_timestamp
        description: "TBC"
        quote: true

      - name: is_validated_dazn_email
        description: "Flag for if the user has verified their email on the DAZN platform"
        quote: true

      - name: is_resuming
        description: "TBC"
        quote: true

      - name: set_payment_method
        description: "TBC"
        quote: true

      - name: user_account_created_event_timestamp
        description: "TBC"
        quote: true

      - name: user_account_update_ingested_event_timestamp
        description: "TBC"
        quote: true

      - name: user_account_migrated_event_timestamp
        description: "TBC"
        quote: true

      - name: user_status_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: sign_up_details_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: my_dazn_details_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: marketing_flags_updated_timestamp
        description: "TBC"
        quote: true

      - name: home_country_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: track_client_status_dazn_bet
        description: "TBC"
        quote: true

      - name: has_allowed_third_party
        description: "TBC"
        quote: true

      - name: has_allowed_dazn_marketing_emails
        description: "Flag for if the user has allowed Dazn marketing emails. Will shortly be deprecated for the User Preferences events."
        quote: true

      - name: has_allowed_nfl_marketing_emails
        description: "Flag for if the Canadian NFL users have allowed marketing emails. Will shortly be deprecated for the User Preferences events."
        quote: true

      - name: context_library_version
        description: "TBC"
        quote: true

      - name: context_library_name
        description: "TBC"
        quote: true

      - name: record_valid_from_timestamp
        description: "The datetime this data point is valid from (inclusive)"
        quote: true

      - name: record_valid_until_timestamp
        description: "The datetime this data point is valid until (exlusive)"
        quote: true

      - name: event_type
        description: "TBC"
        quote: true

      - name: domain_name
        description: "TBC"
        quote: true

  - name: staging__segment__user_preferences_identifies_scd
    description: "Simple Slowly Changing Dimension (SCD) table of the Segment User Preferences IDENTIFIES table to show for all Dazn User IDs the exat timestamp certain traits were valid from and until"
    columns:
      - name: id
        description: "The unique ID of the identify call itself related directly to Segment data."
        quote: true

      - name: dazn_user_id
        description: "The unique ID of the user. Matching the Dazn User Id. E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: dazn_user_id_nulls
        description: "The DAZN User Id of the user. Which is unfortunately sometimes NULL and therefore unusable. E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: timestamp
        description: "The UTC-converted timestamp which is set by the Segment library. If you are importing historical events using a server-side library, this is the timestamp you will want to reference in your queries."
        quote: true

      - name: original_timestamp
        description: "The original timestamp set by the Segment library at the time the event is created. Generally, this timestamp should be ignored in favor of the timestamp column."
        quote: true

      - name: last_updated_timestamp
        description: "TBC"
        quote: true

      - name: sent_at_timestamp
        description: "The UTC timestamp set by the Segment API when the API receives the payload from client or server. This does not ensure chronology of events. For queries based on event chronology, timestamp should be used."
        quote: true

      - name: received_at_timestamp
        description: "The UTC timestamp set by library when the Segment API call was sent. This timestamp can be affected by device clock skew."
        quote: true

      - name: uuid_timestamp
        description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
        quote: true

      - name: has_allowed_dazn_marketing_emails
        description: "Flag for if the user has allowed Dazn marketing emails."
        quote: true

      - name: has_allowed_nfl_marketing_emails
        description: "Flag for if the Canadian NFL users have allowed marketing emails."
        quote: true

      - name: preferences_multi_track_audio_language
        description: "TBC"
        quote: true

      - name: preferences_key_moments_disabled
        description: "TBC"
        quote: true

      - name: preferences_opted_in_thirty_days_cancellation
        description: "TBC"
        quote: true

      - name: preferences_opted_out_from_personalisation
        description: "TBC"
        quote: true

      - name: allow_daznbet_marketing_emails
        description: "TBC"
        quote: true

      - name: marketing_consent_bet_sms
        description: "TBC"
        quote: true

      - name: marketing_consent_bet_push
        description: "TBC"
        quote: true

      - name: news_offers_email
        description: "TBC"
        quote: true

      - name: news_offers_sms
        description: "TBC"
        quote: true

      - name: news_offers_push
        description: "TBC"
        quote: true

      - name: dazn_offers_sms
        description: "TBC"
        quote: true

      - name: dazn_offers_push
        description: "TBC"
        quote: true

      - name: promotion_offers_email
        description: "TBC"
        quote: true

      - name: promotion_offers_sms
        description: "TBC"
        quote: true

      - name: promotion_offers_push
        description: "TBC"
        quote: true

      - name: context_library_version
        description: "TBC"
        quote: true

      - name: context_library_name
        description: "TBC"
        quote: true

      - name: record_valid_from_timestamp
        description: "The datetime this data point is valid from (inclusive)"
        quote: true

      - name: record_valid_until_timestamp
        description: "The datetime this data point is valid until (exlusive)"
        quote: true

  - name: staging__segment__user_events_identifies
    description: ""
    columns:
      - name: id
        description: "The unique ID of the identify call itself related directly to Segment data."
        quote: true

      - name: dazn_user_id
        description: "The unique ID of the user. Matching the Dazn User Id. E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: dazn_user_id_nulls
        description: "The DAZN User Id of the user. Which is unfortunately sometimes NULL and therefore unusable. E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: zuora_id
        description: "The Zuora Account ID of the user. Can be NULL if the user does not have a subscription at the time of the event. E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: salesforce_id
        description: "The Salesforce Account ID of the user. Often NULL mainly for Partial users, as they haven't made it to Salesforce at the exact time of the User created event. E.g. 0011o00001pVfx9AAC"
        quote: true

      - name: viewer_id
        description: "The Viewer ID of the user. Very rarely, if ever, NULL. E.g. 3cc19ed76c25"
        quote: true

      - name: partner_id
        description: "The ID coming from external Partners (like Docomo or NFL)."
        quote: true

      - name: provider_customer_id
        description: "TBC"
        quote: true

      - name: locale_sid_key
        description: "TBC"
        quote: true

      - name: timestamp
        description: "The UTC-converted timestamp which is set by the Segment library. If you are importing historical events using a server-side library, this is the timestamp you will want to reference in your queries."
        quote: true

      - name: original_timestamp
        description: "The original timestamp set by the Segment library at the time the event is created. Generally, this timestamp should be ignored in favor of the timestamp column."
        quote: true

      - name: received_at_timestamp
        description: "The UTC timestamp set by the Segment API when the API receives the payload from client or server. This does not ensure chronology of events. For queries based on event chronology, timestamp should be used."
        quote: true

      - name: sent_at_timestamp
        description: "The UTC timestamp set by library when the Segment API call was sent. This timestamp can be affected by device clock skew."
        quote: true

      - name: uuid_timestamp
        description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
        quote: true

      - name: first_name
        description: "The first name of the user. Masked as is PII."
        quote: true

      - name: last_name
        description: "The last name of the user. Masked as is PII."
        quote: true

      - name: email
        description: "The email of the user. Masked as is PII."
        quote: true

      - name: ip_address
        description: "The IP Address of the user. Masked as is PII."
        quote: true

      - name: home_country_code
        description: "The two-letter Country Code of the user. E.g. GB, IT, DE, ..."
        quote: true

      - name: language
        description: "The language of the user. E.g. en, es, it, ..."
        quote: true

      - name: language_locale_key
        description: "The language locale of the user. E.g. en_US, pt_BR, de, it, ..."
        quote: true

      - name: source_type
        description: "The source type of the user (E.g. NFL)"
        quote: true

      - name: user_type
        description: "The system type of the user. E.g. Dazn, Dazn2, Docomo, ..."
        quote: true

      - name: nfl_user
        description: "The type of NFL user (E.g. New/Existing), NULL if Existing"
        quote: true

      - name: is_nfl_authenticated_email
        description: "Flag for if the user has authenticated their email on the NFL platform"
        quote: true

      - name: product_status_dazn
        description: "The product status for the user on the DAZN platform"
        quote: true

      - name: product_status_nfl
        description: "The product status for the user on the NFL platform"
        quote: true

      - name: status
        description: "Currently always NULL."
        quote: true

      - name: my_account_status
        description: "Currently always NULL."
        quote: true

      - name: subscription_status
        description: "Subscription Status of the user, probably coming dircetly from Salesforce"
        quote: true

      - name: subscription_cancelled
        description: "TBC"
        quote: true

      - name: subcription_pause_from
        description: "TBC"
        quote: true

      - name: latest_giftcode_expires_at_timestamp
        description: "TBC"
        quote: true

      - name: is_validated_dazn_email
        description: "Flag for if the user has verified their email on the DAZN platform"
        quote: true

      - name: is_resuming
        description: "TBC"
        quote: true

      - name: set_payment_method
        description: "TBC"
        quote: true

      - name: user_account_created_event_timestamp
        description: "TBC"
        quote: true

      - name: user_account_update_ingested_event_timestamp
        description: "TBC"
        quote: true

      - name: user_account_migrated_event_timestamp
        description: "TBC"
        quote: true

      - name: user_status_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: sign_up_details_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: my_dazn_details_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: marketing_flags_updated_timestamp
        description: "TBC"
        quote: true

      - name: home_country_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: track_client_status_dazn_bet
        description: "TBC"
        quote: true

      - name: has_allowed_third_party
        description: "TBC"
        quote: true

      - name: has_allowed_dazn_marketing_emails
        description: "Flag for if the user has allowed Dazn marketing emails. Will shortly be deprecated for the User Preferences events."
        quote: true

      - name: has_allowed_nfl_marketing_emails
        description: "Flag for if the Canadian NFL users have allowed marketing emails. Will shortly be deprecated for the User Preferences events."
        quote: true

      - name: context_library_version
        description: "TBC"
        quote: true

      - name: context_library_name
        description: "TBC"
        quote: true

      - name: event_type
        description: "TBC"
        quote: true

      - name: domain_name
        description: "TBC"
        quote: true

  - name: staging__segment__user_events_identifies_current
    description: ""
    columns:
      - name: id
        description: "The unique ID of the identify call itself related directly to Segment data."
        quote: true

      - name: dazn_user_id
        description: "The unique ID of the user. Matching the Dazn User Id. E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: dazn_user_id_nulls
        description: "The DAZN User Id of the user. Which is unfortunately sometimes NULL and therefore unusable. E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: zuora_id
        description: "The Zuora Account ID of the user. Can be NULL if the user does not have a subscription at the time of the event. E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: salesforce_id
        description: "The Salesforce Account ID of the user. Often NULL mainly for Partial users, as they haven't made it to Salesforce at the exact time of the User created event. E.g. 0011o00001pVfx9AAC"
        quote: true

      - name: viewer_id
        description: "The Viewer ID of the user. Very rarely, if ever, NULL. E.g. 3cc19ed76c25"
        quote: true

      - name: partner_id
        description: "The ID coming from external Partners (like Docomo or NFL)."
        quote: true

      - name: provider_customer_id
        description: "TBC"
        quote: true

      - name: locale_sid_key
        description: "TBC"
        quote: true

      - name: timestamp
        description: "The UTC-converted timestamp which is set by the Segment library. If you are importing historical events using a server-side library, this is the timestamp you will want to reference in your queries."
        quote: true

      - name: original_timestamp
        description: "The original timestamp set by the Segment library at the time the event is created. Generally, this timestamp should be ignored in favor of the timestamp column."
        quote: true

      - name: received_at_timestamp
        description: "The UTC timestamp set by the Segment API when the API receives the payload from client or server. This does not ensure chronology of events. For queries based on event chronology, timestamp should be used."
        quote: true

      - name: sent_at_timestamp
        description: "The UTC timestamp set by library when the Segment API call was sent. This timestamp can be affected by device clock skew."
        quote: true

      - name: uuid_timestamp
        description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
        quote: true

      - name: first_name
        description: "The first name of the user. Masked as is PII."
        quote: true

      - name: last_name
        description: "The last name of the user. Masked as is PII."
        quote: true

      - name: email
        description: "The email of the user. Masked as is PII."
        quote: true

      - name: ip_address
        description: "The IP Address of the user. Masked as is PII."
        quote: true

      - name: home_country_code
        description: "The two-letter Country Code of the user. E.g. GB, IT, DE, ..."
        quote: true

      - name: language
        description: "The language of the user. E.g. en, es, it, ..."
        quote: true

      - name: language_locale_key
        description: "The language locale of the user. E.g. en_US, pt_BR, de, it, ..."
        quote: true

      - name: source_type
        description: "The source type of the user (E.g. NFL)"
        quote: true

      - name: user_type
        description: "The system type of the user. E.g. Dazn, Dazn2, Docomo, ..."
        quote: true

      - name: nfl_user
        description: "The type of NFL user (E.g. New/Existing), NULL if Existing"
        quote: true

      - name: is_nfl_authenticated_email
        description: "Flag for if the user has authenticated their email on the NFL platform"
        quote: true

      - name: product_status_dazn
        description: "The product status for the user on the DAZN platform"
        quote: true

      - name: product_status_nfl
        description: "The product status for the user on the NFL platform"
        quote: true

      - name: status
        description: "Currently always NULL."
        quote: true

      - name: my_account_status
        description: "Currently always NULL."
        quote: true

      - name: subscription_status
        description: "Subscription Status of the user, probably coming dircetly from Salesforce"
        quote: true

      - name: subscription_cancelled
        description: "TBC"
        quote: true

      - name: subcription_pause_from
        description: "TBC"
        quote: true

      - name: latest_giftcode_expires_at_timestamp
        description: "TBC"
        quote: true

      - name: is_validated_dazn_email
        description: "Flag for if the user has verified their email on the DAZN platform"
        quote: true

      - name: is_resuming
        description: "TBC"
        quote: true

      - name: set_payment_method
        description: "TBC"
        quote: true

      - name: user_account_created_event_timestamp
        description: "TBC"
        quote: true

      - name: user_account_update_ingested_event_timestamp
        description: "TBC"
        quote: true

      - name: user_account_migrated_event_timestamp
        description: "TBC"
        quote: true

      - name: user_status_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: sign_up_details_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: my_dazn_details_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: marketing_flags_updated_timestamp
        description: "TBC"
        quote: true

      - name: home_country_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: track_client_status_dazn_bet
        description: "TBC"
        quote: true

      - name: has_allowed_third_party
        description: "TBC"
        quote: true

      - name: has_allowed_dazn_marketing_emails
        description: "Flag for if the user has allowed Dazn marketing emails. Will shortly be deprecated for the User Preferences events."
        quote: true

      - name: has_allowed_nfl_marketing_emails
        description: "Flag for if the Canadian NFL users have allowed marketing emails. Will shortly be deprecated for the User Preferences events."
        quote: true

      - name: context_library_version
        description: "TBC"
        quote: true

      - name: context_library_name
        description: "TBC"
        quote: true

      - name: event_type
        description: "TBC"
        quote: true

      - name: domain_name
        description: "TBC"
        quote: true

  - name: staging__segment__user_events_users
    description: ""
    columns:
      - name: id
        description: "The unique ID of the identify call itself related directly to Segment data."
        quote: true

      - name: dazn_user_id
        description: "The unique ID of the user. Matching the Dazn User Id. E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: zuora_id
        description: "The Zuora Account ID of the user. Can be NULL if the user does not have a subscription at the time of the event. E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: salesforce_id
        description: "The Salesforce Account ID of the user. Often NULL mainly for Partial users, as they haven't made it to Salesforce at the exact time of the User created event. E.g. 0011o00001pVfx9AAC"
        quote: true

      - name: viewer_id
        description: "The Viewer ID of the user. Very rarely, if ever, NULL. E.g. 3cc19ed76c25"
        quote: true

      - name: partner_id
        description: "The ID coming from external Partners (like Docomo or NFL)."
        quote: true

      - name: provider_customer_id
        description: "TBC"
        quote: true

      - name: locale_sid_key
        description: "TBC"
        quote: true

      - name: received_at_timestamp
        description: "The UTC timestamp set by the Segment API when the API receives the payload from client or server. This does not ensure chronology of events. For queries based on event chronology, timestamp should be used."
        quote: true

      - name: uuid_timestamp
        description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
        quote: true

      - name: first_name
        description: "The first name of the user. Masked as is PII."
        quote: true

      - name: last_name
        description: "The last name of the user. Masked as is PII."
        quote: true

      - name: email
        description: "The email of the user. Masked as is PII."
        quote: true

      - name: ip_address
        description: "The IP Address of the user. Masked as is PII."
        quote: true

      - name: home_country_code
        description: "The two-letter Country Code of the user. E.g. GB, IT, DE, ..."
        quote: true

      - name: language
        description: "The language of the user. E.g. en, es, it, ..."
        quote: true

      - name: language_locale_key
        description: "The language locale of the user. E.g. en_US, pt_BR, de, it, ..."
        quote: true

      - name: source_type
        description: "The source type of the user (E.g. NFL)"
        quote: true

      - name: user_type
        description: "The system type of the user. E.g. Dazn, Dazn2, Docomo, ..."
        quote: true

      - name: is_nfl_authenticated_email
        description: "Flag for if the user has authenticated their email on the NFL platform"
        quote: true

      - name: product_status_dazn
        description: "The product status for the user on the DAZN platform"
        quote: true

      - name: product_status_nfl
        description: "The product status for the user on the NFL platform"
        quote: true

      - name: product_status_liga_segunda
        description: "The product status for the user on the LIGA SEGUNDA platform"
        quote: true

      - name: product_status_fiba
        description: "The product status for the user on the FIBA platform"
        quote: true

      - name: product_status_pga
        description: "The product status for the user on the PGA platform"
        quote: true

      - name: product_status_rally_tv
        description: "The product status for the user on the RallyTV platform"
        quote: true

      - name: product_status_tennis_tv
        description: "The product status for the user on the TennisTV platform"
        quote: true

      - name: status
        description: "Currently always NULL."
        quote: true

      - name: my_account_status
        description: "Currently always NULL."
        quote: true

      - name: subscription_status
        description: "Subscription Status of the user, probably coming dircetly from Salesforce"
        quote: true

      - name: subscription_cancelled
        description: "TBC"
        quote: true

      - name: subcription_pause_from
        description: "TBC"
        quote: true

      - name: latest_giftcode_expires_at_timestamp
        description: "TBC"
        quote: true

      - name: is_validated_dazn_email
        description: "Flag for if the user has verified their email on the DAZN platform"
        quote: true

      - name: is_resuming
        description: "TBC"
        quote: true

      - name: set_payment_method
        description: "TBC"
        quote: true

      - name: user_account_created_event_timestamp
        description: "TBC"
        quote: true

      - name: user_account_update_ingested_event_timestamp
        description: "TBC"
        quote: true

      - name: user_account_migrated_event_timestamp
        description: "TBC"
        quote: true

      - name: user_status_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: sign_up_details_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: my_dazn_details_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: marketing_flags_updated_timestamp
        description: "TBC"
        quote: true

      - name: home_country_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: track_client_status_dazn_bet
        description: "TBC"
        quote: true

      - name: has_allowed_third_party
        description: "TBC"
        quote: true

      - name: has_allowed_dazn_marketing_emails
        description: "Flag for if the user has allowed Dazn marketing emails. Will shortly be deprecated for the User Preferences events."
        quote: true

      - name: has_allowed_nfl_marketing_emails
        description: "Flag for if the Canadian NFL users have allowed marketing emails. Will shortly be deprecated for the User Preferences events."
        quote: true

      - name: context_library_version
        description: "TBC"
        quote: true

      - name: context_library_name
        description: "TBC"
        quote: true

  - name: staging__segment__new_user_events_user_account_created
    description: "tbc"
    columns:
      - name: dazn_user_id
        description: "tbc"
        quote: true

      - name: account_created_date
        description: "tbc"
        quote: true

  - name: staging__segment__user_events_identifies_v1
    description: ""
    columns:
      - name: meta_dbt_insert_dtts
        description: " "
        quote: true

      - name: id
        description: "The unique ID of the identify call itself related directly to Segment data."
        quote: true

      - name: dazn_user_id
        description: "The unique ID of the user. Matching the Dazn User Id. E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: dazn_user_id_nulls
        description: "The DAZN User Id of the user. Which is unfortunately sometimes NULL and therefore unusable. E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: zuora_id
        description: "The Zuora Account ID of the user. Can be NULL if the user does not have a subscription at the time of the event. E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: salesforce_id
        description: "The Salesforce Account ID of the user. Often NULL mainly for Partial users, as they haven't made it to Salesforce at the exact time of the User created event. E.g. 0011o00001pVfx9AAC"
        quote: true

      - name: viewer_id
        description: "The Viewer ID of the user. Very rarely, if ever, NULL. E.g. 3cc19ed76c25"
        quote: true

      - name: partner_id
        description: "The ID coming from external Partners (like Docomo or NFL)."
        quote: true

      - name: provider_customer_id
        description: "TBC"
        quote: true

      - name: locale_sid_key
        description: "TBC"
        quote: true

      - name: timestamp
        description: "The UTC-converted timestamp which is set by the Segment library. If you are importing historical events using a server-side library, this is the timestamp you will want to reference in your queries."
        quote: true

      - name: original_timestamp
        description: "The original timestamp set by the Segment library at the time the event is created. Generally, this timestamp should be ignored in favor of the timestamp column."
        quote: true

      - name: received_at_timestamp
        description: "The UTC timestamp set by the Segment API when the API receives the payload from client or server. This does not ensure chronology of events. For queries based on event chronology, timestamp should be used."
        quote: true

      - name: sent_at_timestamp
        description: "The UTC timestamp set by library when the Segment API call was sent. This timestamp can be affected by device clock skew."
        quote: true

      - name: uuid_timestamp
        description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
        quote: true

      - name: first_name
        description: "The first name of the user. Masked as is PII."
        quote: true

      - name: last_name
        description: "The last name of the user. Masked as is PII."
        quote: true

      - name: email
        description: "The email of the user. Masked as is PII."
        quote: true

      - name: ip_address
        description: "The IP Address of the user. Masked as is PII."
        quote: true

      - name: home_country_code
        description: "The two-letter Country Code of the user. E.g. GB, IT, DE, ..."
        quote: true

      - name: language
        description: "The language of the user. E.g. en, es, it, ..."
        quote: true

      - name: language_locale_key
        description: "The language locale of the user. E.g. en_US, pt_BR, de, it, ..."
        quote: true

      - name: source_type
        description: "The source type of the user (E.g. NFL)"
        quote: true

      - name: user_type
        description: "The system type of the user. E.g. Dazn, Dazn2, Docomo, ..."
        quote: true

      - name: nfl_user
        description: "The type of NFL user (E.g. New/Existing), NULL if Existing"
        quote: true

      - name: is_nfl_authenticated_email
        description: "Flag for if the user has authenticated their email on the NFL platform"
        quote: true

      - name: product_status_dazn
        description: "The product status for the user on the DAZN platform"
        quote: true

      - name: product_status_nfl
        description: "The product status for the user on the NFL platform"
        quote: true

      - name: status
        description: "Currently always NULL."
        quote: true

      - name: my_account_status
        description: "Currently always NULL."
        quote: true

      - name: subscription_status
        description: "Subscription Status of the user, probably coming dircetly from Salesforce"
        quote: true

      - name: subscription_cancelled
        description: "TBC"
        quote: true

      - name: subcription_pause_from
        description: "TBC"
        quote: true

      - name: latest_giftcode_expires_at_timestamp
        description: "TBC"
        quote: true

      - name: is_validated_dazn_email
        description: "Flag for if the user has verified their email on the DAZN platform"
        quote: true

      - name: is_resuming
        description: "TBC"
        quote: true

      - name: set_payment_method
        description: "TBC"
        quote: true

      - name: user_account_created_event_timestamp
        description: "TBC"
        quote: true

      - name: user_account_update_ingested_event_timestamp
        description: "TBC"
        quote: true

      - name: user_account_migrated_event_timestamp
        description: "TBC"
        quote: true

      - name: user_status_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: sign_up_details_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: my_dazn_details_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: marketing_flags_updated_timestamp
        description: "TBC"
        quote: true

      - name: home_country_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: track_client_status_dazn_bet
        description: "TBC"
        quote: true

      - name: has_allowed_third_party
        description: "TBC"
        quote: true

      - name: has_allowed_dazn_marketing_emails
        description: "Flag for if the user has allowed Dazn marketing emails. Will shortly be deprecated for the User Preferences events."
        quote: true

      - name: has_allowed_nfl_marketing_emails
        description: "Flag for if the Canadian NFL users have allowed marketing emails. Will shortly be deprecated for the User Preferences events."
        quote: true

      - name: context_library_version
        description: "TBC"
        quote: true

      - name: context_library_name
        description: "TBC"
        quote: true

      - name: src_event_type
        description: "TBC"
        quote: true

  - name: staging__segment__user_events_identifies_scd_v1
    description: "Simple Slowly Changing Dimension (SCD) table of the Segment User Events IDENTIFIES table to show for all Dazn User IDs the exat timestamp certain traits were valid from and until"
    columns:
      - name: meta_dbt_insert_dtts
        description: " "
        quote: true

      - name: id
        description: "The unique ID of the identify call itself related directly to Segment data."
        quote: true

      - name: dazn_user_id
        description: "The unique ID of the user. Matching the Dazn User Id. E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: dazn_user_id_nulls
        description: "The DAZN User Id of the user. Which is unfortunately sometimes NULL and therefore unusable. E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: zuora_id
        description: "The Zuora Account ID of the user. Can be NULL if the user does not have a subscription at the time of the event. E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: salesforce_id
        description: "The Salesforce Account ID of the user. Often NULL mainly for Partial users, as they haven't made it to Salesforce at the exact time of the User created event. E.g. 0011o00001pVfx9AAC"
        quote: true

      - name: viewer_id
        description: "The Viewer ID of the user. Very rarely, if ever, NULL. E.g. 3cc19ed76c25"
        quote: true

      - name: partner_id
        description: "The ID coming from external Partners (like Docomo or NFL)."
        quote: true

      - name: provider_customer_id
        description: "TBC"
        quote: true

      - name: locale_sid_key
        description: "TBC"
        quote: true

      - name: timestamp
        description: "The UTC-converted timestamp which is set by the Segment library. If you are importing historical events using a server-side library, this is the timestamp you will want to reference in your queries."
        quote: true

      - name: original_timestamp
        description: "The original timestamp set by the Segment library at the time the event is created. Generally, this timestamp should be ignored in favor of the timestamp column."
        quote: true

      - name: received_at_timestamp
        description: "The UTC timestamp set by the Segment API when the API receives the payload from client or server. This does not ensure chronology of events. For queries based on event chronology, timestamp should be used."
        quote: true

      - name: sent_at_timestamp
        description: "The UTC timestamp set by library when the Segment API call was sent. This timestamp can be affected by device clock skew."
        quote: true

      - name: uuid_timestamp
        description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
        quote: true

      - name: first_name
        description: "The first name of the user. Masked as is PII."
        quote: true

      - name: last_name
        description: "The last name of the user. Masked as is PII."
        quote: true

      - name: email
        description: "The email of the user. Masked as is PII."
        quote: true

      - name: ip_address
        description: "The IP Address of the user. Masked as is PII."
        quote: true

      - name: language
        description: "The language of the user. E.g. en, es, it, ..."
        quote: true

      - name: language_locale_key
        description: "The language locale of the user. E.g. en_US, pt_BR, de, it, ..."
        quote: true

      - name: source_type
        description: "The source type of the user (E.g. NFL)"
        quote: true

      - name: user_type
        description: "The system type of the user. E.g. Dazn, Dazn2, Docomo, ..."
        quote: true

      - name: product_status_dazn
        description: "The product status for the user on the DAZN platform"
        quote: true

      - name: product_status_nfl
        description: "The product status for the user on the NFL platform"
        quote: true

      - name: status
        description: "Currently always NULL."
        quote: true

      - name: my_account_status
        description: "Currently always NULL."
        quote: true

      - name: subscription_status
        description: "Subscription Status of the user, probably coming dircetly from Salesforce"
        quote: true

      - name: subscription_cancelled
        description: "TBC"
        quote: true

      - name: subcription_pause_from
        description: "TBC"
        quote: true

      - name: latest_giftcode_expires_at_timestamp
        description: "TBC"
        quote: true

      - name: is_validated_dazn_email
        description: "Flag for if the user has verified their email on the DAZN platform"
        quote: true

      - name: nfl_user
        description: "TBC"
        quote: true

      - name: is_nfl_authenticated_email
        description: "TBC"
        quote: true

      - name: is_resuming
        description: "TBC"
        quote: true

      - name: set_payment_method
        description: "TBC"
        quote: true

      - name: user_account_created_event_timestamp
        description: "TBC"
        quote: true

      - name: user_account_update_ingested_event_timestamp
        description: "TBC"
        quote: true

      - name: user_account_migrated_event_timestamp
        description: "TBC"
        quote: true

      - name: user_status_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: sign_up_details_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: my_dazn_details_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: marketing_flags_updated_timestamp
        description: "TBC"
        quote: true

      - name: home_country_updated_event_timestamp
        description: "TBC"
        quote: true

      - name: track_client_status_dazn_bet
        description: "TBC"
        quote: true

      - name: has_allowed_third_party
        description: "TBC"
        quote: true

      - name: has_allowed_dazn_marketing_emails
        description: "Flag for if the user has allowed Dazn marketing emails. Will shortly be deprecated for the User Preferences events."
        quote: true

      - name: has_allowed_nfl_marketing_emails
        description: "Flag for if the Canadian NFL users have allowed marketing emails. Will shortly be deprecated for the User Preferences events."
        quote: true

      - name: context_library_version
        description: "TBC"
        quote: true

      - name: context_library_name
        description: "TBC"
        quote: true

      - name: src_event_type
        description: "TBC"
        quote: true

      - name: record_valid_from_timestamp
        description: "The datetime this data point is valid from (inclusive)"
        quote: true

      - name: record_valid_until_timestamp
        description: "The datetime this data point is valid until (exlusive)"
        quote: true

      - name: record_valid_from_date
        description: " "
        quote: true

  - name: staging__segment__user_events_identifies_home_country_scd_v1
    description: "Simple Slowly Changing Dimension (SCD) table of the Segment User Events IDENTIFIES table to show for all Dazn User IDs the exat timestamp certain traits were valid from and until"
    columns:
      - name: dazn_user_id
        description: "The unique ID of the user. Matching the Dazn User Id. E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: original_timestamp
        description: "The original timestamp set by the Segment library at the time the event is created. Generally, this timestamp should be ignored in favor of the timestamp column."
        quote: true

      - name: home_country_code
        description: "The two-letter Country Code of the user. E.g. GB, IT, DE, ..."
        quote: true

      - name: record_valid_from_timestamp
        description: "The datetime this data point is valid from (inclusive)"
        quote: true

      - name: record_valid_until_timestamp
        description: "The datetime this data point is valid until (exlusive)"
        quote: true

  - name: staging__segment__user_events_identifies_nfl_auth
    description: "Simple Slowly Changing Dimension (SCD) table of the Segment User Events IDENTIFIES table to show for all Dazn User IDs the exat timestamp certain traits were valid from and until"
    columns:
      - name: dazn_user_id
        description: "The unique ID of the user. Matching the Dazn User Id. E.g. 8cfdcea4-7b3b-4894-9c99-3cc19ed76c25"
        quote: true

      - name: nfl_authenticated_at
        description: " "
        quote: true

      - name: nfl_user
        description: "The type of NFL user (E.g. New/Existing), NULL if Existing"
        quote: true

      - name: is_nfl_authenticated_email
        description: "Flag for if the user has authenticated their email on the NFL platform"
        quote: true

  - name: staging__segment__user_preferences_users
    description: "USERS table from Segment data of the User Preferences, containing a log of all events with some top-level traits of the users at the time of the event"
    columns:
      - name: id
        description: "The unique ID of the identify call itself related directly to Segment data."
        quote: true

      - name: dazn_user_id
        description: "The DAZN User Id of the user. Never NULL."
        quote: true

      - name: last_updated_timestamp
        description: "TBC"
        quote: true

      - name: received_at
        description: "The UTC timestamp set by the Segment API when the API receives the payload from client or server."
        quote: true

      - name: uuid_ts
        description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
        quote: true

      - name: allow_marketing_emails
        description: "Flag for if the user has allowed Dazn marketing emails."
        quote: true

      - name: allow_nfl_marketing_emails
        description: "Flag for if the user has allowed NFL marketing emails."
        quote: true

      - name: preferences_multi_track_audio_language
        description: "TBC"
        quote: true

      - name: preferences_key_moments_disabled
        description: "TBC"
        quote: true

      - name: preferences_opted_in_thirty_days_cancellation
        description: "TBC"
        quote: true

      - name: preferences_opted_out_from_personalisation
        description: "TBC"
        quote: true

      - name: allow_daznbet_marketing_emails
        description: "TBC"
        quote: true

      - name: marketing_consent_bet_sms
        description: "TBC"
        quote: true

      - name: marketing_consent_bet_push
        description: "TBC"
        quote: true

      - name: news_offers_email
        description: "TBC"
        quote: true

      - name: news_offers_sms
        description: "TBC"
        quote: true

      - name: news_offers_push
        description: "TBC"
        quote: true

      - name: dazn_offers_sms
        description: "TBC"
        quote: true

      - name: dazn_offers_push
        description: "TBC"
        quote: true

      - name: promotion_offers_email
        description: "TBC"
        quote: true

      - name: promotion_offers_sms
        description: "TBC"
        quote: true

      - name: promotion_offers_push
        description: "TBC"
        quote: true

      - name: context_library_version
        description: "TBC"
        quote: true

      - name: context_library_name
        description: "TBC"
        quote: true

      - name: age_consent
        description: "TBC"
        quote: true

      - name: six_to_win_push
        description: "TBC"
        quote: true

      - name: six_to_win_email
        description: "TBC"
        quote: true

      - name: six_to_win_sms
        description: "TBC"
        quote: true

      - name: fiba_offers_email
        description: "TBC"
        quote: true

      - name: fiba_offers_sms
        description: "TBC"
        quote: true

      - name: fiba_offers_push
        description: "TBC"
        quote: true

      - name: pga_offers_email
        description: "TBC"
        quote: true

      - name: pga_offers_sms
        description: "TBC"
        quote: true

      - name: pga_offers_push
        description: "TBC"
        quote: true

      - name: ecommerce_offers_email
        description: "TBC"
        quote: true

      - name: ecommerce_offers_push
        description: "TBC"
        quote: true

      - name: ecommerce_offers_sms
        description: "TBC"
        quote: true

      - name: tennis_tv_offers_email
        description: "TBC"
        quote: true

      - name: tennis_tv_offers_sms
        description: "TBC"
        quote: true

      - name: tennis_tv_offers_push
        description: "TBC"
        quote: true

  - name: staging__segment__user_preferences_user_preferences_current
    description: "the current model of segment user preferences history"
    columns:
      - name: dazn_user_id
        description: "The DAZN User Id of the user. Never NULL."
        quote: true

      - name: user_id
        description: "TBC"
        quote: true

      - name: last_updated_timestamp
        description: "TBC"
        quote: true

      - name: received_at
        description: "The UTC timestamp set by the Segment API when the API receives the payload from client or server."
        quote: true

      - name: uuid_ts
        description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
        quote: true

      - name: allow_marketing_emails
        description: "Flag for if the user has allowed Dazn marketing emails."
        quote: true

      - name: allow_nfl_marketing_emails
        description: "Flag for if the user has allowed NFL marketing emails."
        quote: true

      - name: preferences_multi_track_audio_language
        description: "TBC"
        quote: true

      - name: preferences_key_moments_disabled
        description: "TBC"
        quote: true

      - name: preferences_opted_in_thirty_days_cancellation
        description: "TBC"
        quote: true

      - name: preferences_opted_out_from_personalisation
        description: "gives the preferences which are opted out from personalisation"
        quote: true

      - name: event
        description: "gives the event for the ingest"
        quote: true

      - name: event_text
        description: "gives the event for the ingest"
        quote: true

      - name: allow_daznbet_marketing_emails
        description: "Flag for if the user has allowed dazn bet marketing emails."
        quote: true

      - name: marketing_consent_bet_sms
        description: "Flag for if the user has allowed dazn bet marketing sms."
        quote: true

      - name: marketing_consent_bet_push
        description: "TBC"
        quote: true

      - name: news_offers_email
        description: "Flag for if the user has allowed news marketing emails."
        quote: true

      - name: news_offers_sms
        description: "Flag for if the user has allowed news offers sms."
        quote: true

      - name: news_offers_push
        description: "TBC"
        quote: true

      - name: dazn_offers_sms
        description: "Flag for if the user has allowed dazn offers sms."
        quote: true

      - name: dazn_offers_push
        description: ""
        quote: true

      - name: promotion_offers_email
        description: "Flag for if the user has allowed promotion offers email."
        quote: true

      - name: promotion_offers_sms
        description: "Flag for if the user has allowed promotion offers sms."
        quote: true

      - name: promotion_offers_push
        description: "TBC"
        quote: true

      - name: context_library_version
        description: "TBC"
        quote: true

      - name: context_library_name
        description: "TBC"
        quote: true

      - name: fiba_offers_email
        description: "Flag for if the user has allowed fiba offers email."
        quote: true

      - name: fiba_offers_sms
        description: "Flag for if the user has allowed fiba offers sms."
        quote: true

      - name: fiba_offers_push
        description: "TBC"
        quote: true

      - name: pga_offers_email
        description: "Flag for if the user has allowed pga offers email."
        quote: true

      - name: pga_offers_sms
        description: "Flag for if the user has allowed pga offers sms."
        quote: true

      - name: pga_offers_push
        description: "TBC"
        quote: true

      - name: ecommerce_offers_email
        description: "Flag for if the user has allowed ecommerce offers email."
        quote: true

      - name: ecommerce_offers_push
        description: "TBC"
        quote: true

      - name: ecommerce_offers_sms
        description: "Flag for if the user has allowed ecommerce offers sms."
        quote: true

      - name: rally_offers_email
        description: "Flag for if the user has allowed rally offers email."
        quote: true

      - name: rally_offers_push
        description: ""
        quote: true

      - name: rally_offers_sms
        description: "Flag for if the user has allowed rally offers sms."
        quote: true

      - name: tennis_tv_offers_email
        description: "TBC"
        quote: true

      - name: tennis_tv_offers_sms
        description: "TBC"
        quote: true

      - name: tennis_tv_offers_push
        description: "TBC"
        quote: true

      - name: six_to_win_sms
        description: "Flag for if the user has allowed six to win offers sms."
        quote: true

      - name: six_to_win_email
        description: "Flag for if the user has allowed six to win offers email."
        quote: true

      - name: six_to_win_push
        description: "TBC"
        quote: true

      - name: age_consent
        description: "gives the age consent of the user"
        quote: true

      - name: dbt_insert_dtts
        description: "The timestamp at which recorded is updated to dbt"
        quote: true

  - name: staging__segment__user_preferences_user_preferences__scd
    description: "The history model of curated segment user preferences history"
    columns:
      - name: dazn_user_id
        description: "The DAZN User Id of the user. Never NULL."
        quote: true

      - name: last_updated_timestamp
        description: "last event updated timestamp"
        quote: true

      - name: uuid_ts
        description: "Used to keep track of when the specific event was last processed by the Segment connector, specifically for deduping and debugging purposes. You can generally ignore this column."
        quote: true

      - name: allow_marketing_emails
        description: "Flag for if the user has allowed Dazn marketing emails."
        quote: true

      - name: allow_nfl_marketing_emails
        description: "Flag for if the user has allowed NFL marketing emails."
        quote: true

      - name: preferences_multi_track_audio_language
        description: "TBC"
        quote: true

      - name: preferences_key_moments_disabled
        description: "TBC"
        quote: true

      - name: preferences_opted_in_thirty_days_cancellation
        description: "TBC"
        quote: true

      - name: preferences_opted_out_from_personalisation
        description: "gives whether the preference was opted out from personalisation or not."
        quote: true

      - name: allow_daznbet_marketing_emails
        description: "Flag for if the user has allowed Dazn bet marketing emails."
        quote: true

      - name: marketing_consent_bet_sms
        description: "Flag for if the user has allowed dazn bet marketing sms."
        quote: true

      - name: marketing_consent_bet_push
        description: "TBC"
        quote: true

      - name: news_offers_email
        description: "Flag for if the user has allowed news marketing emails."
        quote: true

      - name: news_offers_sms
        description: "Flag for if the user has allowed news marketing sms."
        quote: true

      - name: news_offers_push
        description: "TBC"
        quote: true

      - name: dazn_offers_sms
        description: "Flag for if the user has allowed Dazn offers emails."
        quote: true

      - name: dazn_offers_push
        description: "TBC"
        quote: true

      - name: promotion_offers_email
        description: "Flag for if the user has allowed promotion offers marketing emails."
        quote: true

      - name: promotion_offers_sms
        description: "Flag for if the user has allowed promotion offers marketing emails."
        quote: true

      - name: promotion_offers_push
        description: "TBC"
        quote: true

      - name: context_library_version
        description: "TBC"
        quote: true

      - name: context_library_name
        description: "TBC"
        quote: true

      - name: fiba_offers_email
        description: "Flag for if the user has allowed fiba marketing emails."
        quote: true

      - name: fiba_offers_sms
        description: "Flag for if the user has allowed fiba marketing sms."
        quote: true

      - name: fiba_offers_push
        description: "TBC"
        quote: true

      - name: pga_offers_email
        description: "Flag for if the user has allowed pga marketing emails."
        quote: true

      - name: pga_offers_sms
        description: "Flag for if the user has allowed pga marketing sms."
        quote: true

      - name: pga_offers_push
        description: "TBC"
        quote: true

      - name: rally_offers_email
        description: "Flag for if the user has allowed rally offers email."
        quote: true

      - name: rally_offers_push
        description: ""
        quote: true

      - name: rally_offers_sms
        description: "Flag for if the user has allowed rally offers sms."
        quote: true

      - name: tennis_tv_offers_email
        description: "TBC"
        quote: true

      - name: tennis_tv_offers_sms
        description: "TBC"
        quote: true

      - name: tennis_tv_offers_push
        description: "TBC"
        quote: true

      - name: ecommerce_offers_email
        description: "Flag for if the user has allowed ecommerce marketing emails."
        quote: true

      - name: ecommerce_offers_push
        description: "TBC"
        quote: true

      - name: ecommerce_offers_sms
        description: "Flag for if the user has allowed ecommerce marketing sms."
        quote: true

      - name: event
        description: "The event for which the ingest occurred."
        quote: true

      - name: event_text
        description: "The event for which the ingest occurred."
        quote: true

      - name: user_id
        description: "The user id of the customer"
        quote: true

      - name: record_valid_from_timestamp
        description: "The datetime this data point is valid from (inclusive)"
        quote: true

      - name: record_valid_until_timestamp
        description: "The datetime this data point is valid until (exlusive)"
        quote: true

      - name: dbt_insert_dtts
        description: "The timestamp at which recorded is updated to dbt"
        quote: true

  - name: staging__segment__user_preferences_user_update_profile_current
    description: "TBC"
    columns:
      - name: context_library_version
        description: "TBC"
        quote: true

      - name: date_of_birth
        description: "TBC"
        quote: true

      - name: event
        description: "TBC"
        quote: true

      - name: profile_id
        description: "TBC"
        quote: true

      - name: received_at
        description: "TBC"
        quote: true

      - name: updated_at
        description: "TBC"
        quote: true

      - name: uuid_ts
        description: "TBC"
        quote: true

      - name: nick_name
        description: "TBC"
        quote: true

      - name: context_library_name
        description: "TBC"
        quote: true

      - name: email
        description: "TBC"
        quote: true

      - name: gender
        description: "TBC"
        quote: true

      - name: id
        description: "TBC"
        quote: true

      - name: timestamp
        description: "TBC"
        quote: true

      - name: user_id
        description: "TBC"
        quote: true

      - name: created_at
        description: "TBC"
        quote: true

      - name: dazn_id
        description: "TBC"
        quote: true

      - name: event_text
        description: "TBC"
        quote: true

      - name: marketing_phone_number
        description: "TBC"
        quote: true

      - name: original_timestamp
        description: "TBC"
        quote: true

      - name: sent_at
        description: "TBC"
        quote: true

      - name: age_range
        description: "TBC"
        quote: true

      - name: phone_country_code
        description: "TBC"
        quote: true
