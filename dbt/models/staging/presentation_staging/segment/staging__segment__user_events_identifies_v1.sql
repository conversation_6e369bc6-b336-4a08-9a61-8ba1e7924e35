WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__SEGMENT__USER_EVENTS_IDENTIFIES_V1') }}
)

SELECT
    "ID" AS "id"
    ,"USER_ID" AS "dazn_user_id"
    ,"DAZN_ID" AS "dazn_user_id_nulls"
    ,"ZUORA_ID" AS "zuora_id"
    ,"SALESFORCE_ID" AS "salesforce_id"
    ,"VIEWER_ID" AS "viewer_id"
    ,"PARTNER_ID" AS "partner_id"
    ,"PROVIDER_CUSTOMER_ID" AS "provider_customer_id"
    ,"LOCALE_SID_KEY" AS "locale_sid_key"
    ,"TIMESTAMP" AS "timestamp"
    ,"OR<PERSON>IN<PERSON>_TIMESTAMP" AS "original_timestamp"
    ,"RECEIVED_AT" AS "received_at_timestamp"
    ,"SENT_AT" AS "sent_at_timestamp"
    ,"UUID_TS" AS "uuid_timestamp"
    ,"FIRST_NAME" AS "first_name"
    ,"LAST_NAME" AS "last_name"
    ,"EMAIL" AS "email"
    ,"IP_ADDRESS" AS "ip_address"
    ,IFF("USER_TYPE" = 'Docomo', 'JP', UPPER("HOME_COUNTRY_CODE")) AS "home_country_code"
    ,"LANGUAGE" AS "language"
    ,"LANGUAGE_LOCALE_KEY" AS "language_locale_key"
    ,"SOURCE_TYPE" AS "source_type"
    ,"USER_TYPE" AS "user_type"
    ,FIRST_VALUE("NFL_USER") IGNORE NULLS OVER (PARTITION BY "USER_ID" ORDER BY "TIMESTAMP") AS "nfl_user"
    ,"NFL_AUTHENTICATED" AS "is_nfl_authenticated_email"
    ,"PRODUCT_STATUS_DAZN" AS "product_status_dazn"
    ,"PRODUCT_STATUS_NFL" AS "product_status_nfl"
    ,"STATUS" AS "status"
    ,"MY_ACCOUNT_STATUS" AS "my_account_status"
    ,"SUBSCRIPTION_STATUS" AS "subscription_status"
    ,"SUBSCRIPTION_CANCELLED" AS "subscription_cancelled"
    ,"SUBCRIPTION_PAUSE_FROM" AS "subcription_pause_from"
    ,"LATEST_GIFTCODE_EXPIRES_AT" AS "latest_giftcode_expires_at_timestamp"
    ,"EMAIL_VERIFIED_AT" AS "is_validated_dazn_email"
    ,"IS_RESUMING" AS "is_resuming"
    ,"SET_PAYMENT_METHOD" AS "set_payment_method"
    ,"USER_ACCOUNT_CREATED_EVENT_TIMESTAMP" AS "user_account_created_event_timestamp"
    ,"USER_ACCOUNT_UPDATE_INGESTED_EVENT_TIMESTAMP" AS "user_account_update_ingested_event_timestamp"
    ,"USER_ACCOUNT_MIGRATED_EVENT_TIMESTAMP" AS "user_account_migrated_event_timestamp"
    ,"USER_STATUS_UPDATED_EVENT_TIMESTAMP" AS "user_status_updated_event_timestamp"
    ,"SIGN_UP_DETAILS_UPDATED_EVENT_TIMESTAMP" AS "sign_up_details_updated_event_timestamp"
    ,"MY_DAZN_DETAILS_UPDATED_EVENT_TIMESTAMP" AS "my_dazn_details_updated_event_timestamp"
    ,"MARKETING_FLAGS_UPDATED_TIMESTAMP" AS "marketing_flags_updated_timestamp"
    ,"HOME_COUNTRY_UPDATED_EVENT_TIMESTAMP" AS "home_country_updated_event_timestamp"
    ,"TRACK_CLIENT_STATUS_DAZN_BET" AS "track_client_status_dazn_bet"
    ,"ALLOW_THIRD_PARTY" AS "has_allowed_third_party"
    ,"ALLOW_MARKETING_EMAILS" AS "has_allowed_dazn_marketing_emails"
    ,"ALLOW_NFL_MARKETING_EMAILS" AS "has_allowed_nfl_marketing_emails"
    ,"CONTEXT_LIBRARY_VERSION" AS "context_library_version"
    ,"CONTEXT_LIBRARY_NAME" AS "context_library_name"
    ,"SRC_EVENT_TYPE" AS "src_event_type"
FROM source
WHERE "SRC_EVENT_TYPE" NOT IN ('new_user_device_login','NewUserDeviceLogin')