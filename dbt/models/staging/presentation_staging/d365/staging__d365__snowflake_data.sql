WITH source_curated__d365__snowflake_data AS (
    SELECT * FROM {{ source('LATEST','CURATED__D365__SNOWFLAKE_DATA') }} 
)

, prioritise_dazn_as_data_source AS (
    SELECT DISTINCT
        "IO" AS "io"
        ,"PL" AS "pl"
        ,"CAMPAIGN_NAME" AS "campaign_name"
        ,"BRAND_ACCOUNT_NAME" AS "brand_account_name"
        ,"BRAND" AS "brand"
        ,"BRAND_INDUSTRY" AS "brand_industry"
        ,"BRAND_CATEGORY" AS "brand_category"
        ,"DATA_SOURCE" AS "data_source"
    FROM source_curated__d365__snowflake_data
)

SELECT 
    * 
FROM prioritise_dazn_as_data_source
