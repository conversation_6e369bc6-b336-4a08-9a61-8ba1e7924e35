WITH source AS (
    SELECT * FROM {{ ref('staging__dmp_billing_events') }}
)

,renamed AS (
    SELECT
        "billing_product_id"
        ,"linked_billing_product_id"
        ,"catalog_product_id"
        ,"dazn_id"
        ,"tracking_id_derived"
        ,"entitlement_set_id"
        ,"billing_charge_type"
        ,"billing_country"
        --,"purchase_date"
        ,"event_timestamp"
        ,"source_system_derived"
        ,"device_platform" 
        ,"is_3pp_exit"
        ,"message_type"
        ,"legacy_subscription_name"
        ,"legacy_rateplan_id"
    FROM  source
    WHERE  "event_status"='SUCCEEDED' 
        AND  UPPER("message_type") ='BILLING_PRODUCT_PURCHASE'
        AND  LOWER("product_type")='ppv' 
        AND LOWER("billing_charge_type") = 'one_time'
        AND "business_type"='B2C' 
)

SELECT * FROM renamed
