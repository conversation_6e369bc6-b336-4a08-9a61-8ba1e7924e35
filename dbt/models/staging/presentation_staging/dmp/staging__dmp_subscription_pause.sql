WITH source AS (
    SELECT * FROM {{ ref('staging__dmp_billing_events') }}
)

,renewal AS (
    SELECT * FROM {{ ref('staging__dmp_subscription_renewal') }}
)

,renamed AS (
    SELECT 
        "billing_product_id"
        ,"pause_start_date"
        ,"pause_end_date"
        ,"event_timestamp"
        ,"validity_end_date"
        ,"next_billing_date"
        ,"message_type"
        ,"legacy_subscription_name"
        ,"legacy_rateplan_id"
    FROM source 
    WHERE UPPER("message_type") IN ('BILLING_PRODUCT_PAUSE','BILLING_PRODUCT_CANCEL_SCHEDULED_PAUSE') 
        AND "event_status" IN ('SUCCEEDED','SCHEDULED')
        AND "business_type"='B2C' 
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "billing_product_id" ORDER BY "event_timestamp" DESC) = 1
)

,pause_renewal AS (
    SELECT 
        renamed."billing_product_id"
        ,renamed."pause_start_date" AS "pause_start_date"
        ,IFNULL(renewal."event_timestamp",renamed."pause_end_date") AS "pause_end_date"
        ,renamed."event_timestamp"
        ,renamed."validity_end_date"
        ,renamed."next_billing_date"
        ,renamed."message_type"
        ,renamed."legacy_subscription_name"
        ,renamed."legacy_rateplan_id" 
        FROM renamed
        LEFT JOIN renewal ON renamed."billing_product_id"=renewal."billing_product_id"
)

SELECT * FROM pause_renewal
