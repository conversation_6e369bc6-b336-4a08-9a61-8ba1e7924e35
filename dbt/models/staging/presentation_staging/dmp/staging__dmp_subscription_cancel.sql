WITH source AS (
    SELECT * FROM {{ ref('staging__dmp_billing_events') }}
)

,renamed AS (
    SELECT 
        "billing_product_id"
        ,"event_timestamp"
        ,"validity_end_date"
        ,CASE WHEN UPPER("message_type") ='BILLING_PRODUCT_KEEP' THEN NULL ELSE IFNULL("churn_type",'VOLUNTARY') END AS "churn_type"
        ,"event_status"
        ,"product_status"
        ,"effective_cancellation_date"
        ,"message_type"
        ,"legacy_subscription_name"
        ,"legacy_rateplan_id"
    FROM source
    WHERE TRUE
        AND UPPER("message_type") IN ('BILLING_PRODUCT_CANCELATION','BILLING_PRODUCT_KEEP') 
        AND "event_status" IN ('SUCCEEDED','SCHEDULED')  
        AND "business_type"='B2C' 
        AND LOWER("product_type")  IN ('subscription','pass') 
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "billing_product_id" ORDER BY "event_timestamp" DESC) = 1
)

SELECT * FROM renamed
