{{
    config(
        materialized='table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_S_WH',
    )
}}

WITH dmp_payload AS (
    SELECT  
        "billing_product_id" AS "billing_product_id"
        ,f.value:promotionId::VARCHAR AS "promotion_id"
        ,f.value:name::VARCHAR AS "name"
        ,f.value:legacyPromotionId::VARCHAR AS "legacy_promotion_id"
        ,f.value:couponCampaign::VARCHAR AS "coupon_campaign"
        ,f.value:couponCode::VARCHAR AS "coupon_code"
        ,f.value:startDate::TIMESTAMP AS "start_timestamp"
        ,f.value:endDate::TIMESTAMP AS "end_timestamp"
        ,f.value:type::VARCHAR AS "type"
        ,f.value:isActive::BOOLEAN AS "is_active"
        ,f.value:promotionAmount::FLOAT AS "promotion_amount"
        ,f.value:promotionPercentage::FLOAT AS "promotion_percentage"
        ,f.value:discount::FLOAT AS  "discount"
        ,f.value:promotionalPrice::FLOAT AS "promotional_price"
        ,"billing_mode" AS "billing_mode"
        ,"partner_product_id" AS "partner_product_id"
        ,"event_status" AS "event_status"
        ,"message_type" AS "message_type"
        ,"catalog_product_name" AS "catalog_product_name"
        ,"dazn_id" AS "dazn_id"
        ,"legacy_subscription_name" AS "legacy_subscription_name"
        ,"legacy_rateplan_id" AS "legacy_rateplan_id"
        ,"product_group" AS "product_group"
        ,"event_timestamp" AS "event_timestamp"
        ,f.value AS "promo_payload"
    FROM {{ ref('staging__dmp_billing_events') }}
    ,LATERAL FLATTEN(input => "promo_payload" ) f
    WHERE "business_type"='B2C'
    AND "message_type" IN ('BILLING_PRODUCT_PURCHASE','BILLING_PRODUCT_RENEWAL','BILLING_PRODUCT_CHANGE')

UNION ALL

 SELECT  
        "billing_product_id" AS "billing_product_id"
        ,'' AS "promotion_id"
        ,'' AS "name"
        ,'' AS "legacy_promotion_id"
        ,"partner_product_id" AS "coupon_campaign"
        ,IFNULL("partner_giftcode","partner_product_id"||'--'||"dazn_id") AS "coupon_code"
        ,"validity_start_date" AS "start_timestamp"
        ,'9999-12-31' AS "end_timestamp"
        ,'PERCENTAGE_PARTNER' AS "type"
        ,True AS "is_active"
        ,0 AS "promotion_amount"
        ,100 AS "promotion_percentage"
        ,NULL AS  "discount"
        ,NULL AS "promotional_price"
        ,"billing_mode" AS "billing_mode"
        ,"partner_product_id" AS "partner_product_id"
        ,"event_status" AS "event_status"
        ,"message_type" AS "message_type"
        ,"catalog_product_name" AS "catalog_product_name"
        ,"dazn_id" AS "dazn_id"
        ,"legacy_subscription_name" AS "legacy_subscription_name"
        ,"legacy_rateplan_id" AS "legacy_rateplan_id"
        ,"product_group" AS "product_group"
        ,"event_timestamp" AS "event_timestamp"
        ,"promo_payload"
    FROM {{ ref('staging__dmp_billing_events') }}
    WHERE "business_type"='B2C' AND UPPER("billing_mode")='PARTNER'
    AND "event_status"='SUCCEEDED' AND LOWER("product_type")='subscription'
    AND "message_type" IN ('BILLING_PRODUCT_PURCHASE','BILLING_PRODUCT_RENEWAL','BILLING_PRODUCT_CHANGE')

)

,rateplancharge_current AS (
    SELECT * FROM {{ ref('staging__zuora__rateplancharge_current') }}
    --WHERE "rateplan_charge_charge_model" = 'Discount-Percentage'
)

,dmp_promotion AS (
    SELECT * FROM {{ ref('staging__dmp_user_promotion') }}

)

,rateplan_current AS (
    SELECT * FROM {{ ref('staging__zuora__rateplan_current') }}
)

,final AS (
    SELECT 
    dmp_payload."billing_product_id" AS "billing_product_id"
    ,dmp_payload."promotion_id" AS "promotion_id"
    ,dmp_payload."name" AS "name"
    ,dmp_payload."legacy_promotion_id" AS "legacy_promotion_id"
    ,IFNULL("rateplan_charge_post_sign_up_giftcode_campaign_name",dmp_payload."coupon_campaign")  AS "coupon_campaign"
    ,IFNULL("rateplan_charge_post_sign_up_giftcode",dmp_payload."coupon_code") AS "coupon_code"
    ,dmp_payload."start_timestamp"::DATE AS "start_date"
    ,dmp_payload."end_timestamp"::DATE AS "end_date"
    ,dmp_payload."start_timestamp"
    ,dmp_payload."end_timestamp"
    ,dmp_payload."type" AS "type"
    ,dmp_payload."is_active" AS "is_active"
    ,dmp_payload."promotion_amount"
    ,dmp_payload."promotion_percentage"
    ,dmp_payload."discount" AS "discount"
    ,dmp_payload."promotional_price" AS "promotional_price"
    ,dmp_payload."event_status" AS "event_status"
    ,dmp_payload."message_type" AS "message_type"
    ,dmp_payload."catalog_product_name" AS "catalog_product_name"
    ,dmp_payload."dazn_id" AS "dazn_id"
    ,dmp_payload."legacy_subscription_name" AS "legacy_subscription_name"
    ,dmp_payload."legacy_rateplan_id" AS "legacy_rateplan_id"
    ,dmp_payload."product_group" AS "product_group"
    ,IFNULL(rateplan_current."rateplan_context",dmp_promotion."channel") AS "channel"
    ,IFNULL(rateplan_current."rateplan_segment",dmp_promotion."segment_id") AS "segment_id"
    ,dmp_payload."promo_payload" AS "promo_payload"
    ,dmp_payload."event_timestamp" AS "event_timestamp"
    FROM dmp_payload
    LEFT JOIN rateplancharge_current 
        ON rateplancharge_current."rateplan_id"=dmp_payload."legacy_promotion_id"
    LEFT JOIN rateplan_current 
        ON rateplan_current."rateplan_id"=dmp_payload."legacy_promotion_id"   
    LEFT JOIN dmp_promotion 
        ON dmp_promotion."dazn_id"=dmp_payload."dazn_id"  
        AND dmp_promotion."promotion_id"=dmp_payload."promotion_id" 
)

SELECT * FROM final
WHERE "promotion_percentage">=0 AND "promotion_percentage"<=100
