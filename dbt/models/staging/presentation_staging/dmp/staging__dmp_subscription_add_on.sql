WITH source AS (
    SELECT * FROM {{ ref('staging__dmp_billing_events') }}
)

,renamed AS (
    SELECT
        "billing_product_id"
        ,"linked_billing_product_id"
        ,"catalog_product_id"
        ,"entitlement_set_id"
        ,"service_provider_user_id"
        ,"validity_start_date"
        ,"validity_end_date"
        ,"purchase_date"
        ,"event_timestamp"
        ,"source_system_derived"
        ,"tracking_id_derived"
        ,"service_provider"
        ,"next_billing_date"
        ,"billing_charge_type"
        ,"product_group"
        ,"product_type"
        ,"cancel_reason"
        ,"message_type"
        ,"legacy_subscription_name"
        ,"legacy_rateplan_id"
    FROM source
    WHERE TRUE
        AND LOWER("product_type")='addon' 
        AND "event_status"='SUCCEEDED'
        AND "business_type"='B2C' 
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "billing_product_id","product_group","catalog_product_id" ORDER BY "event_timestamp" DESC) = 1
)

SELECT * FROM renamed
