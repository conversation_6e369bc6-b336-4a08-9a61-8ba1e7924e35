version: 2

models:

  - name: staging__account_current
    description: ""
    columns:
      - name: billing_account_id
        description: ""
        quote: true

      - name: crm_account_id
        description: ""
        quote: true

      - name: dazn_user_id
        description: ""
        quote: true

      - name: billing_account_created_timestamp
        description: ""
        quote: true

  - name: staging__dmp_billing_account
    description: ""
    columns:
      - name: business_type
        description: ""
        quote: true

      - name: message_type
        description: ""
        quote: true

      - name: event_id
        description: ""
        quote: true

      - name: event_timestamp
        description: ""
        quote: true

      - name: dazn_id
        description: ""
        quote: true

      - name: billing_mode
        description: ""
        quote: true

      - name: event_status
        description: ""
        quote: true

      - name: country
        description: ""
        quote: true

      - name: state
        description: ""
        quote: true

      - name: first_name
        description: ""
        quote: true

      - name: lastname
        description: ""
        quote: true

      - name: triggered_by
        description: ""
        quote: true

      - name: payload
        description: ""
        quote: true

  - name: staging__dmp_billing_events
    description: ""
    columns:
      - name: message_type
        description: ""
        quote: true

      - name: event_id
        description: ""
        quote: true

      - name: event_timestamp
        description: ""
        quote: true

      - name: event_status
        description: ""
        quote: true

      - name: dazn_id
        description: ""
        quote: true

      - name: billing_mode
        description: ""
        quote: true

      - name: dcb_provider_name
        description: ""
        quote: true

      - name: system_source_derived
        description: ""
        quote: true

      - name: tracking_id_derived
        description: ""
        quote: true

      - name: is_3pp_exit
        description: ""
        quote: true

      - name: partner_product_id
        description: ""
        quote: true

      - name: device_manufacturer
        description: ""
        quote: true

      - name: device_platform
        description: ""
        quote: true

      - name: product_group
        description: ""
        quote: true

      - name: product_type
        description: ""
        quote: true

      - name: business_type
        description: ""
        quote: true

      - name: billing_country
        description: ""
        quote: true

      - name: dazn_order_id
        description: ""
        quote: true

      - name: billing_product_id
        description: ""
        quote: true

      - name: parent_billing_product_id
        description: ""
        quote: true

      - name: linked_billing_product_id
        description: ""
        quote: true

      - name: catelog_product_id
        description: ""
        quote: true

      - name: catelog_product_name
        description: ""
        quote: true

      - name: service_provider
        description: ""
        quote: true

      - name: service_provider_user_id
        description: ""
        quote: true

      - name: legacy_subscription_name
        description: ""
        quote: true

      - name: legacy_oli_id
        description: ""
        quote: true

      - name: legacy_rateplan_id
        description: ""
        quote: true

      - name: validity_period_unit
        description: ""
        quote: true

      - name: validity_period_duration
        description: ""
        quote: true

      - name: validity_start_date
        description: ""
        quote: true

      - name: validity_end_date
        description: ""
        quote: true

      - name: next_billing_date
        description: ""
        quote: true

      - name: purchase_date
        description: ""
        quote: true

      - name: renewed_date
        description: ""
        quote: true

      - name: effective_cancellation_date
        description: ""
        quote: true

      - name: cancel_reason
        description: ""
        quote: true

      - name: churn_type
        description: ""
        quote: true

      - name: product_change_type
        description: ""
        quote: true

      - name: is_seasonal
        description: ""
        quote: true

      - name: is_paid_by_installments
        description: ""
        quote: true

      - name: no_of_installments
        description: ""
        quote: true

      - name: paid_installments
        description: ""
        quote: true

      - name: is_recontract
        description: ""
        quote: true

      - name: is_free_trail
        description: ""
        quote: true

      - name: free_trial_payload
        description: ""
        quote: true

      - name: billing_charge_type
        description: ""
        quote: true

      - name: currency
        description: ""
        quote: true

      - name: catalog_price
        description: ""
        quote: true

      - name: installment_amount
        description: ""
        quote: true

      - name: gross_price
        description: ""
        quote: true

      - name: charge_amount
        description: ""
        quote: true

      - name: etf_amount
        description: ""
        quote: true

      - name: refund_amount
        description: ""
        quote: true

      - name: refund_type
        description: ""
        quote: true

      - name: refund_reason
        description: ""
        quote: true

      - name: triggered_by
        description: ""
        quote: true

      - name: invoice_id
        description: ""
        quote: true

      - name: legacy_invoice_id
        description: ""
        quote: true

      - name: original_invoice_id
        description: ""
        quote: true

      - name: invoice_item_period_start_date
        description: ""
        quote: true

      - name: invoice_item_period_end_date
        description: ""
        quote: true

      - name: psp_reference
        description: ""
        quote: true

      - name: created_by
        description: ""
        quote: true

      - name: payment_method_id
        description: ""
        quote: true

      - name: payment_failed
        description: ""
        quote: true

      - name: payment_failure_reason
        description: ""
        quote: true

      - name: payment_failure_attempt
        description: ""
        quote: true

      - name: payment_payload
        description: ""
        quote: true

      - name: entitlement_set_id
        description: ""
        quote: true

      - name: product_status
        description: ""
        quote: true

      - name: pause_start_date
        description: ""
        quote: true

      - name: pause_end_date
        description: ""
        quote: true

      - name: channel
        description: ""
        quote: true

      - name: segment_id
        description: ""
        quote: true

      - name: promo_payLoad
        description: ""
        quote: true

      - name: retention_offer_payLoad
        description: ""
        quote: true

      - name: deviceDetails
        description: ""
        quote: true

      - name: vendor_order_id
        description: ""
        quote: true

      - name: correlation_id
        description: ""
        quote: true

      - name: payload
        description: ""
        quote: true

  - name: staging__dmp_billing_ppv
    description: ""
    columns:
      - name: billing_product_id
        description: ""
        quote: true

      - name: linked_billing_product_id
        description: ""
        quote: true

      - name: catalog_product_id
        description: ""
        quote: true

      - name: dazn_id
        description: ""
        quote: true

      - name: tracking_id_derived
        description: ""
        quote: true

      - name: entitlement_set_id
        description: ""
        quote: true

      - name: billing_charge_type
        description: ""
        quote: true

      - name: billing_country
        description: ""
        quote: true

      - name: purchase_date
        description: ""
        quote: true

      - name: event_timestamp
        description: ""
        quote: true

      - name: source_system_derived
        description: ""
        quote: true

      - name: device_platform
        description: ""
        quote: true

      - name: is_3pp_exit
        description: ""
        quote: true

      - name: legacy_subscription_name
        description: ""
        quote: true

      - name: legacy_rateplan_id
        description: ""
        quote: true

  - name: staging__dmp_subscription_add_on
    description: ""
    columns:
      - name: billing_product_id
        description: ""
        quote: true

      - name: linked_billing_product_id
        description: ""
        quote: true

      - name: catalog_product_id
        description: ""
        quote: true

      - name: entitlement_set_id
        description: ""
        quote: true

      - name: service_provider_user_id
        description: ""
        quote: true

      - name: validity_start_date
        description: ""
        quote: true

      - name: validity_end_date
        description: ""
        quote: true

      - name: purchase_date
        description: ""
        quote: true

      - name: event_timestamp
        description: ""
        quote: true

      - name: source_system_derived
        description: ""
        quote: true

      - name: tracking_id_derived
        description: ""
        quote: true

      - name: service_provider
        description: ""
        quote: true

      - name: next_billing_date
        description: ""
        quote: true

      - name: billing_charge_type
        description: ""
        quote: true

      - name: product_group
        description: ""
        quote: true

      - name: product_type
        description: ""
        quote: true

      - name: cancel_reason
        description: ""
        quote: true

      - name: message_type
        description: ""
        quote: true

      - name: legacy_subscription_name
        description: ""
        quote: true

      - name: legacy_rateplan_id
        description: ""
        quote: true

  - name: staging__dmp_subscription_cancel
    description: ""
    columns:
      - name: billing_product_id
        description: ""
        quote: true

      - name: event_timestamp
        description: ""
        quote: true

      - name: validity_end_date
        description: ""
        quote: true

      - name: churn_type
        description: ""
        quote: true

      - name: event_status
        description: ""
        quote: true

      - name: product_status
        description: ""
        quote: true

      - name: effective_cancellation_date
        description: ""
        quote: true

      - name: legacy_subscription_name
        description: ""
        quote: true

      - name: legacy_rateplan_id
        description: ""
        quote: true

  - name: staging__dmp_subscription_pause
    description: ""
    columns:
      - name: billing_product_id
        description: ""
        quote: true

      - name: pause_start_date
        description: ""
        quote: true

      - name: pause_end_date
        description: ""
        quote: true

      - name: event_timestamp
        description: ""
        quote: true

      - name: validity_end_date
        description: ""
        quote: true

      - name: next_billing_date
        description: ""
        quote: true

      - name: legacy_subscription_name
        description: ""
        quote: true

      - name: legacy_rateplan_id
        description: ""
        quote: true

  - name: staging__dmp_subscription_promotion
    description: ""
    columns:
      - name: billing_product_id
        description: ""
        quote: true

      - name: promotion_id
        description: ""
        quote: true

      - name: name
        description: ""
        quote: true

      - name: legacy_promotion_id
        description: ""
        quote: true

      - name: coupon_campaign
        description: ""
        quote: true

      - name: coupon_code
        description: ""
        quote: true

      - name: start_date
        description: ""
        quote: true

      - name: end_date
        description: ""
        quote: true

      - name: type
        description: ""
        quote: true

      - name: is_active
        description: ""
        quote: true

      - name: promotion_amount
        description: ""
        quote: true

      - name: promotion_percentage
        description: ""
        quote: true

      - name: discount
        description: ""
        quote: true

      - name: promotional_price
        description: ""
        quote: true

      - name: event_status
        description: ""
        quote: true

      - name: message_type
        description: ""
        quote: true

      - name: catalog_product_name
        description: ""
        quote: true

      - name: dazn_id
        description: ""
        quote: true

      - name: legacy_subscription_name
        description: ""
        quote: true

      - name: legacy_rateplan_id
        description: ""
        quote: true

      - name: channel
        description: ""
        quote: true

      - name: segment_id
        description: ""
        quote: true

      - name: promo_payload
        description: ""
        quote: true

  - name: staging__dmp_subscription_renewal
    description: ""
    columns:
      - name: billing_product_id
        description: ""
        quote: true

      - name: event_timestamp
        description: ""
        quote: true

      - name: validity_end_date
        description: ""
        quote: true

      - name: legacy_subscription_name
        description: ""
        quote: true

      - name: legacy_rateplan_id
        description: ""
        quote: true

  - name: staging__dmp_user_promotion
    description: ""
    columns:
      - name: billing_product_id
        description: ""
        quote: true

      - name: billing_mode
        description: ""
        quote: true

      - name: dazn_id
        description: ""
        quote: true

      - name: business_type
        description: ""
        quote: true

      - name: message_type
        description: ""
        quote: true

      - name: event_id
        description: ""
        quote: true

      - name: event_timestamp
        description: ""
        quote: true

      - name: event_status
        description: ""
        quote: true

      - name: promotion_id
        description: ""
        quote: true

      - name: coupon_code
        description: ""
        quote: true

      - name: channel
        description: ""
        quote: true

      - name: segment_id
        description: ""
        quote: true

      - name: payload
        description: ""
        quote: true

  - name: staging__dmp_subscription_mrr
    description: ""
    columns:
      - name: billing_product_id
        description: ""
        quote: true

      - name: validity_start_date
        description: ""
        quote: true

      - name: validity_end_date
        description: ""
        quote: true

      - name: validity_period_unit
        description: ""
        quote: true

      - name: no_of_installments
        description: ""
        quote: true

      - name: is_seasonal
        description: ""
        quote: true

      - name: source_system_derived
        description: ""
        quote: true

      - name: gross_price
        description: ""
        quote: true

      - name: charge_amount
        description: ""
        quote: true

      - name: catalog_price
        description: ""
        quote: true

      - name: catalog_product_id
        description: ""
        quote: true

      - name: is_paid_by_installments
        description: ""
        quote: true

      - name: event_timestamp
        description: ""
        quote: true

      - name: subscription_type
        description: ""
        quote: true

      - name: subscription_monthly_recurring_revenue
        description: ""
        quote: true

      - name: previous_subscription_monthly_recurring_revenue
        description: ""
        quote: true

      - name: discounted_monthly_recurring_revenue
        description: ""
        quote: true

  - name: staging__dmp_subscription_core
    description: ""
    columns:
      - name: META__DBT_INSERT_DTTS
        description: ""
        quote: true

      - name: subscription_id
        description: ""
        quote: true

      - name: subscription_name
        description: ""
        quote: true

      - name: subscription_name_proposed
        description: ""
        quote: true

      - name: subscription_version_new
        description: ""
        quote: true

      - name: subscription_version
        description: ""
        quote: true

      - name: subscription_name_original_created_timestamp
        description: ""
        quote: true

      - name: subscription_id_created_timestamp
        description: ""
        quote: true

      - name: subscription_id_updated_timestamp
        description: ""
        quote: true

      - name: subscription_start_date
        description: ""
        quote: true

      - name: subscription_end_date
        description: ""
        quote: true

      - name: purchase_product_type
        description: ""
        quote: true

      - name: billing_account_id
        description: ""
        quote: true

      - name: subscription_source_system_name
        description: ""
        quote: true

      - name: subscription_tracking_id
        description: ""
        quote: true

      - name: subscription_product_group
        description: ""
        quote: true

      - name: subscription_direct_carrier_billing_carrier_name
        description: ""
        quote: true

      - name: subscription_country
        description: ""
        quote: true

      - name: subscription_territory
        description: ""
        quote: true

      - name: subscription_status
        description: ""
        quote: true

      - name: subscription_product_status
        description: ""
        quote: true

      - name: subscription_validity_period_unit
        description: ""
        quote: true

      - name: subscription_validity_period_duration
        description: ""
        quote: true

      - name: subscription_tier
        description: ""
        quote: true

      - name: dazn_user_id
        description: ""
        quote: true

      - name: subscription_catalog_product_id
        description: ""
        quote: true

      - name: subscription_catalog_product_name
        description: ""
        quote: true

      - name: subscription_free_trial_start_date
        description: ""
        quote: true

      - name: subscription_free_trial_end_date
        description: ""
        quote: true

      - name: has_free_trial
        description: ""
        quote: true

      - name: subscription_free_trail_promo_name
        description: ""
        quote: true

      - name: subscription_free_trail_promo_id
        description: ""
        quote: true

      - name: subscription_platform
        description: ""
        quote: true

      - name: message_type
        description: ""
        quote: true

      - name: purchase_date
        description: ""
        quote: true

      - name: event_timestamp
        description: ""
        quote: true

      - name: subscription_payment_method_id
        description: ""
        quote: true

      - name: subscription_rateplan_charge_charged_through_date
        description: ""
        quote: true

      - name: subscription_bill_cycle_day
        description: ""
        quote: true

      - name: subscription_billing_charge_type
        description: ""
        quote: true

      - name: subscription_cancelled_date
        description: ""
        quote: true

      - name: subscription_effective_cancellation_date
        description: ""
        quote: true

      - name: subscription_rateplan_charge_effective_start_date
        description: ""
        quote: true

      - name: subscription_instalment_period
        description: ""
        quote: true

      - name: purchase_currency
        description: ""
        quote: true

      - name: subscription_catalog_price
        description: ""
        quote: true

      - name: subscription_gross_price
        description: ""
        quote: true

      - name: subscription_charge_amount
        description: ""
        quote: true

      - name: subscription_is_paid_by_installments
        description: ""
        quote: true

      - name: is_seasonal
        description: ""
        quote: true

      - name: rateplan_id
        description: ""
        quote: true

      - name: crm_account_id
        description: ""
        quote: true

      - name: billing_account_created_timestamp
        description: ""
        quote: true

      - name: subscription_churn_type
        description: ""
        quote: true

      - name: subscription_sign_up_campaign_id
        description: ""
        quote: true

      - name: subscription_sign_up_giftcode
        description: ""
        quote: true

      - name: partner_product_id
        description: ""
        quote: true

      - name: billing_mode
        description: ""
        quote: true

      - name: subscription_is_auto_renew
        description: ""
        quote: true

      - name: subscription_term_type
        description: ""
        quote: true

      - name: zr_subscription_tracking_id
        description: ""
        quote: true

      - name: billing_product_id
        description: ""
        quote: true

      - name: subscription_free_trial_start_timestamp
        description: ""
        quote: true

      - name: subscription_free_trial_end_timestamp
        description: ""
        quote: true

  - name: staging__zuora_ev_legacy_orders
    description: ""
    columns:
      - name: ord_prod_id
        description: ""
        quote: true

      - name: legacy_id
        description: ""
        quote: true

      - name: subscription_name
        description: ""
        quote: true

      - name: product_rateplan_id
        description: ""
        quote: true

      - name: rateplan_id
        description: ""
        quote: true

      - name: wh_created_dt
        description: ""
        quote: true

      - name: next_renewal_date
        description: ""
        quote: true

      - name: next_installment_date
        description: ""
        quote: true

      - name: product_type
        description: ""
        quote: true

      - name: subscription_id
        description: ""
        quote: true

      - name: dbt_insert_dtts
        description: ""
        quote: true
