WITH source AS (
    SELECT * FROM {{ ref('staging__dmp_billing_events') }}
   -- WHERE "business_type"='B2C' AND LOWER("product_type") = 'subscription' AND "event_status" = 'SUCCEEDED' 
)


,dmp_requested AS (
    SELECT  
        "billing_product_id"
        ,"validity_start_date"
        ,"validity_end_date"
        ,"validity_period_unit"
        ,"no_of_installments"
        ,"is_seasonal"
        ,"source_system_derived"
        ,"gross_price"
        ,"charge_amount"
        ,"installment_amount"
        ,"catalog_price"
        ,"catalog_product_id"
        ,"is_paid_by_installments"
        ,"event_timestamp"
        ,CASE
            WHEN UPPER("billing_mode")='PARTNER' THEN 'Externally Billed'  
            WHEN "is_seasonal" AND  "no_of_installments" IS NULL THEN 'Season Upfront'
            WHEN "is_seasonal" AND  "no_of_installments" <12 THEN 'Season Instalment'
            WHEN "no_of_installments" =12 THEN 'Instalment'
            WHEN "validity_period_unit" ILIKE '%month%' THEN 'Monthly'
            WHEN "validity_period_unit" ILIKE '%year%' THEN 'Annual'
            WHEN "validity_period_unit" ILIKE '%annual%' THEN 'Annual'
            WHEN "validity_period_unit" ILIKE '%week%' THEN 'Weekly'
            WHEN "validity_period_unit" ILIKE '%day%' AND  "validity_period_duration" IN (7,14) THEN 'Weekly'
            WHEN "validity_period_unit" ILIKE '%day%' THEN 'Days'
            WHEN "validity_period_unit" ILIKE '%lifetime%'THEN 'Lifetime'
            ELSE 'Unknown'
        END AS "subscription_type"
        ,ROUND(CASE 
            WHEN "subscription_type"='Monthly' THEN "gross_price"
            WHEN "subscription_type"='Annual' THEN ("gross_price"/12)
            WHEN "subscription_type"='Instalment' THEN "gross_price"
            WHEN "subscription_type"  IN ('Season Instalment','Season Upfront') 
                THEN ("gross_price"/CASE 
                                        WHEN DATEDIFF('days', "validity_start_date" , "validity_end_date")=0 THEN 1 
                                        ELSE  DATEDIFF('days', "validity_start_date", "validity_end_date") 
                                    END  )*30.41
            --WHEN LOWER("product_type")='pass' THEN ("gross_price"/"validity_period_duration")*30.41
            WHEN LOWER("product_type")='pass' THEN "gross_price"
            WHEN "subscription_type"='Days' THEN 0
            WHEN "subscription_type"='Weekly' THEN 0
            WHEN "subscription_type"='Lifetime' THEN 0
            ELSE 0
        END,5) AS "subscription_monthly_recurring_revenue"
        ,LAG("subscription_monthly_recurring_revenue") OVER (PARTITION BY "billing_product_id" ORDER BY "event_timestamp" ) AS "previous_subscription_monthly_recurring_revenue"
        ,ROUND(CASE 
            WHEN "subscription_type"='Monthly' THEN "charge_amount"
            WHEN "subscription_type"='Annual' THEN ("charge_amount"/12)
            WHEN "subscription_type"='Instalment' THEN "charge_amount"
            WHEN "subscription_type"  IN ('Season Instalment','Season Upfront') 
                THEN ("charge_amount"/CASE 
                                        WHEN DATEDIFF('days', "validity_start_date", "validity_end_date")=0 THEN 1 
                                        ELSE  DATEDIFF('days', "validity_start_date", "validity_end_date")
                                     END )*30.41
            --WHEN LOWER("product_type")='pass' THEN ("charge_amount"/"validity_period_duration")*30.41
            WHEN LOWER("product_type")='pass' THEN "charge_amount"
            WHEN "subscription_type"='Days' THEN 0
            WHEN "subscription_type"='Weekly' THEN 0
            ELSE 0
        END,5) AS "discounted_monthly_recurring_revenue"
    FROM source
    WHERE LOWER("product_type")  IN ('subscription','pass') 
    AND "message_type" != 'BILLING_PRODUCT_CANCELATION'
    AND "event_status" = 'SUCCEEDED' 
    AND "business_type"='B2C'
 )

SELECT * FROM dmp_requested
QUALIFY ROW_NUMBER() OVER (PARTITION BY "billing_product_id" ORDER BY "event_timestamp" DESC)=1
