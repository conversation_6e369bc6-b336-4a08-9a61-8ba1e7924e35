WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__DMP_BILLING_EVENTS') }}
)

,renamed AS (
    SELECT
        message_type AS "message_type"
        ,event_id AS "event_id"
        ,event_timestamp AS "event_timestamp"
        ,event_status AS "event_status"
        ,dazn_id AS "dazn_id"
        ,billing_mode AS "billing_mode"
        ,dcb_provider_name AS "dcb_provider_name"
        ,source_system_derived AS "source_system_derived"
        ,tracking_id_derived AS "tracking_id_derived"
        ,is_3pp_exit AS "is_3pp_exit"
        ,partner_product_id AS "partner_product_id"
        ,partner_giftcode AS  "partner_giftcode"
        ,device_manufacturer AS "device_manufacturer"
        ,device_platform AS "device_platform"
        ,product_group AS "product_group"
        ,product_type AS "product_type"
        ,business_type AS "business_type"
        ,billing_country AS "billing_country"
        ,dazn_order_id AS "dazn_order_id"
        ,billing_product_id AS "billing_product_id"
        ,parent_billing_product_id AS "parent_billing_product_id"
        ,linked_billing_product_id AS "linked_billing_product_id"
        ,catalog_product_id AS "catalog_product_id"
        ,catalog_product_name AS "catalog_product_name"
        ,service_provider AS "service_provider"
        ,service_provider_user_id AS "service_provider_user_id"
        ,legacy_subscription_name AS "legacy_subscription_name"
        ,legacy_oli_id AS "legacy_oli_id"
        ,legacy_rateplan_id AS "legacy_rateplan_id"
        ,validity_period_unit AS "validity_period_unit"
        ,validity_period_duration AS "validity_period_duration"
        ,validity_start_date AS "validity_start_date"
        ,validity_end_date AS "validity_end_date"
        ,next_billing_date AS "next_billing_date"
        ,purchase_date AS "purchase_date"
        ,renewed_date AS "renewed_date"
        ,effective_cancellation_date AS "effective_cancellation_date"
        ,cancel_reason AS "cancel_reason"
        ,churn_type AS "churn_type"
        ,product_change_type AS "product_change_type"
        ,is_seasonal AS "is_seasonal"
        ,is_paid_by_installments AS "is_paid_by_installments"
        ,no_of_installments AS "no_of_installments"
        ,paid_installments AS "paid_installments"
        ,is_recontract AS "is_recontract"
        ,installment_payload AS "installment_payload"
        ,is_free_trial AS "is_free_trial"
        ,free_trial_payload AS "free_trial_payload"
        ,billing_charge_type AS "billing_charge_type"
        ,currency AS "currency"
        ,catalog_price AS "catalog_price"
        ,installment_amount AS "installment_amount"
        ,gross_price AS "gross_price"
        ,charge_amount AS "charge_amount"
        ,etf_amount AS "etf_amount"
        ,refund_amount AS "refund_amount"
        ,refund_type AS "refund_type"
        ,refund_reason AS "refund_reason"
        ,triggered_by AS "triggered_by"
        ,invoice_id AS "invoice_id"
        ,legacy_invoice_id AS "legacy_invoice_id"
        ,original_invoice_id AS "original_invoice_id"
        ,invoice_item_period_start_date AS "invoice_item_period_start_date"
        ,invoice_item_period_end_date AS "invoice_item_period_end_date"
        ,psp_reference AS "psp_reference"
        ,created_by AS "created_by"
        ,payment_method_id AS "payment_method_id"
        ,payment_failed AS "payment_failed"
        ,payment_failure_reason AS "payment_failure_reason"
        ,payment_failure_attempt AS "payment_failure_attempt"
        ,payment_payload AS "payment_payload"
        ,entitlement_set_id AS "entitlement_set_id"
        ,product_status AS "product_status"
        ,pause_start_date AS "pause_start_date"
        ,pause_end_date AS "pause_end_date"
        ,channel AS "channel"
        ,segment_id AS "segment_id"
        ,promo_payload AS "promo_payload"
        ,retention_offer_payload AS "retention_offer_payload"
        ,devicedetails AS "devicedetails"
        ,vendor_order_id AS "vendor_order_id"
        ,correlation_id AS "correlation_id"
        ,payload AS "payload"
    FROM source
)

SELECT * FROM renamed
