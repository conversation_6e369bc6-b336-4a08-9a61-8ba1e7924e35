{{
    config(
        materialized='table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_2XL_WH',
    )
}}

WITH RECURSIVE dmp_subscription_stage AS (
    -- Base case: select the row where ref_id is NULL, this is the root
    SELECT 
        "billing_product_id"
        ,"parent_billing_product_id"
        ,"event_timestamp"
        ,"dazn_id"
        ,"product_type"
        ,"linked_billing_product_id"
        ,"entitlement_set_id"
        ,"validity_period_unit"
        ,"billing_country"
        ,"billing_mode"
        ,"validity_end_date"
        ,"validity_start_date"
        ,"event_timestamp" AS "subscription_name_original_created_timestamp"
        ,"purchase_date"
        ,"product_group"
        ,"message_type"
        ,"catalog_product_id"
        ,"catalog_product_name"
        ,"validity_period_duration"
        ,"is_free_trial"
        ,"is_paid_by_installments"
        ,"is_seasonal"
        ,"catalog_price"
        ,"gross_price"
        ,"charge_amount"
        ,"product_status"
        ,"dcb_provider_name"
        ,"free_trial_payload":startDate::TIMESTAMP AS "subscription_free_trial_start_date"
        ,"free_trial_payload":endDate::TIMESTAMP AS "subscription_free_trial_end_date"
        ,"free_trial_payload":name::VARCHAR AS "subscription_free_trail_promo_name" 
        ,"free_trial_payload":promotionId::VARCHAR AS "subscription_free_trail_promo_id" 
        ,"pause_start_date"
        ,"pause_end_date"
        ,"source_system_derived"
        ,"tracking_id_derived"
        ,"billing_charge_type"
        ,"payment_method_id"
        ,"next_billing_date"
        ,"validity_start_date"::DATE AS "subscription_rateplan_charge_effective_start_date"
        ,"no_of_installments"
        ,"currency"
        ,"legacy_subscription_name"
        ,"legacy_rateplan_id"
        ,"churn_type"
        ,"device_platform"
        ,"partner_product_id"
        ,"billing_product_id" AS "root"
    FROM {{ ref('staging__dmp_billing_events') }}
    WHERE ("parent_billing_product_id" IS NULL 
    AND LOWER("product_type")  IN ('subscription','pass') 
    AND UPPER("message_type") = ('BILLING_PRODUCT_PURCHASE')
    AND "event_status"='SUCCEEDED' 
    AND "business_type"='B2C'
) 

OR 

("legacy_subscription_name" IS NOT NULL AND "event_status"='SUCCEEDED'
AND "parent_billing_product_id" IS NULL AND LOWER("product_type")='subscription'
AND UPPER("message_type") = ('BILLING_PRODUCT_CANCELATION')
)

    UNION ALL

    -- Recursive case: find each row's ancestor chain
    SELECT 
        source."billing_product_id"
        ,source."parent_billing_product_id"
        ,source."event_timestamp"
        ,source."dazn_id"
        ,source."product_type"
        ,source."linked_billing_product_id"
        ,source."entitlement_set_id"
        ,source."validity_period_unit"
        ,source."billing_country"
        ,source."billing_mode"
        ,source."validity_end_date"
        ,dmp_subscription_stage."validity_start_date"
        ,dmp_subscription_stage."subscription_name_original_created_timestamp" 
        ,source."purchase_date"
        ,source."product_group"
        ,source."message_type"
        ,source."catalog_product_id"
        ,source."catalog_product_name"
        ,source."validity_period_duration"
        ,source."is_free_trial"
        ,source."is_paid_by_installments"
        ,source."is_seasonal"
        ,source."catalog_price"
        ,source."gross_price"
        ,source."charge_amount"
        ,source."product_status"
        ,dmp_subscription_stage."dcb_provider_name"
        ,source."free_trial_payload":startDate::TIMESTAMP AS "subscription_free_trial_start_date"
        ,source."free_trial_payload":endDate::TIMESTAMP AS "subscription_free_trial_end_date"
        ,source."free_trial_payload":name::VARCHAR AS "subscription_free_trail_promo_name" 
        ,source."free_trial_payload":promotionId::VARCHAR AS "subscription_free_trail_promo_id" 
        ,source."pause_start_date"
        ,source."pause_end_date"
        ,dmp_subscription_stage."source_system_derived"
        ,dmp_subscription_stage."tracking_id_derived"
        ,source."billing_charge_type"
        ,source."payment_method_id"
        ,source."next_billing_date"
        ,source."validity_start_date"::DATE AS "subscription_rateplan_charge_effective_start_date"
        ,source."no_of_installments"
        ,source."currency"
        ,dmp_subscription_stage."legacy_subscription_name"
        ,source."legacy_rateplan_id"
        ,source."churn_type"
        ,dmp_subscription_stage."device_platform"
        ,dmp_subscription_stage."partner_product_id"
        ,dmp_subscription_stage."root" 
    FROM {{ ref('staging__dmp_billing_events') }} source
    JOIN dmp_subscription_stage ON source."parent_billing_product_id" = dmp_subscription_stage."billing_product_id" 
    WHERE source."event_status"='SUCCEEDED' OR 
    (source."event_status"='FAILED' AND source."parent_billing_product_id" IS NOT NULL 
    AND LOWER(source."product_type") ='subscription' 
    AND UPPER(source."message_type") = ('BILLING_PRODUCT_PURCHASE')
    AND source."business_type"='B2C')
    AND LOWER(source."product_type")='subscription'
    AND source."business_type"='B2C' 
    AND source."churn_type" IS NULL
)

,country_source AS (
    SELECT
        "join_key"
        ,"country"
        ,"territory"
    FROM {{ ref('region_dimension') }}
)

,sub_cancelled as (
    SELECT * FROM {{ ref('staging__dmp_subscription_cancel') }}

)

,sub_renwel as (
    SELECT * FROM {{ ref('staging__dmp_subscription_renewal') }}
)

,rateplan_staging AS (
    SELECT * FROM {{ ref('staging__zuora__rateplan_current') }}
)

,subscription_staging AS (
    SELECT 
        "subscription_name" AS "zu_subscription_name"
        ,"subscription_product_group"
        ,"subscription_name_original_created_timestamp"
        ,"subscription_sign_up_campaign_id"
        ,"subscription_sign_up_giftcode"
        ,"subscription_is_auto_renew"
        ,"subscription_term_type"
        ,"subscription_tracking_id" AS "zr_subscription_tracking_id"
        ,"subscription_version" AS "max_subscription_version"
    FROM {{ ref('staging__zuora__subscription_name_current') }}
    --WHERE "subscription_cancelled_date" IS NULL
)

,account_mapping AS (
    SELECT * FROM {{ ref('staging__account_current') }}

)

,subscription_stage as (
    SELECT * FROM dmp_subscription_stage
    WHERE  "churn_type" IS NULL AND UPPER("message_type") IN ('BILLING_PRODUCT_PURCHASE','BILLING_PRODUCT_CHANGE')
       --need to add renwel and pause and resume as well
)

,final as (
    SELECT 
    CURRENT_TIMESTAMP() AS "META__DBT_INSERT_DTTS"
    ,subscription_stage."billing_product_id" AS "subscription_id"
    ,IFNULL(subscription_stage."legacy_subscription_name",'E-'||subscription_stage."root") AS "subscription_name"
    ,DENSE_RANK() OVER (PARTITION BY "subscription_name",subscription_stage."product_group" ORDER BY subscription_stage."purchase_date",subscription_stage."event_timestamp" ) AS "subscription_version_new"
    ,(IFNULL(subscription_staging."max_subscription_version",0)+"subscription_version_new")::INT AS "subscription_version"
    ,IFNULL(subscription_staging."subscription_name_original_created_timestamp",subscription_stage."subscription_name_original_created_timestamp") AS "subscription_name_original_created_timestamp"
    ,subscription_stage."event_timestamp" AS "subscription_id_created_timestamp"
    -- ,'E-'||HASH("dazn_id","product_group",IFNULL(subscription_staging."subscription_name_original_created_timestamp",subscription_stage."subscription_name_original_created_timestamp")) AS "subscription_name_proposed"
    ,IFNULL(CASE WHEN sub_cancelled."event_status"='SUCCEEDED' OR sub_cancelled."churn_type" IN ('VOLUNTARY','INVOLUNTARY')  THEN sub_cancelled."event_timestamp"
    ELSE NULL END
            ,GREATEST_IGNORE_NULLS(sub_renwel."event_timestamp"
                ,CASE 
                    WHEN LEAD(subscription_stage."event_timestamp") OVER (PARTITION BY "subscription_name",subscription_stage."product_group" ORDER 
     BY subscription_stage."event_timestamp") IS NULL 
                    THEN TIMESTAMPADD('minute',20,subscription_stage."event_timestamp") 
                    ELSE LEAD(subscription_stage."event_timestamp") OVER (PARTITION BY "subscription_name",subscription_stage."product_group" ORDER 
     BY subscription_stage."event_timestamp")
                END)) AS "subscription_id_updated_timestamp"
    ,subscription_stage."validity_start_date" AS "subscription_start_timestamp" 
    ,IFNULL(CASE WHEN sub_cancelled."churn_type" IN ('VOLUNTARY','INVOLUNTARY')  THEN sub_cancelled."effective_cancellation_date"
    ELSE NULL END
            ,GREATEST_IGNORE_NULLS(sub_renwel."validity_end_date"
                ,CASE 
                    WHEN INITCAP(subscription_stage."validity_period_unit")='Month' 
                    THEN '9999-12-31' 
                    WHEN UPPER(subscription_stage."billing_mode")='PARTNER' THEN '9999-12-31'
                    ELSE subscription_stage."validity_end_date" 
                END)) AS "subscription_end_timestamp"
    ,subscription_stage."validity_start_date"::date AS "subscription_start_date"            
    ,"subscription_end_timestamp"::DATE AS "subscription_end_date"
    ,INITCAP(subscription_stage."product_type") AS "purchase_product_type"
    ,IFNULL(account_mapping."billing_account_id",subscription_stage."dazn_id") AS "billing_account_id" 
    ,"source_system_derived" AS "subscription_source_system_name"
    ,"tracking_id_derived" AS "subscription_tracking_id"
    ,subscription_stage."product_group" AS "subscription_product_group"
    ,subscription_stage."dcb_provider_name" AS "subscription_direct_carrier_billing_carrier_name"
    ,country_source."country" AS "subscription_country"
    ,country_source."territory" AS "subscription_territory"
    ,INITCAP(
        CASE 
            WHEN sub_cancelled."churn_type" IN ('VOLUNTARY','INVOLUNTARY') 
                --AND sub_cancelled."event_status"='SUCCEEDED' 
                THEN 'Cancelled'
            WHEN UPPER(subscription_stage."product_status") ='ACTIVE_GRACE'
                THEN 'Active_Grace'
            WHEN LEAD(subscription_stage."product_status") OVER (PARTITION BY "subscription_name",subscription_stage."product_group" ORDER BY subscription_stage."purchase_date",subscription_stage."event_timestamp") IS NULL 
                THEN 'Active'
            ELSE 'Expired' 
        END) AS "subscription_status"
    ,subscription_stage."product_status" AS "subscription_product_status"
    ,INITCAP(subscription_stage."validity_period_unit") AS "subscription_validity_period_unit"
    ,subscription_stage."validity_period_duration" AS  "subscription_validity_period_duration"
    ,subscription_stage."entitlement_set_id" AS "subscription_tier"  
    ,subscription_stage."dazn_id" AS "dazn_user_id"
    ,subscription_stage."catalog_product_id" AS "subscription_catalog_product_id"
    ,subscription_stage."catalog_product_name" AS "subscription_catalog_product_name"
    ,"subscription_free_trial_start_date"::DATE AS "subscription_free_trial_start_date"
    ,"subscription_free_trial_end_date"::DATE AS "subscription_free_trial_end_date"
    ,subscription_stage."is_free_trial" AS "has_free_trial"
    ,subscription_stage."subscription_free_trail_promo_name"AS "subscription_free_trail_promo_name" 
    ,subscription_stage."subscription_free_trail_promo_id" AS "subscription_free_trail_promo_id" 
    ,'DMP' AS "subscription_platform"
    ,subscription_stage."message_type" AS "message_type"
    ,subscription_stage."purchase_date" AS "purchase_date"
    ,subscription_stage."event_timestamp" AS "event_timestamp"
    ,subscription_stage."payment_method_id" AS "subscription_payment_method_id"
    ,GREATEST_IGNORE_NULLS(sub_renwel."next_billing_date",subscription_stage."next_billing_date")::DATE AS "subscription_rateplan_charge_charged_through_date"
    ,DATE_PART('day',"subscription_rateplan_charge_charged_through_date") AS "subscription_bill_cycle_day"
    ,subscription_stage."billing_charge_type" AS "subscription_billing_charge_type"
    ,CASE WHEN sub_cancelled."event_status"='SUCCEEDED' THEN sub_cancelled."validity_end_date"::DATE ELSE NULL END AS "subscription_cancelled_date"
    ,CASE WHEN sub_cancelled."event_status"='SUCCEEDED' THEN sub_cancelled."effective_cancellation_date"::DATE ELSE NULL END AS "subscription_effective_cancellation_date"
    ,subscription_stage."subscription_rateplan_charge_effective_start_date"
    ,subscription_stage."no_of_installments"::INT AS "subscription_instalment_period"
    ,subscription_stage."currency" AS "purchase_currency"
    --,subscription_stage.ORDER_AMOUNT  AS "subscription_order_amount"
    ,subscription_stage."catalog_price" AS "subscription_catalog_price"
    ,subscription_stage."gross_price" AS "subscription_gross_price"
    ,subscription_stage."charge_amount" AS "subscription_charge_amount"
    ,subscription_stage."is_paid_by_installments"  AS "subscription_is_paid_by_installments"
    ,subscription_stage."is_seasonal" AS "is_seasonal"
    ,subscription_stage."device_platform" AS "device_platform"
    ,subscription_stage."legacy_rateplan_id" AS "rateplan_id"
    ,account_mapping."crm_account_id" AS "crm_account_id"
    ,account_mapping."billing_account_created_timestamp"
    ,CASE 
        WHEN "subscription_status" ='Expired' THEN 'Recontract'
        ELSE INITCAP(CASE WHEN sub_cancelled."event_status"='SUCCEEDED' THEN sub_cancelled."churn_type" ELSE NULL END)
    END AS "subscription_churn_type"
    ,subscription_staging."subscription_sign_up_campaign_id"
    ,subscription_staging."subscription_sign_up_giftcode"
    ,subscription_stage."partner_product_id"
    ,subscription_stage."billing_mode"
    ,subscription_staging."subscription_is_auto_renew"
    ,subscription_staging."subscription_term_type"
    ,subscription_staging."zr_subscription_tracking_id"
    ,subscription_stage."billing_product_id"
    ,"subscription_free_trial_start_date" AS "subscription_free_trial_start_timestamp"
    ,"subscription_free_trial_end_date" AS "subscription_free_trial_end_timestamp"
FROM subscription_stage
LEFT JOIN country_source ON country_source."join_key"=subscription_stage."billing_country"
LEFT JOIN sub_renwel USING ("billing_product_id")
LEFT JOIN sub_cancelled USING ("billing_product_id")
--LEFT JOIN rateplan_staging ON rateplan_staging."rateplan_id"=subscription_stage."legacy_rateplan_id"
LEFT JOIN subscription_staging ON subscription_staging."zu_subscription_name" = "subscription_name"
LEFT JOIN account_mapping ON account_mapping."dazn_user_id" = subscription_stage."dazn_id"
)

SELECT * FROM final
QUALIFY ROW_NUMBER() OVER (PARTITION BY "subscription_id" ORDER BY  "subscription_id_created_timestamp","subscription_status" ) = 1
