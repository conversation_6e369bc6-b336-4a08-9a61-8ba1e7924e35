{{
   config(
        materialized='table',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_M_WH',
    )
}}

WITH zuora_account AS (
    SELECT 
        1 "set"
        ,"billing_account_id" 
        ,"crm_account_id"
        ,"dazn_user_id"
        ,"billing_account_created_timestamp" 
    FROM {{ ref('staging__zuora__account_current') }}
    WHERE TRUE
)


,dmp_account AS (
    SELECT 
        2 "set"
        ,"dazn_id" AS "billing_account_id" 
        ,NULL AS "crm_account_id"
        ,"dazn_id" AS "dazn_user_id"
        ,"event_timestamp" AS "billing_account_created_timestamp"
    FROM {{ ref('staging__dmp_billing_account') }} dmp
    LEFT JOIN zuora_account
        ON dmp."dazn_id"=zuora_account."dazn_user_id"
    WHERE 1=1
    AND "event_status"='SUCCEEDED'
    AND zuora_account."dazn_user_id" IS NULL
)

,final AS (
    SELECT * FROM zuora_account --this is zuora
    UNION ALL 
    SELECT * FROM dmp_account --this is ev created
)

SELECT
        "billing_account_id" 
        ,"crm_account_id"
        ,"dazn_user_id"
        ,"billing_account_created_timestamp"  FROM final 
QUALIFY ROW_NUMBER() OVER (PARTITION BY "dazn_user_id" ORDER BY "set","billing_account_created_timestamp" DESC)=1
