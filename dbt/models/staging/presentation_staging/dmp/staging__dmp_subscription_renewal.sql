WITH source AS (
    SELECT * FROM {{ ref('staging__dmp_billing_events') }}
)

,renamed AS (
    SELECT
        "billing_product_id"
        ,"event_timestamp"
        ,"validity_end_date"
        ,"next_billing_date"
        ,"message_type"
        ,"legacy_subscription_name"
        ,"legacy_rateplan_id"
    FROM source
    WHERE TRUE
        AND UPPER("message_type") ='BILLING_PRODUCT_RENEWAL' 
        AND LOWER("product_type")='subscription'
        AND "event_status"='SUCCEEDED' 
        AND "business_type"='B2C' 
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "billing_product_id" ORDER BY "event_timestamp" DESC) = 1
)

SELECT * FROM renamed
