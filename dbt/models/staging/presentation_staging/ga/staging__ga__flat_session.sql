WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED_GA__FLAT_SESSION') }}
)

,renamed AS (

    SELECT
        "ga_unique_session_id"
        , "ga_primary_key"
        , "customer_id"
        , "edm_inserted_at"
        , "visit_number"
        , "session_id"
        , "visit_start_timestamp"
        , "full_date"
        , "full_visitor_id"
        , "client_id"
        , "channel_grouping"
        , "total_events"
        , "device_category"
        , "geo_country"
        , "application_id"
        , "app_version"
        , "hit_number"
        , "hit_time_ms"
        , "event_action"
        , "event_category"
        , "event_label"
        , "hostname"
        , "page_path"
        , "page_title"
        , "experiment_id"
        , "user_agent"
        , "application_type"
        , "application_version"
        , "vwo_test_variant_name"
        , "application_environment"
        , "client_id_scd"
        , "click_ref"
        , "launch_origin"
        , "vwo_test_campaign_name"
        , "vwo_test_variant_id"
        , "publisher_id"
        , "publisher_name"
        , "vwo_test_campaign_id"
        , "open_browse"
        , "test_variant"
        , "test_variant_open_browse"
        , "test_variant_rail_management"
        , "competition_id"
        , "competition_name"
        , "content_title_sport_name"
        , "navigation_tile_article_id"
        , "navigation_tile_navigate_to"
        , "navigation_tile_title"
        , "rail_current_position"
        , "rail_length"
        , "rail_name"
        , "rail_starting_position"
        , "rail_number_in_view"
        , "search_number_of_results"
        , "search_result_category"
        , "search_selected_result_position"
        , "search_term"
        , "rail_number_of_loaded"
        , "article_type"
        , "player_action"
        , "play_origin"
        , "payment_billing_country"
        , "payment_billing_period"
        , "payment_state"
        , "payment_subscription_type"
        , "password_reset_state"
        , "content_tile_position_in_view"
        , "content_tile_position_of_loaded"
        , "navigation_tile_position_in_view"
        , "navigation_tile_position_of_loaded"
        , "content_tile_article_name"
        , "content_tile_coming_up_label"
        , "perform_id_ansii"
        , "perform_id"
        , "ga_page_category"
        , "payment_method"
        , "page_load_delta"
        , "competitor_id"
        , "fixture_id"
        , "gtm_container_version"
        , "rail_title"
        , "content_tile_label_id"
        , "adblock_status"
        , "content_country"
        , "fixture_name"
        , "competitor_name"
        , "giftcode_action"
        , "giftcode_status"
        , "giftcode_type"
        , "giftcode_id"
        , "giftcode_extra_free_months"
        , "giftcode_promo_id"
        , "giftcode_campaign_id"
        , "giftcode_extra_payment_method"
        , "giftcode_eu_content_portability"
        , "payment_action"
        , "payment_additional_payment_method"
        , "payment_auth_country"
        , "payment_auth_provider"
        , "payment_auth_response"
        , "subscription_action"
        , "subscription_action_status"
        , "subscription_billing_period"
        , "subscription_subscription_type"
        , "subscription_restart_date"
        , "edit_item"
        , "edit_action_status"
        , "edit_value"
        , "edit_previous_value"
        , "edit_field"
        , "itm_source"
        , "itm_medium"
        , "itm_campaign"
        , "live_edge"
        , "pulse_alert_position"
        , "player_playback_initiation"
        , "player_playback_source"
        , "dazn_device_id"
        , "hit_time"
        , "hit_id"
        , "device_key"
        , "page_url"
        , "full_date_ts"
        , "session_start_ts"
        , "article_id"
    FROM source
)

SELECT * FROM renamed
