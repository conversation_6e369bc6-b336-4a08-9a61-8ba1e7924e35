
with source as (
    select 
    ITEM_KEY "item_key",
    DWH_BATCH_DATE "dwh_batch_date",
    SCHEDULE_DATE "schedule_date",
    DECOMPRESSED_PAYLOAD "decompressed_payload"
    from  {{ source('CURATED','curated__broadcast__linear_EPG_current' )}}
)

, base_schedule as (
    select * from source,
    LATERAL FLATTEN( INPUT => "decompressed_payload":"raildecompress":"S":"Tiles",OUTER => TRUE) AS "input"
)

,all_block AS (
    SELECT 
    'Now' As Block,
    "item_key",
    "dwh_batch_date",
    "schedule_date",
    D.Value:"AssetId"::String Article_ID,
    D.Value:"Competition":"Title"::String CompetitionTitle,
    D.Value:"Competition":"Id"::String CompetitionId,
    D.Value:"Description"::String  Channel_description,
    D.Value:"DisplayDate"::DATE  Channel_Displaydate,
    D.Value:"Start"::DATE  Channel_Startdate,
    D.Value:"End"::DATE  Channel_Enddate,
    D.Value:"EntitlementIds"::String  Entitlements,
    D.Value:"IsPlayForFree"::Boolean IsPlayForFree ,
    D.Value:"Label"::String Channel_label ,
    D.Value:"Title"::String Channel_Title ,
    D.Value:"Sport":"Id"::String Channel_SportId,
    D.Value:"Sport":"Title"::String Channel_SportTitle,

    D.Value:"LinearSchedule":"Now":"Description"::String PROGRAM_DESCRIPTION,
    D.Value:"LinearSchedule":"Now":"Title"::String PROGRAM_Title,
    D.Value:"LinearSchedule":"Now":"EpisodeTitle"::String PROGRAM_EpisodeTitle,
    D.Value:"LinearSchedule":"Now":"EventYear"::String PROGRAM_EventYear,
    D.Value:"LinearSchedule":"Now":"Start"::Timestamp PROGRAM_Starttime,
    D.Value:"LinearSchedule":"Now":"End"::Timestamp PROGRAM_Endtime,
    D.Value:"LinearSchedule":"Now":"IsLive"::Boolean PROGRAM_IsLive,
    D.Value:"LinearSchedule":"Now":"Genre"::variant PROGRAM_Genre
    from 
    base_schedule D
    Union All

    SELECT 
    'Next' As Block,
    "item_key",
    "dwh_batch_date",
    "schedule_date",
    D.Value:"AssetId"::String Article_ID,
    D.Value:"Competition":"Title"::String CompetitionTitle,
    D.Value:"Competition":"Id"::String CompetitionId,
    D.Value:"Description"::String  Channel_description,
    D.Value:"DisplayDate"::DATE  Channel_Displaydate,
    D.Value:"Start"::DATE  Channel_Startdate,
    D.Value:"End"::DATE  Channel_Enddate,
    D.Value:"EntitlementIds"::String  Entitlements,
    D.Value:"IsPlayForFree"::Boolean IsPlayForFree ,
    D.Value:"Label"::String Channel_label ,
    D.Value:"Title"::String Channel_Title ,
    D.Value:"Sport":"Id"::String Channel_SportId,
    D.Value:"Sport":"Title"::String Channel_SportTitle,

    D.Value:"LinearSchedule":"Next":"Description"::String PROGRAM_DESCRIPTION,
    D.Value:"LinearSchedule":"Next":"Title"::String PROGRAM_Title,
    D.Value:"LinearSchedule":"Next":"EpisodeTitle"::String PROGRAM_EpisodeTitle,
    D.Value:"LinearSchedule":"Next":"EventYear"::String PROGRAM_EventYear,
    D.Value:"LinearSchedule":"Next":"Start"::Timestamp PROGRAM_Starttime,
    D.Value:"LinearSchedule":"Next":"End"::Timestamp PROGRAM_Endtime,
    D.Value:"LinearSchedule":"Next":"IsLive"::Boolean PROGRAM_IsLive,
    D.Value:"LinearSchedule":"Next":"Genre"::variant PROGRAM_Genre
    from 
    base_schedule D

    UNION ALL

    SELECT 
    'Later' As Block,
    "item_key",
    "dwh_batch_date",
    "schedule_date",
    A.Value:"AssetId"::String Article_ID,
    A.Value:"Competition":"Title"::String CompetitionTitle,
    A.Value:"Competition":"Id"::String CompetitionId,
    A.Value:"Description"::String  Channel_description,
    A.Value:"DisplayDate"::DATE  Channel_Displaydate,
    A.Value:"Start"::DATE  Channel_Startdate,
    A.Value:"End"::DATE  Channel_Enddate,
    A.Value:"EntitlementIds"::String  Entitlements,
    A.Value:"IsPlayForFree"::Boolean IsPlayForFree ,
    A.Value:"Label"::String Channel_label ,
    A.Value:"Title"::String Channel_Title ,
    A.Value:"Sport":"Id"::String Channel_SportId,
    A.Value:"Sport":"Title"::String Channel_SportTitle,

    D.Value:"Description"::String PROGRAM_DESCRIPTION,
    D.Value:"Title"::String PROGRAM_Title,
    D.Value:"EpisodeTitle"::String PROGRAM_EpisodeTitle,
    D.Value:"EventYear"::String PROGRAM_EventYear,
    D.Value:"Start"::Timestamp PROGRAM_Starttime,
    D.Value:"End"::Timestamp PROGRAM_Endtime,
    D.Value:"IsLive"::Boolean PROGRAM_IsLive,
    D.Value:"Genre"::variant PROGRAM_Genre
    from 
    base_schedule A
    ,TABLE(FLATTEN(A.Value:"LinearSchedule":"Later")) as D
) 

, final as (
    select 
    "dwh_batch_date",
    "schedule_date",
    ARTICLE_ID "article_id",
    SPLIT_PART("item_key",'#',1) "country",
    SPLIT_PART("item_key",'#',2) "channel_language",
    CompetitionTitle "competition_title",
    CompetitionId "competition_id",
    Channel_description "channel_description",
    Channel_Displaydate "channel_display_date",
    Channel_Startdate "channel_start_date",
    Channel_Enddate "channel_end_date",
    Entitlements "entitlements",
    IsPlayForFree "is_play_for_free",
    Channel_label "channel_label",
    Channel_Title "channel_title_localised",
    --SNOWFLAKE.CORTEX.TRANSLATE(Channel_title,split_part("item_key",'#',2),'en' ) as "channel_title_english",
    Channel_SportId "channel_sportid",
    Channel_SportTitle "channel_sport_title",
    PROGRAM_DESCRIPTION "program_description",
    PROGRAM_Title "program_title",
    PROGRAM_EpisodeTitle "program_episode_title",
    PROGRAM_EventYear "program_event_year",
    PROGRAM_Starttime "program_start_time",
    PROGRAM_Endtime "program_end_time",
    PROGRAM_IsLive "program_islive",
    PROGRAM_Genre "program_genre",
    Block as "block"
    from 
    all_block
)

,cleanse_data as (
    select 
    "dwh_batch_date",
    "schedule_date",
    "article_id",
    "channel_language",
    "competition_title",
    "competition_id",
    "channel_description",
    "channel_display_date",
    "channel_start_date",
    "channel_end_date",
    "entitlements",
    "is_play_for_free",
    "channel_label",
    "channel_title_localised",
    --"channel_title_english",
    "channel_sportid",
    "channel_sport_title",
    "program_description",
    "program_title",
    "program_episode_title",
    "program_event_year",
    "program_start_time",
    "program_end_time",
    "program_islive",
    "program_genre",
    "block",
    case when TRY_TO_DATE(trim("program_start_time"))+1  = "schedule_date" then 2
         when TRY_TO_DATE(trim("program_start_time"))  = "schedule_date" then 1
         else  0 
    end "seq",
    ARRAY_UNIQUE_AGG("country") "country_set"
    from 
    final
    group by all
)
select
*
from 
cleanse_data 
QUALIFY MAX("seq") over(partition by "article_id","program_start_time"::date)="seq"