version: 2

sources:
  - name: LINEAR_ADD_ON_SUBSCRIBERS__DAILY
    schema: LINEAR_ADD_ON_SUBSCRIBERS__DAILY
    database: EXCHANGE__SKY__IN__{{ 'UAT' if env_var('AIRFLOW_VAR_ENV') == 'stage' else env_var('AIRFLOW_VAR_ENV') }}
    quoting:
      identifier: true
    tables:
      - name: DAILY_ACTIVE_USER
        description: "Daily file provided by SKY with all active users, their activation date and the timestamp of when the snapshot was generated"
        columns:
          - name: META__DATA
            description: "Metadata column"
            quote: true

          - name: sky_contract_id
            description: "ID field. Identifies user in the list. This is the equivalent of RatePlan.Partner_user_id__c in Zuora"
            quote: true

          - name: last_activated_at_timestamp
            description: "timestamp when this ID was activated"
            quote: true

          - name: snapshot_timestamp
            description: "timestamp when the file containing this row was generated"
            quote: true
