version: 2

models:

  - name: staging__zuora__subscription
    description: "Staging data of the Open Zuora Subscription table"
    columns:
      - name: subscription_id
        description: ""
        quote: true

      - name: billing_account_id
        description: ""
        quote: true

      - name: subscription_creator_billing_account_id
        description: ""
        quote: true

      - name: subscription_creator_invoice_owner_billing_account_id
        description: ""
        quote: true

      - name: subscription_invoice_owner_billing_account_id
        description: ""
        quote: true

      - name: subscription_original_id
        description: ""
        quote: true

      - name: subscription_previous_subscription_id
        description: ""
        quote: true

      - name: subscription_sign_up_campaign_id
        description: ""
        quote: true

      - name: subscription_direct_carrier_billing_carrier_name
        description: ""
        quote: true

      - name: subscription_device_category
        description: ""
        quote: true

      - name: subscription_sign_up_giftcode
        description: ""
        quote: true

      - name: subscription_sign_up_giftcode_old
        description: ""
        quote: true

      - name: subscription_ip_address__hash
        description: ""
        quote: true

      - name: subscription_is_manually_cancelled
        description: ""
        quote: true

      - name: subscription_giftcode_grants_content_portability
        description: ""
        quote: true

      - name: subscription_giftcode_is_payment_method
        description: ""
        quote: true

      - name: subscription_source_system_name
        description: ""
        quote: true

      - name: subscription_source_system_user_id
        description: ""
        quote: true

      - name: subscription_tracking_id
        description: ""
        quote: true

      - name: subscription_user_agent
        description: ""
        quote: true

      - name: subscription_is_auto_renew
        description: ""
        quote: true

      - name: subscription_cancelled_date
        description: ""
        quote: true

      - name: subscription_contract_acceptance_date
        description: ""
        quote: true

      - name: subscription_contract_effective_date
        description: ""
        quote: true

      - name: subscription_country
        description: ""
        quote: true

      - name: subscription_cpq_bundle_json_id
        description: ""
        quote: true

      - name: subscription_created_by_id
        description: ""
        quote: true

      - name: subscription_id_created_timestamp__unpatched
        description: ""
        quote: true

      - name: subscription_id_created_timestamp
        description: ""
        quote: true

      - name: subscription_current_term
        description: ""
        quote: true

      - name: subscription_current_term_period_type
        description: ""
        quote: true

      - name: subscription_initial_term
        description: ""
        quote: true

      - name: subscription_initial_term_period_type
        description: ""
        quote: true

      - name: subscription_is_invoice_separate
        description: ""
        quote: true

      - name: subscription_name
        description: ""
        quote: true

      - name: subscription_notes
        description: ""
        quote: true

      - name: subscription_number_of_free_trial_periods__unpatched
        description: ""
        quote: true

      - name: subscription_number_of_free_trial_periods
        description: ""
        quote: true

      - name: subscription_number_of_giftcode_periods
        description: ""
        quote: true

      - name: subscription_opportunity_close_date
        description: ""
        quote: true

      - name: subscription_opportunity_name
        description: ""
        quote: true

      - name: subscription_name_original_created_timestamp
        description: ""
        quote: true

      - name: subscription_quote_business_type
        description: ""
        quote: true

      - name: subscription_quote_number
        description: ""
        quote: true

      - name: subscription_quote_type
        description: ""
        quote: true

      - name: subscription_receipt_id
        description: ""
        quote: true

      - name: subscription_renewal_setting
        description: ""
        quote: true

      - name: subscription_renewal_term
        description: ""
        quote: true

      - name: subscription_renewal_term_period_type
        description: ""
        quote: true

      - name: subscription_service_activation_date
        description: ""
        quote: true

      - name: subscription_status
        description: ""
        quote: true

      - name: subscription_end_date__unpatched
        description: ""
        quote: true

      - name: subscription_end_date
        description: ""
        quote: true

      - name: subscription_start_date
        description: ""
        quote: true

      - name: subscription_term_end_date
        description: ""
        quote: true

      - name: subscription_term_start_date
        description: ""
        quote: true

      - name: subscription_term_type
        description: ""
        quote: true

      - name: subscription_updated_by_id
        description: ""
        quote: true

      - name: subscription_id_updated_timestamp__unpatched
        description: ""
        quote: true

      - name: subscription_id_updated_timestamp
        description: ""
        quote: true

      - name: subscription_version
        description: ""
        quote: true

      - name: is_latest_subscription_version
        description: ""
        quote: true

      - name: subscription_free_trial_periods_type__unpatched
        description: ""
        quote: true

      - name: subscription_free_trial_periods_type
        description: ""
        quote: true

      - name: subscription_product_group
        description: ""
        quote: true

      - name: subscription_payment_method_id
        description: ""
        quote: true

  - name: staging__zuora__subscription_name_current
    description: "Shows the latest record for each Subscription.Name"
    columns:
      - name: subscription_id
        description: ""
        quote: true

      - name: billing_account_id
        description: ""
        quote: true

      - name: subscription_creator_billing_account_id
        description: ""
        quote: true

      - name: subscription_creator_invoice_owner_billing_account_id
        description: ""
        quote: true

      - name: subscription_invoice_owner_billing_account_id
        description: ""
        quote: true

      - name: subscription_original_id
        description: ""
        quote: true

      - name: subscription_previous_subscription_id
        description: ""
        quote: true

      - name: subscription_sign_up_campaign_id
        description: ""
        quote: true

      - name: subscription_direct_carrier_billing_carrier_name
        description: ""
        quote: true

      - name: subscription_device_category
        description: ""
        quote: true

      - name: subscription_sign_up_giftcode
        description: ""
        quote: true

      - name: subscription_sign_up_giftcode_old
        description: ""
        quote: true

      - name: subscription_ip_address__hash
        description: ""
        quote: true

      - name: subscription_is_manually_cancelled
        description: ""
        quote: true

      - name: subscription_giftcode_grants_content_portability
        description: ""
        quote: true

      - name: subscription_giftcode_is_payment_method
        description: ""
        quote: true

      - name: subscription_source_system_name
        description: ""
        quote: true

      - name: subscription_source_system_user_id
        description: ""
        quote: true

      - name: subscription_tracking_id
        description: ""
        quote: true

      - name: subscription_user_agent
        description: ""
        quote: true

      - name: subscription_is_auto_renew
        description: ""
        quote: true

      - name: subscription_cancelled_date
        description: ""
        quote: true

      - name: subscription_contract_acceptance_date
        description: ""
        quote: true

      - name: subscription_contract_effective_date
        description: ""
        quote: true

      - name: subscription_country
        description: ""
        quote: true

      - name: subscription_cpq_bundle_json_id
        description: ""
        quote: true

      - name: subscription_created_by_id
        description: ""
        quote: true

      - name: subscription_id_created_timestamp__unpatched
        description: ""
        quote: true

      - name: subscription_id_created_timestamp
        description: ""
        quote: true

      - name: subscription_current_term
        description: ""
        quote: true

      - name: subscription_current_term_period_type
        description: ""
        quote: true

      - name: subscription_initial_term
        description: ""
        quote: true

      - name: subscription_initial_term_period_type
        description: ""
        quote: true

      - name: subscription_is_invoice_separate
        description: ""
        quote: true

      - name: subscription_name
        description: ""
        quote: true

      - name: subscription_notes
        description: ""
        quote: true

      - name: subscription_number_of_free_trial_periods__unpatched
        description: ""
        quote: true

      - name: subscription_number_of_free_trial_periods
        description: ""
        quote: true

      - name: subscription_number_of_giftcode_periods
        description: ""
        quote: true

      - name: subscription_opportunity_close_date
        description: ""
        quote: true

      - name: subscription_opportunity_name
        description: ""
        quote: true

      - name: subscription_name_original_created_timestamp
        description: ""
        quote: true

      - name: subscription_quote_business_type
        description: ""
        quote: true

      - name: subscription_quote_number
        description: ""
        quote: true

      - name: subscription_quote_type
        description: ""
        quote: true

      - name: subscription_receipt_id
        description: ""
        quote: true

      - name: subscription_renewal_setting
        description: ""
        quote: true

      - name: subscription_renewal_term
        description: ""
        quote: true

      - name: subscription_renewal_term_period_type
        description: ""
        quote: true

      - name: subscription_service_activation_date
        description: ""
        quote: true

      - name: subscription_status
        description: ""
        quote: true

      - name: subscription_end_date__unpatched
        description: ""
        quote: true

      - name: subscription_end_date
        description: ""
        quote: true

      - name: subscription_start_date
        description: ""
        quote: true

      - name: subscription_term_end_date
        description: ""
        quote: true

      - name: subscription_term_start_date
        description: ""
        quote: true

      - name: subscription_term_type
        description: ""
        quote: true

      - name: subscription_updated_by_id
        description: ""
        quote: true

      - name: subscription_id_updated_timestamp__unpatched
        description: ""
        quote: true

      - name: subscription_id_updated_timestamp
        description: ""
        quote: true

      - name: subscription_version
        description: ""
        quote: true

      - name: is_latest_subscription_version
        description: ""
        quote: true

      - name: subscription_free_trial_periods_type__unpatched
        description: ""
        quote: true

      - name: subscription_free_trial_periods_type
        description: ""
        quote: true

      - name: subscription_product_group
        description: ""
        quote: true

      - name: subscription_payment_method_id
        description: ""
        quote: true

  - name: staging__zuora__subscription_id_current
    description: "Shows the latest record for each Subscription.ID"
    columns:
      - name: subscription_id
        description: ""
        quote: true

      - name: billing_account_id
        description: ""
        quote: true

      - name: subscription_creator_billing_account_id
        description: ""
        quote: true

      - name: subscription_creator_invoice_owner_billing_account_id
        description: ""
        quote: true

      - name: subscription_invoice_owner_billing_account_id
        description: ""
        quote: true

      - name: subscription_original_id
        description: ""
        quote: true

      - name: subscription_previous_subscription_id
        description: ""
        quote: true

      - name: subscription_sign_up_campaign_id
        description: ""
        quote: true

      - name: subscription_direct_carrier_billing_carrier_name
        description: ""
        quote: true

      - name: subscription_device_category
        description: ""
        quote: true

      - name: subscription_sign_up_giftcode
        description: ""
        quote: true

      - name: subscription_sign_up_giftcode_old
        description: ""
        quote: true

      - name: subscription_ip_address__hash
        description: ""
        quote: true

      - name: subscription_is_manually_cancelled
        description: ""
        quote: true

      - name: subscription_giftcode_grants_content_portability
        description: ""
        quote: true

      - name: subscription_giftcode_is_payment_method
        description: ""
        quote: true

      - name: subscription_source_system_name
        description: ""
        quote: true

      - name: subscription_source_system_user_id
        description: ""
        quote: true

      - name: subscription_tracking_id
        description: ""
        quote: true

      - name: subscription_user_agent
        description: ""
        quote: true

      - name: subscription_is_auto_renew
        description: ""
        quote: true

      - name: subscription_cancelled_date
        description: ""
        quote: true

      - name: subscription_contract_acceptance_date
        description: ""
        quote: true

      - name: subscription_contract_effective_date
        description: ""
        quote: true

      - name: subscription_country
        description: ""
        quote: true

      - name: subscription_cpq_bundle_json_id
        description: ""
        quote: true

      - name: subscription_created_by_id
        description: ""
        quote: true

      - name: subscription_id_created_timestamp__unpatched
        description: ""
        quote: true

      - name: subscription_id_created_timestamp
        description: ""
        quote: true

      - name: subscription_current_term
        description: ""
        quote: true

      - name: subscription_current_term_period_type
        description: ""
        quote: true

      - name: subscription_initial_term
        description: ""
        quote: true

      - name: subscription_initial_term_period_type
        description: ""
        quote: true

      - name: subscription_is_invoice_separate
        description: ""
        quote: true

      - name: subscription_name
        description: ""
        quote: true

      - name: subscription_notes
        description: ""
        quote: true

      - name: subscription_number_of_free_trial_periods__unpatched
        description: ""
        quote: true

      - name: subscription_number_of_free_trial_periods
        description: ""
        quote: true

      - name: subscription_number_of_giftcode_periods
        description: ""
        quote: true

      - name: subscription_opportunity_close_date
        description: ""
        quote: true

      - name: subscription_opportunity_name
        description: ""
        quote: true

      - name: subscription_name_original_created_timestamp
        description: ""
        quote: true

      - name: subscription_quote_business_type
        description: ""
        quote: true

      - name: subscription_quote_number
        description: ""
        quote: true

      - name: subscription_quote_type
        description: ""
        quote: true

      - name: subscription_receipt_id
        description: ""
        quote: true

      - name: subscription_renewal_setting
        description: ""
        quote: true

      - name: subscription_renewal_term
        description: ""
        quote: true

      - name: subscription_renewal_term_period_type
        description: ""
        quote: true

      - name: subscription_service_activation_date
        description: ""
        quote: true

      - name: subscription_status
        description: ""
        quote: true

      - name: subscription_end_date__unpatched
        description: ""
        quote: true

      - name: subscription_end_date
        description: ""
        quote: true

      - name: subscription_start_date
        description: ""
        quote: true

      - name: subscription_term_end_date
        description: ""
        quote: true

      - name: subscription_term_start_date
        description: ""
        quote: true

      - name: subscription_term_type
        description: ""
        quote: true

      - name: subscription_updated_by_id
        description: ""
        quote: true

      - name: subscription_id_updated_timestamp__unpatched
        description: ""
        quote: true

      - name: subscription_id_updated_timestamp
        description: ""
        quote: true

      - name: subscription_version
        description: ""
        quote: true

      - name: is_latest_subscription_version
        description: ""
        quote: true

      - name: subscription_free_trial_periods_type__unpatched
        description: ""
        quote: true

      - name: subscription_free_trial_periods_type
        description: ""
        quote: true

      - name: subscription_product_group
        description: ""
        quote: true

      - name: subscription_payment_method_id
        description: ""
        quote: true

  - name: staging__zuora__amendment
    description: "Staging data of the Open Zuora Amendment table. Contains all amendments."
    columns:
      - name: amendment_id
        description: ""
        quote: true

      - name: subscription_id
        description: ""
        quote: true

      - name: amendment_is_auto_renew
        description: ""
        quote: true

      - name: amendment_code
        description: ""
        quote: true

      - name: amendment_contract_effective_date
        description: ""
        quote: true

      - name: amendment_created_by_id
        description: ""
        quote: true

      - name: amendment_created_timestamp
        description: ""
        quote: true

      - name: amendment_current_term
        description: ""
        quote: true

      - name: amendment_current_term_period_type
        description: ""
        quote: true

      - name: amendment_customer_acceptance_date
        description: ""
        quote: true

      - name: amendment_description
        description: ""
        quote: true

      - name: amendment_effective_date
        description: ""
        quote: true

      - name: amendment_name
        description: ""
        quote: true

      - name: amendment_renewal_setting
        description: ""
        quote: true

      - name: amendment_renewal_term
        description: ""
        quote: true

      - name: amendment_renewal_term_period_type
        description: ""
        quote: true

      - name: amendment_resume_date
        description: ""
        quote: true

      - name: amendment_service_activation_date
        description: ""
        quote: true

      - name: amendment_specific_update_date
        description: ""
        quote: true

      - name: amendment_status
        description: ""
        quote: true

      - name: amendment_suspend_date
        description: ""
        quote: true

      - name: amendment_term_start_date
        description: ""
        quote: true

      - name: amendment_term_type
        description: ""
        quote: true

      - name: amendment_type
        description: ""
        quote: true

      - name: amendment_updated_by_id
        description: ""
        quote: true

      - name: amendment_updated_timestamp
        description: ""
        quote: true

  - name: staging__zuora__account
    description: "Staging data of the Open Zuora Account table"
    columns:
      - name: billing_account_id
        description: ""
        quote: true

      - name: crm_account_id
        description: ""
        quote: true

      - name: billing_account_parent_id
        description: ""
        quote: true

      - name: billing_account_sequence_set_id
        description: ""
        quote: true

      - name: billing_account_tax_exempt_certificate_id
        description: ""
        quote: true

      - name: bill_to_contact_id
        description: ""
        quote: true

      - name: default_payment_method_id
        description: ""
        quote: true

      - name: sold_to_contact_id
        description: ""
        quote: true

      - name: dazn_user_id
        description: ""
        quote: true

      - name: billing_account_number
        description: ""
        quote: true

      - name: billing_account_additional_email_addresses__hash
        description: ""
        quote: true

      - name: billing_account_is_allowed_invoice_edit
        description: ""
        quote: true

      - name: billing_account_is_auto_pay
        description: ""
        quote: true

      - name: billing_account_balance
        description: ""
        quote: true

      - name: billing_account_batch_name
        description: ""
        quote: true

      - name: billing_account_cycle_day_setting_option
        description: ""
        quote: true

      - name: billing_account_bill_cycle_day
        description: ""
        quote: true

      - name: billing_account_communication_profile_id
        description: ""
        quote: true

      - name: billing_account_company_name
        description: ""
        quote: true

      - name: billing_account_created_by_id
        description: ""
        quote: true

      - name: billing_account_created_timestamp
        description: ""
        quote: true

      - name: billing_account_credit_balance
        description: ""
        quote: true

      - name: billing_account_currency_code
        description: ""
        quote: true

      - name: billing_account_customer_service_rep_name
        description: ""
        quote: true

      - name: billing_account_has_invoice_delivery_preference_email
        description: ""
        quote: true

      - name: billing_account_has_invoice_delivery_preference_print
        description: ""
        quote: true

      - name: billing_account_invoice_template_id
        description: ""
        quote: true

      - name: billing_account_last_invoice_date
        description: ""
        quote: true

      - name: billing_account_monthly_recurring_revenue
        description: ""
        quote: true

      - name: billing_account_customer_full_name__hash
        description: ""
        quote: true

      - name: billing_account_notes
        description: ""
        quote: true

      - name: billing_account_payment_gateway
        description: ""
        quote: true

      - name: billing_account_payment_term
        description: ""
        quote: true

      - name: billing_account_purchase_order_number
        description: ""
        quote: true

      - name: billing_account_sales_rep_name
        description: ""
        quote: true

      - name: billing_account_status
        description: ""
        quote: true

      - name: billing_account_tax_company_code
        description: ""
        quote: true

      - name: billing_account_tax_exempt_certificate_type
        description: ""
        quote: true

      - name: billing_account_tax_exempt_description
        description: ""
        quote: true

      - name: billing_account_tax_exempt_effective_date
        description: ""
        quote: true

      - name: billing_account_tax_exempt_entity_use_code
        description: ""
        quote: true

      - name: billing_account_tax_exempt_expiration_date
        description: ""
        quote: true

      - name: billing_account_tax_exempt_issuing_jurisdiction
        description: ""
        quote: true

      - name: billing_account_tax_exempt_status
        description: ""
        quote: true

      - name: billing_account_total_debit_memo_balance
        description: ""
        quote: true

      - name: billing_account_total_invoice_balance
        description: ""
        quote: true

      - name: billing_account_unapplied_balance
        description: ""
        quote: true

      - name: billing_account_unapplied_credit_memo_amount
        description: ""
        quote: true

      - name: billing_account_updated_by_id
        description: ""
        quote: true

      - name: billing_account_last_updated_timestamp
        description: ""
        quote: true

      - name: billing_account_vat_id
        description: ""
        quote: true

      - name: billing_account_has_advanced_payment_manager
        description: ""
        quote: true

  - name: staging__zuora__account_current
    description: "Current view Account Object"
    quoting:
      identifier: true
    columns:
      - name: billing_account_id
        description: ""
        quote: true

      - name: crm_account_id
        description: ""
        quote: true

      - name: billing_account_parent_id
        description: ""
        quote: true

      - name: billing_account_sequence_set_id
        description: ""
        quote: true

      - name: billing_account_tax_exempt_certificate_id
        description: ""
        quote: true

      - name: bill_to_contact_id
        description: ""
        quote: true

      - name: default_payment_method_id
        description: ""
        quote: true

      - name: sold_to_contact_id
        description: ""
        quote: true

      - name: dazn_user_id
        description: ""
        quote: true

      - name: billing_account_number
        description: ""
        quote: true

      - name: billing_account_additional_email_addresses__hash
        description: ""
        quote: true

      - name: billing_account_is_allowed_invoice_edit
        description: ""
        quote: true

      - name: billing_account_is_auto_pay
        description: ""
        quote: true

      - name: billing_account_balance
        description: ""
        quote: true

      - name: billing_account_batch_name
        description: ""
        quote: true

      - name: billing_account_cycle_day_setting_option
        description: ""
        quote: true

      - name: billing_account_bill_cycle_day
        description: ""
        quote: true

      - name: billing_account_communication_profile_id
        description: ""
        quote: true

      - name: billing_account_company_name
        description: ""
        quote: true

      - name: billing_account_created_by_id
        description: ""
        quote: true

      - name: billing_account_created_timestamp
        description: ""
        quote: true

      - name: billing_account_credit_balance
        description: ""
        quote: true

      - name: billing_account_currency_code
        description: ""
        quote: true

      - name: billing_account_customer_service_rep_name
        description: ""
        quote: true

      - name: billing_account_has_invoice_delivery_preference_email
        description: ""
        quote: true

      - name: billing_account_has_invoice_delivery_preference_print
        description: ""
        quote: true

      - name: billing_account_invoice_template_id
        description: ""
        quote: true

      - name: billing_account_last_invoice_date
        description: ""
        quote: true

      - name: billing_account_monthly_recurring_revenue
        description: ""
        quote: true

      - name: billing_account_customer_full_name__hash
        description: ""
        quote: true

      - name: billing_account_notes
        description: ""
        quote: true

      - name: billing_account_payment_gateway
        description: ""
        quote: true

      - name: billing_account_payment_term
        description: ""
        quote: true

      - name: billing_account_purchase_order_number
        description: ""
        quote: true

      - name: billing_account_sales_rep_name
        description: ""
        quote: true

      - name: billing_account_status
        description: ""
        quote: true

      - name: billing_account_tax_company_code
        description: ""
        quote: true

      - name: billing_account_tax_exempt_certificate_type
        description: ""
        quote: true

      - name: billing_account_tax_exempt_description
        description: ""
        quote: true

      - name: billing_account_tax_exempt_effective_date
        description: ""
        quote: true

      - name: billing_account_tax_exempt_entity_use_code
        description: ""
        quote: true

      - name: billing_account_tax_exempt_expiration_date
        description: ""
        quote: true

      - name: billing_account_tax_exempt_issuing_jurisdiction
        description: ""
        quote: true

      - name: billing_account_tax_exempt_status
        description: ""
        quote: true

      - name: billing_account_total_debit_memo_balance
        description: ""
        quote: true

      - name: billing_account_total_invoice_balance
        description: ""
        quote: true

      - name: billing_account_unapplied_balance
        description: ""
        quote: true

      - name: billing_account_unapplied_credit_memo_amount
        description: ""
        quote: true

      - name: billing_account_updated_by_id
        description: ""
        quote: true

      - name: billing_account_last_updated_timestamp
        description: ""
        quote: true

      - name: billing_account_vat_id
        description: ""
        quote: true

      - name: billing_account_has_advanced_payment_manager
        description: ""
        quote: true

  - name: staging__zuora__rateplancharge
    description: "View of Rate Plan Charge"
    quoting:
      identifier: true
    columns:
      - name: rateplan_charge_id
        description: ""
        quote: true

      - name: rateplan_id
        description: ""
        quote: true

      - name: rateplan_charge_original_id
        description: ""
        quote: true

      - name: product_rateplan_charge_id
        description: ""
        quote: true

      - name: rateplan_charge_post_sign_up_giftcode
        description: ""
        quote: true

      - name: rateplan_charge_post_sign_up_giftcode_campaign_name
        description: ""
        quote: true

      - name: rateplan_charge_accounting_code
        description: ""
        quote: true

      - name: rateplan_charge_apply_discount_to
        description: ""
        quote: true

      - name: rateplan_charge_bill_cycle_day
        description: ""
        quote: true

      - name: rateplan_charge_bill_cycle_type
        description: ""
        quote: true

      - name: rateplan_charge_billing_period
        description: ""
        quote: true

      - name: rateplan_charge_billing_period_alignment
        description: ""
        quote: true

      - name: rateplan_charge_billing_timing
        description: ""
        quote: true

      - name: rateplan_charge_charged_through_date
        description: "To identify the next billing cycle date"
        quote: true

      - name: rateplan_charge_charge_model
        description: ""
        quote: true

      - name: rateplan_charge_charge_number
        description: ""
        quote: true

      - name: rateplan_charge_charge_type
        description: ""
        quote: true

      - name: rateplan_charge_created_by_id
        description: ""
        quote: true

      - name: rateplan_charge_created_timestamp
        description: ""
        quote: true

      - name: rateplan_charge_description
        description: ""
        quote: true

      - name: rateplan_charge_discount_level
        description: ""
        quote: true

      - name: rateplan_charge_delta_monthly_recurring_charge
        description: ""
        quote: true

      - name: rateplan_charge_delta_total_contract_value
        description: ""
        quote: true

      - name: rateplan_charge_effective_end_date
        description: ""
        quote: true

      - name: rateplan_charge_effective_start_date
        description: ""
        quote: true

      - name: rateplan_charge_end_date_condition
        description: ""
        quote: true

      - name: rateplan_charge_is_last_segment
        description: ""
        quote: true

      - name: rateplan_charge_list_price_base
        description: ""
        quote: true

      - name: rateplan_charge_monthly_recurring_revenue
        description: ""
        quote: true

      - name: rateplan_charge_name
        description: ""
        quote: true

      - name: rateplan_charge_number_of_periods
        description: ""
        quote: true

      - name: rateplan_charge_overage_calculation_option
        description: ""
        quote: true

      - name: rateplan_charge_overage_used_units_credit_option
        description: ""
        quote: true

      - name: rateplan_charge_price_change_option
        description: ""
        quote: true

      - name: rateplan_charge_prince_increase_percentage
        description: ""
        quote: true

      - name: rateplan_charge_processed_through_date
        description: ""
        quote: true

      - name: rateplan_charge_quantity
        description: ""
        quote: true

      - name: rateplan_charge_rating_group
        description: ""
        quote: true

      - name: rateplan_charge_revenue_recognition_rule_name
        description: ""
        quote: true

      - name: rateplan_charge_revenue_recognition_code
        description: ""
        quote: true

      - name: rateplan_charge_revenue_recognition_trigger_condition
        description: ""
        quote: true

      - name: rateplan_charge_segment
        description: ""
        quote: true

      - name: rateplan_charge_specific_billing_period
        description: ""
        quote: true

      - name: rateplan_charge_specific_end_date
        description: ""
        quote: true

      - name: rateplan_charge_total_contract_value
        description: ""
        quote: true

      - name: rateplan_charge_trigger_date
        description: ""
        quote: true

      - name: rateplan_charge_trigger_event
        description: ""
        quote: true

      - name: rateplan_charge_unit_of_measure
        description: ""
        quote: true

      - name: rateplan_charge_updated_by_id
        description: ""
        quote: true

      - name: rateplan_charge_updated_timestamp
        description: ""
        quote: true

      - name: rateplan_charge_up_to_periods
        description: ""
        quote: true

      - name: rateplan_charge_up_to_periods_type
        description: ""
        quote: true

      - name: rateplan_charge_version
        description: ""
        quote: true

      - name: rateplan_charge_weekly_bill_cycle_day
        description: ""
        quote: true

  - name: staging__zuora__rateplancharge_current
    description: "Current view of Rate Plan Charge"
    quoting:
      identifier: true
    columns:
      - name: rateplan_charge_id
        description: ""
        quote: true

      - name: rateplan_id
        description: ""
        quote: true

      - name: rateplan_charge_original_id
        description: ""
        quote: true

      - name: product_rateplan_charge_id
        description: ""
        quote: true

      - name: rateplan_charge_post_sign_up_giftcode
        description: ""
        quote: true

      - name: rateplan_charge_post_sign_up_giftcode_campaign_name
        description: ""
        quote: true

      - name: rateplan_charge_accounting_code
        description: ""
        quote: true

      - name: rateplan_charge_apply_discount_to
        description: ""
        quote: true

      - name: rateplan_charge_bill_cycle_day
        description: ""
        quote: true

      - name: rateplan_charge_bill_cycle_type
        description: ""
        quote: true

      - name: rateplan_charge_billing_period
        description: ""
        quote: true

      - name: rateplan_charge_billing_period_alignment
        description: ""
        quote: true

      - name: rateplan_charge_billing_timing
        description: ""
        quote: true

      - name: rateplan_charge_charged_through_date
        description: ""
        quote: true

      - name: rateplan_charge_charge_model
        description: ""
        quote: true

      - name: rateplan_charge_charge_number
        description: ""
        quote: true

      - name: rateplan_charge_charge_type
        description: ""
        quote: true

      - name: rateplan_charge_created_by_id
        description: ""
        quote: true

      - name: rateplan_charge_created_timestamp
        description: ""
        quote: true

      - name: rateplan_charge_description
        description: ""
        quote: true

      - name: rateplan_charge_discount_level
        description: ""
        quote: true

      - name: rateplan_charge_delta_monthly_recurring_charge
        description: ""
        quote: true

      - name: rateplan_charge_delta_total_contract_value
        description: ""
        quote: true

      - name: rateplan_charge_effective_end_date
        description: ""
        quote: true

      - name: rateplan_charge_effective_start_date
        description: ""
        quote: true

      - name: rateplan_charge_end_date_condition
        description: ""
        quote: true

      - name: rateplan_charge_is_last_segment
        description: ""
        quote: true

      - name: rateplan_charge_list_price_base
        description: ""
        quote: true

      - name: rateplan_charge_monthly_recurring_revenue
        description: ""
        quote: true

      - name: rateplan_charge_name
        description: ""
        quote: true

      - name: rateplan_charge_number_of_periods
        description: ""
        quote: true

      - name: rateplan_charge_overage_calculation_option
        description: ""
        quote: true

      - name: rateplan_charge_overage_used_units_credit_option
        description: ""
        quote: true

      - name: rateplan_charge_price_change_option
        description: ""
        quote: true

      - name: rateplan_charge_prince_increase_percentage
        description: ""
        quote: true

      - name: rateplan_charge_processed_through_date
        description: ""
        quote: true

      - name: rateplan_charge_quantity
        description: ""
        quote: true

      - name: rateplan_charge_rating_group
        description: ""
        quote: true

      - name: rateplan_charge_revenue_recognition_rule_name
        description: ""
        quote: true

      - name: rateplan_charge_revenue_recognition_code
        description: ""
        quote: true

      - name: rateplan_charge_revenue_recognition_trigger_condition
        description: ""
        quote: true

      - name: rateplan_charge_segment
        description: ""
        quote: true

      - name: rateplan_charge_specific_billing_period
        description: ""
        quote: true

      - name: rateplan_charge_specific_end_date
        description: ""
        quote: true

      - name: rateplan_charge_total_contract_value
        description: ""
        quote: true

      - name: rateplan_charge_trigger_date
        description: ""
        quote: true

      - name: rateplan_charge_trigger_event
        description: ""
        quote: true

      - name: rateplan_charge_unit_of_measure
        description: ""
        quote: true

      - name: rateplan_charge_updated_by_id
        description: ""
        quote: true

      - name: rateplan_charge_updated_timestamp
        description: ""
        quote: true

      - name: rateplan_charge_up_to_periods
        description: ""
        quote: true

      - name: rateplan_charge_up_to_periods_type
        description: ""
        quote: true

      - name: rateplan_charge_version
        description: ""
        quote: true

      - name: rateplan_charge_weekly_bill_cycle_day
        description: ""
        quote: true

  - name: staging__zuora__invoice
    description: "View of Zuora Invoice"
    quoting:
      identifier: true
    columns:
      - name: invoice_id
        description: ""
        quote: true

      - name: billing_account_id
        description: ""
        quote: true

      - name: invoice_source_id
        description: ""
        quote: true

      - name: bill_to_contact_snapshot_id
        description: ""
        quote: true

      - name: sold_to_contact_snapshot_id
        description: ""
        quote: true

      - name: invoice_adjustment_amount
        description: ""
        quote: true

      - name: invoice_amount
        description: ""
        quote: true

      - name: invoice_amount_without_tax
        description: ""
        quote: true

      - name: invoice_is_auto_pay
        description: ""
        quote: true

      - name: invoice_balance
        description: ""
        quote: true

      - name: invoice_comments
        description: ""
        quote: true

      - name: invoice_created_by_id
        description: ""
        quote: true

      - name: invoice_created_timestamp
        description: ""
        quote: true

      - name: invoice_credit_balanced_adjustment_amount
        description: ""
        quote: true

      - name: invoice_due_date
        description: ""
        quote: true

      - name: invoice_includes_one_time
        description: ""
        quote: true

      - name: invoice_includes_recurring
        description: ""
        quote: true

      - name: invoice_inclues_usage
        description: ""
        quote: true

      - name: invoice_date
        description: ""
        quote: true

      - name: invoice_number
        description: ""
        quote: true

      - name: invoice_last_email_sent_date
        description: ""
        quote: true

      - name: invoice_payment_amount
        description: ""
        quote: true

      - name: invoice_posted_by
        description: ""
        quote: true

      - name: invoice_posted_timestamp
        description: ""
        quote: true

      - name: invoice_refund_amount
        description: ""
        quote: true

      - name: invoice_is_reversed
        description: ""
        quote: true

      - name: invoice_source
        description: ""
        quote: true

      - name: invoice_status
        description: ""
        quote: true

      - name: invoice_target_date
        description: ""
        quote: true

      - name: invoice_tax_amount
        description: ""
        quote: true

      - name: invoice_tax_exempt_amount
        description: ""
        quote: true

      - name: invoice_transferred_to_accounting
        description: ""
        quote: true

      - name: invoice_updated_by_id
        description: ""
        quote: true

      - name: invoice_updated_timestamp
        description: ""
        quote: true

      - name: invoice_non_cpr_retry_status
        description: "The status of collecting the payment for this invoice. This can be one of the following values: Success, InRetry, Failed or null. Status will be failed when the maximum number of retries is reached"
        quote: true

      - name: invoice_non_cpr_retry_attempt_count
        description: "Number of attempts made to collect payment"
        quote: true

      - name: invoice_non_cpr_next_retry_date
        description: "Date when the next payment collection attempt is due for this invoice."
        quote: true

      - name: invoice_retry_status
        description: "The status of collecting the payment for this invoice. This can be one of the following values: Success, InRetry, Failed or null. Status will be failed when the maximum number of retries is reached"
        quote: true

  - name: staging__zuora__invoice_current
    description: "Current view of Zuora Invoice"
    quoting:
      identifier: true
    columns:
      - name: invoice_id
        description: ""
        quote: true

      - name: billing_account_id
        description: ""
        quote: true

      - name: invoice_source_id
        description: ""
        quote: true

      - name: bill_to_contact_snapshot_id
        description: ""
        quote: true

      - name: sold_to_contact_snapshot_id
        description: ""
        quote: true

      - name: invoice_adjustment_amount
        description: ""
        quote: true

      - name: invoice_amount
        description: ""
        quote: true

      - name: invoice_amount_without_tax
        description: ""
        quote: true

      - name: invoice_is_auto_pay
        description: ""
        quote: true

      - name: invoice_balance
        description: ""
        quote: true

      - name: invoice_comments
        description: ""
        quote: true

      - name: invoice_created_by_id
        description: ""
        quote: true

      - name: invoice_created_timestamp
        description: ""
        quote: true

      - name: invoice_credit_balanced_adjustment_amount
        description: ""
        quote: true

      - name: invoice_due_date
        description: ""
        quote: true

      - name: invoice_includes_one_time
        description: ""
        quote: true

      - name: invoice_includes_recurring
        description: ""
        quote: true

      - name: invoice_inclues_usage
        description: ""
        quote: true

      - name: invoice_date
        description: ""
        quote: true

      - name: invoice_number
        description: ""
        quote: true

      - name: invoice_last_email_sent_date
        description: ""
        quote: true

      - name: invoice_payment_amount
        description: ""
        quote: true

      - name: invoice_posted_by
        description: ""
        quote: true

      - name: invoice_posted_timestamp
        description: ""
        quote: true

      - name: invoice_refund_amount
        description: ""
        quote: true

      - name: invoice_is_reversed
        description: ""
        quote: true

      - name: invoice_source
        description: ""
        quote: true

      - name: invoice_status
        description: ""
        quote: true

      - name: invoice_target_date
        description: ""
        quote: true

      - name: invoice_tax_amount
        description: ""
        quote: true

      - name: invoice_tax_exempt_amount
        description: ""
        quote: true

      - name: invoice_transferred_to_accounting
        description: ""
        quote: true

      - name: invoice_updated_by_id
        description: ""
        quote: true

      - name: invoice_updated_timestamp
        description: ""
        quote: true

      - name: invoice_non_cpr_retry_status
        description: "The status of collecting the payment for this invoice. This can be one of the following values: Success, InRetry, Failed or null. Status will be failed when the maximum number of retries is reached"
        quote: true

      - name: invoice_non_cpr_retry_attempt_count
        description: "Number of attempts made to collect payment"
        quote: true

      - name: invoice_non_cpr_next_retry_date
        description: "Date when the next payment collection attempt is due for this invoice."
        quote: true

      - name: invoice_retry_status
        description: "The status of collecting the payment for this invoice. This can be one of the following values: Success, InRetry, Failed or null. Status will be failed when the maximum number of retries is reached"
        quote: true

  - name: staging__zuora__invoice_item
    description: "View of Zuora Invoice Item"
    quoting:
      identifier: true
    columns:
      - name: invoice_item_id
        description: ""
        quote: true

      - name: invoice_item_addon_one_time_subscription_id
        description: ""
        quote: true

      - name: invoice_item_addon_one_time_product_rateplan_charge_id
        description: ""
        quote: true

      - name: invoice_item_applied_to_invoice_item_id
        description: ""
        quote: true

      - name: subscription_id
        description: ""
        quote: true

      - name: rateplan_charge_id
        description: ""
        quote: true

      - name: subscription_number
        description: ""
        quote: true

      - name: invoice_id
        description: ""
        quote: true

      - name: invoice_item_accounting_code
        description: ""
        quote: true

      - name: invoice_item_balance
        description: ""
        quote: true

      - name: invoice_item_charge_amount
        description: ""
        quote: true

      - name: invoice_item_charge_timestamp
        description: ""
        quote: true

      - name: invoice_item_charge_name
        description: ""
        quote: true

      - name: invoice_item_created_by_id
        description: ""
        quote: true

      - name: invoice_item_created_timestamp
        description: ""
        quote: true

      - name: invoice_item_processing_type
        description: ""
        quote: true

      - name: invoice_item_quantity
        description: ""
        quote: true

      - name: invoice_item_revenue_recognition_start_date
        description: ""
        quote: true

      - name: invoice_item_service_end_date
        description: ""
        quote: true

      - name: invoice_item_service_start_date
        description: ""
        quote: true

      - name: invoice_item_sku
        description: ""
        quote: true

      - name: invoice_item_tax_amount
        description: ""
        quote: true

      - name: invoice_item_tax_code
        description: ""
        quote: true

      - name: invoice_item_tax_exempt_amount
        description: ""
        quote: true

      - name: invoice_item_tax_mode
        description: ""
        quote: true

      - name: invoice_item_unit_price
        description: ""
        quote: true

      - name: invoice_item_unit_of_measure
        description: ""
        quote: true

      - name: invoice_item_updated_by_id
        description: ""
        quote: true

      - name: invoice_item_updated_timestamp
        description: ""
        quote: true

      - name: invoice_item_source_item_type
        description: "source of inovice item type"
        quote: true

      - name: invoice_item_order_line_item_id
        description: "Id of OrderLineItem"
        quote: true

  - name: staging__zuora__invoice_item_current
    description: "Current view of Zuora Invoice"
    quoting:
      identifier: true
    columns:
      - name: invoice_item_id
        description: ""
        quote: true

      - name: invoice_item_addon_one_time_subscription_id
        description: ""
        quote: true

      - name: invoice_item_addon_one_time_product_rateplan_charge_id
        description: ""
        quote: true

      - name: invoice_item_applied_to_invoice_item_id
        description: ""
        quote: true

      - name: subscription_id
        description: ""
        quote: true

      - name: rateplan_charge_id
        description: ""
        quote: true

      - name: subscription_number
        description: ""
        quote: true

      - name: invoice_id
        description: ""
        quote: true

      - name: invoice_item_accounting_code
        description: ""
        quote: true

      - name: invoice_item_balance
        description: ""
        quote: true

      - name: invoice_item_charge_amount
        description: ""
        quote: true

      - name: invoice_item_charge_timestamp
        description: ""
        quote: true

      - name: invoice_item_charge_name
        description: ""
        quote: true

      - name: invoice_item_created_by_id
        description: ""
        quote: true

      - name: invoice_item_created_timestamp
        description: ""
        quote: true

      - name: invoice_item_processing_type
        description: ""
        quote: true

      - name: invoice_item_quantity
        description: ""
        quote: true

      - name: invoice_item_revenue_recognition_start_date
        description: ""
        quote: true

      - name: invoice_item_service_end_date
        description: ""
        quote: true

      - name: invoice_item_service_start_date
        description: ""
        quote: true

      - name: invoice_item_sku
        description: ""
        quote: true

      - name: invoice_item_tax_amount
        description: ""
        quote: true

      - name: invoice_item_tax_code
        description: ""
        quote: true

      - name: invoice_item_tax_exempt_amount
        description: ""
        quote: true

      - name: invoice_item_tax_mode
        description: ""
        quote: true

      - name: invoice_item_unit_price
        description: ""
        quote: true

      - name: invoice_item_unit_of_measure
        description: ""
        quote: true

      - name: invoice_item_updated_by_id
        description: ""
        quote: true

      - name: invoice_item_updated_timestamp
        description: ""
        quote: true

      - name: invoice_item_source_item_type
        description: "source of inovice item type"
        quote: true

      - name: invoice_item_order_line_item_id
        description: "Id of OrderLineItem"
        quote: true

  - name: staging__zuora__rateplan
    description: "Staging data of the Open Zuora RatePlan table"
    quoting:
      identifier: true
    columns:
      - name: rateplan_id
        description: ""
        quote: true

      - name: subscription_id
        description: ""
        quote: true

      - name: entitlement_set_id
        description: ""
        quote: true

      - name: amendment_id
        description: ""
        quote: true

      - name: product_rateplan_id
        description: ""
        quote: true

      - name: rateplan_tracking_id
        description: ""
        quote: true

      - name: rateplan_source_system_user_id
        description: ""
        quote: true

      - name: rateplan_source_system_name
        description: ""
        quote: true

      - name: rateplan_amendment_type
        description: ""
        quote: true

      - name: rateplan_created_by_id
        description: ""
        quote: true

      - name: rateplan_created_timestamp
        description: ""
        quote: true

      - name: rateplan_name
        description: ""
        quote: true

      - name: rateplan_updated_by_id
        description: ""
        quote: true

      - name: rateplan_updated_timestamp
        description: ""
        quote: true

      - name: rateplan_billing_type
        description: "The billing type associated with this rateplan. Can be one of the following: Recurring, RecurringExternal or OneOff"
        quote: true

      - name: rateplan_partner_id
        description: "The partner identifier for the Addon. For example, SkyIT"
        quote: true

      - name: rateplan_partner_user_id
        description: "The user identifier for the Addon. For example, sky_user_1245612437"
        quote: true

      - name: payment_gateway
        description: "Name of the gateway instance that processes the payment"
        quote: true

      - name: payment_method_id
        description: "The ID of the payment method used by the customer at the time of the addon purchase"
        quote: true

      - name: rateplan_product_type
        description: "The product type associated with this rateplan. Can be one of the following: subscription, addon, ppv or null"
        quote: true

      - name: rateplan_next_invoice_date
        description: "This field will be populated for the first time when the initial amendment is submitted. It will be equal to the contract effective date + 1 month. After then it will be set to the next month."
        quote: true

      - name: rateplan_context
        description: ""
        quote: true

      - name: rateplan_segment
        description: ""
        quote: true

      - name: rateplan_addon_type
        description: "This field identifies the add-on type, the viewership on multiple screens for the same ip."
        quote: true

      - name: rateplan_platform
        description: "TBC"
        quote: true

      - name: is_3pp_exit_flow
        description: "TBC"
        quote: true

  - name: staging__zuora__rateplan_current
    description: "Current view of Rate Plan object"
    quoting:
      identifier: true
    columns:
      - name: rateplan_id
        description: ""
        quote: true

      - name: subscription_id
        description: ""
        quote: true

      - name: entitlement_set_id
        description: ""
        quote: true

      - name: amendment_id
        description: ""
        quote: true

      - name: product_rateplan_id
        description: ""
        quote: true

      - name: rateplan_tracking_id
        description: ""
        quote: true

      - name: rateplan_source_system_user_id
        description: ""
        quote: true

      - name: rateplan_source_system_name
        description: ""
        quote: true

      - name: rateplan_amendment_type
        description: ""
        quote: true

      - name: rateplan_created_by_id
        description: ""
        quote: true

      - name: rateplan_created_timestamp
        description: ""
        quote: true

      - name: rateplan_name
        description: ""
        quote: true

      - name: rateplan_updated_by_id
        description: ""
        quote: true

      - name: rateplan_updated_timestamp
        description: ""
        quote: true

      - name: rateplan_billing_type
        description: "The billing type associated with this rateplan. Can be one of the following: Recurring, RecurringExternal or OneOff"
        quote: true

      - name: rateplan_partner_id
        description: "The partner identifier for the Addon. For example, SkyIT"
        quote: true

      - name: rateplan_partner_user_id
        description: "The user identifier for the Addon. For example, sky_user_1245612437"
        quote: true

      - name: payment_gateway
        description: "Name of the gateway instance that processes the payment"
        quote: true

      - name: payment_method_id
        description: "The ID of the payment method used by the customer at the time of the addon purchase"
        quote: true

      - name: rateplan_product_type
        description: "The product type associated with this rateplan. Can be one of the following: subscription, addon, ppv or null"
        quote: true

      - name: rateplan_next_invoice_date
        description: "This field will be populated for the first time when the initial amendment is submitted. It will be equal to the contract effective date + 1 month. After then it will be set to the next month."
        quote: true

      - name: rateplan_context
        description: "Describes the channel through which the discount was applied - CRM, in app, cancellation flow or via a customer service agent"
        quote: true

      - name: rateplan_segment
        description: ""
        quote: true

      - name: rateplan_addon_type
        description: "This field identifies the add-on type, the viewership on multiple screens for the same ip."
        quote: true

      - name: rateplan_platform
        description: "TBC"
        quote: true

      - name: is_3pp_exit_flow
        description: "TBC"
        quote: true

  - name: staging__zuora__rateplanchargetier
    description: "Staging data of the Open Zuora RatePlanChargeTier table"
    columns:
      - name: rateplan_charge_tier_id
        description: ""
        quote: true

      - name: rateplan_charge_id
        description: ""
        quote: true

      - name: rateplan_charge_tier_created_by_id
        description: ""
        quote: true

      - name: rateplan_charge_tier_created_date
        description: ""
        quote: true

      - name: currency_code
        description: ""
        quote: true

      - name: rateplan_charge_tier_discount_amount
        description: ""
        quote: true

      - name: rateplan_charge_tier_discount_percentage
        description: ""
        quote: true

      - name: rateplan_charge_tier_rateplan_charge_tier_ending_unit
        description: ""
        quote: true

      - name: rateplan_charge_tier_included_units
        description: ""
        quote: true

      - name: rateplan_charge_tier_overage_price
        description: ""
        quote: true

      - name: rateplan_charge_tier_price
        description: ""
        quote: true

      - name: rateplan_charge_tier_price_format
        description: ""
        quote: true

      - name: rateplan_charge_tier_starting_unit
        description: ""
        quote: true

      - name: rateplan_charge_tier_tier
        description: ""
        quote: true

      - name: rateplan_charge_tier_updated_by_id
        description: ""
        quote: true

      - name: rateplan_charge_tier_updated_date
        description: ""
        quote: true

  - name: staging__zuora__payment
    description: ""
    columns:
      - name: payment_id
        description: ""
        quote: true

      - name: billing_account_id
        description: ""
        quote: true

      - name: payment_method_id
        description: ""
        quote: true

      - name: payment_method_snapshot_id
        description: ""
        quote: true

      - name: payment_accounting_code
        description: ""
        quote: true

      - name: payment_amount
        description: ""
        quote: true

      - name: payment_applied_amount
        description: ""
        quote: true

      - name: payment_applied_credit_balance_amount
        description: ""
        quote: true

      - name: payment_auth_transaction_id
        description: ""
        quote: true

      - name: payment_bank_identification_number
        description: ""
        quote: true

      - name: payment_cancelled_on
        description: ""
        quote: true

      - name: payment_comment
        description: ""
        quote: true

      - name: payment_created_by_id
        description: ""
        quote: true

      - name: payment_created_timestamp
        description: ""
        quote: true

      - name: payment_currency
        description: ""
        quote: true

      - name: payment_effective_timestamp
        description: ""
        quote: true

      - name: payment_gateway
        description: ""
        quote: true

      - name: payment_gateway_order_id
        description: ""
        quote: true

      - name: payment_gateway_response
        description: ""
        quote: true

      - name: payment_gateway_response_code
        description: ""
        quote: true

      - name: payment_gateway_state
        description: ""
        quote: true

      - name: payment_marked_for_submission_on
        description: ""
        quote: true

      - name: payment_number
        description: ""
        quote: true

      - name: payment_payment_source
        description: ""
        quote: true

      - name: payment_reference_payment_id
        description: ""
        quote: true

      - name: payment_reference_id
        description: ""
        quote: true

      - name: payment_refund_amount
        description: ""
        quote: true

      - name: payment_second_payment_reference_id
        description: ""
        quote: true

      - name: payment_settled_on
        description: ""
        quote: true

      - name: payment_soft_descriptor
        description: ""
        quote: true

      - name: payment_soft_descriptor_phone
        description: ""
        quote: true

      - name: payment_source
        description: ""
        quote: true

      - name: payment_source_name
        description: ""
        quote: true

      - name: payment_status
        description: ""
        quote: true

      - name: payment_submitted_on
        description: ""
        quote: true

      - name: payment_transferred_to_accounting
        description: ""
        quote: true

      - name: payment_type
        description: ""
        quote: true

      - name: payment_unapplied_amount
        description: ""
        quote: true

      - name: payment_updated_by_id
        description: ""
        quote: true

      - name: payment_updated_timestamp
        description: ""
        quote: true

  - name: staging__zuora__payment_current
    description: "Staging data of the Open Zuora RatePlanChargeTier table"
    columns:
      - name: payment_id
        description: ""
        quote: true

      - name: billing_account_id
        description: ""
        quote: true

      - name: payment_method_id
        description: ""
        quote: true

      - name: payment_method_snapshot_id
        description: ""
        quote: true

      - name: payment_accounting_code
        description: ""
        quote: true

      - name: payment_amount
        description: ""
        quote: true

      - name: payment_applied_amount
        description: ""
        quote: true

      - name: payment_applied_credit_balance_amount
        description: ""
        quote: true

      - name: payment_auth_transaction_id
        description: ""
        quote: true

      - name: payment_bank_identification_number
        description: ""
        quote: true

      - name: payment_cancelled_on
        description: ""
        quote: true

      - name: payment_comment
        description: ""
        quote: true

      - name: payment_created_by_id
        description: ""
        quote: true

      - name: payment_created_timestamp
        description: ""
        quote: true

      - name: payment_currency
        description: ""
        quote: true

      - name: payment_effective_timestamp
        description: ""
        quote: true

      - name: payment_gateway
        description: ""
        quote: true

      - name: payment_gateway_order_id
        description: ""
        quote: true

      - name: payment_gateway_response
        description: ""
        quote: true

      - name: payment_gateway_response_code
        description: ""
        quote: true

      - name: payment_gateway_state
        description: ""
        quote: true

      - name: payment_marked_for_submission_on
        description: ""
        quote: true

      - name: payment_number
        description: ""
        quote: true

      - name: payment_payment_source
        description: ""
        quote: true

      - name: payment_reference_payment_id
        description: ""
        quote: true

      - name: payment_reference_id
        description: ""
        quote: true

      - name: payment_refund_amount
        description: ""
        quote: true

      - name: payment_second_payment_reference_id
        description: ""
        quote: true

      - name: payment_settled_on
        description: ""
        quote: true

      - name: payment_soft_descriptor
        description: ""
        quote: true

      - name: payment_soft_descriptor_phone
        description: ""
        quote: true

      - name: payment_source
        description: ""
        quote: true

      - name: payment_source_name
        description: ""
        quote: true

      - name: payment_status
        description: ""
        quote: true

      - name: payment_submitted_on
        description: ""
        quote: true

      - name: payment_transferred_to_accounting
        description: ""
        quote: true

      - name: payment_type
        description: ""
        quote: true

      - name: payment_unapplied_amount
        description: ""
        quote: true

      - name: payment_updated_by_id
        description: ""
        quote: true

      - name: payment_updated_timestamp
        description: ""
        quote: true

  - name: staging__zuora__invoice_payment
    description: ""
    columns:
      - name: invoice_payment_id
        description: ""
        quote: true

      - name: invoice_id
        description: ""
        quote: true

      - name: payment_id
        description: ""
        quote: true

      - name: invoice_payment_amount
        description: ""
        quote: true

      - name: invoice_payment_created_by_id
        description: ""
        quote: true

      - name: invoice_payment_created_timestamp
        description: ""
        quote: true

      - name: invoice_payment_refund_amount
        description: ""
        quote: true

      - name: invoice_payment_updated_by_id
        description: ""
        quote: true

      - name: invoice_payment_updated_timestamp
        description: ""
        quote: true

  - name: staging__zuora__invoice_payment_current
    description: ""
    columns:
      - name: invoice_payment_id
        description: ""
        quote: true

      - name: invoice_id
        description: ""
        quote: true

      - name: payment_id
        description: ""
        quote: true

      - name: invoice_payment_amount
        description: ""
        quote: true

      - name: invoice_payment_created_by_id
        description: ""
        quote: true

      - name: invoice_payment_created_timestamp
        description: ""
        quote: true

      - name: invoice_payment_refund_amount
        description: ""
        quote: true

      - name: invoice_payment_updated_by_id
        description: ""
        quote: true

      - name: invoice_payment_updated_timestamp
        description: ""
        quote: true

  - name: staging__zuora__payment_method
    description: ""
    columns:
      - name: payment_method_id
        description: ""
        quote: true

      - name: billing_account_id
        description: ""
        quote: true

      - name: payment_method_ach_aba_code
        description: ""
        quote: true

      - name: payment_method_ach_account_name
        description: ""
        quote: true

      - name: payment_method_ach_account_number_mask
        description: ""
        quote: true

      - name: payment_method_ach_account_type
        description: ""
        quote: true

      - name: payment_method_ach_address_1
        description: ""
        quote: true

      - name: payment_method_ach_address_2
        description: ""
        quote: true

      - name: payment_method_ach_bank_name
        description: ""
        quote: true

      - name: payment_method_ach_city
        description: ""
        quote: true

      - name: payment_method_ach_country
        description: ""
        quote: true

      - name: payment_method_ach_postal_code
        description: ""
        quote: true

      - name: payment_method_ach_state
        description: ""
        quote: true

      - name: payment_method_active
        description: ""
        quote: true

      - name: payment_method_bank_branch_code
        description: ""
        quote: true

      - name: payment_method_bank_check_digit
        description: ""
        quote: true

      - name: payment_method_bank_city
        description: ""
        quote: true

      - name: payment_method_bank_code
        description: ""
        quote: true

      - name: payment_method_bank_identification_number
        description: ""
        quote: true

      - name: payment_method_bank_name
        description: ""
        quote: true

      - name: payment_method_bank_postal_code
        description: ""
        quote: true

      - name: payment_method_bank_street_name
        description: ""
        quote: true

      - name: payment_method_bank_street_number
        description: ""
        quote: true

      - name: payment_method_bank_transfer_account_name
        description: ""
        quote: true

      - name: payment_method_bank_transfer_account_number_mask
        description: ""
        quote: true

      - name: payment_method_bank_transfer_account_type
        description: ""
        quote: true

      - name: payment_method_bank_transfer_type
        description: ""
        quote: true

      - name: payment_method_business_identification_code
        description: ""
        quote: true

      - name: payment_method_city
        description: ""
        quote: true

      - name: payment_method_company_name
        description: ""
        quote: true

      - name: payment_method_country
        description: ""
        quote: true

      - name: payment_method_created_by_id
        description: ""
        quote: true

      - name: payment_method_created_timestamp
        description: ""
        quote: true

      - name: payment_method_credit_card_address_1
        description: ""
        quote: true

      - name: payment_method_credit_card_address_2
        description: ""
        quote: true

      - name: payment_method_credit_card_city
        description: ""
        quote: true

      - name: payment_method_credit_card_country
        description: ""
        quote: true

      - name: payment_method_credit_card_expiration_month
        description: ""
        quote: true

      - name: payment_method_credit_card_expiration_year
        description: ""
        quote: true

      - name: payment_method_credit_card_holder_name
        description: ""
        quote: true

      - name: payment_method_credit_card_mask_number
        description: ""
        quote: true

      - name: payment_method_credit_card_postal_code
        description: ""
        quote: true

      - name: payment_method_credit_card_state
        description: ""
        quote: true

      - name: payment_method_credit_card_type
        description: ""
        quote: true

      - name: payment_method_device_session_id
        description: ""
        quote: true

      - name: payment_method_email
        description: ""
        quote: true

      - name: payment_method_existing_mandate
        description: ""
        quote: true

      - name: payment_method_first_name
        description: ""
        quote: true

      - name: payment_method_iban
        description: ""
        quote: true

      - name: payment_method_identity_number
        description: ""
        quote: true

      - name: payment_method_ip_address
        description: ""
        quote: true

      - name: payment_method_is_company
        description: ""
        quote: true

      - name: payment_method_last_failed_sale_transaction_timestamp
        description: ""
        quote: true

      - name: payment_method_last_name
        description: ""
        quote: true

      - name: payment_method_last_transaction_timestamp
        description: ""
        quote: true

      - name: payment_method_last_transaction_status
        description: ""
        quote: true

      - name: payment_method_mandate_creation_timestamp
        description: ""
        quote: true

      - name: payment_method_mandate_id
        description: ""
        quote: true

      - name: payment_method_mandate_received
        description: ""
        quote: true

      - name: payment_method_mandate_update_timestamp
        description: ""
        quote: true

      - name: payment_method_max_consecutive_payment_failures
        description: ""
        quote: true

      - name: payment_method_name
        description: ""
        quote: true

      - name: payment_method_num_consecutive_failures
        description: ""
        quote: true

      - name: payment_method_payment_method_status
        description: ""
        quote: true

      - name: payment_method_payment_retry_window
        description: ""
        quote: true

      - name: payment_method_paypal_baid
        description: ""
        quote: true

      - name: payment_method_paypal_email
        description: ""
        quote: true

      - name: payment_method_paypal_preapproval_key
        description: ""
        quote: true

      - name: payment_method_paypal_type
        description: ""
        quote: true

      - name: payment_method_phone
        description: ""
        quote: true

      - name: payment_method_postal_code
        description: ""
        quote: true

      - name: payment_method_second_token_id
        description: ""
        quote: true

      - name: payment_method_state
        description: ""
        quote: true

      - name: payment_method_street_name
        description: ""
        quote: true

      - name: payment_method_street_number
        description: ""
        quote: true

      - name: payment_method_tmx_id
        description: ""
        quote: true

      - name: payment_method_token_id
        description: ""
        quote: true

      - name: payment_method_total_number_of_error_payments
        description: ""
        quote: true

      - name: payment_method_total_number_of_processed_payments
        description: ""
        quote: true

      - name: payment_method_type
        description: ""
        quote: true

      - name: payment_method_updated_by_id
        description: ""
        quote: true

      - name: payment_method_updated_timestamp
        description: ""
        quote: true

      - name: payment_method_use_default_retry_rule
        description: ""
        quote: true

      - name: payment_method_actual_payment_method
        description: ""
        quote: true

  - name: staging__zuora__payment_method_current
    description: ""
    columns:
      - name: payment_method_id
        description: ""
        quote: true

      - name: billing_account_id
        description: ""
        quote: true

      - name: payment_method_ach_aba_code
        description: ""
        quote: true

      - name: payment_method_ach_account_name
        description: ""
        quote: true

      - name: payment_method_ach_account_number_mask
        description: ""
        quote: true

      - name: payment_method_ach_account_type
        description: ""
        quote: true

      - name: payment_method_ach_address_1
        description: ""
        quote: true

      - name: payment_method_ach_address_2
        description: ""
        quote: true

      - name: payment_method_ach_bank_name
        description: ""
        quote: true

      - name: payment_method_ach_city
        description: ""
        quote: true

      - name: payment_method_ach_country
        description: ""
        quote: true

      - name: payment_method_ach_postal_code
        description: ""
        quote: true

      - name: payment_method_ach_state
        description: ""
        quote: true

      - name: payment_method_active
        description: ""
        quote: true

      - name: payment_method_bank_branch_code
        description: ""
        quote: true

      - name: payment_method_bank_check_digit
        description: ""
        quote: true

      - name: payment_method_bank_city
        description: ""
        quote: true

      - name: payment_method_bank_code
        description: ""
        quote: true

      - name: payment_method_bank_identification_number
        description: ""
        quote: true

      - name: payment_method_bank_name
        description: ""
        quote: true

      - name: payment_method_bank_postal_code
        description: ""
        quote: true

      - name: payment_method_bank_street_name
        description: ""
        quote: true

      - name: payment_method_bank_street_number
        description: ""
        quote: true

      - name: payment_method_bank_transfer_account_name
        description: ""
        quote: true

      - name: payment_method_bank_transfer_account_number_mask
        description: ""
        quote: true

      - name: payment_method_bank_transfer_account_type
        description: ""
        quote: true

      - name: payment_method_bank_transfer_type
        description: ""
        quote: true

      - name: payment_method_business_identification_code
        description: ""
        quote: true

      - name: payment_method_city
        description: ""
        quote: true

      - name: payment_method_company_name
        description: ""
        quote: true

      - name: payment_method_country
        description: ""
        quote: true

      - name: payment_method_created_by_id
        description: ""
        quote: true

      - name: payment_method_created_timestamp
        description: ""
        quote: true

      - name: payment_method_credit_card_address_1
        description: ""
        quote: true

      - name: payment_method_credit_card_address_2
        description: ""
        quote: true

      - name: payment_method_credit_card_city
        description: ""
        quote: true

      - name: payment_method_credit_card_country
        description: ""
        quote: true

      - name: payment_method_credit_card_expiration_month
        description: ""
        quote: true

      - name: payment_method_credit_card_expiration_year
        description: ""
        quote: true

      - name: payment_method_credit_card_holder_name
        description: ""
        quote: true

      - name: payment_method_credit_card_mask_number
        description: ""
        quote: true

      - name: payment_method_credit_card_postal_code
        description: ""
        quote: true

      - name: payment_method_credit_card_state
        description: ""
        quote: true

      - name: payment_method_credit_card_type
        description: ""
        quote: true

      - name: payment_method_device_session_id
        description: ""
        quote: true

      - name: payment_method_email
        description: ""
        quote: true

      - name: payment_method_existing_mandate
        description: ""
        quote: true

      - name: payment_method_first_name
        description: ""
        quote: true

      - name: payment_method_iban
        description: ""
        quote: true

      - name: payment_method_identity_number
        description: ""
        quote: true

      - name: payment_method_ip_address
        description: ""
        quote: true

      - name: payment_method_is_company
        description: ""
        quote: true

      - name: payment_method_last_failed_sale_transaction_timestamp
        description: ""
        quote: true

      - name: payment_method_last_name
        description: ""
        quote: true

      - name: payment_method_last_transaction_timestamp
        description: ""
        quote: true

      - name: payment_method_last_transaction_status
        description: ""
        quote: true

      - name: payment_method_mandate_creation_timestamp
        description: ""
        quote: true

      - name: payment_method_mandate_id
        description: ""
        quote: true

      - name: payment_method_mandate_received
        description: ""
        quote: true

      - name: payment_method_mandate_update_timestamp
        description: ""
        quote: true

      - name: payment_method_max_consecutive_payment_failures
        description: ""
        quote: true

      - name: payment_method_name
        description: ""
        quote: true

      - name: payment_method_num_consecutive_failures
        description: ""
        quote: true

      - name: payment_method_payment_method_status
        description: ""
        quote: true

      - name: payment_method_payment_retry_window
        description: ""
        quote: true

      - name: payment_method_paypal_baid
        description: ""
        quote: true

      - name: payment_method_paypal_email
        description: ""
        quote: true

      - name: payment_method_paypal_preapproval_key
        description: ""
        quote: true

      - name: payment_method_paypal_type
        description: ""
        quote: true

      - name: payment_method_phone
        description: ""
        quote: true

      - name: payment_method_postal_code
        description: ""
        quote: true

      - name: payment_method_second_token_id
        description: ""
        quote: true

      - name: payment_method_state
        description: ""
        quote: true

      - name: payment_method_street_name
        description: ""
        quote: true

      - name: payment_method_street_number
        description: ""
        quote: true

      - name: payment_method_tmx_id
        description: ""
        quote: true

      - name: payment_method_token_id
        description: ""
        quote: true

      - name: payment_method_total_number_of_error_payments
        description: ""
        quote: true

      - name: payment_method_total_number_of_processed_payments
        description: ""
        quote: true

      - name: payment_method_type
        description: ""
        quote: true

      - name: payment_method_updated_by_id
        description: ""
        quote: true

      - name: payment_method_updated_timestamp
        description: ""
        quote: true

      - name: payment_method_use_default_retry_rule
        description: ""
        quote: true

      - name: payment_method_actual_payment_method
        description: ""
        quote: true

  - name: staging__zuora__invoice_item_adjustment
    description: ""
    columns:
      - name: invoice_item_adjustment_id
        description: ""
        quote: true

      - name: billing_account_id
        description: ""
        quote: true

      - name: invoice_id
        description: ""
        quote: true

      - name: invoice_number
        description: ""
        quote: true

      - name: invoice_item_name
        description: ""
        quote: true

      - name: customer_number
        description: ""
        quote: true

      - name: invoice_item_adjustment_accounting_code
        description: ""
        quote: true

      - name: invoice_item_adjustment_timestamp
        description: ""
        quote: true

      - name: invoice_item_adjustment_number
        description: ""
        quote: true

      - name: invoice_item_adjustment_amount
        description: ""
        quote: true

      - name: invoice_item_adjustment_cancelled_by_id
        description: ""
        quote: true

      - name: invoice_item_adjustment_cancelled_timestamp
        description: ""
        quote: true

      - name: invoice_item_adjustment_comment
        description: ""
        quote: true

      - name: invoice_item_adjustment_created_by_id
        description: ""
        quote: true

      - name: invoice_item_adjustment_created_timestamp
        description: ""
        quote: true

      - name: invoice_item_adjustment_reason_code
        description: ""
        quote: true

      - name: invoice_item_adjustment_reference_id
        description: ""
        quote: true

      - name: invoice_item_adjustment_service_end_date
        description: ""
        quote: true

      - name: invoice_item_adjustment_service_start_date
        description: ""
        quote: true

      - name: invoice_item_adjustment_source_id
        description: ""
        quote: true

      - name: invoice_item_adjustment_source_type
        description: ""
        quote: true

      - name: invoice_item_adjustment_status
        description: ""
        quote: true

      - name: invoice_item_adjustment_transferred_to_accounting
        description: ""
        quote: true

      - name: invoice_item_adjustment_type
        description: ""
        quote: true

      - name: invoice_item_adjustment_updated_by_id
        description: ""
        quote: true

      - name: invoice_item_adjustment_updated_timestamp
        description: ""
        quote: true

      - name: invoice_item_adjustment_deferred_revenue_account
        description: ""
        quote: true

      - name: invoice_item_adjustment_recognized_revenue_account
        description: ""
        quote: true

  - name: staging__zuora__invoice_item_adjustment_current
    description: ""
    columns:
      - name: invoice_item_adjustment_id
        description: ""
        quote: true

      - name: billing_account_id
        description: ""
        quote: true

      - name: invoice_id
        description: ""
        quote: true

      - name: invoice_number
        description: ""
        quote: true

      - name: invoice_item_name
        description: ""
        quote: true

      - name: customer_number
        description: ""
        quote: true

      - name: invoice_item_adjustment_accounting_code
        description: ""
        quote: true

      - name: invoice_item_adjustment_timestamp
        description: ""
        quote: true

      - name: invoice_item_adjustment_number
        description: ""
        quote: true

      - name: invoice_item_adjustment_amount
        description: ""
        quote: true

      - name: invoice_item_adjustment_cancelled_by_id
        description: ""
        quote: true

      - name: invoice_item_adjustment_cancelled_timestamp
        description: ""
        quote: true

      - name: invoice_item_adjustment_comment
        description: ""
        quote: true

      - name: invoice_item_adjustment_created_by_id
        description: ""
        quote: true

      - name: invoice_item_adjustment_created_timestamp
        description: ""
        quote: true

      - name: invoice_item_adjustment_reason_code
        description: ""
        quote: true

      - name: invoice_item_adjustment_reference_id
        description: ""
        quote: true

      - name: invoice_item_adjustment_service_end_date
        description: ""
        quote: true

      - name: invoice_item_adjustment_service_start_date
        description: ""
        quote: true

      - name: invoice_item_adjustment_source_id
        description: ""
        quote: true

      - name: invoice_item_adjustment_source_type
        description: ""
        quote: true

      - name: invoice_item_adjustment_status
        description: ""
        quote: true

      - name: invoice_item_adjustment_transferred_to_accounting
        description: ""
        quote: true

      - name: invoice_item_adjustment_type
        description: ""
        quote: true

      - name: invoice_item_adjustment_updated_by_id
        description: ""
        quote: true

      - name: invoice_item_adjustment_updated_timestamp
        description: ""
        quote: true

      - name: invoice_item_adjustment_deferred_revenue_account
        description: ""
        quote: true

      - name: invoice_item_adjustment_recognized_revenue_account
        description: ""
        quote: true

  - name: staging__zuora__contact
    description: ""
    columns:
      - name: EDM_INSERT_DTTS
        description: ""
        quote: true

      - name: billing_account_id
        description: ""
        quote: true

      - name: contact_address1
        description: ""
        quote: true

      - name: contact_address2
        description: ""
        quote: true

      - name: contact_city
        description: ""
        quote: true

      - name: contact_country
        description: ""
        quote: true

      - name: contact_created_by_id
        description: ""
        quote: true

      - name: contact_created_timestamp
        description: ""
        quote: true

      - name: contact_description
        description: ""
        quote: true

      - name: contact_fax
        description: ""
        quote: true

      - name: contact_first_name
        description: ""
        quote: true

      - name: contact_home_phone
        description: ""
        quote: true

      - name: contact_id
        description: ""
        quote: true

      - name: contact_last_name
        description: ""
        quote: true

      - name: contact_mobile_phone
        description: ""
        quote: true

      - name: contact_nick_name
        description: ""
        quote: true

      - name: contact_other_phone
        description: ""
        quote: true

      - name: contact_other_phone_type
        description: ""
        quote: true

      - name: contact_personal_email
        description: ""
        quote: true

      - name: contact_postal_code
        description: ""
        quote: true

      - name: contact_state
        description: ""
        quote: true

      - name: contact_tax_region
        description: ""
        quote: true

      - name: contact_updated_by_id
        description: ""
        quote: true

      - name: contact_updated_timestamp
        description: ""
        quote: true

      - name: contact_work_email
        description: ""
        quote: true

      - name: contact_work_phone
        description: ""
        quote: true

  - name: staging__zuora__contact_current
    description: ""
    columns:
      - name: EDM_INSERT_DTTS
        description: ""
        quote: true

      - name: billing_account_id
        description: ""
        quote: true

      - name: contact_address1
        description: ""
        quote: true

      - name: contact_address2
        description: ""
        quote: true

      - name: contact_city
        description: ""
        quote: true

      - name: contact_country
        description: ""
        quote: true

      - name: contact_created_by_id
        description: ""
        quote: true

      - name: contact_created_timestamp
        description: ""
        quote: true

      - name: contact_description
        description: ""
        quote: true

      - name: contact_fax
        description: ""
        quote: true

      - name: contact_first_name
        description: ""
        quote: true

      - name: contact_home_phone
        description: ""
        quote: true

      - name: contact_id
        description: ""
        quote: true

      - name: contact_last_name
        description: ""
        quote: true

      - name: contact_mobile_phone
        description: ""
        quote: true

      - name: contact_nick_name
        description: ""
        quote: true

      - name: contact_other_phone
        description: ""
        quote: true

      - name: contact_other_phone_type
        description: ""
        quote: true

      - name: contact_personal_email
        description: ""
        quote: true

      - name: contact_postal_code
        description: ""
        quote: true

      - name: contact_state
        description: ""
        quote: true

      - name: contact_tax_region
        description: ""
        quote: true

      - name: contact_updated_by_id
        description: ""
        quote: true

      - name: contact_updated_timestamp
        description: ""
        quote: true

      - name: contact_work_email
        description: ""
        quote: true

      - name: contact_work_phone
        description: ""
        quote: true

  - name: staging__zuora__rateplanchargetier_current
    description: "Staging data of the Open Zuora RatePlanChargeTier table"
    columns:
      - name: rateplan_charge_tier_id
        description: ""
        quote: true

      - name: rateplan_charge_id
        description: ""
        quote: true

      - name: rateplan_charge_tier_created_by_id
        description: ""
        quote: true

      - name: rateplan_charge_tier_created_date
        description: ""
        quote: true

      - name: currency_code
        description: ""
        quote: true

      - name: rateplan_charge_tier_discount_amount
        description: ""
        quote: true

      - name: rateplan_charge_tier_discount_percentage
        description: ""
        quote: true

      - name: rateplan_charge_tier_rateplan_charge_tier_ending_unit
        description: ""
        quote: true

      - name: rateplan_charge_tier_included_units
        description: ""
        quote: true

      - name: rateplan_charge_tier_overage_price
        description: ""
        quote: true

      - name: rateplan_charge_tier_price
        description: ""
        quote: true

      - name: rateplan_charge_tier_price_format
        description: ""
        quote: true

      - name: rateplan_charge_tier_starting_unit
        description: ""
        quote: true

      - name: rateplan_charge_tier_tier
        description: ""
        quote: true

      - name: rateplan_charge_tier_updated_by_id
        description: ""
        quote: true

      - name: rateplan_charge_tier_updated_date
        description: ""
        quote: true

  - name: staging__zuora_share__account_current
    description: "Current view Account Object"
    quoting:
      identifier: true
    columns:
      - name: billing_account_id
        description: ""
        quote: true

      - name: crm_account_id
        description: ""
        quote: true

      - name: billing_account_parent_id
        description: ""
        quote: true

      - name: billing_account_sequence_set_id
        description: ""
        quote: true

      - name: billing_account_tax_exempt_certificate_id
        description: ""
        quote: true

      - name: bill_to_contact_id
        description: ""
        quote: true

      - name: default_payment_method_id
        description: ""
        quote: true

      - name: sold_to_contact_id
        description: ""
        quote: true

      - name: dazn_user_id
        description: ""
        quote: true

      - name: billing_account_number
        description: ""
        quote: true

      - name: billing_account_additional_email_addresses__hash
        description: ""
        quote: true

      - name: billing_account_is_allowed_invoice_edit
        description: ""
        quote: true

      - name: billing_account_is_auto_pay
        description: ""
        quote: true

      - name: billing_account_balance
        description: ""
        quote: true

      - name: billing_account_batch_name
        description: ""
        quote: true

      - name: billing_account_cycle_day_setting_option
        description: ""
        quote: true

      - name: billing_account_bill_cycle_day
        description: ""
        quote: true

      - name: billing_account_communication_profile_id
        description: ""
        quote: true

      - name: billing_account_company_name
        description: ""
        quote: true

      - name: billing_account_created_by_id
        description: ""
        quote: true

      - name: billing_account_created_timestamp
        description: ""
        quote: true

      - name: billing_account_credit_balance
        description: ""
        quote: true

      - name: billing_account_currency_code
        description: ""
        quote: true

      - name: billing_account_customer_service_rep_name
        description: ""
        quote: true

      - name: billing_account_has_invoice_delivery_preference_email
        description: ""
        quote: true

      - name: billing_account_has_invoice_delivery_preference_print
        description: ""
        quote: true

      - name: billing_account_invoice_template_id
        description: ""
        quote: true

      - name: billing_account_last_invoice_date
        description: ""
        quote: true

      - name: billing_account_monthly_recurring_revenue
        description: ""
        quote: true

      - name: billing_account_customer_full_name__hash
        description: ""
        quote: true

      - name: billing_account_notes
        description: ""
        quote: true

      - name: billing_account_payment_gateway
        description: ""
        quote: true

      - name: billing_account_payment_term
        description: ""
        quote: true

      - name: billing_account_purchase_order_number
        description: ""
        quote: true

      - name: billing_account_sales_rep_name
        description: ""
        quote: true

      - name: billing_account_status
        description: ""
        quote: true

      - name: billing_account_tax_company_code
        description: ""
        quote: true

      - name: billing_account_tax_exempt_certificate_type
        description: ""
        quote: true

      - name: billing_account_tax_exempt_description
        description: ""
        quote: true

      - name: billing_account_tax_exempt_effective_date
        description: ""
        quote: true

      - name: billing_account_tax_exempt_entity_use_code
        description: ""
        quote: true

      - name: billing_account_tax_exempt_expiration_date
        description: ""
        quote: true

      - name: billing_account_tax_exempt_issuing_jurisdiction
        description: ""
        quote: true

      - name: billing_account_tax_exempt_status
        description: ""
        quote: true

      - name: billing_account_total_debit_memo_balance
        description: ""
        quote: true

      - name: billing_account_total_invoice_balance
        description: ""
        quote: true

      - name: billing_account_unapplied_balance
        description: ""
        quote: true

      - name: billing_account_unapplied_credit_memo_amount
        description: ""
        quote: true

      - name: billing_account_updated_by_id
        description: ""
        quote: true

      - name: billing_account_last_updated_timestamp
        description: ""
        quote: true

      - name: billing_account_vat_id
        description: ""
        quote: true

      - name: billing_account_has_advanced_payment_manager
        description: ""
        quote: true

  - name: staging__zuora_share__subscription_name_current
    description: "Shows the latest record for each Subscription.Name"
    columns:
      - name: subscription_id
        description: ""
        quote: true

      - name: billing_account_id
        description: ""
        quote: true

      - name: subscription_creator_billing_account_id
        description: ""
        quote: true

      - name: subscription_creator_invoice_owner_billing_account_id
        description: ""
        quote: true

      - name: subscription_invoice_owner_billing_account_id
        description: ""
        quote: true

      - name: subscription_original_id
        description: ""
        quote: true

      - name: subscription_previous_subscription_id
        description: ""
        quote: true

      - name: subscription_sign_up_campaign_id
        description: ""
        quote: true

      - name: subscription_direct_carrier_billing_carrier_name
        description: ""
        quote: true

      - name: subscription_device_category
        description: ""
        quote: true

      - name: subscription_sign_up_giftcode
        description: ""
        quote: true

      - name: subscription_sign_up_giftcode_old
        description: ""
        quote: true

      - name: subscription_ip_address__hash
        description: ""
        quote: true

      - name: subscription_is_manually_cancelled
        description: ""
        quote: true

      - name: subscription_giftcode_grants_content_portability
        description: ""
        quote: true

      - name: subscription_giftcode_is_payment_method
        description: ""
        quote: true

      - name: subscription_source_system_name
        description: ""
        quote: true

      - name: subscription_source_system_user_id
        description: ""
        quote: true

      - name: subscription_tracking_id
        description: ""
        quote: true

      - name: subscription_user_agent
        description: ""
        quote: true

      - name: subscription_is_auto_renew
        description: ""
        quote: true

      - name: subscription_cancelled_date
        description: ""
        quote: true

      - name: subscription_contract_acceptance_date
        description: ""
        quote: true

      - name: subscription_contract_effective_date
        description: ""
        quote: true

      - name: subscription_country
        description: ""
        quote: true

      - name: subscription_cpq_bundle_json_id
        description: ""
        quote: true

      - name: subscription_created_by_id
        description: ""
        quote: true

      - name: subscription_id_created_timestamp
        description: ""
        quote: true

      - name: subscription_current_term
        description: ""
        quote: true

      - name: subscription_current_term_period_type
        description: ""
        quote: true

      - name: subscription_initial_term
        description: ""
        quote: true

      - name: subscription_initial_term_period_type
        description: ""
        quote: true

      - name: subscription_is_invoice_separate
        description: ""
        quote: true

      - name: subscription_name
        description: ""
        quote: true

      - name: subscription_notes
        description: ""
        quote: true

      - name: subscription_number_of_free_trial_periods
        description: ""
        quote: true

      - name: subscription_number_of_giftcode_periods
        description: ""
        quote: true

      - name: subscription_opportunity_close_date
        description: ""
        quote: true

      - name: subscription_opportunity_name
        description: ""
        quote: true

      - name: subscription_name_original_created_timestamp
        description: ""
        quote: true

      - name: subscription_quote_business_type
        description: ""
        quote: true

      - name: subscription_quote_number
        description: ""
        quote: true

      - name: subscription_quote_type
        description: ""
        quote: true

      - name: subscription_receipt_id
        description: ""
        quote: true

      - name: subscription_renewal_setting
        description: ""
        quote: true

      - name: subscription_renewal_term
        description: ""
        quote: true

      - name: subscription_renewal_term_period_type
        description: ""
        quote: true

      - name: subscription_service_activation_date
        description: ""
        quote: true

      - name: subscription_status
        description: ""
        quote: true

      - name: subscription_end_date
        description: ""
        quote: true

      - name: subscription_start_date
        description: ""
        quote: true

      - name: subscription_term_end_date
        description: ""
        quote: true

      - name: subscription_term_start_date
        description: ""
        quote: true

      - name: subscription_term_type
        description: ""
        quote: true

      - name: subscription_updated_by_id
        description: ""
        quote: true

      - name: subscription_id_updated_timestamp
        description: ""
        quote: true

      - name: subscription_version
        description: ""
        quote: true

      - name: is_latest_subscription_version
        description: ""
        quote: true

      - name: subscription_free_trial_periods_type
        description: ""
        quote: true

      - name: subscription_product_group
        description: ""
        quote: true

      - name: subscription_payment_method_id
        description: ""
        quote: true

  - name: staging__zuora_share__rateplancharge_current
    description: "Current view of Rate Plan Charge"
    quoting:
      identifier: true
    columns:
      - name: rateplan_charge_id
        description: ""
        quote: true

      - name: rateplan_id
        description: ""
        quote: true

      - name: rateplan_charge_original_id
        description: ""
        quote: true

      - name: product_rateplan_charge_id
        description: ""
        quote: true

      - name: rateplan_charge_post_sign_up_giftcode
        description: ""
        quote: true

      - name: rateplan_charge_post_sign_up_giftcode_campaign_name
        description: ""
        quote: true

      - name: rateplan_charge_accounting_code
        description: ""
        quote: true

      - name: rateplan_charge_apply_discount_to
        description: ""
        quote: true

      - name: rateplan_charge_bill_cycle_day
        description: ""
        quote: true

      - name: rateplan_charge_bill_cycle_type
        description: ""
        quote: true

      - name: rateplan_charge_billing_period
        description: ""
        quote: true

      - name: rateplan_charge_billing_period_alignment
        description: ""
        quote: true

      - name: rateplan_charge_billing_timing
        description: ""
        quote: true

      - name: rateplan_charge_charged_through_date
        description: ""
        quote: true

      - name: rateplan_charge_charge_model
        description: ""
        quote: true

      - name: rateplan_charge_charge_number
        description: ""
        quote: true

      - name: rateplan_charge_charge_type
        description: ""
        quote: true

      - name: rateplan_charge_created_by_id
        description: ""
        quote: true

      - name: rateplan_charge_created_timestamp
        description: ""
        quote: true

      - name: rateplan_charge_description
        description: ""
        quote: true

      - name: rateplan_charge_discount_level
        description: ""
        quote: true

      - name: rateplan_charge_delta_monthly_recurring_charge
        description: ""
        quote: true

      - name: rateplan_charge_delta_total_contract_value
        description: ""
        quote: true

      - name: rateplan_charge_effective_end_date
        description: ""
        quote: true

      - name: rateplan_charge_effective_start_date
        description: ""
        quote: true

      - name: rateplan_charge_end_date_condition
        description: ""
        quote: true

      - name: rateplan_charge_is_last_segment
        description: ""
        quote: true

      - name: rateplan_charge_list_price_base
        description: ""
        quote: true

      - name: rateplan_charge_monthly_recurring_revenue
        description: ""
        quote: true

      - name: rateplan_charge_name
        description: ""
        quote: true

      - name: rateplan_charge_number_of_periods
        description: ""
        quote: true

      - name: rateplan_charge_overage_calculation_option
        description: ""
        quote: true

      - name: rateplan_charge_overage_used_units_credit_option
        description: ""
        quote: true

      - name: rateplan_charge_price_change_option
        description: ""
        quote: true

      - name: rateplan_charge_prince_increase_percentage
        description: ""
        quote: true

      - name: rateplan_charge_processed_through_date
        description: ""
        quote: true

      - name: rateplan_charge_quantity
        description: ""
        quote: true

      - name: rateplan_charge_rating_group
        description: ""
        quote: true

      - name: rateplan_charge_revenue_recognition_rule_name
        description: ""
        quote: true

      - name: rateplan_charge_revenue_recognition_code
        description: ""
        quote: true

      - name: rateplan_charge_revenue_recognition_trigger_condition
        description: ""
        quote: true

      - name: rateplan_charge_segment
        description: ""
        quote: true

      - name: rateplan_charge_specific_billing_period
        description: ""
        quote: true

      - name: rateplan_charge_specific_end_date
        description: ""
        quote: true

      - name: rateplan_charge_total_contract_value
        description: ""
        quote: true

      - name: rateplan_charge_trigger_date
        description: ""
        quote: true

      - name: rateplan_charge_trigger_event
        description: ""
        quote: true

      - name: rateplan_charge_unit_of_measure
        description: ""
        quote: true

      - name: rateplan_charge_updated_by_id
        description: ""
        quote: true

      - name: rateplan_charge_updated_timestamp
        description: ""
        quote: true

      - name: rateplan_charge_up_to_periods
        description: ""
        quote: true

      - name: rateplan_charge_up_to_periods_type
        description: ""
        quote: true

      - name: rateplan_charge_version
        description: ""
        quote: true

      - name: rateplan_charge_weekly_bill_cycle_day
        description: ""
        quote: true

  - name: staging__zuora_share__rateplan_current
    description: "Current view of Rate Plan object"
    quoting:
      identifier: true
    columns:
      - name: rateplan_id
        description: ""
        quote: true

      - name: subscription_id
        description: ""
        quote: true

      - name: entitlement_set_id
        description: ""
        quote: true

      - name: amendment_id
        description: ""
        quote: true

      - name: product_rateplan_id
        description: ""
        quote: true

      - name: rateplan_tracking_id
        description: ""
        quote: true

      - name: rateplan_source_system_user_id
        description: ""
        quote: true

      - name: rateplan_source_system_name
        description: ""
        quote: true

      - name: rateplan_amendment_type
        description: ""
        quote: true

      - name: rateplan_created_by_id
        description: ""
        quote: true

      - name: rateplan_created_timestamp
        description: ""
        quote: true

      - name: rateplan_name
        description: ""
        quote: true

      - name: rateplan_updated_by_id
        description: ""
        quote: true

      - name: rateplan_updated_timestamp
        description: ""
        quote: true

      - name: rateplan_billing_type
        description: "The billing type associated with this rateplan. Can be one of the following: Recurring, RecurringExternal or OneOff"
        quote: true

      - name: rateplan_partner_id
        description: "The partner identifier for the Addon. For example, SkyIT"
        quote: true

      - name: rateplan_partner_user_id
        description: "The user identifier for the Addon. For example, sky_user_1245612437"
        quote: true

      - name: payment_gateway
        description: "Name of the gateway instance that processes the payment"
        quote: true

      - name: payment_method_id
        description: "The ID of the payment method used by the customer at the time of the addon purchase"
        quote: true

      - name: rateplan_product_type
        description: "The product type associated with this rateplan. Can be one of the following: subscription, addon, ppv or null"
        quote: true

      - name: rateplan_next_invoice_date
        description: "This field will be populated for the first time when the initial amendment is submitted. It will be equal to the contract effective date + 1 month. After then it will be set to the next month."
        quote: true

      - name: rateplan_context
        description: "Describes the channel through which the discount was applied - CRM, in app, cancellation flow or via a customer service agent"
        quote: true


  - name: staging__zuora__refund
    description: "A Refund object is used to create and query refunds. There are two types of refunds."
    quoting:
      identifier: true
    columns:
      - name: refund_accounting_code
        description: ""
        quote: true

      - name: refund_amount
        description: ""
        quote: true

      - name: refund_cancelled_on
        description: ""
        quote: true

      - name: refund_comment
        description: ""
        quote: true

      - name: refund_created_by_id
        description: ""
        quote: true

      - name: refund_created_date
        description: ""
        quote: true

      - name: refund_gateway
        description: ""
        quote: true

      - name: refund_gateway_response
        description: ""
        quote: true

      - name: refund_gateway_response_code
        description: ""
        quote: true

      - name: refund_gateway_state
        description: ""
        quote: true

      - name: refund_id
        description: ""
        quote: true

      - name: refund_marked_for_submission_on
        description: ""
        quote: true

      - name: refund_method_type
        description: ""
        quote: true

      - name: refund_payment_method_id
        description: ""
        quote: true

      - name: refund_reason_code
        description: ""
        quote: true

      - name: refund_reference_id
        description: ""
        quote: true

      - name: refund_date
        description: ""
        quote: true

      - name: refund_number
        description: ""
        quote: true

      - name: refund_reference_id
        description: ""
        quote: true

      - name: refund_source
        description: ""
        quote: true

      - name: refund_transaction_time
        description: ""
        quote: true

      - name: refund_second_refund_reference_id
        description: ""
        quote: true

      - name: refund_settled_on
        description: ""
        quote: true

      - name: refund_soft_descriptor
        description: ""
        quote: true

      - name: refund_soft_descriptor_phone
        description: ""
        quote: true

      - name: refund_source_type
        description: ""
        quote: true

      - name: refund_status
        description: ""
        quote: true

      - name: refund_submitted_on
        description: ""
        quote: true

      - name: refund_transferred_to_accounting
        description: ""
        quote: true

      - name: refund_type
        description: ""
        quote: true

      - name: refund_updated_by_id
        description: ""
        quote: true

      - name: refund_updated_date
        description: ""
        quote: true

      - name: refund_account_id
        description: ""
        quote: true

      - name: refund_payment_method_snapshot_id
        description: ""
        quote: true

  - name: staging__zuora__refund_invoice_payment
    description: "A refund invoice payment represents a portion of the refund that's being made against a payment that was applied to an invoice."
    columns:
      - name: refund_invoice_payment_created_by_id
        description: ""
        quote: true

      - name: refund_invoice_payment_created_date
        description: ""
        quote: true

      - name: refund_invoice_payment_id
        description: ""
        quote: true

      - name: refund_invoice_payment_refund_amount
        description: ""
        quote: true

      - name: refund_invoice_payment_updated_by_id
        description: ""
        quote: true

      - name: refund_invoice_payment_updated_date
        description: ""
        quote: true

      - name: refund_invoice_payment_invoice_id
        description: ""
        quote: true

      - name: refund_invoice_payment_invoice_payment_id
        description: ""
        quote: true

      - name: refund_invoice_payment_refund_id
        description: ""
        quote: true

  - name: staging__zuora__productrateplan
    description: "Use the ProductRatePlan object to define the services that make up the Product objects that can be associated with Subscription objects."
    columns:
      - name: EDM_INSERT_DTTS
        description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
        quote: true

      - name: product_rateplan_created_by_id
        description: "This is the UserID / API that created that record."
        quote: true

      - name: product_rateplan_created_timestamp
        description: "Date of creation."
        quote: true

      - name: product_rateplan_description
        description: "TBC"
        quote: true

      - name: product_rateplan_effective_end_date
        description: "TBC"
        quote: true

      - name: product_rateplan_effective_start_date
        description: "TBC"
        quote: true

      - name: product_rateplan_id
        description: "The ID of this object."
        quote: true

      - name: product_rateplan_name
        description: "TBC"
        quote: true

      - name: product_rateplan_offer_type
        description: "TBC"
        quote: true

      - name: product_rateplan_updated_by_id
        description: "The userId that updated the product rate plan current"
        quote: true

      - name: product_rateplan_updated_timestamp
        description: "The date of last update of the product rate plan current"
        quote: true

      - name: entitlement_set_id
        description: "The field will present an unique id of the fixture being sold based on the rateplan. This will be used as an indicator of the PPV offer customer purchased."
        quote: true

      - name: ppv_type
        description: "The field will differentiate if plan is a regular one (used post sign up with full PPV price) or discounted one (sign up bundle)."
        quote: true

      - name: product_rateplan_billing_type
        description: "The billing type associated with this product rateplan. Can be one of the following: Recurring, RecurringExternal or OneOff"
        quote: true

      - name: product_rateplan_product_type
        description: "The product type associated with this product rateplan. Can be one of the following: subscription, addon, ppv or null"
        quote: true

  - name: staging__zuora__productrateplan_current
    description: "Use the ProductRatePlan object to define the services that make up the Product objects that can be associated with Subscription objects."
    columns:
      - name: EDM_INSERT_DTTS
        description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
        quote: true

      - name: product_rateplan_created_by_id
        description: "This is the UserID / API that created that record."
        quote: true

      - name: product_rateplan_created_timestamp
        description: "Date of creation."
        quote: true

      - name: product_rateplan_description
        description: "TBC"
        quote: true

      - name: product_rateplan_effective_end_date
        description: "TBC"
        quote: true

      - name: product_rateplan_effective_start_date
        description: "TBC"
        quote: true

      - name: product_rateplan_id
        description: "The ID of this object."
        quote: true

      - name: product_rateplan_name
        description: "TBC"
        quote: true

      - name: product_rateplan_offer_type
        description: "TBC"
        quote: true

      - name: product_rateplan_updated_by_id
        description: "The userId that updated the product rate plan current"
        quote: true

      - name: product_rateplan_updated_timestamp
        description: "The date of last update of the product rate plan current"
        quote: true

      - name: entitlement_set_id
        description: "The field will present an unique id of the fixture being sold based on the rateplan. This will be used as an indicator of the PPV offer customer purchased."
        quote: true

      - name: ppv_type
        description: "The field will differentiate if plan is a regular one (used post sign up with full PPV price) or discounted one (sign up bundle)."
        quote: true

      - name: product_rateplan_billing_type
        description: "The billing type associated with this rateplan. Can be one of the following: Recurring, RecurringExternal or OneOff"
        quote: true

      - name: product_rateplan_product_type
        description: "The product type associated with this rateplan. Can be one of the following: subscription, addon, ppv or null"
        quote: true

  - name: staging__zuora__productrateplancharge
    description: "A product rate plan charge represents a charge model or a set of fees associated with a product rate plan, which is the part of a product that your customers subscribe to."
    columns:
      - name: EDM_INSERT_DTTS
        description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
        quote: true

      - name: product_rateplan_charge_accounting_code
        description: "The account code Id"
        quote: true

      - name: product_rateplan_charge_apply_discount_to
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_bill_cycle_day
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_bill_cycle_type
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_billing_period
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_billing_period_alignment
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_billing_timing
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_charge_model
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_charge_type
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_created_by_id
        description: "This is the UserID / API that created that record."
        quote: true

      - name: product_rateplan_charge_created_timestamp
        description: "Date of creation."
        quote: true

      - name: product_rateplan_charge_default_quantity
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_deferred_revenue_account
        description: "The accounting code for deferred revenue e.g. 7074."
        quote: true

      - name: product_rateplan_charge_description
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_discount_level
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_end_date_condition
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_id
        description: "The ID of this object."
        quote: true

      - name: product_rateplan_charge_included_units
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_legacy_revenue_reporting
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_list_price_base
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_max_quantity
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_min_quantity
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_name
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_number_of_periods
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_overage_calculation_option
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_overage_used_units_credit_option
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_price_change_option
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_prince_increase_percentage
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_rating_group
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_recognized_revenue_account
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_revenue_recognition_rule_name
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_revenue_recognition_code
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_revenue_recognition_trigger_condition
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_smoothing_model
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_specific_billing_period
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_taxable
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_item_tax_code
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_item_tax_mode
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_trigger_event
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_unit_of_measure
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_updated_by_id
        description: "The userId that updated the product rate plan charge current"
        quote: true

      - name: product_rateplan_charge_updated_timestamp
        description: "The date of last update of the product rate plan charge current"
        quote: true

      - name: product_rateplan_charge_up_to_periods
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_up_to_periods_type
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_usage_record_rating_option
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_use_discount_specific_accounting_code
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_use_tenant_default_for_price_change
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_weekly_bill_cycle_day
        description: "TBC"
        quote: true

      - name: product_rateplan_id
        description: "The ID of the associated product rate plan."
        quote: true

  - name: staging__zuora__productrateplancharge_current
    description: "A product rate plan charge represents a charge model or a set of fees associated with a product rate plan, which is the part of a product that your customers subscribe to."
    columns:
      - name: EDM_INSERT_DTTS
        description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
        quote: true

      - name: product_rateplan_charge_accounting_code
        description: "The account code Id"
        quote: true

      - name: product_rateplan_charge_apply_discount_to
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_bill_cycle_day
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_bill_cycle_type
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_billing_period
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_billing_period_alignment
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_billing_timing
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_charge_model
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_charge_type
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_created_by_id
        description: "This is the UserID / API that created that record."
        quote: true

      - name: product_rateplan_charge_created_timestamp
        description: "Date of creation."
        quote: true

      - name: product_rateplan_charge_default_quantity
        description: "TBC"
        quote: true

      - name: DeferredRevenueAccount
        description: "The accounting code for deferred revenue e.g. 7074."
        quote: true

      - name: product_rateplan_charge_deferred_revenue_account
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_description
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_discount_level
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_end_date_condition
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_id
        description: "The ID of this object."
        quote: true

      - name: product_rateplan_charge_included_units
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_legacy_revenue_reporting
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_list_price_base
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_max_quantity
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_min_quantity
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_name
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_number_of_periods
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_overage_calculation_option
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_overage_used_units_credit_option
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_price_change_option
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_prince_increase_percentage
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_rating_group
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_recognized_revenue_account
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_revenue_recognition_rule_name
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_revenue_recognition_code
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_revenue_recognition_trigger_condition
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_smoothing_model
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_specific_billing_period
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_taxable
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_item_tax_code
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_item_tax_mode
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_trigger_event
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_unit_of_measure
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_updated_by_id
        description: "The userId that updated the product rate plan charge current"
        quote: true

      - name: product_rateplan_charge_updated_timestamp
        description: "The date of last update of the product rate plan charge current"
        quote: true

      - name: product_rateplan_charge_up_to_periods
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_up_to_periods_type
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_usage_record_rating_option
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_use_discount_specific_accounting_code
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_use_tenant_default_for_price_change
        description: "TBC"
        quote: true

      - name: product_rateplan_charge_weekly_bill_cycle_day
        description: "TBC"
        quote: true

      - name: product_rateplan_id
        description: "The ID of the associated product rate plan."
        quote: true

  - name: staging__zuora__orderlineitem
    description: "The OrderLineItem object contains the information needed to create and maintain a PPV associated with an Account object."
    columns:
      - name: EDM_INSERT_DTTS
        description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
        quote: true

      - name: order_line_item_Amount
        description: "Amount of the payment (authorisation amount)."
        quote: true

      - name: order_line_item_amount_per_unit
        description: "TBC"
        quote: true

      - name: order_line_item_amount_without_tax
        description: "TBC"
        quote: true

      - name: order_line_item_billing_rule
        description: "TBC"
        quote: true

      - name: order_line_item_bill_target_date
        description: "TBC"
        quote: true

      - name: order_line_item_created_by_id
        description: "This is the UserID / API that created that record."
        quote: true

      - name: order_line_item_created_timestamp
        description: "Date of creation."
        quote: true

      - name: currency
        description: "The three-character ISO currency code."
        quote: true

      - name: order_line_item_description
        description: "TBC"
        quote: true

      - name: order_line_item_discount
        description: "TBC"
        quote: true

      - name: entitlement_set_id
        description: "The field will present an unique id of the fixture being sold based on the rateplan. This will be used as an indicator of the PPV offer customer purchased."
        quote: true

      - name: order_line_item_id
        description: "The ID of this object."
        quote: true

      - name: order_line_item_inline_discount_per_unit
        description: "TBC"
        quote: true

      - name: order_line_item_inline_discount_type
        description: "TBC"
        quote: true

      - name: order_line_item_item_category
        description: "TBC"
        quote: true

      - name: order_line_item_item_name
        description: "The name of the OrderLineItem."
        quote: true

      - name: order_line_item_item_number
        description: "TBC"
        quote: true

      - name: order_line_item_item_state
        description: "TBC"
        quote: true

      - name: order_line_item_item_type
        description: "Indicates the line item type of a OrderLineItem."
        quote: true

      - name: order_line_item_list_price
        description: "TBC"
        quote: true

      - name: order_line_item_list_price_per_unit
        description: "TBC"
        quote: true

      - name: order_line_item_quantity
        description: "TBC"
        quote: true

      - name: order_line_item_quantity_available_for_return
        description: "TBC"
        quote: true

      - name: order_line_item_quantity_fulfilled
        description: "TBC"
        quote: true

      - name: order_line_item_quantity_pending_fulfillment
        description: "TBC"
        quote: true

      - name: subscription_name
        description: "TBC"
        quote: true

      - name: order_line_item_requires_fulfillment
        description: "TBC"
        quote: true

      - name: order_line_item_sold_to
        description: "TBC"
        quote: true

      - name: order_line_item_source_system
        description: "This field will be used for tracking the 3PP partner name: Apple IAP, Googlepay, Amazon, etc."
        quote: true

      - name: order_line_item_tracking_id
        description: "Front End Partnerships Tracking ID usually for use in commercial partnership tracking"
        quote: true

      - name: order_line_item_transaction_start_date
        description: "TBC"
        quote: true

      - name: order_line_item_transaction_end_date
        description: "TBC"
        quote: true

      - name: order_line_item_updated_by_id
        description: "The userId that updated the OrderLineItem."
        quote: true

      - name: order_line_item_updated_timestamp
        description: "The date of last update of the OrderLineItem."
        quote: true

      - name: order_line_item_order_id
        description: "The ID of the Order to which the OrderLineItem belongs."
        quote: true

      - name: billing_account_id
        description: "Account id relating to the OrderLineItem."
        quote: true

      - name: product_rate_plan_charge_id
        description: "The ID of the associated product rate plan charge."
        quote: true

      - name: Country__c
        description: "Country Name."
        quote: true

      - name: Giftcode__c
        description: "Gift code no."
        quote: true

      - name: giftcodeismethod__c
        description: "TBC"
        quote: true

      - name: SourceSystemUserID__c
        description: "Field for storing the 3PP transacitionID for example Apple is orginaltrxID."
        quote: true

      - name: PartnerOfferName__c
        description: "TBC"
        quote: true

      - name: Platform__c
        description: "TBC"
        quote: true

      - name: is_3pp_exit_flow
        description: "TBC"
        quote: true

  - name: staging__zuora__orderlineitem_current
    description: "The OrderLineItem object contains the information needed to create and maintain a PPV associated with an Account object."
    columns:
      - name: EDM_INSERT_DTTS
        description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
        quote: true

      - name: order_line_item_Amount
        description: "Amount of the payment (authorisation amount)."
        quote: true

      - name: order_line_item_amount_per_unit
        description: "TBC"
        quote: true

      - name: order_line_item_amount_without_tax
        description: "TBC"
        quote: true

      - name: order_line_item_billing_rule
        description: "TBC"
        quote: true

      - name: order_line_item_bill_target_date
        description: "TBC"
        quote: true

      - name: order_line_item_created_by_id
        description: "This is the UserID / API that created that record."
        quote: true

      - name: order_line_item_created_timestamp
        description: "Date of creation."
        quote: true

      - name: currency
        description: "The three-character ISO currency code."
        quote: true

      - name: order_line_item_description
        description: "TBC"
        quote: true

      - name: order_line_item_discount
        description: "TBC"
        quote: true

      - name: entitlement_set_id
        description: "The field will present an unique id of the fixture being sold based on the rateplan. This will be used as an indicator of the PPV offer customer purchased."
        quote: true

      - name: order_line_item_id
        description: "The ID of this object."
        quote: true

      - name: order_line_item_inline_discount_per_unit
        description: "TBC"
        quote: true

      - name: order_line_item_inline_discount_type
        description: "TBC"
        quote: true

      - name: order_line_item_item_category
        description: "TBC"
        quote: true

      - name: order_line_item_item_name
        description: "The name of the OrderLineItem."
        quote: true

      - name: order_line_item_item_number
        description: "TBC"
        quote: true

      - name: order_line_item_item_state
        description: "TBC"
        quote: true

      - name: order_line_item_item_type
        description: "Indicates the line item type of a OrderLineItem."
        quote: true

      - name: order_line_item_list_price
        description: "TBC"
        quote: true

      - name: order_line_item_list_price_per_unit
        description: "TBC"
        quote: true

      - name: order_line_item_quantity
        description: "TBC"
        quote: true

      - name: order_line_item_quantity_available_for_return
        description: "TBC"
        quote: true

      - name: order_line_item_quantity_fulfilled
        description: "TBC"
        quote: true

      - name: order_line_item_quantity_pending_fulfillment
        description: "TBC"
        quote: true

      - name: subscription_name
        description: "TBC"
        quote: true

      - name: order_line_item_requires_fulfillment
        description: "TBC"
        quote: true

      - name: order_line_item_sold_to
        description: "TBC"
        quote: true

      - name: order_line_item_source_system
        description: "This field will be used for tracking the 3PP partner name: Apple IAP, Googlepay, Amazon, etc."
        quote: true

      - name: order_line_item_tracking_id
        description: "Front End Partnerships Tracking ID usually for use in commercial partnership tracking"
        quote: true

      - name: order_line_item_transaction_start_date
        description: "TBC"
        quote: true

      - name: order_line_item_transaction_end_date
        description: "TBC"
        quote: true

      - name: order_line_item_updated_by_id
        description: "The userId that updated the OrderLineItem."
        quote: true

      - name: order_line_item_updated_timestamp
        description: "The date of last update of the OrderLineItem."
        quote: true

      - name: order_line_item_order_id
        description: "The ID of the Order to which the OrderLineItem belongs."
        quote: true

      - name: billing_account_id
        description: "Account id relating to the OrderLineItem."
        quote: true

      - name: product_rate_plan_charge_id
        description: "The ID of the associated product rate plan charge."
        quote: true

      - name: Country__c
        description: "Country Name."
        quote: true

      - name: Giftcode__c
        description: "Gift code no."
        quote: true

      - name: giftcodeismethod__c
        description: "TBC"
        quote: true

      - name: SourceSystemUserID__c
        description: "Field for storing the 3PP transacitionID for example Apple is orginaltrxID."
        quote: true

      - name: PartnerOfferName__c
        description: "TBC"
        quote: true

      - name: Platform__c
        description: "TBC"
        quote: true

      - name: is_3pp_exit_flow
        description: "TBC"
        quote: true
