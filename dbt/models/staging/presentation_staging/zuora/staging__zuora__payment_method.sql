WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA__PAYMENT_METHOD') }}
)

, renamed AS (
    SELECT
        "Id" AS "payment_method_id"
        ,"AccountId" AS "billing_account_id"
        ,"AchAbaCode" AS "payment_method_ach_aba_code"
        ,"AchAccountName" AS "payment_method_ach_account_name"
        ,"AchAccountNumberMask" AS "payment_method_ach_account_number_mask"
        ,"AchAccountType" AS "payment_method_ach_account_type"
        ,"AchAddress1" AS "payment_method_ach_address_1"
        ,"AchAddress2" AS "payment_method_ach_address_2"
        ,"AchBankName" AS "payment_method_ach_bank_name"
        ,"AchCity" AS "payment_method_ach_city"
        ,"AchCountry" AS "payment_method_ach_country"
        ,"AchPostalCode" AS "payment_method_ach_postal_code"
        ,"AchState" AS "payment_method_ach_state"
        ,"Active" AS "payment_method_active"
        ,"BankBranchCode" AS "payment_method_bank_branch_code"
        ,"BankCheckDigit" AS "payment_method_bank_check_digit"
        ,"BankCity" AS "payment_method_bank_city"
        ,"BankCode" AS "payment_method_bank_code"
        ,"BankIdentificationNumber" AS "payment_method_bank_identification_number"
        ,"BankName" AS "payment_method_bank_name"
        ,"BankPostalCode" AS "payment_method_bank_postal_code"
        ,"BankStreetName" AS "payment_method_bank_street_name"
        ,"BankStreetNumber" AS "payment_method_bank_street_number"
        ,"BankTransferAccountName" AS "payment_method_bank_transfer_account_name"
        ,"BankTransferAccountNumberMask" AS "payment_method_bank_transfer_account_number_mask"
        ,"BankTransferAccountType" AS "payment_method_bank_transfer_account_type"
        ,"BankTransferType" AS "payment_method_bank_transfer_type"
        ,"BusinessIdentificationCode" AS "payment_method_business_identification_code"
        ,"City" AS "payment_method_city"
        ,"CompanyName" AS "payment_method_company_name"
        ,"Country" AS "payment_method_country"
        ,"CreatedById" AS "payment_method_created_by_id"
        ,"CreatedDate" AS "payment_method_created_timestamp"
        ,"CreditCardAddress1" AS "payment_method_credit_card_address_1"
        ,"CreditCardAddress2" AS "payment_method_credit_card_address_2"
        ,"CreditCardCity" AS "payment_method_credit_card_city"
        ,"CreditCardCountry" AS "payment_method_credit_card_country"
        ,"CreditCardExpirationMonth" AS "payment_method_credit_card_expiration_month"
        ,"CreditCardExpirationYear" AS "payment_method_credit_card_expiration_year"
        ,"CreditCardHolderName" AS "payment_method_credit_card_holder_name"
        ,"CreditCardMaskNumber" AS "payment_method_credit_card_mask_number"
        ,"CreditCardPostalCode" AS "payment_method_credit_card_postal_code"
        ,"CreditCardState" AS "payment_method_credit_card_state"
        ,"CreditCardType" AS "payment_method_credit_card_type"
        ,"DeviceSessionId" AS "payment_method_device_session_id"
        ,"Email" AS "payment_method_email"
        ,"ExistingMandate" AS "payment_method_existing_mandate"
        ,"FirstName" AS "payment_method_first_name"
        ,"IBAN" AS "payment_method_iban"
        ,"IdentityNumber" AS "payment_method_identity_number"
        ,"IPAddress" AS "payment_method_ip_address"
        ,"IsCompany" AS "payment_method_is_company"
        ,"LastFailedSaleTransactionDate" AS "payment_method_last_failed_sale_transaction_timestamp"
        ,"LastName" AS "payment_method_last_name"
        ,"LastTransactionDateTime" AS "payment_method_last_transaction_timestamp"
        ,"LastTransactionStatus" AS "payment_method_last_transaction_status"
        ,"MandateCreationDate" AS "payment_method_mandate_creation_timestamp"
        ,"MandateID" AS "payment_method_mandate_id"
        ,"MandateReceived" AS "payment_method_mandate_received"
        ,"MandateUpdateDate" AS "payment_method_mandate_update_timestamp"
        ,"MaxConsecutivePaymentFailures" AS "payment_method_max_consecutive_payment_failures"
        ,"Name" AS "payment_method_name"
        ,"NumConsecutiveFailures" AS "payment_method_num_consecutive_failures"
        ,"PaymentMethodStatus" AS "payment_method_payment_method_status"
        ,"PaymentRetryWindow" AS "payment_method_payment_retry_window"
        ,"PaypalBaid" AS "payment_method_paypal_baid"
        ,"PaypalEmail" AS "payment_method_paypal_email"
        ,"PaypalPreapprovalKey" AS "payment_method_paypal_preapproval_key"
        ,"PaypalType" AS "payment_method_paypal_type"
        ,"Phone" AS "payment_method_phone"
        ,"PostalCode" AS "payment_method_postal_code"
        ,"SecondTokenId" AS "payment_method_second_token_id"
        ,"State" AS "payment_method_state"
        ,"StreetName" AS "payment_method_street_name"
        ,"StreetNumber" AS "payment_method_street_number"
        ,"TMX_Id__c" AS "payment_method_tmx_id"
        ,"TokenId" AS "payment_method_token_id"
        ,"TotalNumberOfErrorPayments" AS "payment_method_total_number_of_error_payments"
        ,"TotalNumberOfProcessedPayments" AS "payment_method_total_number_of_processed_payments"
        ,"Type" AS "payment_method_type"
        ,"UpdatedById" AS "payment_method_updated_by_id"
        ,"UpdatedDate" AS "payment_method_updated_timestamp"
        ,"UseDefaultRetryRule" AS "payment_method_use_default_retry_rule"
        ,"ActualPaymentMethod__c" AS "payment_method_actual_payment_method"
    FROM source
)

SELECT * FROM renamed
