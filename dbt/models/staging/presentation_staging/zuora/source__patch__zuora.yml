version: 2

sources:
  - name: PATCH
    database: TRANSFORMATION_{{ 'UAT' if env_var('AIRFLOW_VAR_ENV') == 'stage' else env_var('AIRFLOW_VAR_ENV') }}
    tables:
      - name: PATCH__SUBSCRIPTION__LEGACY_3PP_FREE_TRIALS
        description: "Table containing a list of all subscriptions that were affected by the issue of 3PP (Apple/Amazon) free trials not cancelling correctly and their correct expiry date so we can backdate the subscription domain. Table created under ticket EB-1717"
        columns:
          - name: subscription_name
            description: "The subscription name of the user, coming from <PERSON><PERSON><PERSON>"
            quote: true

          - name: expiry_date
            description: "The date on which the subscription has expired"
            quote: true
