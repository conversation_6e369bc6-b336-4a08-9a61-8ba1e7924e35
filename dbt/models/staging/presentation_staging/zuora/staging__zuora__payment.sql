WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA__PAYMENT') }}
)

, renamed AS (
    SELECT
        "Id" AS "payment_id"
        ,"AccountId" AS "billing_account_id"
        ,"PaymentMethodId" AS "payment_method_id"
        ,"PaymentMethodSnapshotId" AS "payment_method_snapshot_id"
        ,"AccountingCode" AS "payment_accounting_code"
        ,"Amount" AS "payment_amount"
        ,"AppliedAmount" AS "payment_applied_amount"
        ,"AppliedCreditBalanceAmount" AS "payment_applied_credit_balance_amount"
        ,"AuthTransactionId" AS "payment_auth_transaction_id"
        ,"BankIdentificationNumber" AS "payment_bank_identification_number"
        ,"CancelledOn" AS "payment_cancelled_on"
        ,"Comment" AS "payment_comment"
        ,"CreatedById" AS "payment_created_by_id"
        ,"CreatedDate" AS "payment_created_timestamp"
        ,"Currency" AS "payment_currency"
        ,"EffectiveDate" AS "payment_effective_timestamp"
        ,"Gateway" AS "payment_gateway"
        ,"GatewayOrderId" AS "payment_gateway_order_id"
        ,"GatewayResponse" AS "payment_gateway_response"
        ,"GatewayResponseCode" AS "payment_gateway_response_code"
        ,"GatewayState" AS "payment_gateway_state"
        ,"MarkedForSubmissionOn" AS "payment_marked_for_submission_on"
        ,"PaymentNumber" AS "payment_number"
        ,"PaymentSource__c" AS "payment_payment_source"
        ,"ReferencedPaymentID" AS "payment_reference_payment_id"
        ,"ReferenceId" AS "payment_reference_id"
        ,"RefundAmount" AS "payment_refund_amount"
        ,"SecondPaymentReferenceId" AS "payment_second_payment_reference_id"
        ,"SettledOn" AS "payment_settled_on"
        ,"SoftDescriptor" AS "payment_soft_descriptor"
        ,"SoftDescriptorPhone" AS "payment_soft_descriptor_phone"
        ,"Source" AS "payment_source"
        ,"SourceName" AS "payment_source_name"
        ,"Status" AS "payment_status"
        ,"SubmittedOn" AS "payment_submitted_on"
        ,"TransferredToAccounting" AS "payment_transferred_to_accounting"
        ,"Type" AS "payment_type"
        ,"UnappliedAmount" AS "payment_unapplied_amount"
        ,"UpdatedById" AS "payment_updated_by_id"
        ,"UpdatedDate" AS "payment_updated_timestamp"
    FROM source
)

SELECT * FROM renamed
