{{
	config(
		materialized = 'view',
		schema = 'STAGING',
		tags = ['presentation-staging']
	)
}}

WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA__ORDER_LINE_ITEM_CURRENT') }}
)

,renamed AS (
    SELECT
        "EDM_INSERT_DTTS"
        ,"Id" AS "order_line_item_id"
        ,"OrderId" AS "order_line_item_order_id"
        ,"AccountId" AS "billing_account_id"
        ,"ProductRatePlanChargeId" AS "product_rateplan_charge_id"
        ,"Amount" AS "order_line_item_amount"
        ,"AmountPerUnit" AS "order_line_item_amount_per_unit"
        ,"AmountWithoutTax" AS "order_line_item_amount_without_tax"
        ,"BillingRule" AS "order_line_item_billing_rule"
        ,"BillTargetDate" AS "order_line_item_bill_target_date"
        ,"CreatedById" AS "order_line_item_created_by_id"
        ,"CreatedDate" AS "order_line_item_created_timestamp"
        ,"Currency" AS "currency"
        ,"Description" AS "order_line_item_description"
        ,"Discount" AS "order_line_item_discount"
        ,"entitlementsetid__c" AS "entitlement_set_id"
        ,"InlineDiscountPerUnit" AS "order_line_item_inline_discount_per_unit"
        ,"InlineDiscountType" AS "order_line_item_inline_discount_type"
        ,"ItemCategory" AS "order_line_item_item_category"
        ,"ItemName" AS "order_line_item_item_name"
        ,"ItemNumber" AS "order_line_item_item_number"
        ,"ItemState" AS "order_line_item_item_state"
        ,"ItemType" AS "order_line_item_item_type"
        ,"ListPrice" AS "order_line_item_list_price"
        ,"ListPricePerUnit" AS "order_line_item_list_price_per_unit"
        ,"Quantity" AS "order_line_item_quantity"
        ,"QuantityAvailableForReturn" AS "order_line_item_quantity_available_for_return"
        ,"QuantityFulfilled" AS "order_line_item_quantity_fulfilled"
        ,"QuantityPendingFulfillment" AS "order_line_item_quantity_pending_fulfillment"
        ,"RelatedSubscriptionNumber" AS "subscription_name"
        ,"RequiresFulfillment" AS "order_line_item_requires_fulfillment"
        ,"SoldTo" AS "order_line_item_sold_to"
        ,"SourceSystem__c" AS "order_line_item_source_system"
        ,"TrackingID__c" AS "order_line_item_tracking_id"
        ,"TransactionStartDate" AS "order_line_item_transaction_start_date"
        ,"TransactionEndDate" AS "order_line_item_transaction_end_date"
        ,"UpdatedById" AS "order_line_item_updated_by_id"
        ,"UpdatedDate" AS "order_line_item_updated_timestamp"
        ,"Country__c" AS "order_line_item_country"
        ,"Giftcode__c" AS "order_line_item_giftcode"
        ,"giftcodeismethod__c" AS "order_line_item_giftcode_is_payment_method"
        ,"SourceSystemUserID__c" AS "order_line_item_source_system_user_id"
        ,"PartnerOfferName__c" AS "order_line_item_partner_offer_name"
        ,"Platform__c" AS "order_line_item_Platform"
        ,"Is3ppExitFlow__c" AS "is_3pp_exit_flow"
    FROM SOURCE
)

SELECT * FROM renamed
