WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA__INVOICE') }}
)

, renamed AS (
    SELECT
        "Id" AS "invoice_id"
        ,"AccountId" AS "billing_account_id"
        ,"SourceId" AS "invoice_source_id"
        ,"BillToContactSnapshotId" AS "bill_to_contact_snapshot_id"
        ,"SoldToContactSnapshotId" AS "sold_to_contact_snapshot_id"
        ,"AdjustmentAmount" AS "invoice_adjustment_amount"
        ,"Amount" AS "invoice_amount"
        ,"AmountWithoutTax" AS "invoice_amount_without_tax"
        ,"AutoPay" AS "invoice_is_auto_pay"
        ,"Balance" AS "invoice_balance"
        ,"Comments" AS "invoice_comments"
        ,"CreatedById" AS "invoice_created_by_id"
        ,"CreatedDate" AS "invoice_created_timestamp"
        ,"CreditBalanceAdjustmentAmount" AS "invoice_credit_balanced_adjustment_amount"
        ,"DueDate" AS "invoice_due_date"
        ,"IncludesOneTime" AS "invoice_includes_one_time"
        ,"IncludesRecurring" AS "invoice_includes_recurring"
        ,"IncludesUsage" AS "invoice_inclues_usage"
        ,"InvoiceDate" AS "invoice_date"
        ,"InvoiceNumber" AS "invoice_number"
        ,"LastEmailSentDate" AS "invoice_last_email_sent_date"
        ,"PaymentAmount" AS "invoice_payment_amount"
        ,"PostedBy" AS "invoice_posted_by"
        ,"PostedDate" AS "invoice_posted_timestamp"
        ,"RefundAmount" AS "invoice_refund_amount"
        ,"Reversed" AS "invoice_is_reversed"
        ,"Source" AS "invoice_source"
        ,"Status" AS "invoice_status"
        ,"TargetDate" AS "invoice_target_date"
        ,"TaxAmount" AS "invoice_tax_amount"
        ,"TaxExemptAmount" AS "invoice_tax_exempt_amount"
        ,"TransferredToAccounting" AS "invoice_transferred_to_accounting"
        ,"UpdatedById" AS "invoice_updated_by_id"
        ,"UpdatedDate" AS "invoice_updated_timestamp"
        ,"NonCPRRetryStatus__c" AS "invoice_non_cpr_retry_status"
        ,"NonCPRRetryAttemptCounter__c" AS "invoice_non_cpr_retry_attempt_count"
        ,"NonCPRNextRetryDate__c" AS "invoice_non_cpr_next_retry_date"
        ,"RetryStatus__c" AS "invoice_retry_status"
    FROM source
)

SELECT * FROM renamed
