WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA_SHARE__RATE_PLAN_CURRENT') }}
)

,renamed AS (
    SELECT
        "ID" AS "rateplan_id"
        ,"SUBSCRIPTIONID" AS "subscription_id"
        ,"ENTITLEMENTSETID__C" AS "entitlement_set_id"
        ,"AMENDMENTID" AS "amendment_id"
        ,"PRODUCTRATEPLANID" AS "product_rateplan_id"
        ,"TRACKINGID__C" AS "rateplan_tracking_id"
        ,"SOURCESYSTEMUSERID__C" AS "rateplan_source_system_user_id"
        ,"SOURCESYSTEM__C" AS "rateplan_source_system_name"
        ,"AMENDMENTTYPE" AS "rateplan_amendment_type"
        ,"CREATEDBYID" AS "rateplan_created_by_id"
        ,"CREATEDDATE" AS "rateplan_created_timestamp"
        ,"NAME" AS "rateplan_name"
        ,"UPDATEDBYID" AS "rateplan_updated_by_id"
        ,"UPDATEDDATE" AS "rateplan_updated_timestamp"
        ,"BILLINGTYPE__C" AS "rateplan_billing_type"
        ,"PARTNERID__C" AS "rateplan_partner_id"
        ,"PARTNERUSERID__C" AS "rateplan_partner_user_id"
        ,"PAYMENTGATEWAY__C" AS "payment_gateway"
        ,"PAYMENTMETHODID__C" AS "payment_method_id"
        ,"PRODUCTTYPE__C" AS "rateplan_product_type"
        ,"NEXTINVOICEDATE__C" AS "rateplan_next_invoice_date"
        ,"CONTEXT__C" AS "rateplan_context"
    FROM source
)

SELECT * FROM renamed
