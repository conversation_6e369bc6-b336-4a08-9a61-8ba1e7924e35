WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA__ACCOUNT_CURRENT') }}
)

, renamed AS (
    SELECT
        "Id" AS "billing_account_id"
        ,"CrmId" AS "crm_account_id"
        ,"ParentId" AS "billing_account_parent_id"
        ,"SequenceSetId" AS "billing_account_sequence_set_id"
        ,"TaxExemptCertificateID" AS "billing_account_tax_exempt_certificate_id"
        ,"BillToContactId" AS "bill_to_contact_id"
        ,"DefaultPaymentMethodId" AS "default_payment_method_id"
        ,"SoldToContactId" AS "sold_to_contact_id"
        ,"DaznUserId__c" AS "dazn_user_id"
        ,"AccountNumber" AS "billing_account_number"
        ,"AdditionalEmailAddresses" AS "billing_account_additional_email_addresses__hash"
        ,"AllowInvoiceEdit" AS "billing_account_is_allowed_invoice_edit"
        ,"AutoPay" AS "billing_account_is_auto_pay"
        ,"Balance" AS "billing_account_balance"
        ,"Batch" AS "billing_account_batch_name"
        ,"BcdSettingOption" AS "billing_account_cycle_day_setting_option"
        ,"BillCycleDay" AS "billing_account_bill_cycle_day"
        ,"CommunicationProfileId" AS "billing_account_communication_profile_id"
        ,"Company__c" AS "billing_account_company_name"
        ,"CreatedById" AS "billing_account_created_by_id"
        ,"CreatedDate" AS "billing_account_created_timestamp"
        ,"CreditBalance" AS "billing_account_credit_balance"
        ,"Currency" AS "billing_account_currency_code"
        ,"CustomerServiceRepName" AS "billing_account_customer_service_rep_name"
        ,"InvoiceDeliveryPrefsEmail" AS "billing_account_has_invoice_delivery_preference_email"
        ,"InvoiceDeliveryPrefsPrint" AS "billing_account_has_invoice_delivery_preference_print"
        ,"InvoiceTemplateId" AS "billing_account_invoice_template_id"
        ,"LastInvoiceDate" AS "billing_account_last_invoice_date"
        ,"Mrr" AS "billing_account_monthly_recurring_revenue"
        ,"Name" AS "billing_account_customer_full_name__hash"
        ,"Notes" AS "billing_account_notes"
        ,"PaymentGateway" AS "billing_account_payment_gateway"
        ,"PaymentTerm" AS "billing_account_payment_term"
        ,"PurchaseOrderNumber" AS "billing_account_purchase_order_number"
        ,"SalesRepName" AS "billing_account_sales_rep_name"
        ,"Status" AS "billing_account_status"
        ,"TaxCompanyCode" AS "billing_account_tax_company_code"
        ,"TaxExemptCertificateType" AS "billing_account_tax_exempt_certificate_type"
        ,"TaxExemptDescription" AS "billing_account_tax_exempt_description"
        ,"TaxExemptEffectiveDate" AS "billing_account_tax_exempt_effective_date"
        ,"TaxExemptEntityUseCode" AS "billing_account_tax_exempt_entity_use_code"
        ,"TaxExemptExpirationDate" AS "billing_account_tax_exempt_expiration_date"
        ,"TaxExemptIssuingJurisdiction" AS "billing_account_tax_exempt_issuing_jurisdiction"
        ,"TaxExemptStatus" AS "billing_account_tax_exempt_status"
        ,"TotalDebitMemoBalance" AS "billing_account_total_debit_memo_balance"
        ,"TotalInvoiceBalance" AS "billing_account_total_invoice_balance"
        ,"UnappliedBalance" AS "billing_account_unapplied_balance"
        ,"UnappliedCreditMemoAmount" AS "billing_account_unapplied_credit_memo_amount"
        ,"UpdatedById" AS "billing_account_updated_by_id"
        ,"UpdatedDate" AS "billing_account_last_updated_timestamp"
        ,"VATId" AS "billing_account_vat_id"
        ,IFNULL("APM__c", FALSE) AS "billing_account_has_advanced_payment_manager"
    FROM source
)

SELECT * FROM renamed
