WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA_SHARE__RATE_PLAN_CHARGE_CURRENT') }}
)

,renamed AS (
    SELECT
        "ID" AS "rateplan_charge_id"
        ,"RATEPLANID" AS "rateplan_id"
        ,"ORIGIN<PERSON>ID" AS "rateplan_charge_original_id"
        ,"PRODUCTRATEPLANCHARGEID" AS "product_rateplan_charge_id"
        ,"POSTSIGNUPGIFTCODE__C" AS "rateplan_charge_post_sign_up_giftcode"
        ,"POSTSIG<PERSON>PGIFTCODECAMPAIGNNAME__C" AS "rateplan_charge_post_sign_up_giftcode_campaign_name"
        ,"ACCOUNTINGCODE" AS "rateplan_charge_accounting_code"
        ,"APPLYDISCOUNTTO" AS "rateplan_charge_apply_discount_to"
        ,"BILLCYCLEDAY" AS "rateplan_charge_bill_cycle_day"
        ,"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" AS "rateplan_charge_bill_cycle_type"
        ,"BIL<PERSON>ING<PERSON>ERIOD" AS "rateplan_charge_billing_period"
        ,"BIL<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>IGNMENT" AS "rateplan_charge_billing_period_alignment"
        ,"B<PERSON><PERSON>INGTIMING" AS "rateplan_charge_billing_timing"
        ,"CHARGEDTHROUGHDATE" AS "rateplan_charge_charged_through_date"
        ,"CHARGEMODEL" AS "rateplan_charge_charge_model"
        ,"CHARGENUMBER" AS "rateplan_charge_charge_number"
        ,"CHARGETYPE" AS "rateplan_charge_charge_type"
        ,"CREATEDBYID" AS "rateplan_charge_created_by_id"
        ,"CREATEDDATE" AS "rateplan_charge_created_timestamp"
        ,"DESCRIPTION" AS "rateplan_charge_description"
        ,"DISCOUNTLEVEL" AS "rateplan_charge_discount_level"
        ,"DMRC" AS "rateplan_charge_delta_monthly_recurring_charge"
        ,"DTCV" AS "rateplan_charge_delta_total_contract_value"
        ,"EFFECTIVEENDDATE" AS "rateplan_charge_effective_end_date"
        ,"EFFECTIVESTARTDATE" AS "rateplan_charge_effective_start_date"
        ,"ENDDATECONDITION" AS "rateplan_charge_end_date_condition"
        ,"ISLASTSEGMENT" AS "rateplan_charge_is_last_segment"
        ,"LISTPRICEBASE" AS "rateplan_charge_list_price_base"
        ,"MRR" AS "rateplan_charge_monthly_recurring_revenue"
        ,"NAME" AS "rateplan_charge_name"
        ,"NUMBEROFPERIODS" AS "rateplan_charge_number_of_periods"
        ,"OVERAGECALCULATIONOPTION" AS "rateplan_charge_overage_calculation_option"
        ,"OVERAGEUNUSEDUNITSCREDITOPTION" AS "rateplan_charge_overage_used_units_credit_option"
        ,"PRICECHANGEOPTION" AS "rateplan_charge_price_change_option"
        ,"PRICEINCREASEPERCENTAGE" AS "rateplan_charge_prince_increase_percentage"
        ,"PROCESSEDTHROUGHDATE" AS "rateplan_charge_processed_through_date"
        ,"QUANTITY" AS "rateplan_charge_quantity"
        ,"RATINGGROUP" AS "rateplan_charge_rating_group"
        ,"REVENUERECOGNITIONRULENAME" AS "rateplan_charge_revenue_recognition_rule_name"
        ,"REVRECCODE" AS "rateplan_charge_revenue_recognition_code"
        ,"REVRECTRIGGERCONDITION" AS "rateplan_charge_revenue_recognition_trigger_condition"
        ,"SEGMENT" AS "rateplan_charge_segment"
        ,"SPECIFICBILLINGPERIOD" AS "rateplan_charge_specific_billing_period"
        ,"SPECIFICENDDATE" AS "rateplan_charge_specific_end_date"
        ,"TCV" AS "rateplan_charge_total_contract_value"
        ,"TRIGGERDATE" AS "rateplan_charge_trigger_date"
        ,"TRIGGEREVENT" AS "rateplan_charge_trigger_event"
        ,"UOM" AS "rateplan_charge_unit_of_measure"
        ,"UPDATEDBYID" AS "rateplan_charge_updated_by_id"
        ,"UPDATEDDATE" AS "rateplan_charge_updated_timestamp"
        ,"UPTOPERIODS" AS "rateplan_charge_up_to_periods"
        ,"UPTOPERIODSTYPE" AS "rateplan_charge_up_to_periods_type"
        ,"VERSION" AS "rateplan_charge_version"
        ,"WEEKLYBILLCYCLEDAY" AS "rateplan_charge_weekly_bill_cycle_day"
    FROM source
)

SELECT * FROM renamed
