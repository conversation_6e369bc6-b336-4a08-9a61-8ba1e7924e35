WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA__INVOICE_ITEM') }}
)

, renamed AS (
    SELECT
        "Id" AS "invoice_item_id"
        ,"AddonOneTimeSubscriptionId__c" AS "invoice_item_addon_one_time_subscription_id"
        ,"AddonOneTimeSubscriptionRPC__c" AS "invoice_item_addon_one_time_product_rateplan_charge_id"
        ,"AppliedToInvoiceItemId" AS "invoice_item_applied_to_invoice_item_id"
        ,"SubscriptionId" AS "subscription_id"
        ,"RatePlanChargeId" AS "rateplan_charge_id"
        ,"SubscriptionNumber" AS "subscription_number"
        ,"InvoiceId" AS "invoice_id"
        ,"AccountingCode" AS "invoice_item_accounting_code"
        ,"Balance" AS "invoice_item_balance"
        ,"ChargeAmount" AS "invoice_item_charge_amount"
        ,"ChargeDate" AS "invoice_item_charge_timestamp"
        ,"ChargeName" AS "invoice_item_charge_name"
        ,"CreatedById" AS "invoice_item_created_by_id"
        ,"CreatedDate" AS "invoice_item_created_timestamp"
        ,"ProcessingType" AS "invoice_item_processing_type"
        ,"Quantity" AS "invoice_item_quantity"
        ,"RevRecStartDate" AS "invoice_item_revenue_recognition_start_date"
        ,"ServiceEndDate" AS "invoice_item_service_end_date"
        ,"ServiceStartDate" AS "invoice_item_service_start_date"
        ,"SKU" AS "invoice_item_sku"
        ,"TaxAmount" AS "invoice_item_tax_amount"
        ,"TaxCode" AS "invoice_item_tax_code"
        ,"TaxExemptAmount" AS "invoice_item_tax_exempt_amount"
        ,"TaxMode" AS "invoice_item_tax_mode"
        ,"UnitPrice" AS "invoice_item_unit_price"
        ,"UOM" AS "invoice_item_unit_of_measure"
        ,"UpdatedById" AS "invoice_item_updated_by_id"
        ,"UpdatedDate" AS "invoice_item_updated_timestamp"
        ,"SourceItemType" AS "invoice_item_source_item_type" 
        ,"OrderLineItemId" AS "invoice_item_order_line_item_id"
    FROM source
)

SELECT * FROM renamed
