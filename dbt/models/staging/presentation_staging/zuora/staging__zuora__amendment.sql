WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA__AMENDMENT') }}
)

, renamed AS (
    SELECT
        "Id" AS "amendment_id"
        , "SubscriptionId" AS "subscription_id"
        , "AutoRenew" AS "amendment_is_auto_renew"
        , "Code" AS "amendment_code"
        , "ContractEffectiveDate" AS "amendment_contract_effective_date"
        , "CreatedById" AS "amendment_created_by_id"
        , "CreatedDate" AS "amendment_created_timestamp"
        , "CurrentTerm" AS "amendment_current_term"
        , "CurrentTermPeriodType" AS "amendment_current_term_period_type"
        , "CustomerAcceptanceDate" AS "amendment_customer_acceptance_date"
        , "Description" AS "amendment_description"
        , "EffectiveDate" AS "amendment_effective_date"
        , "Name" AS "amendment_name"
        , "RenewalSetting" AS "amendment_renewal_setting"
        , "RenewalTerm" AS "amendment_renewal_term"
        , "RenewalTermPeriodType" AS "amendment_renewal_term_period_type"
        , "ResumeDate" AS "amendment_resume_date"
        , "ServiceActivationDate" AS "amendment_service_activation_date"
        , "SpecificUpdateDate" AS "amendment_specific_update_date"
        , "Status" AS "amendment_status"
        , "SuspendDate" AS "amendment_suspend_date"
        , "TermStartDate" AS "amendment_term_start_date"
        , "TermType" AS "amendment_term_type"
        , "Type" AS "amendment_type"
        , "UpdatedById" AS "amendment_updated_by_id"
        , "UpdatedDate" AS "amendment_updated_timestamp"
    FROM source
)

SELECT * FROM renamed
