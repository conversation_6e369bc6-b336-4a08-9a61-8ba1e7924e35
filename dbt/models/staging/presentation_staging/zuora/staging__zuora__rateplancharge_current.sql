WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA__RATE_PLAN_CHARGE_CURRENT') }}
)

, renamed AS (
    SELECT
        "Id" AS "rateplan_charge_id"
        ,"RatePlanId" AS "rateplan_id"
        ,"OriginalId" AS "rateplan_charge_original_id"
        ,"ProductRatePlanChargeId" AS "product_rateplan_charge_id"
        ,"PostSignUpGiftcode__c" AS "rateplan_charge_post_sign_up_giftcode"
        ,"PostSignUpGiftcodeCampaignName__c" AS "rateplan_charge_post_sign_up_giftcode_campaign_name"
        ,"AccountingCode" AS "rateplan_charge_accounting_code"
        ,"ApplyDiscountTo" AS "rateplan_charge_apply_discount_to"
        ,"BillCycleDay" AS "rateplan_charge_bill_cycle_day"
        ,"BillCycleType" AS "rateplan_charge_bill_cycle_type"
        ,"BillingPeriod" AS "rateplan_charge_billing_period"
        ,"BillingPeriodAlignment" AS "rateplan_charge_billing_period_alignment"
        ,"BillingTiming" AS "rateplan_charge_billing_timing"
        ,"ChargedThroughDate" AS "rateplan_charge_charged_through_date"
        ,"ChargeModel" AS "rateplan_charge_charge_model"
        ,"ChargeNumber" AS "rateplan_charge_charge_number"
        ,"ChargeType" AS "rateplan_charge_charge_type"
        ,"CreatedById" AS "rateplan_charge_created_by_id"
        ,"CreatedDate" AS "rateplan_charge_created_timestamp"
        ,"Description" AS "rateplan_charge_description"
        ,"DiscountLevel" AS "rateplan_charge_discount_level"
        ,"DMRC" AS "rateplan_charge_delta_monthly_recurring_charge"
        ,"DTCV" AS "rateplan_charge_delta_total_contract_value"
        ,"EffectiveEndDate" AS "rateplan_charge_effective_end_date"
        ,"EffectiveStartDate" AS "rateplan_charge_effective_start_date"
        ,"EndDateCondition" AS "rateplan_charge_end_date_condition"
        ,"IsLastSegment" AS "rateplan_charge_is_last_segment"
        ,"ListPriceBase" AS "rateplan_charge_list_price_base"
        ,"MRR" AS "rateplan_charge_monthly_recurring_revenue"
        ,"Name" AS "rateplan_charge_name"
        ,"NumberOfPeriods" AS "rateplan_charge_number_of_periods"
        ,"OverageCalculationOption" AS "rateplan_charge_overage_calculation_option"
        ,"OverageUnusedUnitsCreditOption" AS "rateplan_charge_overage_used_units_credit_option"
        ,"PriceChangeOption" AS "rateplan_charge_price_change_option"
        ,"PriceIncreasePercentage" AS "rateplan_charge_prince_increase_percentage"
        ,"ProcessedThroughDate" AS "rateplan_charge_processed_through_date"
        ,"Quantity" AS "rateplan_charge_quantity"
        ,"RatingGroup" AS "rateplan_charge_rating_group"
        ,"RevenueRecognitionRuleName" AS "rateplan_charge_revenue_recognition_rule_name"
        ,"RevRecCode" AS "rateplan_charge_revenue_recognition_code"
        ,"RevRecTriggerCondition" AS "rateplan_charge_revenue_recognition_trigger_condition"
        ,"Segment" AS "rateplan_charge_segment"
        ,"SpecificBillingPeriod" AS "rateplan_charge_specific_billing_period"
        ,"SpecificEndDate" AS "rateplan_charge_specific_end_date"
        ,"TCV" AS "rateplan_charge_total_contract_value"
        ,"TriggerDate" AS "rateplan_charge_trigger_date"
        ,"TriggerEvent" AS "rateplan_charge_trigger_event"
        ,"UOM" AS "rateplan_charge_unit_of_measure"
        ,"UpdatedById" AS "rateplan_charge_updated_by_id"
        ,"UpdatedDate" AS "rateplan_charge_updated_timestamp"
        ,"UpToPeriods" AS "rateplan_charge_up_to_periods"
        ,"UpToPeriodsType" AS "rateplan_charge_up_to_periods_type"
        ,"Version" AS "rateplan_charge_version"
        ,"WeeklyBillCycleDay" AS "rateplan_charge_weekly_bill_cycle_day"
    FROM source
)

SELECT * FROM renamed
