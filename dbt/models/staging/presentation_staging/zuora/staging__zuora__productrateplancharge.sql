{{
	config(
		materialized = 'view',
		schema = 'STAGING',
		tags = ['presentation-staging']
	)
}}

WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA__PRODUCT_RATE_PLAN_CHARGE') }}
)

,renamed AS (
        SELECT
        "EDM_INSERT_DTTS"
        ,"Id" AS "product_rateplan_charge_id"
        ,"ProductRatePlanId" AS "product_rateplan_id"
        ,"AccountingCode" AS "product_rateplan_charge_accounting_code"
        ,"ApplyDiscountTo" AS "product_rateplan_charge_apply_discount_to"
        ,"BillCycleDay" AS "product_rateplan_charge_bill_cycle_day"
        ,"BillCycleType" AS "product_rateplan_charge_bill_cycle_type"
        ,"BillingPeriod" AS "product_rateplan_charge_billing_period"
        ,"BillingPeriodAlignment" AS "product_rateplan_charge_billing_period_alignment"
        ,"BillingTiming" AS "product_rateplan_charge_billing_timing"
        ,"ChargeModel" AS "product_rateplan_charge_charge_model"
        ,"ChargeType" AS "product_rateplan_charge_charge_type"
        ,"CreatedById" AS "product_rateplan_charge_created_by_id"
        ,"CreatedDate" AS "product_rateplan_charge_created_timestamp"
        ,"DefaultQuantity" AS "product_rateplan_charge_default_quantity"
        ,"DeferredRevenueAccount" AS "product_rateplan_charge_deferred_revenue_account"
        ,"Description" AS "product_rateplan_charge_description"
        ,"DiscountLevel" AS "product_rateplan_charge_discount_level"
        ,"EndDateCondition" AS "product_rateplan_charge_end_date_condition"
        ,"IncludedUnits" AS "product_rateplan_charge_included_units"
        ,"LegacyRevenueReporting" AS "product_rateplan_charge_legacy_revenue_reporting"
        ,"ListPriceBase" AS "product_rateplan_charge_list_price_base"
        ,"MaxQuantity" AS "product_rateplan_charge_max_quantity"
        ,"MinQuantity" AS "product_rateplan_charge_min_quantity"
        ,"Name" AS "product_rateplan_charge_name"
        ,"NumberOfPeriod" AS "product_rateplan_charge_number_of_periods"
        ,"OverageCalculationOption" AS "product_rateplan_charge_overage_calculation_option"
        ,"OverageUnusedUnitsCreditOption" AS "product_rateplan_charge_overage_used_units_credit_option"
        ,"PriceChangeOption" AS "product_rateplan_charge_price_change_option"
        ,"PriceIncreasePercentage" AS "product_rateplan_charge_prince_increase_percentage"
        ,"RatingGroup" AS "product_rateplan_charge_rating_group"
        ,"RecognizedRevenueAccount" AS "product_rateplan_charge_item_adjustment_recognized_revenue_account"
        ,"RevenueRecognitionRuleName" AS "product_rateplan_charge_revenue_recognition_rule_name"
        ,"RevRecCode" AS "product_rateplan_charge_revenue_recognition_code"
        ,"RevRecTriggerCondition" AS "product_rateplan_charge_revenue_recognition_trigger_condition"
        ,"SmoothingModel" AS "product_rateplan_charge_smoothing_model"
        ,"SpecificBillingPeriod" AS "product_rateplan_charge_specific_billing_period"
        ,"Taxable" AS "product_rateplan_charge_taxable"
        ,"TaxCode" AS "product_rateplan_charge_item_tax_code"
        ,"TaxMode" AS "product_rateplan_charge_item_tax_mode"
        ,"TriggerEvent" AS "product_rateplan_charge_trigger_event"
        ,"UOM" AS "product_rateplan_charge_unit_of_measure"
        ,"UpdatedById" AS "product_rateplan_charge_updated_by_id"
        ,"UpdatedDate" AS "product_rateplan_charge_updated_timestamp"
        ,"UpToPeriods" AS "product_rateplan_charge_up_to_periods"
        ,"UpToPeriodsType" AS "product_rateplan_charge_up_to_periods_type"
        ,"UsageRecordRatingOption" AS "product_rateplan_charge_usage_record_rating_option"
        ,"UseDiscountSpecificAccountingCode" AS "product_rateplan_charge_use_discount_specific_accounting_code"
        ,"UseTenantDefaultForPriceChange" AS "product_rateplan_charge_use_tenant_default_for_price_change"
        ,"WeeklyBillCycleDay" AS "product_rateplan_charge_weekly_bill_cycle_day"
    FROM source
)

SELECT * FROM renamed
