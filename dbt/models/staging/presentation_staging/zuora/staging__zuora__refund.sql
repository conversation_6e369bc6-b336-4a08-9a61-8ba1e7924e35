{{
	config(
		materialized = 'view',
		schema = 'STAGING',
		tags =  ['presentation-staging']
		)
}}


WITH source AS(
	SELECT * FROM {{  source('CURATED', 'CURATED__ZUORA__REFUND')  }}
)

, renamed AS (
	SELECT 
		"AccountingCode" AS "refund_accouting_code"
		,"Amount" AS "refund_amount"
		,"CancelledOn" AS "refund_cancelled_on"
		,"Comment" AS "refund_comment"
		,"CreatedById" AS "refund_created_by_id"
		,"CreatedDate" AS "refund_created_date"
		,"Gateway" AS "refund_gateway"
		,"GatewayResponse" AS "refund_gateway_response"
		,"GatewayResponseCode" AS "refund_gateway_response_code"
		,"GatewayState" AS "refund_gateway_state"
		,"Id" AS "refund_id"
		,"MarkedForSubmissionOn" AS "refund_marked_for_submission_on"
		,"MethodType" AS "refund_method_type"
		,"PaymentMethodId" AS "refund_payment_method_id"
		,"ReasonCode" AS "refund_reason_code"
		,"ReferenceID" AS "refund_reference_id"
		,"RefundDate" AS "refund_date"
		,"RefundNumber" AS "refund_number"
		,"RefundReferenceId__c" AS "refund_reference_id"
		,"RefundSource__c" AS "refund_source"
		,"RefundTransactionTime" AS "refund_transaction_time"
		,"SecondRefundReferenceId" AS "refund_second_refund_reference_id"
		,"SettledOn" AS "refund_settled_on"
		,"SoftDescriptor" AS "refund_soft_descriptor"
		,"SoftDescriptorPhone" AS "refund_soft_descriptor_phone"
		,"SourceType" AS "refund_source_type"
		,"Status" AS "refund_status"
		,"SubmittedOn" AS "refund_submitted_on"
		,"TransferredToAccounting" AS "refund_transferred_to_accounting"
		,"Type" AS "refund_type"
		,"UpdatedById" AS "refund_updated_by_id"
		,"UpdatedDate" AS "refund_updated_date"
		,"AccountId" AS "refund_account_id"
		,"PaymentMethodSnapshotId" AS "refund_payment_method_snapshot_id"
	
	FROM source
)

SELECT * FROM renamed
		
