WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA__SUBSCRIPTION') }}
)

, ft_patch AS (
    SELECT * FROM {{ source('PATCH', 'PATCH__SUBSCRIPTION__LEGACY_3PP_FREE_TRIALS') }}
)

, kddi_giftcodes AS (
    SELECT * FROM {{ ref('staging__kddi__sold_giftcodes_current') }}
)

, italy_retro_cancelled_subs_patch AS (
    SELECT * FROM {{ ref('italy_retro_cancelled_subs_patch') }}
)

, renamed AS (
    SELECT
        "Id" AS "subscription_id"
        , "AccountId" AS "billing_account_id"
        , "CreatorAccountId" AS "subscription_creator_billing_account_id"
        , "CreatorInvoiceOwnerId" AS "subscription_creator_invoice_owner_billing_account_id"
        , "InvoiceOwnerId" AS "subscription_invoice_owner_billing_account_id"
        , "OriginalId" AS "subscription_original_id"
        , "PreviousSubscriptionId" AS "subscription_previous_subscription_id"
        -- The patch is defined in macros/Subscription/kddi_patch.sql
        , {{ kddi_patch() }} AS "subscription_sign_up_campaign_id"
        , "DCB_Carier_Name__c" AS "subscription_direct_carrier_billing_carrier_name"
        , "DeviceInformation__c" AS "subscription_device_category"
        , CASE WHEN "Giftcode__c" IS NOT NULL THEN "Giftcode__c" 
          WHEN "PartnerOfferName__c" IS NOT NULL THEN "PartnerOfferName__c"||' - '||"AccountId" 
          ELSE NULL END AS "subscription_sign_up_giftcode"
        , "Giftcode_Old__c" AS "subscription_sign_up_giftcode_old"
        , "IPAddress__c" AS "subscription_ip_address__hash"
        , "Manually_Cancelled__c" AS "subscription_is_manually_cancelled"
        , "GiftCodeGrantsContentPortability__c" AS "subscription_giftcode_grants_content_portability"
        , "giftcodeismethod__c" AS "subscription_giftcode_is_payment_method"
        , "SourceSystem__c" AS "subscription_source_system_name"
        , "SourceSystemUserID__c" AS "subscription_source_system_user_id"
        , "TrackingID__c" AS "subscription_tracking_id"
        , "UserAgent__c" AS "subscription_user_agent"
        , "AutoRenew" AS "subscription_is_auto_renew"
        , "CancelledDate" AS "subscription_cancelled_date"
        , "ContractAcceptanceDate" AS "subscription_contract_acceptance_date"
        , "ContractEffectiveDate" AS "subscription_contract_effective_date"
        , "CountryOfSubscription__c" AS "subscription_country"
        , "CpqBundleJsonId__QT" AS "subscription_cpq_bundle_json_id"
        , "CreatedById" AS "subscription_created_by_id"
        , "CreatedDate" AS "subscription_id_created_timestamp__unpatched"
        , IFF(source."CreatedDate" > ft_patch."expiry_date", ft_patch."expiry_date", source."CreatedDate") AS  "subscription_id_created_timestamp"
        , "CurrentTerm" AS "subscription_current_term"
        , "CurrentTermPeriodType" AS "subscription_current_term_period_type"
        , "InitialTerm" AS "subscription_initial_term"
        , "InitialTermPeriodType" AS "subscription_initial_term_period_type"
        , "IsInvoiceSeparate" AS "subscription_is_invoice_separate"
        , "Name" AS "subscription_name"
        , "Notes" AS "subscription_notes"
        , "NumFreeTrialPeriods__c" AS "subscription_number_of_free_trial_periods__unpatched"
        , CASE
            -- When it's an Extended Free Trial KDDI giftcode before 2023-01-20 then it's 3 month Free Trial
            WHEN kddi_giftcodes."kddi_rateplan" = 'addon' AND source."OriginalCreatedDate" < '2023-01-20' THEN '3'
            -- When it's an Extended Free Trial KDDI giftcode after 2023-01-20 then it's 1 month Free Trial
            WHEN kddi_giftcodes."kddi_rateplan" = 'addon' AND source."OriginalCreatedDate" >= '2023-01-20' THEN '1'
            ELSE "NumFreeTrialPeriods__c"
        END AS "subscription_number_of_free_trial_periods"
        , "NumGiftPeriods__c" AS "subscription_number_of_giftcode_periods"
        , "OpportunityCloseDate__QT" AS "subscription_opportunity_close_date"
        , "OpportunityName__QT" AS "subscription_opportunity_name"
        , "OriginalCreatedDate" AS "subscription_name_original_created_timestamp"
        , "QuoteBusinessType__QT" AS "subscription_quote_business_type"
        , "QuoteNumber__QT" AS "subscription_quote_number"
        , "QuoteType__QT" AS "subscription_quote_type"
        , "ReceiptID__c" AS "subscription_receipt_id"
        , "RenewalSetting" AS "subscription_renewal_setting"
        , "RenewalTerm" AS "subscription_renewal_term"
        , "RenewalTermPeriodType" AS "subscription_renewal_term_period_type"
        , "ServiceActivationDate" AS "subscription_service_activation_date"
        , "Status" AS "subscription_status"
        , "SubscriptionEndDate" AS "subscription_end_date__unpatched"
        , IFF(source."SubscriptionEndDate" > ft_patch."expiry_date", ft_patch."expiry_date", source."SubscriptionEndDate") AS "subscription_end_date"
        , "SubscriptionStartDate" AS "subscription_start_date"
        , "TermEndDate" AS "subscription_term_end_date"
        , "TermStartDate" AS "subscription_term_start_date"
        , "TermType" AS "subscription_term_type"
        , "UpdatedById" AS "subscription_updated_by_id"
        , "UpdatedDate" AS "subscription_id_updated_timestamp__unpatched"
        , IFF(source."UpdatedDate" > ft_patch."expiry_date", ft_patch."expiry_date", source."UpdatedDate") AS "subscription_id_updated_timestamp"
        , "Version" AS "subscription_version"
        , "IsLatestVersion" AS "is_latest_subscription_version"
        , "FreeTrialPeriodsType__c" AS "subscription_free_trial_periods_type__unpatched"
        , CASE
            -- When it's an Extended Free Trial KDDI giftcode then it has monthly Free Trials
            WHEN kddi_giftcodes."kddi_rateplan" = 'addon' THEN 'months'
            ELSE "FreeTrialPeriodsType__c"
        END AS "subscription_free_trial_periods_type"
        , IFNULL("productGroup__c", 'DAZN') AS "subscription_product_group"
        , "PaymentMethodId__c" AS "subscription_payment_method_id"
    FROM source
    LEFT JOIN ft_patch ON source."Name" = ft_patch."subscription_name"
    LEFT JOIN kddi_giftcodes ON source."Giftcode__c" = kddi_giftcodes."giftcode_id"
)

, final AS (
SELECT
    "subscription_id"
    , "billing_account_id"
    , "subscription_creator_billing_account_id"
    , "subscription_creator_invoice_owner_billing_account_id"
    , "subscription_invoice_owner_billing_account_id"
    , "subscription_original_id"
    , "subscription_previous_subscription_id"
    , "subscription_sign_up_campaign_id"
    , "subscription_direct_carrier_billing_carrier_name"
    , "subscription_device_category"
    , "subscription_sign_up_giftcode"
    , "subscription_sign_up_giftcode_old"
    , "subscription_ip_address__hash"
    , "subscription_is_manually_cancelled"
    , "subscription_giftcode_grants_content_portability"
    , "subscription_giftcode_is_payment_method"
    , "subscription_source_system_name"
    , "subscription_source_system_user_id"
    , "subscription_tracking_id"
    , "subscription_user_agent"
    , "subscription_is_auto_renew"
    , "subscription_cancelled_date"
    , "subscription_contract_acceptance_date"
    , "subscription_contract_effective_date"
    , "subscription_country"
    , "subscription_cpq_bundle_json_id"
    , "subscription_created_by_id"
    , "subscription_id_created_timestamp__unpatched"
    , CASE
        WHEN italy_retro_cancelled_subs_patch."cancelled_subscription_id" IS NULL THEN "subscription_id_created_timestamp"
        ELSE DATEADD('day', -1, "subscription_id_created_timestamp")
    END AS "subscription_id_created_timestamp"
    , "subscription_current_term"
    , "subscription_current_term_period_type"
    , "subscription_initial_term"
    , "subscription_initial_term_period_type"
    , "subscription_is_invoice_separate"
    , "subscription_name"
    , "subscription_notes"
    , "subscription_number_of_free_trial_periods__unpatched"
    , "subscription_number_of_free_trial_periods"
    , "subscription_number_of_giftcode_periods"
    , "subscription_opportunity_close_date"
    , "subscription_opportunity_name"
    , "subscription_name_original_created_timestamp"
    , "subscription_quote_business_type"
    , "subscription_quote_number"
    , "subscription_quote_type"
    , "subscription_receipt_id"
    , "subscription_renewal_setting"
    , "subscription_renewal_term"
    , "subscription_renewal_term_period_type"
    , "subscription_service_activation_date"
    , "subscription_status"
    , "subscription_end_date__unpatched"
    , "subscription_end_date"
    , "subscription_start_date"
    , "subscription_term_end_date"
    , "subscription_term_start_date"
    , "subscription_term_type"
    , "subscription_updated_by_id"
    , "subscription_id_updated_timestamp__unpatched"
    , "subscription_id_updated_timestamp"
    , "subscription_version"
    , "is_latest_subscription_version"
    , "subscription_free_trial_periods_type__unpatched"
    , "subscription_free_trial_periods_type"
    , "subscription_product_group"
    , "subscription_payment_method_id"
FROM renamed
    LEFT JOIN italy_retro_cancelled_subs_patch
ON renamed."subscription_id"=italy_retro_cancelled_subs_patch."cancelled_subscription_id"
    )

SELECT * FROM final
