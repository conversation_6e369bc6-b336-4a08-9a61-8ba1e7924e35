WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA__RATE_PLAN') }}
)

, renamed AS (
    SELECT
        "Id" AS "rateplan_id"
        ,"SubscriptionId" AS "subscription_id"
        ,"EntitlementSetId__c" AS "entitlement_set_id"
        ,"AmendmentId" AS "amendment_id"
        ,"ProductRatePlanId" AS "product_rateplan_id"
        ,"TrackingId__c" AS "rateplan_tracking_id"
        ,"SourceSystemUserId__c" AS "rateplan_source_system_user_id"
        ,"SourceSystem__c" AS "rateplan_source_system_name"
        ,"AmendmentType" AS "rateplan_amendment_type"
        ,"CreatedById" AS "rateplan_created_by_id"
        ,"CreatedDate" AS "rateplan_created_timestamp"
        ,"Name" AS "rateplan_name"
        ,"UpdatedById" AS "rateplan_updated_by_id"
        ,"UpdatedDate" AS "rateplan_updated_timestamp"
        ,"BillingType__c" AS "rateplan_billing_type"
        ,IFNULL("PartnerID__c", 'Unknown') AS "rateplan_partner_id"
        ,"PartnerUserID__c" as "rateplan_partner_user_id"
        ,"PaymentGateway__c" AS "payment_gateway"
        ,"PaymentMethodId__c" AS "payment_method_id"
        ,"ProductType__c" AS "rateplan_product_type"
        ,"NextInvoiceDate__c" AS "rateplan_next_invoice_date"
        ,"Context__c" AS "rateplan_context"
        ,"SegmentID__c" AS "rateplan_segment"
        ,"AddonType__c" AS "rateplan_addon_type"
        ,"Platform__c" AS "rateplan_platform"
        ,"Is3ppExitFlow__c" AS "is_3pp_exit_flow"
        ,"PartnerOfferName__c" AS "partner_offer_name"
    FROM source
)

SELECT * FROM renamed
