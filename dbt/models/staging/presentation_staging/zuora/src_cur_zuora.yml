version: 2

sources:
  - name: CURATED
    database: TRANSFORMATION_{{ 'UAT' if env_var('AIRFLOW_VAR_ENV') == 'stage' else env_var('AIRFLOW_VAR_ENV') }}
    tables:
      - name: CURATED__ZUORA__ACCOUNT
        description: "Zuora account contains information about the customer, such as contact information, payment terms, and payment methods."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountNumber
            description: ""
            quote: true

          - name: AdditionalEmailAddresses
            description: ""
            quote: true

          - name: AllowInvoiceEdit
            description: ""
            quote: true

          - name: AutoPay
            description: ""
            quote: true

          - name: Balance
            description: ""
            quote: true

          - name: Batch
            description: ""
            quote: true

          - name: BcdSettingOption
            description: ""
            quote: true

          - name: BillCycleDay
            description: ""
            quote: true

          - name: CommunicationProfileId
            description: ""
            quote: true

          - name: Company__c
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: CreditBalance
            description: ""
            quote: true

          - name: CrmId
            description: ""
            quote: true

          - name: Currency
            description: ""
            quote: true

          - name: CustomerServiceRepName
            description: ""
            quote: true

          - name: DaznUserId__c
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: InvoiceDeliveryPrefsEmail
            description: ""
            quote: true

          - name: InvoiceDeliveryPrefsPrint
            description: ""
            quote: true

          - name: InvoiceTemplateId
            description: ""
            quote: true

          - name: LastInvoiceDate
            description: ""
            quote: true

          - name: Mrr
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: Notes
            description: ""
            quote: true

          - name: ParentId
            description: ""
            quote: true

          - name: PaymentGateway
            description: ""
            quote: true

          - name: PaymentTerm
            description: ""
            quote: true

          - name: PurchaseOrderNumber
            description: ""
            quote: true

          - name: SalesRepName
            description: ""
            quote: true

          - name: SequenceSetId
            description: ""
            quote: true

          - name: Status
            description: ""
            quote: true

          - name: TaxCompanyCode
            description: ""
            quote: true

          - name: TaxExemptCertificateID
            description: ""
            quote: true

          - name: TaxExemptCertificateType
            description: ""
            quote: true

          - name: TaxExemptDescription
            description: ""
            quote: true

          - name: TaxExemptEffectiveDate
            description: ""
            quote: true

          - name: TaxExemptEntityUseCode
            description: ""
            quote: true

          - name: TaxExemptExpirationDate
            description: ""
            quote: true

          - name: TaxExemptIssuingJurisdiction
            description: ""
            quote: true

          - name: TaxExemptStatus
            description: ""
            quote: true

          - name: TotalDebitMemoBalance
            description: ""
            quote: true

          - name: TotalInvoiceBalance
            description: ""
            quote: true

          - name: UnappliedBalance
            description: ""
            quote: true

          - name: UnappliedCreditMemoAmount
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: VATId
            description: ""
            quote: true

          - name: BillToContactId
            description: ""
            quote: true

          - name: DefaultPaymentMethodId
            description: ""
            quote: true

          - name: SoldToContactId
            description: ""
            quote: true

      - name: CURATED__ZUORA__ACCOUNTING_CODE
        description: "Contains accounting codes for various reporting transactions enabling Zuora revenue accounting."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: Category
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: GLAccountName
            description: ""
            quote: true

          - name: GLAccountNumber
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: Notes
            description: ""
            quote: true

          - name: Status
            description: ""
            quote: true

          - name: Type
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

      - name: CURATED__ZUORA__ACCOUNTING_PERIOD
        description: "An accounting period holds the information you need to perform your companys bookkeeping activities, especially tracking and reporting financial activities."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: EndDate
            description: ""
            quote: true

          - name: FiscalQuarter
            description: ""
            quote: true

          - name: FiscalYear
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: Notes
            description: ""
            quote: true

          - name: StartDate
            description: ""
            quote: true

          - name: Status
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

      - name: CURATED__ZUORA__AMENDMENT
        description: "Amendment objects hold changes to Subscription object, including its RatePlan, RatePlanCharge, and RatePlanChargeTier."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AutoRenew
            description: ""
            quote: true

          - name: Code
            description: ""
            quote: true

          - name: ContractEffectiveDate
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: CurrentTerm
            description: ""
            quote: true

          - name: CurrentTermPeriodType
            description: ""
            quote: true

          - name: CustomerAcceptanceDate
            description: ""
            quote: true

          - name: Description
            description: ""
            quote: true

          - name: EffectiveDate
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: RenewalSetting
            description: ""
            quote: true

          - name: RenewalTerm
            description: ""
            quote: true

          - name: RenewalTermPeriodType
            description: ""
            quote: true

          - name: ResumeDate
            description: ""
            quote: true

          - name: ServiceActivationDate
            description: ""
            quote: true

          - name: SpecificUpdateDate
            description: ""
            quote: true

          - name: Status
            description: ""
            quote: true

          - name: SubscriptionId
            description: ""
            quote: true

          - name: SuspendDate
            description: ""
            quote: true

          - name: TermStartDate
            description: ""
            quote: true

          - name: TermType
            description: ""
            quote: true

          - name: Type
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

      - name: CURATED__ZUORA__BILLING_RUN
        description: "Use this data source to export bill runs and statistics about the bill runs, including the number of invoices and accounts generated, statuses, and key dates"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: BillingRunNumber
            description: "The number of the billing run"
            quote: true

          - name: BillingRunType
            description: "The type of billing run"
            quote: true

          - name: CreatedById
            description: "The UserID who created the billing run"
            quote: true

          - name: CreatedDate
            description: "Date of creation"
            quote: true

          - name: EndDate
            description: "Billing run end date"
            quote: true

          - name: ErrorMessage
            description: "Error message on the billing run"
            quote: true

          - name: ExecutedDate
            description: "Billing run execution date"
            quote: true

          - name: Id
            description: "The Id of this object"
            quote: true

          - name: InvoiceDate
            description: "The date of the invoice"
            quote: true

          - name: NumberOfAccounts
            description: "The number of accounts"
            quote: true

          - name: NumberOfCreditMemos
            description: "The number of credit memos"
            quote: true

          - name: NumberOfInvoices
            description: "The number of invoices"
            quote: true

          - name: PostedDate
            description: "Date of when the billing run was posted"
            quote: true

          - name: StartDate
            description: "Start date of the billing run"
            quote: true

          - name: Status
            description: "Status of the billing run"
            quote: true

          - name: TargetDate
            description: "Target date of the billing run"
            quote: true

          - name: TargetType
            description: "The target type of the billing run"
            quote: true

          - name: TotalTime
            description: "The total time of the billing run"
            quote: true

          - name: UpdatedById
            description: "The userId that updated the billing run"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the billing run"
            quote: true

      - name: CURATED__ZUORA__CONTACT
        description: "Provides attribute to a contact referenced in the Account. It hold information about BillTo and SoldTo contacts."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountId
            description: ""
            quote: true

          - name: Address1
            description: ""
            quote: true

          - name: Address2
            description: ""
            quote: true

          - name: City
            description: ""
            quote: true

          - name: Country
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Description
            description: ""
            quote: true

          - name: Fax
            description: ""
            quote: true

          - name: FirstName
            description: ""
            quote: true

          - name: HomePhone
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: LastName
            description: ""
            quote: true

          - name: MobilePhone
            description: ""
            quote: true

          - name: NickName
            description: ""
            quote: true

          - name: OtherPhone
            description: ""
            quote: true

          - name: OtherPhoneType
            description: ""
            quote: true

          - name: PersonalEmail
            description: ""
            quote: true

          - name: PostalCode
            description: ""
            quote: true

          - name: State
            description: ""
            quote: true

          - name: TaxRegion
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: WorkEmail
            description: ""
            quote: true

          - name: WorkPhone
            description: ""
            quote: true

      - name: CURATED__ZUORA__CONTACT_SNAPSHOT
        description: "The ContactSnapshot object preserves a record at a particular point in time of the Bill-To contact or Sold-To contact on a customer account."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountId
            description: ""
            quote: true

          - name: Address1
            description: ""
            quote: true

          - name: Address2
            description: ""
            quote: true

          - name: City
            description: ""
            quote: true

          - name: ContactId
            description: ""
            quote: true

          - name: Country
            description: ""
            quote: true

          - name: County
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Description
            description: ""
            quote: true

          - name: Fax
            description: ""
            quote: true

          - name: FirstName
            description: ""
            quote: true

          - name: HomePhone
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: LastName
            description: ""
            quote: true

          - name: MobilePhone
            description: ""
            quote: true

          - name: NickName
            description: ""
            quote: true

          - name: OtherPhone
            description: ""
            quote: true

          - name: OtherPhoneType
            description: ""
            quote: true

          - name: PersonalEmail
            description: ""
            quote: true

          - name: PostalCode
            description: ""
            quote: true

          - name: State
            description: ""
            quote: true

          - name: TaxRegion
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: WorkEmail
            description: ""
            quote: true

          - name: WorkPhone
            description: ""
            quote: true

      - name: CURATED__ZUORA__F_X_CUSTOM_RATE
        description: "Use this data source to export your custom foreign exchange rates that you imported using Mass Updater. The Custom Exchange Rate data source object is not joined to any other object"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: CreatedById
            description: "This is the UserID / API that created that record"
            quote: true

          - name: CreatedDate
            description: "The date at which the exchange rate record was created in Zuora"
            quote: true

          - name: CurrencyFrom
            description: "This is the currency that the conversion will be applied from"
            quote: true

          - name: CurrencyTo
            description: "This is the currency that the conversion will be applied to"
            quote: true

          - name: Id
            description: "This is the Zuora Unique Identifier for that record"
            quote: true

          - name: Rate
            description: "This is the actual exchange rate"
            quote: true

          - name: RateDate
            description: "This is the date at which the provider sent the exchange rate data to Zuora"
            quote: true

          - name: UpdatedById
            description: "This is the UserID / API that created that record"
            quote: true

          - name: UpdatedDate
            description: "This is the date at which the exchange rate record was updated. Should be the same as created in most cases"
            quote: true

      - name: CURATED__ZUORA__INVOICE
        description: "The Invoice object provides information about customers accounts for invoices, including dates, the status, and amounts."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AdjustmentAmount
            description: ""
            quote: true

          - name: Amount
            description: ""
            quote: true

          - name: AmountWithoutTax
            description: ""
            quote: true

          - name: AutoPay
            description: ""
            quote: true

          - name: Balance
            description: ""
            quote: true

          - name: Comments
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: CreditBalanceAdjustmentAmount
            description: ""
            quote: true

          - name: DueDate
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: IncludesOneTime
            description: ""
            quote: true

          - name: IncludesRecurring
            description: ""
            quote: true

          - name: IncludesUsage
            description: ""
            quote: true

          - name: InvoiceDate
            description: ""
            quote: true

          - name: InvoiceNumber
            description: ""
            quote: true

          - name: LastEmailSentDate
            description: ""
            quote: true

          - name: PaymentAmount
            description: ""
            quote: true

          - name: PostedBy
            description: ""
            quote: true

          - name: PostedDate
            description: ""
            quote: true

          - name: RefundAmount
            description: ""
            quote: true

          - name: Reversed
            description: ""
            quote: true

          - name: Source
            description: ""
            quote: true

          - name: SourceId
            description: ""
            quote: true

          - name: Status
            description: ""
            quote: true

          - name: TargetDate
            description: ""
            quote: true

          - name: TaxAmount
            description: ""
            quote: true

          - name: TaxExemptAmount
            description: ""
            quote: true

          - name: TransferredToAccounting
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: AccountId
            description: ""
            quote: true

          - name: BillToContactSnapshotId
            description: ""
            quote: true

          - name: SoldToContactSnapshotId
            description: ""
            quote: true

          - name: RetryStatus__c
            description: ""
            quote: true

      - name: CURATED__ZUORA__INVOICE_ITEM
        description: "An invoice item is an individual line item in an invoice."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingCode
            description: ""
            quote: true

          - name: AddonOneTimeSubscriptionId__c
            description: "Subscription ID for the addon purchase that triggered this invoice item"
            quote: true

          - name: AddonOneTimeSubscriptionRPC__c
            description: "Product Rate Plan ID for the addon purchase that triggered this invoice item"
            quote: true

          - name: AppliedToInvoiceItemId
            description: ""
            quote: true

          - name: Balance
            description: ""
            quote: true

          - name: ChargeAmount
            description: ""
            quote: true

          - name: ChargeDate
            description: ""
            quote: true

          - name: ChargeName
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: ProcessingType
            description: ""
            quote: true

          - name: Quantity
            description: ""
            quote: true

          - name: RevRecStartDate
            description: ""
            quote: true

          - name: ServiceEndDate
            description: ""
            quote: true

          - name: ServiceStartDate
            description: ""
            quote: true

          - name: SKU
            description: ""
            quote: true

          - name: SubscriptionId
            description: ""
            quote: true

          - name: TaxAmount
            description: ""
            quote: true

          - name: TaxCode
            description: ""
            quote: true

          - name: TaxExemptAmount
            description: ""
            quote: true

          - name: TaxMode
            description: ""
            quote: true

          - name: UnitPrice
            description: ""
            quote: true

          - name: UOM
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: InvoiceId
            description: ""
            quote: true

          - name: RatePlanChargeId
            description: ""
            quote: true

          - name: SubscriptionNumber
            description: "The number or name of the subscription, matching values in column 'SubscriptionName' from table Subscription."
            quote: true

          - name: SourceItemType
            description: "source of ItemType"
            quote: true

          - name: OrderLineItemId
            description: "Id of OrderLineItem"
            quote: true

      - name: CURATED__ZUORA__INVOICE_ITEM_ADJUSTMENT
        description: "Record of a modification of a specific line item invoice."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountId
            description: ""
            quote: true

          - name: AccountingCode
            description: ""
            quote: true

          - name: AdjustmentDate
            description: ""
            quote: true

          - name: AdjustmentNumber
            description: ""
            quote: true

          - name: Amount
            description: ""
            quote: true

          - name: CancelledById
            description: ""
            quote: true

          - name: CancelledDate
            description: ""
            quote: true

          - name: Comment
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: InvoiceId
            description: ""
            quote: true

          - name: InvoiceItemName
            description: ""
            quote: true

          - name: InvoiceNumber
            description: ""
            quote: true

          - name: ReasonCode
            description: ""
            quote: true

          - name: ReferenceId
            description: ""
            quote: true

          - name: ServiceEndDate
            description: ""
            quote: true

          - name: ServiceStartDate
            description: ""
            quote: true

          - name: SourceId
            description: ""
            quote: true

          - name: SourceType
            description: ""
            quote: true

          - name: Status
            description: ""
            quote: true

          - name: TransferredToAccounting
            description: ""
            quote: true

          - name: Type
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: CustomerNumber
            description: "The account number, matching values in column 'AccountNumber' from table 'Account',"
            quote: true

          - name: DeferredRevenueAccount
            description: "The accounting code for deferred revenue e.g. 7074."
            quote: true

          - name: RecognizedRevenueAccount
            description: "The accounting code for recognized revenue e.g. 1015."
            quote: true

      - name: CURATED__ZUORA__INVOICE_PAYMENT
        description: "Linking Payment to an Invoice and indicates how much of the invoice was paid."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: Amount
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: RefundAmount
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: InvoiceId
            description: ""
            quote: true

          - name: PaymentId
            description: ""
            quote: true

      - name: CURATED__ZUORA__JOURNAL_ENTRY
        description: "No information in Zuora object reference"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: Company__s
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Currency
            description: ""
            quote: true

          - name: HomeCurrency
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: InvoiceItemAdjustmentAdjustmentType__s
            description: ""
            quote: true

          - name: InvoiceItemAdjustmentReasonCode__s
            description: ""
            quote: true

          - name: JournalEntryDate
            description: ""
            quote: true

          - name: Notes
            description: ""
            quote: true

          - name: Number
            description: ""
            quote: true

          - name: PaymentGateway__s
            description: ""
            quote: true

          - name: PaymentMethod__s
            description: ""
            quote: true

          - name: PaymentMethodPaymentMethodType__s
            description: ""
            quote: true

          - name: RefundReasonCode__s
            description: ""
            quote: true

          - name: SoldToContactCountry__s
            description: ""
            quote: true

          - name: Status
            description: ""
            quote: true

          - name: SubscriptionSourceSystem__s
            description: ""
            quote: true

          - name: TransactionCount
            description: ""
            quote: true

          - name: TransactionType
            description: ""
            quote: true

          - name: TransferDate
            description: ""
            quote: true

          - name: TransferredBy
            description: ""
            quote: true

          - name: TransferredToAccounting
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

      - name: CURATED__ZUORA__JOURNAL_ENTRY_ITEM
        description: "This is the base Zuora object for the Journal Entry Item data source export"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingCodeId
            description: "The account code Id"
            quote: true

          - name: Amount
            description: "This is the amount of the journal entry in local currency"
            quote: true

          - name: AmountHomeCurrency
            description: "The amount in home currency (The currency you use to create financial reports. You only have one home currency.)"
            quote: true

          - name: CreatedById
            description: "The UserID who created the journal"
            quote: true

          - name: CreatedDate
            description: "Date at which the journal was created"
            quote: true

          - name: Currency
            description: "This is the currency of the journal"
            quote: true

          - name: HomeCurrency
            description: "The currency you use to create financial reports. You only have one home currency"
            quote: true

          - name: Id
            description: "This is the unique identfier for that journal item"
            quote: true

          - name: JournalEntryId
            description: "Id of the journal entry item"
            quote: true

          - name: Type
            description: "This tells us whether it is a credit or debit"
            quote: true

          - name: UpdatedById
            description: "The UserID who updated the JournalEntryItem"
            quote: true

          - name: UpdatedDate
            description: "Date at which the journal was updated"
            quote: true

      - name: CURATED__ZUORA__JOURNAL_RUN
        description: "No information in Zuora object reference."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: Number
            description: ""
            quote: true

          - name: ProcessEndDateTime
            description: ""
            quote: true

          - name: ProcessStartDateTime
            description: ""
            quote: true

          - name: Status
            description: ""
            quote: true

          - name: TargetDateType
            description: ""
            quote: true

          - name: TargetEndDate
            description: ""
            quote: true

          - name: TargetStartDate
            description: ""
            quote: true

          - name: TotalJournalEntryCount
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

      - name: CURATED__ZUORA__PAYMENT
        description: "The Payment object holds all of the information about an individual payment, including the payment amount and to which invoices the payment was applied to."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingCode
            description: ""
            quote: true

          - name: Amount
            description: ""
            quote: true

          - name: AppliedAmount
            description: ""
            quote: true

          - name: AppliedCreditBalanceAmount
            description: ""
            quote: true

          - name: AuthTransactionId
            description: ""
            quote: true

          - name: BankIdentificationNumber
            description: ""
            quote: true

          - name: CancelledOn
            description: ""
            quote: true

          - name: Comment
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Currency
            description: ""
            quote: true

          - name: EffectiveDate
            description: ""
            quote: true

          - name: Gateway
            description: ""
            quote: true

          - name: GatewayOrderId
            description: ""
            quote: true

          - name: GatewayResponse
            description: ""
            quote: true

          - name: GatewayResponseCode
            description: ""
            quote: true

          - name: GatewayState
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: MarkedForSubmissionOn
            description: ""
            quote: true

          - name: PaymentNumber
            description: ""
            quote: true

          - name: PaymentSource__c
            description: ""
            quote: true

          - name: ReferencedPaymentID
            description: ""
            quote: true

          - name: ReferenceId
            description: ""
            quote: true

          - name: RefundAmount
            description: ""
            quote: true

          - name: SecondPaymentReferenceId
            description: ""
            quote: true

          - name: SettledOn
            description: ""
            quote: true

          - name: SoftDescriptor
            description: ""
            quote: true

          - name: SoftDescriptorPhone
            description: ""
            quote: true

          - name: Source
            description: ""
            quote: true

          - name: SourceName
            description: ""
            quote: true

          - name: Status
            description: ""
            quote: true

          - name: SubmittedOn
            description: ""
            quote: true

          - name: TransferredToAccounting
            description: ""
            quote: true

          - name: Type
            description: ""
            quote: true

          - name: UnappliedAmount
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: AccountId
            description: ""
            quote: true

          - name: PaymentMethodSnapshotId
            description: ""
            quote: true

          - name: PaymentMethodId
            description: ""
            quote: true

      - name: CURATED__ZUORA__PAYMENT_METHOD
        description: "The PaymentMethod object represents payment method details associated with a customer account."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountId
            description: ""
            quote: true

          - name: AchAbaCode
            description: ""
            quote: true

          - name: AchAccountName
            description: ""
            quote: true

          - name: AchAccountNumberMask
            description: ""
            quote: true

          - name: AchAccountType
            description: ""
            quote: true

          - name: AchAddress1
            description: ""
            quote: true

          - name: AchAddress2
            description: ""
            quote: true

          - name: AchBankName
            description: ""
            quote: true

          - name: AchCity
            description: ""
            quote: true

          - name: AchCountry
            description: ""
            quote: true

          - name: AchPostalCode
            description: ""
            quote: true

          - name: AchState
            description: ""
            quote: true

          - name: Active
            description: ""
            quote: true

          - name: BankBranchCode
            description: ""
            quote: true

          - name: BankCheckDigit
            description: ""
            quote: true

          - name: BankCity
            description: ""
            quote: true

          - name: BankCode
            description: ""
            quote: true

          - name: BankIdentificationNumber
            description: ""
            quote: true

          - name: BankName
            description: ""
            quote: true

          - name: BankPostalCode
            description: ""
            quote: true

          - name: BankStreetName
            description: ""
            quote: true

          - name: BankStreetNumber
            description: ""
            quote: true

          - name: BankTransferAccountName
            description: ""
            quote: true

          - name: BankTransferAccountNumberMask
            description: ""
            quote: true

          - name: BankTransferAccountType
            description: ""
            quote: true

          - name: BankTransferType
            description: ""
            quote: true

          - name: BusinessIdentificationCode
            description: ""
            quote: true

          - name: City
            description: ""
            quote: true

          - name: CompanyName
            description: ""
            quote: true

          - name: Country
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: CreditCardAddress1
            description: ""
            quote: true

          - name: CreditCardAddress2
            description: ""
            quote: true

          - name: CreditCardCity
            description: ""
            quote: true

          - name: CreditCardCountry
            description: ""
            quote: true

          - name: CreditCardExpirationMonth
            description: ""
            quote: true

          - name: CreditCardExpirationYear
            description: ""
            quote: true

          - name: CreditCardHolderName
            description: ""
            quote: true

          - name: CreditCardMaskNumber
            description: ""
            quote: true

          - name: CreditCardPostalCode
            description: ""
            quote: true

          - name: CreditCardState
            description: ""
            quote: true

          - name: CreditCardType
            description: ""
            quote: true

          - name: DeviceSessionId
            description: ""
            quote: true

          - name: Email
            description: ""
            quote: true

          - name: ExistingMandate
            description: ""
            quote: true

          - name: FirstName
            description: ""
            quote: true

          - name: IBAN
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: IdentityNumber
            description: ""
            quote: true

          - name: IPAddress
            description: ""
            quote: true

          - name: IsCompany
            description: ""
            quote: true

          - name: LastFailedSaleTransactionDate
            description: ""
            quote: true

          - name: LastName
            description: ""
            quote: true

          - name: LastTransactionDateTime
            description: ""
            quote: true

          - name: LastTransactionStatus
            description: ""
            quote: true

          - name: MandateCreationDate
            description: ""
            quote: true

          - name: MandateID
            description: ""
            quote: true

          - name: MandateReceived
            description: ""
            quote: true

          - name: MandateUpdateDate
            description: ""
            quote: true

          - name: MaxConsecutivePaymentFailures
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: NumConsecutiveFailures
            description: ""
            quote: true

          - name: PaymentMethodStatus
            description: ""
            quote: true

          - name: PaymentRetryWindow
            description: ""
            quote: true

          - name: PaypalBaid
            description: ""
            quote: true

          - name: PaypalEmail
            description: ""
            quote: true

          - name: PaypalPreapprovalKey
            description: ""
            quote: true

          - name: PaypalType
            description: ""
            quote: true

          - name: Phone
            description: ""
            quote: true

          - name: PostalCode
            description: ""
            quote: true

          - name: SecondTokenId
            description: ""
            quote: true

          - name: State
            description: ""
            quote: true

          - name: StreetName
            description: ""
            quote: true

          - name: StreetNumber
            description: ""
            quote: true

          - name: TMX_Id__c
            description: ""
            quote: true

          - name: TokenId
            description: ""
            quote: true

          - name: TotalNumberOfErrorPayments
            description: ""
            quote: true

          - name: TotalNumberOfProcessedPayments
            description: ""
            quote: true

          - name: Type
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: UseDefaultRetryRule
            description: ""
            quote: true

          - name: ActualPaymentMethod__c
            description: "Payment Method reference e.g. CARD PAYNOW etc.. Not all payment methods have this reference."
            quote: true

      - name: CURATED__ZUORA__PAYMENT_METHOD_SNAPSHOT
        description: "A Payment Method Snapshot is a copy of the particular Payment Method used in a transaction. If the Payment Method is deleted, the Snapshot continues to retain the data used in each of the past transactions."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountId
            description: ""
            quote: true

          - name: AchAbaCode
            description: ""
            quote: true

          - name: AchAccountName
            description: ""
            quote: true

          - name: AchAccountNumberMask
            description: ""
            quote: true

          - name: AchAccountType
            description: ""
            quote: true

          - name: AchBankName
            description: ""
            quote: true

          - name: BankBranchCode
            description: ""
            quote: true

          - name: BankCheckDigit
            description: ""
            quote: true

          - name: BankCity
            description: ""
            quote: true

          - name: BankCode
            description: ""
            quote: true

          - name: BankIdentificationNumber
            description: ""
            quote: true

          - name: BankName
            description: ""
            quote: true

          - name: BankPostalCode
            description: ""
            quote: true

          - name: BankStreetName
            description: ""
            quote: true

          - name: BankStreetNumber
            description: ""
            quote: true

          - name: BankTransferAccountName
            description: ""
            quote: true

          - name: BankTransferAccountNumberMask
            description: ""
            quote: true

          - name: BankTransferAccountType
            description: ""
            quote: true

          - name: BankTransferType
            description: ""
            quote: true

          - name: BusinessIdentificationCode
            description: ""
            quote: true

          - name: City
            description: ""
            quote: true

          - name: CompanyName
            description: ""
            quote: true

          - name: Country
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: CreditCardAddress1
            description: ""
            quote: true

          - name: CreditCardAddress2
            description: ""
            quote: true

          - name: CreditCardCity
            description: ""
            quote: true

          - name: CreditCardCountry
            description: ""
            quote: true

          - name: CreditCardExpirationMonth
            description: ""
            quote: true

          - name: CreditCardExpirationYear
            description: ""
            quote: true

          - name: CreditCardHolderName
            description: ""
            quote: true

          - name: CreditCardMaskNumber
            description: ""
            quote: true

          - name: CreditCardPostalCode
            description: ""
            quote: true

          - name: CreditCardState
            description: ""
            quote: true

          - name: CreditCardType
            description: ""
            quote: true

          - name: DeviceSessionId
            description: ""
            quote: true

          - name: Email
            description: ""
            quote: true

          - name: ExistingMandate
            description: ""
            quote: true

          - name: FirstName
            description: ""
            quote: true

          - name: IBAN
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: IdentityNumber
            description: ""
            quote: true

          - name: IPAddress
            description: ""
            quote: true

          - name: IsCompany
            description: ""
            quote: true

          - name: LastFailedSaleTransactionDate
            description: ""
            quote: true

          - name: LastName
            description: ""
            quote: true

          - name: LastTransactionDateTime
            description: ""
            quote: true

          - name: LastTransactionStatus
            description: ""
            quote: true

          - name: MandateCreationDate
            description: ""
            quote: true

          - name: MandateID
            description: ""
            quote: true

          - name: MandateReceived
            description: ""
            quote: true

          - name: MandateUpdateDate
            description: ""
            quote: true

          - name: MaxConsecutivePaymentFailures
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: NumConsecutiveFailures
            description: ""
            quote: true

          - name: PaymentMethodId
            description: ""
            quote: true

          - name: PaymentMethodStatus
            description: ""
            quote: true

          - name: PaymentRetryWindow
            description: ""
            quote: true

          - name: PaypalBaid
            description: ""
            quote: true

          - name: PaypalEmail
            description: ""
            quote: true

          - name: PaypalPreapprovalKey
            description: ""
            quote: true

          - name: PaypalType
            description: ""
            quote: true

          - name: Phone
            description: ""
            quote: true

          - name: PostalCode
            description: ""
            quote: true

          - name: SecondTokenId
            description: ""
            quote: true

          - name: State
            description: ""
            quote: true

          - name: StreetName
            description: ""
            quote: true

          - name: StreetNumber
            description: ""
            quote: true

          - name: TokenId
            description: ""
            quote: true

          - name: TotalNumberOfErrorPayments
            description: ""
            quote: true

          - name: TotalNumberOfProcessedPayments
            description: ""
            quote: true

          - name: Type
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: UseDefaultRetryRule
            description: ""
            quote: true

      - name: CURATED__ZUORA__PAYMENT_METHOD_TRANSACTION_LOG
        description: "Selected Information about Payment method transactions."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: Gateway
            description: ""
            quote: true

          - name: GatewayReasonCode
            description: ""
            quote: true

          - name: GatewayReasonCodeDescription
            description: ""
            quote: true

          - name: GatewayTransactionType
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: PaymentMethodType
            description: ""
            quote: true

          - name: RequestString
            description: ""
            quote: true

          - name: ResponseString
            description: ""
            quote: true

          - name: TransactionDate
            description: ""
            quote: true

          - name: TransactionId
            description: ""
            quote: true

      - name: CURATED__ZUORA__PAYMENT_TRANSACTION_LOG
        description: "Contains all transactions from Zuora to the Gateway for the Payment. This data source includes the request string, the response string and the related payment, the payment method, and the account"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AVSResponseCode
            description: "Address verification response code - Not used for Recurring payments"
            quote: true

          - name: BatchId
            description: "Payment batch ID - Possibly not used"
            quote: true

          - name: CreatedDate
            description: "The date the payment transaction log was created"
            quote: true

          - name: CVVResponseCode
            description: "Card Verification Value response - not used for recurring payments"
            quote: true

          - name: Gateway
            description: "The payment gateway the transaction was processed through"
            quote: true

          - name: GatewayReasonCode
            description: "The response code from the gateway"
            quote: true

          - name: GatewayReasonCodeDescription
            description: "Description of the response code"
            quote: true

          - name: GatewayState
            description: "State of the transaction relative to the gateway (Submitted or Not Submitted)"
            quote: true

          - name: GatewayTransactionType
            description: "Transaction type eg Sale"
            quote: true

          - name: Id
            description: "Id relating to the transaction log object"
            quote: true

          - name: RequestString
            description: "The full request sent to the gateway"
            quote: true

          - name: ResponseString
            description: "The full response from the gateway"
            quote: true

          - name: TransactionDate
            description: "Date of the transaction"
            quote: true

          - name: TransactionId
            description: "Internal ID"
            quote: true

      - name: CURATED__ZUORA__PRODUCT
        description: "A Product object contains all of the items in a specific product, especially product rate plans and product rate plan charges."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AllowFeatureChanges
            description: ""
            quote: true

          - name: Category
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Description
            description: ""
            quote: true

          - name: EffectiveEndDate
            description: ""
            quote: true

          - name: EffectiveStartDate
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: SKU
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

      - name: CURATED__ZUORA__PRODUCT_RATE_PLAN
        description: "Use the ProductRatePlan object to define the services that make up the Product objects that can be associated with Subscription objects."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Description
            description: ""
            quote: true

          - name: EffectiveEndDate
            description: ""
            quote: true

          - name: EffectiveStartDate
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: OfferType__c
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: EntitlementSetId__c
            description: "The field will present an unique id of the fixture being sold based on the rateplan. This will be used as an indicator of the PPV offer customer purchased."
            quote: true

          - name: PPVType__c
            description: "The field will differentiate if plan is a regular one (used post sign up with full PPV price) or discounted one (sign up bundle)."
            quote: true

      - name: CURATED__ZUORA__PRODUCT_RATE_PLAN_CHARGE
        description: "A product rate plan charge represents a charge model or a set of fees associated with a product rate plan, which is the part of a product that your customers subscribe to."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingCode
            description: ""
            quote: true

          - name: ApplyDiscountTo
            description: ""
            quote: true

          - name: BillCycleDay
            description: ""
            quote: true

          - name: BillCycleType
            description: ""
            quote: true

          - name: BillingPeriod
            description: ""
            quote: true

          - name: BillingPeriodAlignment
            description: ""
            quote: true

          - name: BillingTiming
            description: ""
            quote: true

          - name: ChargeModel
            description: ""
            quote: true

          - name: ChargeType
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: DefaultQuantity
            description: ""
            quote: true

          - name: DeferredRevenueAccount
            description: ""
            quote: true

          - name: Description
            description: ""
            quote: true

          - name: DiscountLevel
            description: ""
            quote: true

          - name: EndDateCondition
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: IncludedUnits
            description: ""
            quote: true

          - name: LegacyRevenueReporting
            description: ""
            quote: true

          - name: ListPriceBase
            description: ""
            quote: true

          - name: MaxQuantity
            description: ""
            quote: true

          - name: MinQuantity
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: NumberOfPeriod
            description: ""
            quote: true

          - name: OverageCalculationOption
            description: ""
            quote: true

          - name: OverageUnusedUnitsCreditOption
            description: ""
            quote: true

          - name: PriceChangeOption
            description: ""
            quote: true

          - name: PriceIncreasePercentage
            description: ""
            quote: true

          - name: RatingGroup
            description: ""
            quote: true

          - name: RecognizedRevenueAccount
            description: ""
            quote: true

          - name: RevenueRecognitionRuleName
            description: ""
            quote: true

          - name: RevRecCode
            description: ""
            quote: true

          - name: RevRecTriggerCondition
            description: ""
            quote: true

          - name: SmoothingModel
            description: ""
            quote: true

          - name: SpecificBillingPeriod
            description: ""
            quote: true

          - name: Taxable
            description: ""
            quote: true

          - name: TaxCode
            description: ""
            quote: true

          - name: TaxMode
            description: ""
            quote: true

          - name: TriggerEvent
            description: ""
            quote: true

          - name: UOM
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: UpToPeriods
            description: ""
            quote: true

          - name: UpToPeriodsType
            description: ""
            quote: true

          - name: UsageRecordRatingOption
            description: ""
            quote: true

          - name: UseDiscountSpecificAccountingCode
            description: ""
            quote: true

          - name: UseTenantDefaultForPriceChange
            description: ""
            quote: true

          - name: WeeklyBillCycleDay
            description: ""
            quote: true

          - name: ProductRatePlanId
            description: "The ID of the associated product rate plan."
            quote: true

      - name: CURATED__ZUORA__RATE_PLAN
        description: "Rate plans represent a price or a collection of prices for a service sold."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AmendmentType
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: SubscriptionId
            description: ""
            quote: true

          - name: EntitlementSetId__c
            description: "The field will present an unique id of the fixture being sold based on the rateplan. This will be used as an indicator of the PPV offer customer purchased."
            quote: true

          - name: SourceSystem__c
            description: "This field will be used for tracking the 3PP partner name: Apple IAP, Googlepay, Amazon, etc."
            quote: true

          - name: SourceSystemUserId__c
            description: "Field for storing the 3PP transacitionID for example Apple is orginaltrxID."
            quote: true

          - name: TrackingId__c
            description: "ID used to associate the rateplan purchase to a landing page or campaign, and hence to a partner for any revenue share agreements."
            quote: true

          - name: AmendmentId
            description: "The ID of the amendment associated with the rate plan. This field only applies to amendment rate plans."
            quote: true

          - name: ProductRatePlanId
            description: "The ID of the associated product rate plan."
            quote: true

          - name: Context__c
            description: ""
            quote: true

          - name: SegmentID__c
            description: ""
            quote: true

          - name: AddonType__c
            description: ""
            quote: true

          - name: Platform__c
            description: "TBC"
            quote: true

          - name: Is3ppExitFlow__c
            description: "TBC"
            quote: true

          - name: PartnerOfferName__c
            description: "TBC"
            quote: true


      - name: CURATED__ZUORA__RATE_PLAN_CHARGE
        description: "Rate plan charges represent the actual charges for the rate plans or services sold and are part of a subscription."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingCode
            description: ""
            quote: true

          - name: ApplyDiscountTo
            description: ""
            quote: true

          - name: BillCycleDay
            description: ""
            quote: true

          - name: BillCycleType
            description: ""
            quote: true

          - name: BillingPeriod
            description: ""
            quote: true

          - name: BillingPeriodAlignment
            description: ""
            quote: true

          - name: BillingTiming
            description: ""
            quote: true

          - name: ChargedThroughDate
            description: ""
            quote: true

          - name: ChargeModel
            description: ""
            quote: true

          - name: ChargeNumber
            description: ""
            quote: true

          - name: ChargeType
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Description
            description: ""
            quote: true

          - name: DiscountLevel
            description: ""
            quote: true

          - name: DMRC
            description: ""
            quote: true

          - name: DTCV
            description: ""
            quote: true

          - name: EffectiveEndDate
            description: ""
            quote: true

          - name: EffectiveStartDate
            description: ""
            quote: true

          - name: EndDateCondition
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: IsLastSegment
            description: ""
            quote: true

          - name: ListPriceBase
            description: ""
            quote: true

          - name: MRR
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: NumberOfPeriods
            description: ""
            quote: true

          - name: OriginalId
            description: ""
            quote: true

          - name: OverageCalculationOption
            description: ""
            quote: true

          - name: OverageUnusedUnitsCreditOption
            description: ""
            quote: true

          - name: PriceChangeOption
            description: ""
            quote: true

          - name: PriceIncreasePercentage
            description: ""
            quote: true

          - name: ProcessedThroughDate
            description: ""
            quote: true

          - name: Quantity
            description: ""
            quote: true

          - name: RatingGroup
            description: ""
            quote: true

          - name: RevenueRecognitionRuleName
            description: ""
            quote: true

          - name: RevRecCode
            description: ""
            quote: true

          - name: RevRecTriggerCondition
            description: ""
            quote: true

          - name: Segment
            description: ""
            quote: true

          - name: SpecificBillingPeriod
            description: ""
            quote: true

          - name: SpecificEndDate
            description: ""
            quote: true

          - name: TCV
            description: ""
            quote: true

          - name: TriggerDate
            description: ""
            quote: true

          - name: TriggerEvent
            description: ""
            quote: true

          - name: UOM
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: UpToPeriods
            description: ""
            quote: true

          - name: UpToPeriodsType
            description: ""
            quote: true

          - name: Version
            description: ""
            quote: true

          - name: WeeklyBillCycleDay
            description: ""
            quote: true

          - name: RatePlanId
            description: ""
            quote: true

          - name: ProductRatePlanChargeId
            description: ""
            quote: true

          - name: PostSignUpGiftcode__c
            description: "Giftode number (Giftcode redeemed post sign up)"
            quote: true

          - name: PostSignUpGiftcodeCampaignName__c
            description: "Campaign Name for a giftcode used post sign up"
            quote: true

      - name: CURATED__ZUORA__RATE_PLAN_CHARGE_TIER
        description: "A product rate plan charge represents a charge model or a set of fees associated with a product rate plan, which is the part of a product that your customers subscribe to."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Currency
            description: ""
            quote: true

          - name: DiscountAmount
            description: ""
            quote: true

          - name: DiscountPercentage
            description: ""
            quote: true

          - name: EndingUnit
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: IncludedUnits
            description: ""
            quote: true

          - name: OveragePrice
            description: ""
            quote: true

          - name: Price
            description: ""
            quote: true

          - name: PriceFormat
            description: ""
            quote: true

          - name: StartingUnit
            description: ""
            quote: true

          - name: Tier
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: RatePlanChargeId
            description: ""
            quote: true

      - name: CURATED__ZUORA__REFUND
        description: "A Refund object is used to create and query refunds. There are two types of refunds."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingCode
            description: ""
            quote: true

          - name: Amount
            description: ""
            quote: true

          - name: CancelledOn
            description: ""
            quote: true

          - name: Comment
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Gateway
            description: ""
            quote: true

          - name: GatewayResponse
            description: ""
            quote: true

          - name: GatewayResponseCode
            description: ""
            quote: true

          - name: GatewayState
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: MarkedForSubmissionOn
            description: ""
            quote: true

          - name: MethodType
            description: ""
            quote: true

          - name: PaymentMethodId
            description: ""
            quote: true

          - name: ReasonCode
            description: ""
            quote: true

          - name: ReferenceID
            description: ""
            quote: true

          - name: RefundDate
            description: ""
            quote: true

          - name: RefundNumber
            description: ""
            quote: true

          - name: RefundReferenceId__c
            description: ""
            quote: true

          - name: RefundSource__c
            description: ""
            quote: true

          - name: RefundTransactionTime
            description: ""
            quote: true

          - name: SecondRefundReferenceId
            description: ""
            quote: true

          - name: SettledOn
            description: ""
            quote: true

          - name: SoftDescriptor
            description: ""
            quote: true

          - name: SoftDescriptorPhone
            description: ""
            quote: true

          - name: SourceType
            description: ""
            quote: true

          - name: Status
            description: ""
            quote: true

          - name: SubmittedOn
            description: ""
            quote: true

          - name: TransferredToAccounting
            description: ""
            quote: true

          - name: Type
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: AccountId
            description: ""
            quote: true

          - name: PaymentMethodSnapshotId
            description: ""
            quote: true

      - name: CURATED__ZUORA__REFUND_INVOICE_PAYMENT
        description: "A refund invoice payment represents a portion of the refund thats being made against a payment that was applied to an invoice."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: RefundAmount
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: InvoiceId
            description: ""
            quote: true

          - name: InvoicePaymentId
            description: ""
            quote: true

          - name: RefundId
            description: ""
            quote: true

      - name: CURATED__ZUORA__REVENUE_CHARGE_SUMMARY
        description: "Use this data source to export revenue charge summary item data. This is the base Zuora object for the Revenue Charge Summary Item data source"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountId
            description: "Account id relating to the revenue charge summary"
            quote: true

          - name: CreatedById
            description: "The UserID who created the revenue charge summary"
            quote: true

          - name: CreatedDate
            description: "The date at which the RevenueChargeSummary record was created in Zuora"
            quote: true

          - name: Currency
            description: "Currency of the revenue charge summary"
            quote: true

          - name: Description
            description: "Description of the revenue charge summary"
            quote: true

          - name: Id
            description: "The ID of this object. Upon creation of this object, this field becomes RevenueChargeSummaryId"
            quote: true

          - name: Number
            description: "The number relating to the revenue charge summary"
            quote: true

          - name: RatePlanChargeId
            description: "Id relating to the rate plan charge"
            quote: true

          - name: UpdatedById
            description: "The UserID who updated the revenue charge summary"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the revenue charge summary"
            quote: true

      - name: CURATED__ZUORA__REVENUE_CHARGE_SUMMARY_ITEM
        description: "Contains the revenue charge item. This is the base Zuora object for the Revenue Charge Summary Item data source"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingPeriodId
            description: "The Id relating to the account period"
            quote: true

          - name: Amount
            description: "The amount of revenue charge summary item"
            quote: true

          - name: CreatedById
            description: "The UserID who created the RevenueChargeSummaryItem"
            quote: true

          - name: CreatedDate
            description: "The date at which the RevenueChargeSummaryItem record was created in Zuora"
            quote: true

          - name: Currency
            description: "Currency of the RevenueChargeSummaryItem"
            quote: true

          - name: Id
            description: "The ID of this object. Upon creation of this object, this field becomes RevenueChargeSummaryItemId"
            quote: true

          - name: RevenueChargeSummaryId
            description: "The id relating to the revenue charge summary item"
            quote: true

          - name: UpdatedById
            description: "The UserID who updated the RevenueChargeSummaryItem"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the RevenueChargeSummaryItem"
            quote: true

      - name: CURATED__ZUORA__REVENUE_EVENT
        description: "This is the base Zuora object for the Revenue Event data source export"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: Amount
            description: "The revenue event amount"
            quote: true

          - name: CreatedById
            description: "The UserID who created the RevenueEvent"
            quote: true

          - name: CreatedDate
            description: "The date at which the RevenueEvent record was created in Zuora"
            quote: true

          - name: Currency
            description: "The currency for the revenue event"
            quote: true

          - name: Description
            description: "Description of the revenue event"
            quote: true

          - name: Id
            description: "The ID of this object. Upon creation of this object"
            quote: true

          - name: Number
            description: "Unique revenue event number"
            quote: true

          - name: RecognitionEnd
            description: "Revenue recognition end date"
            quote: true

          - name: RecognitionStart
            description: "Revenue recognition start date"
            quote: true

          - name: RevenueEventTypeId
            description: "Unique Id relating to the revenue event type"
            quote: true

          - name: RevenueScheduleId
            description: "Unique Id relating to the revenue schedule"
            quote: true

          - name: UpdatedById
            description: "The UserID who updated the revenue event"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the revenue event"
            quote: true

      - name: CURATED__ZUORA__REVENUE_EVENT_CREDIT_MEMO_ITEM
        description: "Use this data source to export data about revenue event items that are associated with credit memo items"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: Amount
            description: "The amount of RevenueEventCreditMemoItem"
            quote: true

          - name: CreatedById
            description: "The UserID who created the RevenueEventCreditMemoItem"
            quote: true

          - name: CreatedDate
            description: "The date at which the RevenueEventCreditMemoItem record was created in Zuora"
            quote: true

          - name: Currency
            description: "Currency of the RevenueEventCreditMemoItem"
            quote: true

          - name: Description
            description: "The description of the RevenueEventCreditMemoItem"
            quote: true

          - name: Id
            description: "The ID of this object"
            quote: true

          - name: Number
            description: "Number relating to the RevenueEventCreditMemoItem object"
            quote: true

          - name: RecognitionEnd
            description: "The recognition end date for the RevenueEventCreditMemoItem"
            quote: true

          - name: RecognitionStart
            description: "The recognition start date for the RevenueEventCreditMemoItem"
            quote: true

          - name: RevenueEventTypeId
            description: "The Id of the revenue event type"
            quote: true

          - name: RevenueScheduleCreditMemoItemId
            description: "The id of the revenue schedule credit memo item"
            quote: true

          - name: UpdatedById
            description: "The userId that updated the RevenueEventCreditMemoItem"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the RevenueEventCreditMemoItem"
            quote: true

      - name: CURATED__ZUORA__REVENUE_EVENT_ITEM
        description: "The revenue event item. This is the base Zuora object for the Revenue Event Item data source"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingPeriodId
            description: "The account period Id relating to the revenue event item"
            quote: true

          - name: Amount
            description: "The amount of revenue event item"
            quote: true

          - name: CreatedById
            description: "The UserID who created the RevenueEventItem"
            quote: true

          - name: CreatedDate
            description: "The date at which the RevenueEventItem record was created in Zuora"
            quote: true

          - name: Currency
            description: "Currency of the RevenueEventItem"
            quote: true

          - name: DeferredRevenueAccountingCodeId
            description: "The deferred revenue accounting code Id relating to the revenue event item"
            quote: true

          - name: Id
            description: "The ID of this object. Upon creation of this object, this field becomes RevenueEventItemId"
            quote: true

          - name: JournalEntryId
            description: "The journal entry Id relating to the revenue event item"
            quote: true

          - name: RecognizedRevenueAccountingCodeId
            description: "The recognized revenue accounting code Id relating to the revenue event item"
            quote: true

          - name: RevenueEventId
            description: "The revenue event Id relating to the revenue event item"
            quote: true

          - name: UpdatedById
            description: "The UserID who updated the RevenueEventItem"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the RevenueEventItem"
            quote: true

      - name: CURATED__ZUORA__REVENUE_EVENT_ITEM_CREDIT_MEMO_ITEM
        description: "Use this data source to export data about revenue event items that are associated with credit memo items"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingPeriodId
            description: "The account period Id relating to the RevenueEventItemCreditMemoItem"
            quote: true

          - name: Amount
            description: "The amount of RevenueEventItemCreditMemoItem"
            quote: true

          - name: CreatedById
            description: "The UserID who created the RevenueEventItemCreditMemoItem"
            quote: true

          - name: CreatedDate
            description: "The date at which the RevenueEventItemCreditMemoItem record was created in Zuora"
            quote: true

          - name: Currency
            description: "Currency of the RevenueEventItemCreditMemoItem"
            quote: true

          - name: DeferredRevenueAccountingCodeId
            description: "The deferred revenue accounting code Id relating to the RevenueEventItemCreditMemoItem"
            quote: true

          - name: Id
            description: "The Id of this object"
            quote: true

          - name: JournalEntryId
            description: "The journal entry Id"
            quote: true

          - name: RecognizedRevenueAccountingCodeId
            description: "The recognized revenue accounting code Id relating to the RevenueEventItemCreditMemoItem"
            quote: true

          - name: RevenueEventCreditMemoItemId
            description: "The revenue event credit memo item Id"
            quote: true

          - name: UpdatedById
            description: "The userId that updated the RevenueEventItemCreditMemoItem"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the RevenueEventItemCreditMemoItem"
            quote: true

      - name: CURATED__ZUORA__REVENUE_EVENT_ITEM_INVOICE_ITEM
        description: "Use this data source to export data about revenue event items that are associated with invoice items"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingPeriodId
            description: "The account period Id relating to the revenue event item"
            quote: true

          - name: Amount
            description: "The amount of revenue event item"
            quote: true

          - name: CreatedById
            description: "The UserID who created the RevenueEventItem"
            quote: true

          - name: CreatedDate
            description: "The date at which the RevenueEventItem record was created in Zuora"
            quote: true

          - name: Currency
            description: "Currency of the RevenueEventItem"
            quote: true

          - name: DeferredRevenueAccountingCodeId
            description: "The deferred revenue accounting code Id relating to the revenue event item"
            quote: true

          - name: Id
            description: "The ID of this object"
            quote: true

          - name: JournalEntryId
            description: "The journal entry Id relating to the revenue event item"
            quote: true

          - name: RecognizedRevenueAccountingCodeId
            description: "The recognized revenue accounting code Id relating to the revenue event item"
            quote: true

          - name: RevenueEventInvoiceItemId
            description: "Revenue event invoice item Id that is associated with an invoice item"
            quote: true

          - name: UpdatedById
            description: "The UserID who updated the RevenueEventItem"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the RevenueEventItem"
            quote: true

      - name: CURATED__ZUORA__REVENUE_EVENT_ITEM_INVOICE_ITEM_ADJUSTMENT
        description: "Use this data source to export data about revenue event items that are associated with invoice item adjustments"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingPeriodId
            description: "The account period Id relating to the revenue event item"
            quote: true

          - name: Amount
            description: "The amount of revenue event item"
            quote: true

          - name: CreatedById
            description: "The UserID who created the RevenueEventItem"
            quote: true

          - name: CreatedDate
            description: "The date at which the RevenueEventItem record was created in Zuora"
            quote: true

          - name: Currency
            description: "Currency of the RevenueEventItem"
            quote: true

          - name: DeferredRevenueAccountingCodeId
            description: "The deferred revenue accounting code Id relating to the revenue event item"
            quote: true

          - name: Id
            description: "The ID of this object"
            quote: true

          - name: JournalEntryId
            description: "The journal entry Id relating to the revenue event item"
            quote: true

          - name: RecognizedRevenueAccountingCodeId
            description: "The recognized revenue accounting code Id relating to the revenue event item"
            quote: true

          - name: RevenueEventInvoiceItemAdjustmentId
            description: "Revenue event invoice item adjustment Id that is associated with an invoice item adjustment"
            quote: true

          - name: UpdatedById
            description: "The UserID who updated the RevenueEventItem"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the RevenueEventItem"
            quote: true

      - name: CURATED__ZUORA__REVENUE_SCHEDULE
        description: "The distribution of revenue amounts over a number of accounting periods. A revenue schedule represents how revenue is recognized over time"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: Amount
            description: "The amount of revenue schedule"
            quote: true

          - name: BillingTransactionId
            description: "The billing transaction Id"
            quote: true

          - name: CreatedById
            description: "The user who created the revenue schedule"
            quote: true

          - name: CreatedDate
            description: "The date at which the revenue schedule record was created in Zuora"
            quote: true

          - name: Currency
            description: "Currency of the revenue schedule"
            quote: true

          - name: Description
            description: "Description of the revenue schedule"
            quote: true

          - name: ExchangeRateDate
            description: "Date of the exchange rate used"
            quote: true

          - name: FinanceTransactionType
            description: "Type of the finance transaction"
            quote: true

          - name: Id
            description: "The Id of this object"
            quote: true

          - name: Number
            description: "Number relating to the revenue schedule object"
            quote: true

          - name: RecognitionPeriodEnd
            description: "Date of revenue shecdule recognition end"
            quote: true

          - name: RecognitionPeriodStart
            description: "Date of Revenue Schedule Recognition Start"
            quote: true

          - name: ReferenceId
            description: "A code to reference an object external to Zuora. For example, you can use this field to reference a case number in an external system"
            quote: true

          - name: RevenueScheduleDate
            description: "Date of revenue schedule creation"
            quote: true

          - name: Rule
            description: "Revenue rules are instances of revenue rule models"
            quote: true

          - name: TransactionDate
            description: "Date of original transaction"
            quote: true

          - name: TransactionNumber
            description: "Number of original transaction"
            quote: true

          - name: UndistributedAmount
            description: "Amount of unrecognised revenue from Revenue Schedule which is not distributed between months"
            quote: true

          - name: UpdatedById
            description: "The userId that updated the RevenueSchedule"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the RevenueSchedule"
            quote: true

      - name: CURATED__ZUORA__REVENUE_SCHEDULE_CREDIT_MEMO_ITEM
        description: "Use this data source to export revenue schedule item credit memo item data"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: Amount
            description: "The amount of RevenueScheduleCreditMemoItem"
            quote: true

          - name: CreatedById
            description: "The UserID who created the RevenueScheduleCreditMemoItem"
            quote: true

          - name: CreatedDate
            description: "The date at which the RevenueScheduleCreditMemoItem record was created in Zuora"
            quote: true

          - name: Currency
            description: "Currency of the RevenueScheduleCreditMemoItem"
            quote: true

          - name: Description
            description: "Description of the RevenueScheduleCreditMemoItem"
            quote: true

          - name: ExchangeRateDate
            description: "Date of the exchange rate used"
            quote: true

          - name: FinanceTransactionType
            description: "Type of the finance transaction"
            quote: true

          - name: Id
            description: "The Id of this object"
            quote: true

          - name: Number
            description: "Number relating to the RevenueScheduleCreditMemoItem object"
            quote: true

          - name: RecognitionPeriodEnd
            description: "Recognition end date"
            quote: true

          - name: RecognitionPeriodStart
            description: "Recognition start date"
            quote: true

          - name: ReferenceId
            description: "A code to reference an object external to Zuora. For example, you can use this field to reference a case number in an external system"
            quote: true

          - name: RevenueScheduleDate
            description: "Date of revenue schedule creation"
            quote: true

          - name: Rule
            description: "Revenue rules are instances of revenue rule models"
            quote: true

          - name: TransactionDate
            description: "Date of original transaction"
            quote: true

          - name: TransactionNumber
            description: "Number of original transaction"
            quote: true

          - name: UndistributedAmount
            description: "Amount of unrecognised revenue from Revenue Schedule which is not distributed between months"
            quote: true

          - name: UpdatedById
            description: "The userId that updated the RevenueScheduleCreditMemoItem"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the RevenueScheduleCreditMemoItem"
            quote: true

      - name: CURATED__ZUORA__REVENUE_SCHEDULE_INVOICE_ITEM
        description: "Use this data source to export revenue schedule item – invoice item data"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: Amount
            description: "The amount of revenue schedule invoice item"
            quote: true

          - name: CreatedById
            description: "The UserID who created the Revenue schedule invoice item"
            quote: true

          - name: CreatedDate
            description: "The date at which the RevenueScheduleInvoiceItem record was created in Zuora"
            quote: true

          - name: Currency
            description: "Currency of the RevenueScheduleInvoiceItem"
            quote: true

          - name: Description
            description: "Description of the RevenueScheduleInvoiceItem"
            quote: true

          - name: ExchangeRateDate
            description: "Date of the exchange rate used"
            quote: true

          - name: FinanceTransactionType
            description: "Type of the finance transaction"
            quote: true

          - name: Id
            description: "The Id of this object"
            quote: true

          - name: Number
            description: "Number relating to the RevenueScheduleInvoiceItem object"
            quote: true

          - name: RecognitionPeriodEnd
            description: "Revenue recognition end date"
            quote: true

          - name: RecognitionPeriodStart
            description: "Revenue recognition start date"
            quote: true

          - name: ReferenceId
            description: "A code to reference an object external to Zuora. For example, you can use this field to reference a case number in an external system"
            quote: true

          - name: RevenueScheduleDate
            description: "Date of revenue schedule creation"
            quote: true

          - name: Rule
            description: "Revenue rules are instances of revenue rule models"
            quote: true

          - name: TransactionDate
            description: "Date of original transaction"
            quote: true

          - name: TransactionNumber
            description: "Number of original transaction"
            quote: true

          - name: UndistributedAmount
            description: "Amount of unrecognised revenue from Revenue Schedule which is not distributed between months"
            quote: true

          - name: UpdatedById
            description: "The userId that updated the RevenueSchedule"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the RevenueSchedule"
            quote: true

      - name: CURATED__ZUORA__REVENUE_SCHEDULE_INVOICE_ITEM_ADJUSTMENT
        description: "Use this data source to export revenue schedule item – invoice item adjustment data"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: Amount
            description: "The amount of revenue schedule invoice item adjustment"
            quote: true

          - name: CreatedById
            description: "The UserID who created the revenue schedule invoice item adjustment"
            quote: true

          - name: CreatedDate
            description: "The date at which the revenue schedule invoice item adjustment record was created in Zuora"
            quote: true

          - name: Currency
            description: "Currency of the revenue schedule invoice item adjustment"
            quote: true

          - name: Description
            description: "Description of the revenue schedule invoice item adjustment"
            quote: true

          - name: ExchangeRateDate
            description: "Date of the exchange rate used"
            quote: true

          - name: FinanceTransactionType
            description: "Type of the finance transaction"
            quote: true

          - name: Id
            description: "The Id of this object"
            quote: true

          - name: Number
            description: "Number relating to the revenue schedule invoice item adjustment object"
            quote: true

          - name: RecognitionPeriodEnd
            description: "Revenue recognition end date"
            quote: true

          - name: RecognitionPeriodStart
            description: "Revenue recognition start date"
            quote: true

          - name: ReferenceId
            description: "A code to reference an object external to Zuora. For example, you can use this field to reference a case number in an external system"
            quote: true

          - name: RevenueScheduleDate
            description: "Date of revenue schedule creation"
            quote: true

          - name: Rule
            description: "Revenue rules are instances of revenue rule models"
            quote: true

          - name: TransactionDate
            description: "Date of original transaction"
            quote: true

          - name: TransactionNumber
            description: "Number of original transaction"
            quote: true

          - name: UndistributedAmount
            description: "Amount of unrecognised revenue from Revenue Schedule which is not distributed between months"
            quote: true

          - name: UpdatedById
            description: "The userId that updated the RevenueSchedule"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the RevenueSchedule"
            quote: true

      - name: CURATED__ZUORA__REVENUE_SCHEDULE_ITEM
        description: "This is the base Zuora object for the Revenue Schedule Item data source export. The Revenue Schedule Item represents one revenue line item"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingPeriodId
            description: "The accounting period Id relating to the revenue schedule item"
            quote: true

          - name: Amount
            description: "The amount of revenue schedule item"
            quote: true

          - name: CreatedById
            description: "The UserID who created the RevenueScheduleItem"
            quote: true

          - name: CreatedDate
            description: "The date at which the RevenueScheduleItem record was created in Zuora"
            quote: true

          - name: Currency
            description: "Currency of the RevenueScheduleItem"
            quote: true

          - name: DeferredRevenueAccountingCodeId
            description: "The deferred revenue accounting code Id relating to the revenue schedule item"
            quote: true

          - name: Id
            description: "The ID of this object. Upon creation of this object, this field becomes RevenueScheduleItemId"
            quote: true

          - name: RecognizedRevenueAccountingCodeId
            description: "The recognized revenue  accounting code Id relating to the revenue schedule item"
            quote: true

          - name: RevenueScheduleId
            description: "The revenue schedule Id relating to the revenue schedule item"
            quote: true

          - name: UpdatedById
            description: "The UserID who updated the RevenueScheduleItem"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the RevenueScheduleItem"
            quote: true

      - name: CURATED__ZUORA__REVENUE_SCHEDULE_ITEM_CREDIT_MEMO_ITEM
        description: "Use this data source to export revenue schedule item credit memo item data"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingPeriodId
            description: "The account period Id relating to the RevenueScheduleItemCreditMemoItem"
            quote: true

          - name: Amount
            description: "The amount of RevenueScheduleItemCreditMemoItem"
            quote: true

          - name: CreatedById
            description: "The UserID who created the RevenueScheduleItemCreditMemoItem"
            quote: true

          - name: CreatedDate
            description: "The date at which the RevenueScheduleItemCreditMemoItem record was created in Zuora"
            quote: true

          - name: Currency
            description: "Currency of the RevenueScheduleItemCreditMemoItem"
            quote: true

          - name: DeferredRevenueAccountingCodeId
            description: "The deferred revenue accounting code Id relating to the RevenueScheduleItemCreditMemoItem"
            quote: true

          - name: Id
            description: "The ID of this object"
            quote: true

          - name: RecognizedRevenueAccountingCodeId
            description: "The RevenueScheduleItemCreditMemoItem relating to the RevenueScheduleItemInvoiceItemAdjustment"
            quote: true

          - name: RevenueScheduleCreditMemoItemId
            description: "The RevenueScheduleItemCreditMemoItem Id"
            quote: true

          - name: UpdatedById
            description: "The userId that updated the RevenueScheduleItemCreditMemoItem"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the RevenueScheduleItemCreditMemoItem"
            quote: true

      - name: CURATED__ZUORA__REVENUE_SCHEDULE_ITEM_INVOICE_ITEM
        description: "Use this data source to export revenue schedule item – invoice item data"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingPeriodId
            description: "The account period Id relating to the RevenueScheduleItemInvoiceItem"
            quote: true

          - name: Amount
            description: "The amount of RevenueScheduleItemInvoiceItem"
            quote: true

          - name: CreatedById
            description: "The UserID who created the RevenueScheduleItemInvoiceItem"
            quote: true

          - name: CreatedDate
            description: "The date at which the RevenueScheduleItemInvoiceItem record was created in Zuora"
            quote: true

          - name: Currency
            description: "Currency of the RevenueScheduleItemInvoiceItem"
            quote: true

          - name: DeferredRevenueAccountingCodeId
            description: "The deferred revenue accounting code Id relating to the RevenueScheduleItemInvoiceItem"
            quote: true

          - name: Id
            description: "The ID of this object"
            quote: true

          - name: RecognizedRevenueAccountingCodeId
            description: "The recognized revenue accounting code Id relating to the RevenueScheduleItemInvoiceItem"
            quote: true

          - name: RevenueScheduleInvoiceItemId
            description: "The revenue schedule invoice item Id"
            quote: true

          - name: UpdatedById
            description: "The userId that updated the RevenueScheduleItemInvoiceItem"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the RevenueScheduleItemInvoiceItem"
            quote: true

      - name: CURATED__ZUORA__REVENUE_SCHEDULE_ITEM_INVOICE_ITEM_ADJUSTMENT
        description: "Use this data source to export revenue schedule item – invoice item adjustment data"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingPeriodId
            description: "The account period Id relating to the RevenueScheduleItemInvoiceItemAdjustment"
            quote: true

          - name: Amount
            description: "The amount of RevenueScheduleItemInvoiceItemAdjustment"
            quote: true

          - name: CreatedById
            description: "The UserID who created the RevenueScheduleItemInvoiceItemAdjustment"
            quote: true

          - name: CreatedDate
            description: "The date at which the RevenueScheduleItemInvoiceItemAdjustment record was created in Zuora"
            quote: true

          - name: Currency
            description: "Currency of the RevenueScheduleItemInvoiceItemAdjustment"
            quote: true

          - name: DeferredRevenueAccountingCodeId
            description: "The deferred revenue accounting code Id relating to the RevenueScheduleItemInvoiceItemAdjustment"
            quote: true

          - name: Id
            description: "The ID of this object"
            quote: true

          - name: RecognizedRevenueAccountingCodeId
            description: "The recognized revenue accounting code Id relating to the RevenueScheduleItemInvoiceItemAdjustment"
            quote: true

          - name: RevenueScheduleInvoiceItemAdjustmentId
            description: "The revenue schedule invoice item adjustment Id"
            quote: true

          - name: UpdatedById
            description: "The userId that updated the RevenueScheduleItemInvoiceItemAdjustment"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the RevenueScheduleItemInvoiceItemAdjustment"
            quote: true

      - name: CURATED__ZUORA__SUBSCRIPTION
        description: "The Subscription object contains the information needed to create and maintain a subscription associated with an Account object."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AutoRenew
            description: ""
            quote: true

          - name: CampaignID__c
            description: ""
            quote: true

          - name: CancelledDate
            description: ""
            quote: true

          - name: ContractAcceptanceDate
            description: ""
            quote: true

          - name: ContractEffectiveDate
            description: ""
            quote: true

          - name: CountryOfSubscription__c
            description: ""
            quote: true

          - name: CpqBundleJsonId__QT
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: CreatorAccountId
            description: ""
            quote: true

          - name: CreatorInvoiceOwnerId
            description: ""
            quote: true

          - name: CurrentTerm
            description: ""
            quote: true

          - name: CurrentTermPeriodType
            description: ""
            quote: true

          - name: DCB_Carier_Name__c
            description: ""
            quote: true

          - name: DeviceInformation__c
            description: ""
            quote: true

          - name: Giftcode__c
            description: ""
            quote: true

          - name: Giftcode_Old__c
            description: ""
            quote: true

          - name: GiftCodeGrantsContentPortability__c
            description: ""
            quote: true

          - name: giftcodeismethod__c
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: InitialTerm
            description: ""
            quote: true

          - name: InitialTermPeriodType
            description: ""
            quote: true

          - name: InvoiceOwnerId
            description: ""
            quote: true

          - name: IPAddress__c
            description: ""
            quote: true

          - name: IsInvoiceSeparate
            description: ""
            quote: true

          - name: Manually_Cancelled__c
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: Notes
            description: ""
            quote: true

          - name: NumFreeTrialPeriods__c
            description: ""
            quote: true

          - name: NumGiftPeriods__c
            description: ""
            quote: true

          - name: OpportunityCloseDate__QT
            description: ""
            quote: true

          - name: OpportunityName__QT
            description: ""
            quote: true

          - name: OriginalCreatedDate
            description: ""
            quote: true

          - name: OriginalId
            description: ""
            quote: true

          - name: PreviousSubscriptionId
            description: ""
            quote: true

          - name: QuoteBusinessType__QT
            description: ""
            quote: true

          - name: QuoteNumber__QT
            description: ""
            quote: true

          - name: QuoteType__QT
            description: ""
            quote: true

          - name: ReceiptID__c
            description: ""
            quote: true

          - name: RenewalSetting
            description: ""
            quote: true

          - name: RenewalTerm
            description: ""
            quote: true

          - name: RenewalTermPeriodType
            description: ""
            quote: true

          - name: ServiceActivationDate
            description: ""
            quote: true

          - name: SourceSystem__c
            description: ""
            quote: true

          - name: SourceSystemUserID__c
            description: ""
            quote: true

          - name: Status
            description: ""
            quote: true

          - name: SubscriptionEndDate
            description: ""
            quote: true

          - name: SubscriptionStartDate
            description: ""
            quote: true

          - name: TermEndDate
            description: ""
            quote: true

          - name: TermStartDate
            description: ""
            quote: true

          - name: TermType
            description: ""
            quote: true

          - name: TrackingID__c
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: UserAgent__c
            description: ""
            quote: true

          - name: Version
            description: ""
            quote: true

          - name: AccountId
            description: ""
            quote: true

          - name: FreeTrialPeriodsType__c
            description: "Describes the units of the NumFreeTrialPeriods__c value"
            quote: true

          - name: PartnerOfferName__c
            description: ""
            quote: true

      - name: CURATED__ZUORA__TAXATION_ITEM
        description: "The TaxationItem object is used to add a tax amount to an invoice item."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingCode
            description: ""
            quote: true

          - name: Balance
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: CreditAmount
            description: ""
            quote: true

          - name: ExemptAmount
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: Jurisdiction
            description: ""
            quote: true

          - name: LocationCode
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: PaymentAmount
            description: ""
            quote: true

          - name: TaxAmount
            description: ""
            quote: true

          - name: TaxCode
            description: ""
            quote: true

          - name: TaxCodeDescription
            description: ""
            quote: true

          - name: TaxDate
            description: ""
            quote: true

          - name: TaxMode
            description: ""
            quote: true

          - name: TaxRate
            description: ""
            quote: true

          - name: TaxRateDescription
            description: ""
            quote: true

          - name: TaxRateType
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: InvoiceItemId
            description: ""
            quote: true

          - name: InvoiceId
            description: "Id of the Invoice that taxation was applied to."
            quote: true

      - name: CURATED__ZUORA__SUBSCRIPTION_NAME_CURRENT
        description: "Curated Subscription, Shows the latest record for each Subscription.Name"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AutoRenew
            description: ""
            quote: true

          - name: CampaignID__c
            description: ""
            quote: true

          - name: CancelledDate
            description: ""
            quote: true

          - name: ContractAcceptanceDate
            description: ""
            quote: true

          - name: ContractEffectiveDate
            description: ""
            quote: true

          - name: CountryOfSubscription__c
            description: ""
            quote: true

          - name: CpqBundleJsonId__QT
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: CreatorAccountId
            description: ""
            quote: true

          - name: CreatorInvoiceOwnerId
            description: ""
            quote: true

          - name: CurrentTerm
            description: ""
            quote: true

          - name: CurrentTermPeriodType
            description: ""
            quote: true

          - name: DCB_Carier_Name__c
            description: ""
            quote: true

          - name: DeviceInformation__c
            description: ""
            quote: true

          - name: Giftcode__c
            description: ""
            quote: true

          - name: Giftcode_Old__c
            description: ""
            quote: true

          - name: GiftCodeGrantsContentPortability__c
            description: ""
            quote: true

          - name: giftcodeismethod__c
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: InitialTerm
            description: ""
            quote: true

          - name: InitialTermPeriodType
            description: ""
            quote: true

          - name: InvoiceOwnerId
            description: ""
            quote: true

          - name: IPAddress__c
            description: ""
            quote: true

          - name: IsInvoiceSeparate
            description: ""
            quote: true

          - name: Manually_Cancelled__c
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: Notes
            description: ""
            quote: true

          - name: NumFreeTrialPeriods__c
            description: ""
            quote: true

          - name: NumGiftPeriods__c
            description: ""
            quote: true

          - name: OpportunityCloseDate__QT
            description: ""
            quote: true

          - name: OpportunityName__QT
            description: ""
            quote: true

          - name: OriginalCreatedDate
            description: ""
            quote: true

          - name: OriginalId
            description: ""
            quote: true

          - name: PreviousSubscriptionId
            description: ""
            quote: true

          - name: QuoteBusinessType__QT
            description: ""
            quote: true

          - name: QuoteNumber__QT
            description: ""
            quote: true

          - name: QuoteType__QT
            description: ""
            quote: true

          - name: ReceiptID__c
            description: ""
            quote: true

          - name: RenewalSetting
            description: ""
            quote: true

          - name: RenewalTerm
            description: ""
            quote: true

          - name: RenewalTermPeriodType
            description: ""
            quote: true

          - name: ServiceActivationDate
            description: ""
            quote: true

          - name: SourceSystem__c
            description: ""
            quote: true

          - name: SourceSystemUserID__c
            description: ""
            quote: true

          - name: Status
            description: ""
            quote: true

          - name: SubscriptionEndDate
            description: ""
            quote: true

          - name: SubscriptionStartDate
            description: ""
            quote: true

          - name: TermEndDate
            description: ""
            quote: true

          - name: TermStartDate
            description: ""
            quote: true

          - name: TermType
            description: ""
            quote: true

          - name: TrackingID__c
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: UserAgent__c
            description: ""
            quote: true

          - name: Version
            description: ""
            quote: true

          - name: AccountId
            description: ""
            quote: true

          - name: FreeTrialPeriodsType__c
            description: ""
            quote: true

          - name: PartnerOfferName__c
            description: ""
            quote: true

      - name: CURATED__ZUORA__SUBSCRIPTION_ID_CURRENT
        description: "Curated Subscription, Shows the latest record for each Subscription.ID"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AutoRenew
            description: ""
            quote: true

          - name: CampaignID__c
            description: ""
            quote: true

          - name: CancelledDate
            description: ""
            quote: true

          - name: ContractAcceptanceDate
            description: ""
            quote: true

          - name: ContractEffectiveDate
            description: ""
            quote: true

          - name: CountryOfSubscription__c
            description: ""
            quote: true

          - name: CpqBundleJsonId__QT
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: CreatorAccountId
            description: ""
            quote: true

          - name: CreatorInvoiceOwnerId
            description: ""
            quote: true

          - name: CurrentTerm
            description: ""
            quote: true

          - name: CurrentTermPeriodType
            description: ""
            quote: true

          - name: DCB_Carier_Name__c
            description: ""
            quote: true

          - name: DeviceInformation__c
            description: ""
            quote: true

          - name: Giftcode__c
            description: ""
            quote: true

          - name: Giftcode_Old__c
            description: ""
            quote: true

          - name: GiftCodeGrantsContentPortability__c
            description: ""
            quote: true

          - name: giftcodeismethod__c
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: InitialTerm
            description: ""
            quote: true

          - name: InitialTermPeriodType
            description: ""
            quote: true

          - name: InvoiceOwnerId
            description: ""
            quote: true

          - name: IPAddress__c
            description: ""
            quote: true

          - name: IsInvoiceSeparate
            description: ""
            quote: true

          - name: Manually_Cancelled__c
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: Notes
            description: ""
            quote: true

          - name: NumFreeTrialPeriods__c
            description: ""
            quote: true

          - name: NumGiftPeriods__c
            description: ""
            quote: true

          - name: OpportunityCloseDate__QT
            description: ""
            quote: true

          - name: OpportunityName__QT
            description: ""
            quote: true

          - name: OriginalCreatedDate
            description: ""
            quote: true

          - name: OriginalId
            description: ""
            quote: true

          - name: PreviousSubscriptionId
            description: ""
            quote: true

          - name: QuoteBusinessType__QT
            description: ""
            quote: true

          - name: QuoteNumber__QT
            description: ""
            quote: true

          - name: QuoteType__QT
            description: ""
            quote: true

          - name: ReceiptID__c
            description: ""
            quote: true

          - name: RenewalSetting
            description: ""
            quote: true

          - name: RenewalTerm
            description: ""
            quote: true

          - name: RenewalTermPeriodType
            description: ""
            quote: true

          - name: ServiceActivationDate
            description: ""
            quote: true

          - name: SourceSystem__c
            description: ""
            quote: true

          - name: SourceSystemUserID__c
            description: ""
            quote: true

          - name: Status
            description: ""
            quote: true

          - name: SubscriptionEndDate
            description: ""
            quote: true

          - name: SubscriptionStartDate
            description: ""
            quote: true

          - name: TermEndDate
            description: ""
            quote: true

          - name: TermStartDate
            description: ""
            quote: true

          - name: TermType
            description: ""
            quote: true

          - name: TrackingID__c
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: UserAgent__c
            description: ""
            quote: true

          - name: Version
            description: ""
            quote: true

          - name: AccountId
            description: ""
            quote: true

          - name: FreeTrialPeriodsType__c
            description: ""
            quote: true

          - name: PartnerOfferName__c
            description: ""
            quote: true

      - name: CURATED__ZUORA__ACCOUNT_CURRENT
        description: "Current view Account Object"
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was processed by dbt"
            quote: true

          - name: AccountNumber
            description: ""
            quote: true

          - name: AdditionalEmailAddresses
            description: ""
            quote: true

          - name: AllowInvoiceEdit
            description: ""
            quote: true

          - name: AutoPay
            description: ""
            quote: true

          - name: Balance
            description: ""
            quote: true

          - name: Batch
            description: ""
            quote: true

          - name: BcdSettingOption
            description: ""
            quote: true

          - name: BillCycleDay
            description: ""
            quote: true

          - name: CommunicationProfileId
            description: ""
            quote: true

          - name: Company__c
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: CreditBalance
            description: ""
            quote: true

          - name: CrmId
            description: ""
            quote: true

          - name: Currency
            description: ""
            quote: true

          - name: CustomerServiceRepName
            description: ""
            quote: true

          - name: DaznUserId__c
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: InvoiceDeliveryPrefsEmail
            description: ""
            quote: true

          - name: InvoiceDeliveryPrefsPrint
            description: ""
            quote: true

          - name: InvoiceTemplateId
            description: ""
            quote: true

          - name: LastInvoiceDate
            description: ""
            quote: true

          - name: Mrr
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: Notes
            description: ""
            quote: true

          - name: ParentId
            description: ""
            quote: true

          - name: PaymentGateway
            description: ""
            quote: true

          - name: PaymentTerm
            description: ""
            quote: true

          - name: PurchaseOrderNumber
            description: ""
            quote: true

          - name: SalesRepName
            description: ""
            quote: true

          - name: SequenceSetId
            description: ""
            quote: true

          - name: Status
            description: ""
            quote: true

          - name: TaxCompanyCode
            description: ""
            quote: true

          - name: TaxExemptCertificateID
            description: ""
            quote: true

          - name: TaxExemptCertificateType
            description: ""
            quote: true

          - name: TaxExemptDescription
            description: ""
            quote: true

          - name: TaxExemptEffectiveDate
            description: ""
            quote: true

          - name: TaxExemptEntityUseCode
            description: ""
            quote: true

          - name: TaxExemptExpirationDate
            description: ""
            quote: true

          - name: TaxExemptIssuingJurisdiction
            description: ""
            quote: true

          - name: TaxExemptStatus
            description: ""
            quote: true

          - name: TotalDebitMemoBalance
            description: ""
            quote: true

          - name: TotalInvoiceBalance
            description: ""
            quote: true

          - name: UnappliedBalance
            description: ""
            quote: true

          - name: UnappliedCreditMemoAmount
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: VATId
            description: ""
            quote: true

          - name: BillToContactId
            description: ""
            quote: true

          - name: DefaultPaymentMethodId
            description: ""
            quote: true

          - name: SoldToContactId
            description: ""
            quote: true

      - name: CURATED__ZUORA__CONTACT_CURRENT
        description: "Current view of Contact object"
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountId
            description: ""
            quote: true

          - name: Address1
            description: ""
            quote: true

          - name: Address2
            description: ""
            quote: true

          - name: City
            description: ""
            quote: true

          - name: Country
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Description
            description: ""
            quote: true

          - name: Fax
            description: ""
            quote: true

          - name: FirstName
            description: ""
            quote: true

          - name: HomePhone
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: LastName
            description: ""
            quote: true

          - name: MobilePhone
            description: ""
            quote: true

          - name: NickName
            description: ""
            quote: true

          - name: OtherPhone
            description: ""
            quote: true

          - name: OtherPhoneType
            description: ""
            quote: true

          - name: PersonalEmail
            description: ""
            quote: true

          - name: PostalCode
            description: ""
            quote: true

          - name: State
            description: ""
            quote: true

          - name: TaxRegion
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: WorkEmail
            description: ""
            quote: true

          - name: WorkPhone
            description: ""
            quote: true

      - name: CURATED__ZUORA__RATE_PLAN_CURRENT
        description: "Current view of Rate Plan object"
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AmendmentType
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: SubscriptionId
            description: ""
            quote: true

          - name: EntitlementSetId__c
            description: "The field will present an unique id of the fixture being sold based on the rateplan. This will be used as an indicator of the PPV offer customer purchased."
            quote: true

          - name: SourceSystem__c
            description: "This field will be used for tracking the 3PP partner name: Apple IAP, Googlepay, Amazon, etc."
            quote: true

          - name: SourceSystemUserId__c
            description: "Field for storing the 3PP transacitionID for example Apple is orginaltrxID."
            quote: true

          - name: TrackingId__c
            description: "ID used to associate the rateplan purchase to a landing page or campaign, and hence to a partner for any revenue share agreements."
            quote: true

          - name: AmendmentId
            description: "The ID of the amendment associated with the rate plan. This field only applies to amendment rate plans."
            quote: true

          - name: ProductRatePlanId
            description: "The ID of the associated product rate plan."
            quote: true

          - name: Context__c
            description: ""
            quote: true

          - name: SegmentID__c
            description: ""
            quote: true

          - name: AddonType__c
            description: ""
            quote: true

          - name: Platform__c
            description: "TBC"
            quote: true

          - name: Is3ppExitFlow__c
            description: "TBC"
            quote: true

          - name: PartnerOfferName__c
            description: "TBC"
            quote: true

      - name: CURATED__ZUORA__RATE_PLAN_CHARGE_CURRENT
        description: "Current view of Rate Plan Charge"
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was processed by dbt"
            quote: true

          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingCode
            description: ""
            quote: true

          - name: ApplyDiscountTo
            description: ""
            quote: true

          - name: BillCycleDay
            description: ""
            quote: true

          - name: BillCycleType
            description: ""
            quote: true

          - name: BillingPeriod
            description: ""
            quote: true

          - name: BillingPeriodAlignment
            description: ""
            quote: true

          - name: BillingTiming
            description: ""
            quote: true

          - name: ChargedThroughDate
            description: ""
            quote: true

          - name: ChargeModel
            description: ""
            quote: true

          - name: ChargeNumber
            description: ""
            quote: true

          - name: ChargeType
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Description
            description: ""
            quote: true

          - name: DiscountLevel
            description: ""
            quote: true

          - name: DMRC
            description: ""
            quote: true

          - name: DTCV
            description: ""
            quote: true

          - name: EffectiveEndDate
            description: ""
            quote: true

          - name: EffectiveStartDate
            description: ""
            quote: true

          - name: EndDateCondition
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: IsLastSegment
            description: ""
            quote: true

          - name: ListPriceBase
            description: ""
            quote: true

          - name: MRR
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: NumberOfPeriods
            description: ""
            quote: true

          - name: OriginalId
            description: ""
            quote: true

          - name: OverageCalculationOption
            description: ""
            quote: true

          - name: OverageUnusedUnitsCreditOption
            description: ""
            quote: true

          - name: PriceChangeOption
            description: ""
            quote: true

          - name: PriceIncreasePercentage
            description: ""
            quote: true

          - name: ProcessedThroughDate
            description: ""
            quote: true

          - name: Quantity
            description: ""
            quote: true

          - name: RatingGroup
            description: ""
            quote: true

          - name: RevenueRecognitionRuleName
            description: ""
            quote: true

          - name: RevRecCode
            description: ""
            quote: true

          - name: RevRecTriggerCondition
            description: ""
            quote: true

          - name: Segment
            description: ""
            quote: true

          - name: SpecificBillingPeriod
            description: ""
            quote: true

          - name: SpecificEndDate
            description: ""
            quote: true

          - name: TCV
            description: ""
            quote: true

          - name: TriggerDate
            description: ""
            quote: true

          - name: TriggerEvent
            description: ""
            quote: true

          - name: UOM
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: UpToPeriods
            description: ""
            quote: true

          - name: UpToPeriodsType
            description: ""
            quote: true

          - name: Version
            description: ""
            quote: true

          - name: WeeklyBillCycleDay
            description: ""
            quote: true

          - name: RatePlanId
            description: ""
            quote: true

          - name: ProductRatePlanChargeId
            description: ""
            quote: true

          - name: PostSignUpGiftcode__c
            description: "Giftode number (Giftcode redeemed post sign up)"
            quote: true

          - name: PostSignUpGiftcodeCampaignName__c
            description: "Campaign Name for a giftcode used post sign up"
            quote: true

      - name: CURATED__ZUORA__INVOICE_CURRENT
        description: "Current view of Zuora Invoice"
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was processed by dbt"
            quote: true

          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AdjustmentAmount
            description: ""
            quote: true

          - name: Amount
            description: ""
            quote: true

          - name: AmountWithoutTax
            description: ""
            quote: true

          - name: AutoPay
            description: ""
            quote: true

          - name: Balance
            description: ""
            quote: true

          - name: Comments
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: CreditBalanceAdjustmentAmount
            description: ""
            quote: true

          - name: DueDate
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: IncludesOneTime
            description: ""
            quote: true

          - name: IncludesRecurring
            description: ""
            quote: true

          - name: IncludesUsage
            description: ""
            quote: true

          - name: InvoiceDate
            description: ""
            quote: true

          - name: InvoiceNumber
            description: ""
            quote: true

          - name: LastEmailSentDate
            description: ""
            quote: true

          - name: PaymentAmount
            description: ""
            quote: true

          - name: PostedBy
            description: ""
            quote: true

          - name: PostedDate
            description: ""
            quote: true

          - name: RefundAmount
            description: ""
            quote: true

          - name: Reversed
            description: ""
            quote: true

          - name: Source
            description: ""
            quote: true

          - name: SourceId
            description: ""
            quote: true

          - name: Status
            description: ""
            quote: true

          - name: TargetDate
            description: ""
            quote: true

          - name: TaxAmount
            description: ""
            quote: true

          - name: TaxExemptAmount
            description: ""
            quote: true

          - name: TransferredToAccounting
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: AccountId
            description: ""
            quote: true

          - name: BillToContactSnapshotId
            description: ""
            quote: true

          - name: SoldToContactSnapshotId
            description: ""
            quote: true

          - name: RetryStatus__c
            description: ""
            quote: true

      - name: CURATED__ZUORA__INVOICE_ITEM_CURRENT
        description: "Current view of Zuora Invoice Item"

        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was processed by dbt"
            quote: true

          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingCode
            description: ""
            quote: true

          - name: AddonOneTimeSubscriptionId__c
            description: "Subscription ID for the addon purchase that triggered this invoice item"
            quote: true

          - name: AddonOneTimeSubscriptionRPC__c
            description: "Product Rate Plan ID for the addon purchase that triggered this invoice item"
            quote: true

          - name: AppliedToInvoiceItemId
            description: ""
            quote: true

          - name: Balance
            description: ""
            quote: true

          - name: ChargeAmount
            description: ""
            quote: true

          - name: ChargeDate
            description: ""
            quote: true

          - name: ChargeName
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: ProcessingType
            description: ""
            quote: true

          - name: Quantity
            description: ""
            quote: true

          - name: RevRecStartDate
            description: ""
            quote: true

          - name: ServiceEndDate
            description: ""
            quote: true

          - name: ServiceStartDate
            description: ""
            quote: true

          - name: SKU
            description: ""
            quote: true

          - name: SubscriptionId
            description: ""
            quote: true

          - name: TaxAmount
            description: ""
            quote: true

          - name: TaxCode
            description: ""
            quote: true

          - name: TaxExemptAmount
            description: ""
            quote: true

          - name: TaxMode
            description: ""
            quote: true

          - name: UnitPrice
            description: ""
            quote: true

          - name: UOM
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: InvoiceId
            description: ""
            quote: true

          - name: RatePlanChargeId
            description: ""
            quote: true

          - name: SubscriptionNumber
            description: "The number or name of the subscription, matching values in column 'SubscriptionName' from table Subscription."
            quote: true

          - name: SourceItemType
            description: "source of ItemType"
            quote: true

          - name: OrderLineItemId
            description: "Id of OrderLineItem"
            quote: true

      - name: CURATED__ZUORA__PAYMENT_METHOD_CURRENT
        description: "The PaymentMethod object represents payment method details associated with a customer account."
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was processed by dbt"
            quote: true

          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountId
            description: ""
            quote: true

          - name: AchAbaCode
            description: ""
            quote: true

          - name: AchAccountName
            description: ""
            quote: true

          - name: AchAccountNumberMask
            description: ""
            quote: true

          - name: AchAccountType
            description: ""
            quote: true

          - name: AchAddress1
            description: ""
            quote: true

          - name: AchAddress2
            description: ""
            quote: true

          - name: AchBankName
            description: ""
            quote: true

          - name: AchCity
            description: ""
            quote: true

          - name: AchCountry
            description: ""
            quote: true

          - name: AchPostalCode
            description: ""
            quote: true

          - name: AchState
            description: ""
            quote: true

          - name: Active
            description: ""
            quote: true

          - name: BankBranchCode
            description: ""
            quote: true

          - name: BankCheckDigit
            description: ""
            quote: true

          - name: BankCity
            description: ""
            quote: true

          - name: BankCode
            description: ""
            quote: true

          - name: BankIdentificationNumber
            description: ""
            quote: true

          - name: BankName
            description: ""
            quote: true

          - name: BankPostalCode
            description: ""
            quote: true

          - name: BankStreetName
            description: ""
            quote: true

          - name: BankStreetNumber
            description: ""
            quote: true

          - name: BankTransferAccountName
            description: ""
            quote: true

          - name: BankTransferAccountNumberMask
            description: ""
            quote: true

          - name: BankTransferAccountType
            description: ""
            quote: true

          - name: BankTransferType
            description: ""
            quote: true

          - name: BusinessIdentificationCode
            description: ""
            quote: true

          - name: City
            description: ""
            quote: true

          - name: CompanyName
            description: ""
            quote: true

          - name: Country
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: CreditCardAddress1
            description: ""
            quote: true

          - name: CreditCardAddress2
            description: ""
            quote: true

          - name: CreditCardCity
            description: ""
            quote: true

          - name: CreditCardCountry
            description: ""
            quote: true

          - name: CreditCardExpirationMonth
            description: ""
            quote: true

          - name: CreditCardExpirationYear
            description: ""
            quote: true

          - name: CreditCardHolderName
            description: ""
            quote: true

          - name: CreditCardMaskNumber
            description: ""
            quote: true

          - name: CreditCardPostalCode
            description: ""
            quote: true

          - name: CreditCardState
            description: ""
            quote: true

          - name: CreditCardType
            description: ""
            quote: true

          - name: DeviceSessionId
            description: ""
            quote: true

          - name: Email
            description: ""
            quote: true

          - name: ExistingMandate
            description: ""
            quote: true

          - name: FirstName
            description: ""
            quote: true

          - name: IBAN
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: IdentityNumber
            description: ""
            quote: true

          - name: IPAddress
            description: ""
            quote: true

          - name: IsCompany
            description: ""
            quote: true

          - name: LastFailedSaleTransactionDate
            description: ""
            quote: true

          - name: LastName
            description: ""
            quote: true

          - name: LastTransactionDateTime
            description: ""
            quote: true

          - name: LastTransactionStatus
            description: ""
            quote: true

          - name: MandateCreationDate
            description: ""
            quote: true

          - name: MandateID
            description: ""
            quote: true

          - name: MandateReceived
            description: ""
            quote: true

          - name: MandateUpdateDate
            description: ""
            quote: true

          - name: MaxConsecutivePaymentFailures
            description: ""
            quote: true

          - name: Name
            description: ""
            quote: true

          - name: NumConsecutiveFailures
            description: ""
            quote: true

          - name: PaymentMethodStatus
            description: ""
            quote: true

          - name: PaymentRetryWindow
            description: ""
            quote: true

          - name: PaypalBaid
            description: ""
            quote: true

          - name: PaypalEmail
            description: ""
            quote: true

          - name: PaypalPreapprovalKey
            description: ""
            quote: true

          - name: PaypalType
            description: ""
            quote: true

          - name: Phone
            description: ""
            quote: true

          - name: PostalCode
            description: ""
            quote: true

          - name: SecondTokenId
            description: ""
            quote: true

          - name: State
            description: ""
            quote: true

          - name: StreetName
            description: ""
            quote: true

          - name: StreetNumber
            description: ""
            quote: true

          - name: TMX_Id__c
            description: ""
            quote: true

          - name: TokenId
            description: ""
            quote: true

          - name: TotalNumberOfErrorPayments
            description: ""
            quote: true

          - name: TotalNumberOfProcessedPayments
            description: ""
            quote: true

          - name: Type
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: UseDefaultRetryRule
            description: ""
            quote: true

          - name: ActualPaymentMethod__c
            description: "Payment Method reference e.g. CARD PAYNOW etc.. Not all payment methods have this reference."
            quote: true

      - name: CURATED__ZUORA__INVOICE_PAYMENT_CURRENT
        description: "Linking Payment to an Invoice and indicates how much of the invoice was paid."
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was processed by dbt"
            quote: true

          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: Amount
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: RefundAmount
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: InvoiceId
            description: ""
            quote: true

          - name: PaymentId
            description: ""
            quote: true

      - name: CURATED__ZUORA__PAYMENT_CURRENT
        description: "The Payment object holds all of the information about an individual payment, including the payment amount and to which invoices the payment was applied to."
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was processed by dbt"
            quote: true

          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingCode
            description: ""
            quote: true

          - name: Amount
            description: ""
            quote: true

          - name: AppliedAmount
            description: ""
            quote: true

          - name: AppliedCreditBalanceAmount
            description: ""
            quote: true

          - name: AuthTransactionId
            description: ""
            quote: true

          - name: BankIdentificationNumber
            description: ""
            quote: true

          - name: CancelledOn
            description: ""
            quote: true

          - name: Comment
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Currency
            description: ""
            quote: true

          - name: EffectiveDate
            description: ""
            quote: true

          - name: Gateway
            description: ""
            quote: true

          - name: GatewayOrderId
            description: ""
            quote: true

          - name: GatewayResponse
            description: ""
            quote: true

          - name: GatewayResponseCode
            description: ""
            quote: true

          - name: GatewayState
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: MarkedForSubmissionOn
            description: ""
            quote: true

          - name: PaymentNumber
            description: ""
            quote: true

          - name: PaymentSource__c
            description: ""
            quote: true

          - name: ReferencedPaymentID
            description: ""
            quote: true

          - name: ReferenceId
            description: ""
            quote: true

          - name: RefundAmount
            description: ""
            quote: true

          - name: SecondPaymentReferenceId
            description: ""
            quote: true

          - name: SettledOn
            description: ""
            quote: true

          - name: SoftDescriptor
            description: ""
            quote: true

          - name: SoftDescriptorPhone
            description: ""
            quote: true

          - name: Source
            description: ""
            quote: true

          - name: SourceName
            description: ""
            quote: true

          - name: Status
            description: ""
            quote: true

          - name: SubmittedOn
            description: ""
            quote: true

          - name: TransferredToAccounting
            description: ""
            quote: true

          - name: Type
            description: ""
            quote: true

          - name: UnappliedAmount
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: AccountId
            description: ""
            quote: true

          - name: PaymentMethodSnapshotId
            description: ""
            quote: true

          - name: PaymentMethodId
            description: ""
            quote: true

      - name: CURATED__ZUORA__INVOICE_ITEM_ADJUSTMENT_CURRENT
        description: "Record of a modification of a specific line item invoice."
        columns:
          - name: DBT_INSERT_DTTS
            description: "Date/Timestamp the record was processed by dbt"
            quote: true

          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountId
            description: ""
            quote: true

          - name: AccountingCode
            description: ""
            quote: true

          - name: AdjustmentDate
            description: ""
            quote: true

          - name: AdjustmentNumber
            description: ""
            quote: true

          - name: Amount
            description: ""
            quote: true

          - name: CancelledById
            description: ""
            quote: true

          - name: CancelledDate
            description: ""
            quote: true

          - name: Comment
            description: ""
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: InvoiceId
            description: ""
            quote: true

          - name: InvoiceItemName
            description: ""
            quote: true

          - name: InvoiceNumber
            description: ""
            quote: true

          - name: ReasonCode
            description: ""
            quote: true

          - name: ReferenceId
            description: ""
            quote: true

          - name: ServiceEndDate
            description: ""
            quote: true

          - name: ServiceStartDate
            description: ""
            quote: true

          - name: SourceId
            description: ""
            quote: true

          - name: SourceType
            description: ""
            quote: true

          - name: Status
            description: ""
            quote: true

          - name: TransferredToAccounting
            description: ""
            quote: true

          - name: Type
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: CustomerNumber
            description: "The account number, matching values in column 'AccountNumber' from table 'Account',"
            quote: true

          - name: DeferredRevenueAccount
            description: "The accounting code for deferred revenue e.g. 7074."
            quote: true

          - name: RecognizedRevenueAccount
            description: "The accounting code for recognized revenue e.g. 1015."
            quote: true

      - name: CURATED__ZUORA__RATE_PLAN_CHARGE_TIER_CURRENT
        description: "A product rate plan charge represents a charge model or a set of fees associated with a product rate plan, which is the part of a product that your customers subscribe to."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: CreatedById
            description: ""
            quote: true

          - name: CreatedDate
            description: ""
            quote: true

          - name: Currency
            description: ""
            quote: true

          - name: DiscountAmount
            description: ""
            quote: true

          - name: DiscountPercentage
            description: ""
            quote: true

          - name: EndingUnit
            description: ""
            quote: true

          - name: Id
            description: ""
            quote: true

          - name: IncludedUnits
            description: ""
            quote: true

          - name: OveragePrice
            description: ""
            quote: true

          - name: Price
            description: ""
            quote: true

          - name: PriceFormat
            description: ""
            quote: true

          - name: StartingUnit
            description: ""
            quote: true

          - name: Tier
            description: ""
            quote: true

          - name: UpdatedById
            description: ""
            quote: true

          - name: UpdatedDate
            description: ""
            quote: true

          - name: RatePlanChargeId
            description: ""
            quote: true

      - name: CURATED__ZUORA_SHARE__ACCOUNT_CURRENT
        description: "Zuora account contains information about the customer, such as contact information, payment terms, and payment methods."
        columns:
          - name: PARENTID
            description: ""
            quote: true

          - name: TOTALINVOICEBALANCE
            description: ""
            quote: true

          - name: UNAPPLIEDCREDITMEMOAMOUNT
            description: ""
            quote: true

          - name: CREATEDBYID
            description: ""
            quote: true

          - name: DELETED
            description: ""
            quote: true

          - name: NAME
            description: ""
            quote: true

          - name: TAXEXEMPTCERTIFICATEID
            description: ""
            quote: true

          - name: TAXEXEMPTCERTIFICATETYPE
            description: ""
            quote: true

          - name: ID
            description: ""
            quote: true

          - name: UPDATEDBYID
            description: ""
            quote: true

          - name: DEFAULTPAYMENTMETHODID
            description: ""
            quote: true

          - name: TAXEXEMPTDESCRIPTION
            description: ""
            quote: true

          - name: SALESREPNAME
            description: ""
            quote: true

          - name: TAXEXEMPTENTITYUSECODE
            description: ""
            quote: true

          - name: TAXEXEMPTEXPIRATIONDATE
            description: ""
            quote: true

          - name: ACCOUNTNUMBER
            description: ""
            quote: true

          - name: COMMUNICATIONPROFILEID
            description: ""
            quote: true

          - name: TAXCOMPANYCODE
            description: ""
            quote: true

          - name: SEQUENCESETID
            description: ""
            quote: true

          - name: BILLTOID
            description: ""
            quote: true

          - name: STATUS
            description: ""
            quote: true

          - name: TOTALDEBITMEMOBALANCE
            description: ""
            quote: true

          - name: PURCHASEORDERNUMBER
            description: ""
            quote: true

          - name: LASTINVOICEDATE
            description: ""
            quote: true

          - name: BCDSETTINGOPTION
            description: ""
            quote: true

          - name: TAXEXEMPTSTATUS
            description: ""
            quote: true

          - name: CREDITMEMOTEMPLATEID
            description: ""
            quote: true

          - name: CREATEDDATE
            description: ""
            quote: true

          - name: BILLCYCLEDAY
            description: ""
            quote: true

          - name: AUTOPAY
            description: ""
            quote: true

          - name: TAXEXEMPTEFFECTIVEDATE
            description: ""
            quote: true

          - name: INVOICEDELIVERYPREFSEMAIL
            description: ""
            quote: true

          - name: INVOICEDELIVERYPREFSPRINT
            description: ""
            quote: true

          - name: UNAPPLIEDBALANCE
            description: ""
            quote: true

          - name: VATID
            description: ""
            quote: true

          - name: SOLDTOID
            description: ""
            quote: true

          - name: TAXEXEMPTISSUINGJURISDICTION
            description: ""
            quote: true

          - name: ALLOWINVOICEEDIT
            description: ""
            quote: true

          - name: BALANCE
            description: ""
            quote: true

          - name: PAYMENTTERM
            description: ""
            quote: true

          - name: DEBITMEMOTEMPLATEID
            description: ""
            quote: true

          - name: CRMID
            description: ""
            quote: true

          - name: PAYMENTGATEWAY
            description: ""
            quote: true

          - name: MRR
            description: ""
            quote: true

          - name: INVOICETEMPLATEID
            description: ""
            quote: true

          - name: UPDATEDDATE
            description: ""
            quote: true

          - name: NOTES
            description: ""
            quote: true

          - name: CREDITBALANCE
            description: ""
            quote: true

          - name: CUSTOMERSERVICEREPNAME
            description: ""
            quote: true

          - name: CURRENCY
            description: ""
            quote: true

          - name: ADDITIONALEMAILADDRESSES
            description: ""
            quote: true

          - name: CURRENCYCODE
            description: ""
            quote: true

          - name: BATCH
            description: ""
            quote: true

          - name: APM__C
            description: ""
            quote: true

          - name: RETRYSTATUS__C
            description: ""
            quote: true

          - name: COMPANY__C
            description: ""
            quote: true

          - name: DAZNUSERID__C
            description: ""
            quote: true

          - name: TAX_ID__C
            description: ""
            quote: true

      - name: CURATED__ZUORA_SHARE__SUBSCRIPTION_NAME_CURRENT
        description: "The Subscription object contains the information needed to create and maintain a subscription associated with an Account object."
        columns:
          - name: TERMENDDATE
            description: ""
            quote: true

          - name: NOTES
            description: ""
            quote: true

          - name: ISINVOICESEPARATE
            description: ""
            quote: true

          - name: REVISION
            description: ""
            quote: true

          - name: RENEWALTERMPERIODTYPE
            description: ""
            quote: true

          - name: PAYMENTTERM
            description: ""
            quote: true

          - name: ACCOUNTID
            description: ""
            quote: true

          - name: CONTRACTEFFECTIVEDATE
            description: ""
            quote: true

          - name: NAME
            description: ""
            quote: true

          - name: BILLTOCONTACTSNAPSHOTID
            description: ""
            quote: true

          - name: UPDATEDDATE
            description: ""
            quote: true

          - name: SUBSCRIPTIONENDDATE
            description: ""
            quote: true

          - name: CPQBUNDLEJSONID__QT
            description: ""
            quote: true

          - name: AUTORENEW
            description: ""
            quote: true

          - name: ORIGINALCREATEDDATE
            description: ""
            quote: true

          - name: DELETED
            description: ""
            quote: true

          - name: SUBSCRIPTIONSTARTDATE
            description: ""
            quote: true

          - name: VERSION
            description: ""
            quote: true

          - name: CREATORACCOUNTID
            description: ""
            quote: true

          - name: SERVICEACTIVATIONDATE
            description: ""
            quote: true

          - name: CANCELLEDDATE
            description: ""
            quote: true

          - name: SUBSCRIPTIONVERSIONAMENDMENTID
            description: ""
            quote: true

          - name: CURRENTTERMPERIODTYPE
            description: ""
            quote: true

          - name: BILLTOCONTACTID
            description: ""
            quote: true

          - name: QUOTETYPE__QT
            description: ""
            quote: true

          - name: TERMTYPE
            description: ""
            quote: true

          - name: ID
            description: ""
            quote: true

          - name: INVOICESCHEDULEID
            description: ""
            quote: true

          - name: CMRR
            description: ""
            quote: true

          - name: INITIALTERM
            description: ""
            quote: true

          - name: INVOICEOWNERID
            description: ""
            quote: true

          - name: TERMSTARTDATE
            description: ""
            quote: true

          - name: RENEWALSETTING
            description: ""
            quote: true

          - name: RENEWALTERM
            description: ""
            quote: true

          - name: OPPORTUNITYNAME__QT
            description: ""
            quote: true

          - name: CREATEDDATE
            description: ""
            quote: true

          - name: UPDATEDBYID
            description: ""
            quote: true

          - name: QUOTENUMBER__QT
            description: ""
            quote: true

          - name: CONTRACTACCEPTANCEDATE
            description: ""
            quote: true

          - name: ORIGINALID
            description: ""
            quote: true

          - name: CREATEDBYID
            description: ""
            quote: true

          - name: LASTBOOKINGDATE
            description: ""
            quote: true

          - name: INITIALTERMPERIODTYPE
            description: ""
            quote: true

          - name: CREATORINVOICEOWNERID
            description: ""
            quote: true

          - name: OPPORTUNITYCLOSEDATE__QT
            description: ""
            quote: true

          - name: RAMPID
            description: ""
            quote: true

          - name: PREVIOUSSUBSCRIPTIONID
            description: ""
            quote: true

          - name: QUOTEBUSINESSTYPE__QT
            description: ""
            quote: true

          - name: EXTERNALLYMANAGEDBY
            description: ""
            quote: true

          - name: ISLATESTVERSION
            description: ""
            quote: true

          - name: STATUS
            description: ""
            quote: true

          - name: CURRENTTERM
            description: ""
            quote: true

          - name: PAYMENTMETHODID__C
            description: ""
            quote: true

          - name: PAUSE_FROM__C
            description: ""
            quote: true

          - name: PRICE_CONSENT__C
            description: ""
            quote: true

          - name: PAYMENTGATEWAY__C
            description: ""
            quote: true

          - name: PAUSE_UNTIL__C
            description: ""
            quote: true

          - name: RENEWALCONSENTGIVENDATE__C
            description: ""
            quote: true

          - name: MANUALLY_CANCELLED__C
            description: ""
            quote: true

          - name: PAUSE_SCHEDULED_TIME__C
            description: ""
            quote: true

          - name: DCB_CARIER_NAME__C
            description: ""
            quote: true

          - name: RECEIPTID__C
            description: ""
            quote: true

          - name: UNPAUSE_SCHEDULED_TIME__C
            description: ""
            quote: true

          - name: GIFTCODEGRANTSCONTENTPORTABILITY__C
            description: ""
            quote: true

          - name: TRACKINGID__C
            description: ""
            quote: true

          - name: GIFTCODEISMETHOD__C
            description: ""
            quote: true

          - name: SOURCESYSTEM__C
            description: ""
            quote: true

          - name: CAMPAIGNID__C
            description: ""
            quote: true

          - name: COUNTRYOFSUBSCRIPTION__C
            description: ""
            quote: true

          - name: IPADDRESS__C
            description: ""
            quote: true

          - name: BCD_RETRY__C
            description: ""
            quote: true

          - name: SOURCESYSTEMUSERID__C
            description: ""
            quote: true

          - name: NUMGIFTPERIODS__C
            description: ""
            quote: true

          - name: GIFTCODE_OLD__C
            description: ""
            quote: true

          - name: NUMFREETRIALPERIODS__C
            description: ""
            quote: true

          - name: USERAGENT__C
            description: ""
            quote: true

          - name: DEVICEINFORMATION__C
            description: ""
            quote: true

          - name: FREETRIALPERIODSTYPE__C
            description: ""
            quote: true

          - name: ACTION_PERFORM__C
            description: ""
            quote: true

          - name: CLEANUP_TRACKER__C
            description: ""
            quote: true

          - name: GIFTCODE__C
            description: ""
            quote: true

          - name: PRODUCTGROUP__C
            description: ""
            quote: true

      - name: CURATED__ZUORA_SHARE__RATE_PLAN_CURRENT
        description: "Rate plans represent a price or a collection of prices for a service sold."
        columns:
          - name: UPDATEDBYID
            description: ""
            quote: true

          - name: DELETED
            description: ""
            quote: true

          - name: UPDATEDDATE
            description: ""
            quote: true

          - name: ORIGINALRATEPLANID
            description: ""
            quote: true

          - name: SUBSCRIPTIONID
            description: ""
            quote: true

          - name: ID
            description: ""
            quote: true

          - name: PRODUCTID
            description: ""
            quote: true

          - name: CREATEDBYID
            description: ""
            quote: true

          - name: CREATEDDATE
            description: ""
            quote: true

          - name: NAME
            description: ""
            quote: true

          - name: AMENDMENTTYPE
            description: ""
            quote: true

          - name: PRODUCTRATEPLANID
            description: ""
            quote: true

          - name: AMENDMENTID
            description: ""
            quote: true

          - name: SOURCESYSTEMUSERID__C
            description: ""
            quote: true

          - name: PARTNERID__C
            description: ""
            quote: true

          - name: ENTITLEMENTSETID__C
            description: ""
            quote: true

          - name: PAYMENTGATEWAY__C
            description: ""
            quote: true

          - name: SOURCESYSTEM__C
            description: ""
            quote: true

          - name: CONTEXT__C
            description: ""
            quote: true

          - name: TRACKINGID__C
            description: ""
            quote: true

          - name: NEXTINVOICEDATE__C
            description: ""
            quote: true

          - name: PRODUCTTYPE__C
            description: ""
            quote: true

          - name: BILLINGTYPE__C
            description: ""
            quote: true

          - name: PAYMENTMETHODID__C
            description: ""
            quote: true

          - name: PRODUCTGROUP__C
            description: ""
            quote: true

          - name: PARTNERUSERID__C
            description: ""
            quote: true

      - name: CURATED__ZUORA_SHARE__RATE_PLAN_CHARGE_CURRENT
        description: "Rate plan charges represent the actual charges for the rate plans or services sold and are part of a subscription."
        columns:
          - name: LISTPRICEBASE
            description: ""
            quote: true

          - name: UPTOPERIODS
            description: ""
            quote: true

          - name: BILLCYCLETYPE
            description: ""
            quote: true

          - name: ID
            description: ""
            quote: true

          - name: DISCOUNTLEVEL
            description: ""
            quote: true

          - name: BILLINGPERIODALIGNMENT
            description: ""
            quote: true

          - name: PRODUCTRATEPLANCHARGEID
            description: ""
            quote: true

          - name: ENDDATECONDITION
            description: ""
            quote: true

          - name: APPLYDISCOUNTTO
            description: ""
            quote: true

          - name: REVRECTRIGGERCONDITION
            description: ""
            quote: true

          - name: CREATEDDATE
            description: ""
            quote: true

          - name: PRICEINCREASEPERCENTAGE
            description: ""
            quote: true

          - name: UPTOPERIODSTYPE
            description: ""
            quote: true

          - name: DTCV
            description: ""
            quote: true

          - name: REVENUERECOGNITIONRULENAME
            description: ""
            quote: true

          - name: SPECIFICBILLINGPERIOD
            description: ""
            quote: true

          - name: CHARGETYPE
            description: ""
            quote: true

          - name: SPECIFICENDDATE
            description: ""
            quote: true

          - name: CHARGEMODEL
            description: ""
            quote: true

          - name: UOM
            description: ""
            quote: true

          - name: QUANTITY
            description: ""
            quote: true

          - name: OVERAGECALCULATIONOPTION
            description: ""
            quote: true

          - name: TRIGGEREVENT
            description: ""
            quote: true

          - name: PROCESSEDTHROUGHDATE
            description: ""
            quote: true

          - name: CREATEDBYID
            description: ""
            quote: true

          - name: UPDATEDBYID
            description: ""
            quote: true

          - name: WEEKLYBILLCYCLEDAY
            description: ""
            quote: true

          - name: NUMBEROFPERIODS
            description: ""
            quote: true

          - name: DMRC
            description: ""
            quote: true

          - name: ISLASTSEGMENT
            description: ""
            quote: true

          - name: BILLCYCLEDAY
            description: ""
            quote: true

          - name: PRICECHANGEOPTION
            description: ""
            quote: true

          - name: VERSION
            description: ""
            quote: true

          - name: NAME
            description: ""
            quote: true

          - name: OVERAGEUNUSEDUNITSCREDITOPTION
            description: ""
            quote: true

          - name: TCV
            description: ""
            quote: true

          - name: CHARGEDTHROUGHDATE
            description: ""
            quote: true

          - name: DELETED
            description: ""
            quote: true

          - name: MRR
            description: ""
            quote: true

          - name: EFFECTIVEENDDATE
            description: ""
            quote: true

          - name: SEGMENT
            description: ""
            quote: true

          - name: BILLINGPERIOD
            description: ""
            quote: true

          - name: REVRECCODE
            description: ""
            quote: true

          - name: ACCOUNTRECEIVABLEACCOUNTINGCODEID
            description: ""
            quote: true

          - name: EFFECTIVESTARTDATE
            description: ""
            quote: true

          - name: CHARGENUMBER
            description: ""
            quote: true

          - name: ORIGINALID
            description: ""
            quote: true

          - name: RECOGNIZEDREVENUEACCOUNTINGCODEID
            description: ""
            quote: true

          - name: DESCRIPTION
            description: ""
            quote: true

          - name: UPDATEDDATE
            description: ""
            quote: true

          - name: RATINGGROUP
            description: ""
            quote: true

          - name: TRIGGERDATE
            description: ""
            quote: true

          - name: ACCOUNTINGCODE
            description: ""
            quote: true

          - name: BILLINGTIMING
            description: ""
            quote: true

          - name: DEFERREDREVENUEACCOUNTINGCODEID
            description: ""
            quote: true

          - name: ISPROCESSED
            description: ""
            quote: true

          - name: RATEPLANID
            description: ""
            quote: true

          - name: POSTSIGNUPGIFTCODECAMPAIGNNAME__C
            description: ""
            quote: true

          - name: POSTSIGNUPGIFTCODE__C
            description: ""
            quote: true

      - name: CURATED__ZUORA__PRODUCT_RATE_PLAN_CURRENT
        description: "Use the ProductRatePlan object to define the services that make up the Product objects that can be associated with Subscription objects."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: CreatedById
            description: "This is the UserID / API that created that record."
            quote: true

          - name: CreatedDate
            description: "Date of creation."
            quote: true

          - name: Description
            description: "TBC"
            quote: true

          - name: EffectiveEndDate
            description: "TBC"
            quote: true

          - name: EffectiveStartDate
            description: "TBC"
            quote: true

          - name: Id
            description: "The ID of this object."
            quote: true

          - name: Name
            description: "TBC"
            quote: true

          - name: OfferType__c
            description: "TBC"
            quote: true

          - name: UpdatedById
            description: "The userId that updated the product rate plan current"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the product rate plan current"
            quote: true

          - name: EntitlementSetId__c
            description: "The field will present an unique id of the fixture being sold based on the rateplan. This will be used as an indicator of the PPV offer customer purchased."
            quote: true

          - name: PPVType__c
            description: "The field will differentiate if plan is a regular one (used post sign up with full PPV price) or discounted one (sign up bundle)."
            quote: true

          - name: BillingType__c
            description: "The billing type associated with this rateplan. Can be one of the following: Recurring, RecurringExternal or OneOff"
            quote: true

          - name: ProductType__c
            description: "The product type associated with this rateplan. Can be one of the following: subscription, addon, ppv or null"
            quote: true

      - name: CURATED__ZUORA__PRODUCT_RATE_PLAN_CHARGE_CURRENT
        description: "A product rate plan charge represents a charge model or a set of fees associated with a product rate plan, which is the part of a product that your customers subscribe to."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: AccountingCode
            description: "The account code Id"
            quote: true

          - name: ApplyDiscountTo
            description: "TBC"
            quote: true

          - name: BillCycleDay
            description: "TBC"
            quote: true

          - name: BillCycleType
            description: "TBC"
            quote: true

          - name: BillingPeriod
            description: "TBC"
            quote: true

          - name: BillingPeriodAlignment
            description: "TBC"
            quote: true

          - name: BillingTiming
            description: "TBC"
            quote: true

          - name: ChargeModel
            description: "TBC"
            quote: true

          - name: ChargeType
            description: "TBC"
            quote: true

          - name: CreatedById
            description: "This is the UserID / API that created that record."
            quote: true

          - name: CreatedDate
            description: "Date of creation."
            quote: true

          - name: DefaultQuantity
            description: "TBC"
            quote: true

          - name: DeferredRevenueAccount
            description: "The accounting code for deferred revenue e.g. 7074."
            quote: true

          - name: Description
            description: "TBC"
            quote: true

          - name: DiscountLevel
            description: "TBC"
            quote: true

          - name: EndDateCondition
            description: "TBC"
            quote: true

          - name: Id
            description: "The ID of this object."
            quote: true

          - name: IncludedUnits
            description: "TBC"
            quote: true

          - name: LegacyRevenueReporting
            description: "TBC"
            quote: true

          - name: ListPriceBase
            description: "TBC"
            quote: true

          - name: MaxQuantity
            description: "TBC"
            quote: true

          - name: MinQuantity
            description: "TBC"
            quote: true

          - name: Name
            description: "TBC"
            quote: true

          - name: NumberOfPeriod
            description: "TBC"
            quote: true

          - name: OverageCalculationOption
            description: "TBC"
            quote: true

          - name: OverageUnusedUnitsCreditOption
            description: "TBC"
            quote: true

          - name: PriceChangeOption
            description: "TBC"
            quote: true

          - name: PriceIncreasePercentage
            description: "TBC"
            quote: true

          - name: RatingGroup
            description: "TBC"
            quote: true

          - name: RecognizedRevenueAccount
            description: "TBC"
            quote: true

          - name: RevenueRecognitionRuleName
            description: "TBC"
            quote: true

          - name: RevRecCode
            description: "TBC"
            quote: true

          - name: RevRecTriggerCondition
            description: "TBC"
            quote: true

          - name: SmoothingModel
            description: "TBC"
            quote: true

          - name: SpecificBillingPeriod
            description: "TBC"
            quote: true

          - name: Taxable
            description: "TBC"
            quote: true

          - name: TaxCode
            description: "TBC"
            quote: true

          - name: TaxMode
            description: "TBC"
            quote: true

          - name: TriggerEvent
            description: "TBC"
            quote: true

          - name: UOM
            description: "TBC"
            quote: true

          - name: UpdatedById
            description: "The userId that updated the product rate plan charge current"
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the product rate plan charge current"
            quote: true

          - name: UpToPeriods
            description: "TBC"
            quote: true

          - name: UpToPeriodsType
            description: "TBC"
            quote: true

          - name: UsageRecordRatingOption
            description: "TBC"
            quote: true

          - name: UseDiscountSpecificAccountingCode
            description: "TBC"
            quote: true

          - name: UseTenantDefaultForPriceChange
            description: "TBC"
            quote: true

          - name: WeeklyBillCycleDay
            description: "TBC"
            quote: true

          - name: ProductRatePlanId
            description: "The ID of the associated product rate plan."
            quote: true

      - name: CURATED__ZUORA__ORDER_LINE_ITEM
        description: "The OrderLineItem object contains the information needed to create and maintain a PPV associated with an Account object."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: Amount
            description: "Amount of the payment (authorisation amount)."
            quote: true

          - name: AmountPerUnit
            description: "TBC"
            quote: true

          - name: AmountWithoutTax
            description: "TBC"
            quote: true

          - name: BillingRule
            description: "TBC"
            quote: true

          - name: BillTargetDate
            description: "TBC"
            quote: true

          - name: CreatedById
            description: "This is the UserID / API that created that record."
            quote: true

          - name: CreatedDate
            description: "Date of creation."
            quote: true

          - name: Currency
            description: "The three-character ISO currency code."
            quote: true

          - name: Description
            description: "TBC"
            quote: true

          - name: Discount
            description: "TBC"
            quote: true

          - name: entitlementsetId__c
            description: "The field will present an unique id of the fixture being sold based on the rateplan. This will be used as an indicator of the PPV offer customer purchased."
            quote: true

          - name: Id
            description: "The ID of this object."
            quote: true

          - name: InlineDiscountPerUnit
            description: "TBC"
            quote: true

          - name: InlineDiscountType
            description: "TBC"
            quote: true

          - name: ItemCategory
            description: "TBC"
            quote: true

          - name: ItemName
            description: "The name of the OrderLineItem."
            quote: true

          - name: ItemNumber
            description: "TBC"
            quote: true

          - name: ItemState
            description: "TBC"
            quote: true

          - name: ItemType
            description: "Indicates the line item type of a OrderLineItem."
            quote: true

          - name: ListPrice
            description: "TBC"
            quote: true

          - name: ListPricePerUnit
            description: "TBC"
            quote: true

          - name: Quantity
            description: "TBC"
            quote: true

          - name: QuantityAvailableForReturn
            description: "TBC"
            quote: true

          - name: QuantityFulfilled
            description: "TBC"
            quote: true

          - name: QuantityPendingFulfillment
            description: "TBC"
            quote: true

          - name: RelatedSubscriptionNumber
            description: "TBC"
            quote: true

          - name: RequiresFulfillment
            description: "TBC"
            quote: true

          - name: SoldTo
            description: "TBC"
            quote: true

          - name: SourceSystem__c
            description: "This field will be used for tracking the 3PP partner name: Apple IAP, Googlepay, Amazon, etc."
            quote: true

          - name: TrackingID__c
            description: "Front End Partnerships Tracking ID usually for use in commercial partnership tracking"
            quote: true

          - name: TransactionStartDate
            description: "TBC"
            quote: true

          - name: TransactionEndDate
            description: "TBC"
            quote: true

          - name: UpdatedById
            description: "The userId that updated the OrderLineItem."
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the OrderLineItem."
            quote: true

          - name: OrderId
            description: "The ID of the Order to which the OrderLineItem belongs."
            quote: true

          - name: AccountId
            description: "Account id relating to the OrderLineItem."
            quote: true

          - name: ProductRatePlanChargeId
            description: "The ID of the associated product rate plan charge."
            quote: true

          - name: Country__c
            description: "Country Name."
            quote: true

          - name: Giftcode__c
            description: "Gift code no."
            quote: true

          - name: giftcodeismethod__c
            description: "TBC"
            quote: true

          - name: SourceSystemUserID__c
            description: "Field for storing the 3PP transacitionID for example Apple is orginaltrxID."
            quote: true

          - name: PartnerOfferName__c
            description: "TBC"
            quote: true

          - name: Platform__c
            description: "TBC"
            quote: true

          - name: Is3ppExitFlow__c
            description: "TBC"
            quote: true

      - name: CURATED__ZUORA__ORDER_LINE_ITEM_CURRENT
        description: "The OrderLineItem object contains the information needed to create and maintain a PPV associated with an Account object."
        columns:
          - name: EDM_INSERT_DTTS
            description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
            quote: true

          - name: Amount
            description: "Amount of the payment (authorisation amount)."
            quote: true

          - name: AmountPerUnit
            description: "TBC"
            quote: true

          - name: AmountWithoutTax
            description: "TBC"
            quote: true

          - name: BillingRule
            description: "TBC"
            quote: true

          - name: BillTargetDate
            description: "TBC"
            quote: true

          - name: CreatedById
            description: "This is the UserID / API that created that record."
            quote: true

          - name: CreatedDate
            description: "Date of creation."
            quote: true

          - name: Currency
            description: "The three-character ISO currency code."
            quote: true

          - name: Description
            description: "TBC"
            quote: true

          - name: Discount
            description: "TBC"
            quote: true

          - name: entitlementsetId__c
            description: "The field will present an unique id of the fixture being sold based on the rateplan. This will be used as an indicator of the PPV offer customer purchased."
            quote: true

          - name: Id
            description: "The ID of this object."
            quote: true

          - name: InlineDiscountPerUnit
            description: "TBC"
            quote: true

          - name: InlineDiscountType
            description: "TBC"
            quote: true

          - name: ItemCategory
            description: "TBC"
            quote: true

          - name: ItemName
            description: "The name of the OrderLineItem."
            quote: true

          - name: ItemNumber
            description: "TBC"
            quote: true

          - name: ItemState
            description: "TBC"
            quote: true

          - name: ItemType
            description: "Indicates the line item type of a OrderLineItem."
            quote: true

          - name: ListPrice
            description: "TBC"
            quote: true

          - name: ListPricePerUnit
            description: "TBC"
            quote: true

          - name: Quantity
            description: "TBC"
            quote: true

          - name: QuantityAvailableForReturn
            description: "TBC"
            quote: true

          - name: QuantityFulfilled
            description: "TBC"
            quote: true

          - name: QuantityPendingFulfillment
            description: "TBC"
            quote: true

          - name: RelatedSubscriptionNumber
            description: "TBC"
            quote: true

          - name: RequiresFulfillment
            description: "TBC"
            quote: true

          - name: SoldTo
            description: "TBC"
            quote: true

          - name: SourceSystem__c
            description: "This field will be used for tracking the 3PP partner name: Apple IAP, Googlepay, Amazon, etc."
            quote: true

          - name: TrackingID__c
            description: "Front End Partnerships Tracking ID usually for use in commercial partnership tracking"
            quote: true

          - name: TransactionStartDate
            description: "TBC"
            quote: true

          - name: TransactionEndDate
            description: "TBC"
            quote: true

          - name: UpdatedById
            description: "The userId that updated the OrderLineItem."
            quote: true

          - name: UpdatedDate
            description: "The date of last update of the OrderLineItem."
            quote: true

          - name: OrderId
            description: "The ID of the Order to which the OrderLineItem belongs."
            quote: true

          - name: AccountId
            description: "Account id relating to the OrderLineItem."
            quote: true

          - name: ProductRatePlanChargeId
            description: "The ID of the associated product rate plan charge."
            quote: true

          - name: Country__c
            description: "Country Name."
            quote: true

          - name: Giftcode__c
            description: "Gift code no."
            quote: true

          - name: giftcodeismethod__c
            description: "TBC"
            quote: true

          - name: SourceSystemUserID__c
            description: "Field for storing the 3PP transacitionID for example Apple is orginaltrxID."
            quote: true

          - name: PartnerOfferName__c
            description: "TBC"
            quote: true

          - name: Platform__c
            description: "TBC"
            quote: true

          - name: Is3ppExitFlow__c
            description: "TBC"
            quote: true
