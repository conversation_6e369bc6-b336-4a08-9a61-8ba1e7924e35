WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA__INVOICE_ITEM_ADJUSTMENT') }}
)

, renamed AS (
    SELECT
        "Id" AS "invoice_item_adjustment_id"
        ,"AccountId" AS "billing_account_id"
        ,"InvoiceId" AS "invoice_id"
        ,"InvoiceNumber" AS "invoice_number"
        ,"InvoiceItemName" AS "invoice_item_name"
        ,"CustomerNumber" AS "customer_number"
        ,"AccountingCode" AS "invoice_item_adjustment_accounting_code"
        ,"AdjustmentDate" AS "invoice_item_adjustment_timestamp"
        ,"AdjustmentNumber" AS "invoice_item_adjustment_number"
        ,"Amount" AS "invoice_item_adjustment_amount"
        ,"CancelledById" AS "invoice_item_adjustment_cancelled_by_id"
        ,"CancelledDate" AS "invoice_item_adjustment_cancelled_timestamp"
        ,"Comment" AS "invoice_item_adjustment_comment"
        ,"CreatedById" AS "invoice_item_adjustment_created_by_id"
        ,"CreatedDate" AS "invoice_item_adjustment_created_timestamp"
        ,"ReasonCode" AS "invoice_item_adjustment_reason_code"
        ,"ReferenceId" AS "invoice_item_adjustment_reference_id"
        ,"ServiceEndDate" AS "invoice_item_adjustment_service_end_date"
        ,"ServiceStartDate" AS "invoice_item_adjustment_service_start_date"
        ,"SourceId" AS "invoice_item_adjustment_source_id"
        ,"SourceType" AS "invoice_item_adjustment_source_type"
        ,"Status" AS "invoice_item_adjustment_status"
        ,"TransferredToAccounting" AS "invoice_item_adjustment_transferred_to_accounting"
        ,"Type" AS "invoice_item_adjustment_type"
        ,"UpdatedById" AS "invoice_item_adjustment_updated_by_id"
        ,"UpdatedDate" AS "invoice_item_adjustment_updated_timestamp"
        ,"DeferredRevenueAccount" AS "invoice_item_adjustment_deferred_revenue_account"
        ,"RecognizedRevenueAccount" AS "invoice_item_adjustment_recognized_revenue_account"
    FROM source
)

SELECT * FROM renamed
