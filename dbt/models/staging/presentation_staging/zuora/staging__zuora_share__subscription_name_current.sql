WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA_SHARE__SUBSCRIPTION_NAME_CURRENT') }}
)
,renamed AS (
    SELECT
        "ID" AS "subscription_id"
        ,"ACCOUNTID" AS "billing_account_id"
        ,"CREATORACCOUNTID" AS "subscription_creator_billing_account_id"
        ,"CREATORINVOICEOWNERID" AS "subscription_creator_invoice_owner_billing_account_id"
        ,"INVOICEOWNERID" AS "subscription_invoice_owner_billing_account_id"
        ,"ORIGINALID" AS "subscription_original_id"
        ,"PREVIOUSSUBSCRIPTIONID" AS "subscription_previous_subscription_id"
        ,"CAMPAIGNID__C" AS "subscription_sign_up_campaign_id"
        ,"DCB_CARIER_NAME__C" AS "subscription_direct_carrier_billing_carrier_name"
        ,"DEVICEINFORMATION__C" AS "subscription_device_category"
        ,"GIFTCODE__C" AS "subscription_sign_up_giftcode"
        ,"GIFTCODE_OLD__C" AS "subscription_sign_up_giftcode_old"
        ,"IPADDRESS__C" AS "subscription_ip_address__hash"
        ,"MANUALLY_CANCELLED__C" AS "subscription_is_manually_cancelled"
        ,"GIFTCODEGRANTSCONTENTPORTABILITY__C" AS "subscription_giftcode_grants_content_portability"
        ,"GIFTCODEISMETHOD__C" AS "subscription_giftcode_is_payment_method"
        ,"SOURCESYSTEM__C" AS "subscription_source_system_name"
        ,"SOURCESYSTEMUSERID__C" AS "subscription_source_system_user_id"
        ,"TRACKINGID__C" AS "subscription_tracking_id"
        ,"USERAGENT__C" AS "subscription_user_agent"
        ,"AUTORENEW" AS "subscription_is_auto_renew"
        ,"CANCELLEDDATE" AS "subscription_cancelled_date"
        ,"CONTRACTACCEPTANCEDATE" AS "subscription_contract_acceptance_date"
        ,"CONTRACTEFFECTIVEDATE" AS "subscription_contract_effective_date"
        ,"COUNTRYOFSUBSCRIPTION__C" AS "subscription_country"
        ,"CPQBUNDLEJSONID__QT" AS "subscription_cpq_bundle_json_id"
        ,"CREATEDBYID" AS "subscription_created_by_id"
        ,"CREATEDDATE" AS "subscription_id_created_timestamp"
        ,"CURRENTTERM" AS "subscription_current_term"
        ,"CURRENTTERMPERIODTYPE" AS "subscription_current_term_period_type"
        ,"INITIALTERM" AS "subscription_initial_term"
        ,"INITIALTERMPERIODTYPE" AS "subscription_initial_term_period_type"
        ,"ISINVOICESEPARATE" AS "subscription_is_invoice_separate"
        ,"NAME" AS "subscription_name"
        ,"NOTES" AS "subscription_notes"
        ,"NUMFREETRIALPERIODS__C" AS "subscription_number_of_free_trial_periods"
        ,"NUMGIFTPERIODS__C" AS "subscription_number_of_giftcode_periods"
        ,"OPPORTUNITYCLOSEDATE__QT" AS "subscription_opportunity_close_date"
        ,"OPPORTUNITYNAME__QT" AS "subscription_opportunity_name"
        ,"ORIGINALCREATEDDATE" AS "subscription_name_original_created_timestamp"
        ,"QUOTEBUSINESSTYPE__QT" AS "subscription_quote_business_type"
        ,"QUOTENUMBER__QT" AS "subscription_quote_number"
        ,"QUOTETYPE__QT" AS "subscription_quote_type"
        ,"RECEIPTID__C" AS "subscription_receipt_id"
        ,"RENEWALSETTING" AS "subscription_renewal_setting"
        ,"RENEWALTERM" AS "subscription_renewal_term"
        ,"RENEWALTERMPERIODTYPE" AS "subscription_renewal_term_period_type"
        ,"SERVICEACTIVATIONDATE" AS "subscription_service_activation_date"
        ,"STATUS" AS "subscription_status"
        ,"SUBSCRIPTIONENDDATE" AS "subscription_end_date"
        ,"SUBSCRIPTIONSTARTDATE" AS "subscription_start_date"
        ,"TERMENDDATE" AS "subscription_term_end_date"
        ,"TERMSTARTDATE" AS "subscription_term_start_date"
        ,"TERMTYPE" AS "subscription_term_type"
        ,"UPDATEDBYID" AS "subscription_updated_by_id"
        ,"UPDATEDDATE" AS "subscription_id_updated_timestamp"
        ,"VERSION" AS "subscription_version"
        ,"ISLATESTVERSION" AS "is_latest_subscription_version"
        ,"FREETRIALPERIODSTYPE__C" AS "subscription_free_trial_periods_type"
        ,IFNULL("PRODUCTGROUP__C", 'DAZN') AS "subscription_product_group"
        ,"PAYMENTMETHODID__C" AS "subscription_payment_method_id"
    FROM source
)

SELECT * FROM renamed
