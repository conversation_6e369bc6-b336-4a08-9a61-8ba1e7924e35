{{
	config(
		materialized = 'view',
		schema = 'STAGING',
		tags = ['presentation-staging']
	)
}}

WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA__PRODUCT_RATE_PLAN_CURRENT') }}
)

,renamed AS (
    SELECT
        "EDM_INSERT_DTTS"
        ,"Id" AS "product_rateplan_id"
        ,"CreatedById" AS "product_rateplan_created_by_id"
        ,"CreatedDate" AS "product_rateplan_created_timestamp"
        ,"Description" AS "product_rateplan_description"
        ,"EffectiveEndDate" AS "product_rateplan_effective_end_date"
        ,"EffectiveStartDate" AS "product_rateplan_effective_start_date"
        ,"Name" AS "product_rateplan_name"
        ,"OfferType__c" AS "product_rateplan_offer_type"
        ,"UpdatedById" AS "product_rateplan_updated_by_id"
        ,"UpdatedDate" AS "product_rateplan_updated_timestamp"
        ,"EntitlementSetId__c" AS "entitlement_set_id"
        ,"PPVType__c" AS "ppv_type"
        ,"BillingType__c" AS "product_rateplan_billing_type"
        ,"ProductType__c" AS "product_rateplan_product_type"
    FROM source
)

SELECT * FROM renamed
