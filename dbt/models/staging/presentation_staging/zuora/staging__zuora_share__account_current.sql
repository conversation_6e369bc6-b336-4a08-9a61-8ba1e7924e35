WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA_SHARE__ACCOUNT_CURRENT') }}
)

, renamed AS (
    SELECT
        "ID" AS "billing_account_id"
        ,"CRMID" AS "crm_account_id"
        ,"PARENTID" AS "billing_account_parent_id"
        ,"SEQUENCESETID" AS "billing_account_sequence_set_id"
        ,"TAXEXEMPTCERTIFICATEID" AS "billing_account_tax_exempt_certificate_id"
        ,"BILLTOID" AS "bill_to_contact_id"
        ,"DEFAULTPAYMENTMETHODID" AS "default_payment_method_id"
        ,"SOLDTOID" AS "sold_to_contact_id"
        ,"DAZNUSERID__C" AS "dazn_user_id"
        ,"ACCOUNTNUMBER" AS "billing_account_number"
        ,"ADDITIONALEMAILADDRESSES" AS "billing_account_additional_email_addresses__hash"
        ,"ALLOWINVOICEEDIT" AS "billing_account_is_allowed_invoice_edit"
        ,"AUTOPAY" AS "billing_account_is_auto_pay"
        ,"BA<PERSON>NCE" AS "billing_account_balance"
        ,"BATCH" AS "billing_account_batch_name"
        ,"BCDSETTINGOPTION" AS "billing_account_cycle_day_setting_option"
        ,"BILLCYCLEDAY" AS "billing_account_bill_cycle_day"
        ,"COMMUNICATIONPROFILEID" AS "billing_account_communication_profile_id"
        ,"COMPANY__C" AS "billing_account_company_name"
        ,"CREATEDBYID" AS "billing_account_created_by_id"
        ,"CREATEDDATE" AS "billing_account_created_timestamp"
        ,"CREDITBALANCE" AS "billing_account_credit_balance"
        ,"CURRENCY" AS "billing_account_currency_code"
        ,"CUSTOMERSERVICEREPNAME" AS "billing_account_customer_service_rep_name"
        ,"INVOICEDELIVERYPREFSEMAIL" AS "billing_account_has_invoice_delivery_preference_email"
        ,"INVOICEDELIVERYPREFSPRINT" AS "billing_account_has_invoice_delivery_preference_print"
        ,"INVOICETEMPLATEID" AS "billing_account_invoice_template_id"
        ,"LASTINVOICEDATE" AS "billing_account_last_invoice_date"
        ,"MRR" AS "billing_account_monthly_recurring_revenue"
        ,"NAME" AS "billing_account_customer_full_name__hash"
        ,"NOTES" AS "billing_account_notes"
        ,"PAYMENTGATEWAY" AS "billing_account_payment_gateway"
        ,"PAYMENTTERM" AS "billing_account_payment_term"
        ,"PURCHASEORDERNUMBER" AS "billing_account_purchase_order_number"
        ,"SALESREPNAME" AS "billing_account_sales_rep_name"
        ,"STATUS" AS "billing_account_status"
        ,"TAXCOMPANYCODE" AS "billing_account_tax_company_code"
        ,"TAXEXEMPTCERTIFICATETYPE" AS "billing_account_tax_exempt_certificate_type"
        ,"TAXEXEMPTDESCRIPTION" AS "billing_account_tax_exempt_description"
        ,"TAXEXEMPTEFFECTIVEDATE" AS "billing_account_tax_exempt_effective_date"
        ,"TAXEXEMPTENTITYUSECODE" AS "billing_account_tax_exempt_entity_use_code"
        ,"TAXEXEMPTEXPIRATIONDATE" AS "billing_account_tax_exempt_expiration_date"
        ,"TAXEXEMPTISSUINGJURISDICTION" AS "billing_account_tax_exempt_issuing_jurisdiction"
        ,"TAXEXEMPTSTATUS" AS "billing_account_tax_exempt_status"
        ,"TOTALDEBITMEMOBALANCE" AS "billing_account_total_debit_memo_balance"
        ,"TOTALINVOICEBALANCE" AS "billing_account_total_invoice_balance"
        ,"UNAPPLIEDBALANCE" AS "billing_account_unapplied_balance"
        ,"UNAPPLIEDCREDITMEMOAMOUNT" AS "billing_account_unapplied_credit_memo_amount"
        ,"UPDATEDBYID" AS "billing_account_updated_by_id"
        ,"UPDATEDDATE" AS "billing_account_last_updated_timestamp"
        ,"VATID" AS "billing_account_vat_id"
        ,IFNULL("APM__C", FALSE) AS "billing_account_has_advanced_payment_manager"
    FROM source
)

SELECT * FROM renamed
