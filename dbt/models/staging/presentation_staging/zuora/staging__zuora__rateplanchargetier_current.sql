WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA__RATE_PLAN_CHARGE_TIER_CURRENT') }}
)

, renamed AS (
    SELECT
        "Id" AS "rateplan_charge_tier_id"
        ,"RatePlanChargeId" AS "rateplan_charge_id"
        ,"CreatedById" AS "rateplan_charge_tier_created_by_id"
        ,"CreatedDate" AS "rateplan_charge_tier_created_date"
        ,"Currency" AS "currency_code"
        ,"DiscountAmount" AS "rateplan_charge_tier_discount_amount"
        ,"DiscountPercentage" AS "rateplan_charge_tier_discount_percentage"
        ,"EndingUnit" AS "rateplan_charge_tier_rateplan_charge_tier_ending_unit"
        ,"IncludedUnits" AS "rateplan_charge_tier_included_units"
        ,"OveragePrice" AS "rateplan_charge_tier_overage_price"
        ,"Price" AS "rateplan_charge_tier_price"
        ,"PriceFormat" AS "rateplan_charge_tier_price_format"
        ,"StartingUnit" AS "rateplan_charge_tier_starting_unit"
        ,"Tier" AS "rateplan_charge_tier_tier"
        ,"UpdatedById" AS "rateplan_charge_tier_updated_by_id"
        ,"UpdatedDate" AS "rateplan_charge_tier_updated_date"
    FROM source
)

SELECT * FROM renamed
