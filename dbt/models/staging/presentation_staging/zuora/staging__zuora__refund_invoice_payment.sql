{{
	config(
		materialized = 'view',
		schema = 'STAGING',
		tags = ['presentation-staging']
	)
}}

WITH source AS (
	SELECT * FROM {{ source('CURATED', 'CURATED__ZUORA__REFUND_INVOICE_PAYMENT') }}
)

,renamed AS(
	SELECT
		"CreatedById" AS "refund_invoice_payment_created_by_id"
		,"CreatedDate" AS "refund_invoice_payment_created_date"
		,"Id" AS "refund_invoice_payment_id"
		,"RefundAmount" AS "refund_invoice_payment_refund_amount"
		,"UpdatedById" AS "refund_invoice_payment_updated_by_id"
		,"UpdatedDate" AS "refund_invoice_payment_updated_date"
		,"InvoiceId" AS "refund_invoice_payment_invoice_id"
		,"InvoicePaymentId" AS "refund_invoice_payment_invoice_payment_id"
		,"RefundId" AS "refund_invoice_payment_refund_id"
	FROM source
)

select * FROM renamed

