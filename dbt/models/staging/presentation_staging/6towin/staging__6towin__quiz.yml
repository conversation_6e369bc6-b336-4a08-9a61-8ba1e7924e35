version: 2

models:
  - name: staging__6towin__quiz_user_answers
    columns:
      - name: dazn_user_id
        description: "This uniquely identifies each user within the DAZN platform."
        quote: true

      - name: quiz_id
        description: "This uniquely identifies each quiz in the system."
        quote: true

      - name: question1_id
        description: "Identifies the question associated with the first question slot."
        quote: true

      - name: user_answer1
        description: "Stores the user's response for the first question."
        quote: true

      - name: question2_id
        description: "Identifies the question associated with the second question slot."
        quote: true

      - name: user_answer2
        description: "Stores the user's response for the second question."
        quote: true

      - name: question3_id
        description: "Identifies the question associated with the third question slot."
        quote: true

      - name: user_answer3
        description: "Stores the user's response for the third question."
        quote: true

      - name: question4_id
        description: "Identifies the question associated with the fourth question slot."
        quote: true

      - name: user_answer4
        description: "Stores the user's response for the fourth question."
        quote: true

      - name: question5_id
        description: "Identifies the question associated with the fifth question slot."
        quote: true

      - name: user_answer5
        description: "Stores the user's response for the fifth question."
        quote: true

      - name: question6_id
        description: "Identifies the question associated with the sixth question slot."
        quote: true

      - name: user_answer6
        description: "Stores the user's response for the sixth question."
        quote: true

      - name: country
        description: "Represents the country associated with the user's profile."
        quote: true

  - name: staging__6towin__quiz_guest_user_answers
    columns:
      - name: guest_user_id
        description: "This uniquely identifies each guest user participating in the quiz."
        quote: true

      - name: quiz_id
        description: "This uniquely identifies each quiz in the system."
        quote: true

      - name: question1_id
        description: "Identifies the question associated with the first question slot."
        quote: true

      - name: user_answer1
        description: "Stores the guest user's response for the first question."
        quote: true

      - name: question2_id
        description: "Identifies the question associated with the second question slot."
        quote: true

      - name: user_answer2
        description: "Stores the guest user's response for the second question."
        quote: true

      - name: question3_id
        description: "Identifies the question associated with the third question slot."
        quote: true

      - name: user_answer3
        description: "Stores the guest user's response for the third question."
        quote: true

      - name: question4_id
        description: "Identifies the question associated with the fourth question slot."
        quote: true

      - name: user_answer4
        description: "Stores the guest user's response for the fourth question."
        quote: true

      - name: question5_id
        description: "Identifies the question associated with the fifth question slot."
        quote: true

      - name: user_answer5
        description: "Stores the guest user's response for the fifth question."
        quote: true

      - name: question6_id
        description: "Identifies the question associated with the sixth question slot."
        quote: true

      - name: user_answer6
        description: "Stores the guest user's response for the sixth question."
        quote: true

      - name: country
        description: "Represents the country associated with the guest user."
        quote: true

  - name: staging__6towin__quiz_details
    columns:
      - name: quiz_id
        description: "This uniquely identifies each quiz in the system."
        quote: true

      - name: quiz_name
        description: "The name/title of the quiz."
        quote: true

      - name: quiz_description
        description: "A brief description or overview of the quiz."
        quote: true

      - name: quiz_start_date
        description: "The date and time when the quiz begins."
        quote: true

      - name: quiz_end_date
        description: "The date and time when the quiz ends."
        quote: true

      - name: country
        description: "Represents the country associated with the quiz."
        quote: true

  - name: staging__6towin__quiz_match_result
    columns:
      - name: quiz_id
        description: "Identifies the quiz to which the match result belongs."
        quote: true

      - name: question1_id
        description: "Identifies the question associated with the first question slot in the quiz."
        quote: true

      - name: question1_answer
        description: "Stores the answer provided for the first question in the match result."
        quote: true

      - name: q1_answer_timestamp
        description: "Records the timestamp when the answer for the first question was submitted."
        quote: true

      - name: question2_id
        description: "Identifies the question associated with the second question slot in the quiz."
        quote: true

      - name: question2_answer
        description: "Stores the answer provided for the second question in the match result."
        quote: true

      - name: q2_answer_timestamp
        description: "Records the timestamp when the answer for the second question was submitted."
        quote: true

      - name: question3_id
        description: "Identifies the question associated with the third question slot in the quiz."
        quote: true

      - name: question3_answer
        description: "Stores the answer provided for the third question in the match result."
        quote: true

      - name: q3_answer_timestamp
        description: "Records the timestamp when the answer for the third question was submitted."
        quote: true

      - name: question4_id
        description: "Identifies the question associated with the fourth question slot in the quiz."
        quote: true

      - name: question4_answer
        description: "Stores the answer provided for the fourth question in the match result."
        quote: true

      - name: q4_answer_timestamp
        description: "Records the timestamp when the answer for the fourth question was submitted."
        quote: true

      - name: question5_id


        description: "Identifies the question associated with the fifth question slot in the quiz."
        quote: true

      - name: question5_answer
        description: "Stores the answer provided for the fifth question in the match result."
        quote: true

      - name: q5_answer_timestamp
        description: "Records the timestamp when the answer for the fifth question was submitted."
        quote: true

      - name: question6_id
        description: "Identifies the question associated with the sixth question slot in the quiz."
        quote: true

      - name: question6_answer
        description: "Stores the answer provided for the sixth question in the match result."
        quote: true

      - name: q6_answer_timestamp
        description: "Records the timestamp when the answer for the sixth question was submitted."
        quote: true

      - name: country
        description: "Represents the country associated with the match result."
        quote: true
