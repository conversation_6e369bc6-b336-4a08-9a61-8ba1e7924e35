version: 2

sources:
  - name: CURATED_6TOWIN
    database: TRANSFORMATION_PROD
    schema: CURATED
    quoting:
      identifier: true
    tables:
      - name: CURATED__6TOWIN__QUIZ_USER_ANSWERS
        columns:
          - name: DAZN_USER_ID
            description: "This uniquely identifies each user within the DAZN platform."
            quote: true

          - name: QUIZ_ID
            description: "This uniquely identifies each quiz in the system."
            quote: true

          - name: QUESTION1_ID
            description: "Identifies the question associated with the first question slot."
            quote: true

          - name: USER_ANSWER1
            description: "Stores the user's response for the first question."
            quote: true

          - name: QUESTION2_ID
            description: "Identifies the question associated with the second question slot."
            quote: true

          - name: USER_ANSWER2
            description: "Stores the user's response for the second question."
            quote: true

          - name: QUESTION3_ID
            description: "Identifies the question associated with the third question slot."
            quote: true

          - name: USER_ANSWER3
            description: "Stores the user's response for the third question."
            quote: true

          - name: QUESTION4_ID
            description: "Identifies the question associated with the fourth question slot."
            quote: true

          - name: USER_ANSWER4
            description: "Stores the user's response for the fourth question."
            quote: true

          - name: QUESTION5_ID
            description: "Identifies the question associated with the fifth question slot."
            quote: true

          - name: USER_ANSWER5
            description: "Stores the user's response for the fifth question."
            quote: true

          - name: QUESTION6_ID
            description: "Identifies the question associated with the sixth question slot."
            quote: true

          - name: USER_ANSWER6
            description: "Stores the user's response for the sixth question."
            quote: true

          - name: COUNTRY
            description: "Represents the country associated with the user's profile."
            quote: true

      - name: CURATED__6TOWIN__QUIZ_GUEST_USER_ANSWERS
        columns:
          - name: GUEST_USER_ID
            description: "This uniquely identifies each guest user participating in the quiz."
            quote: true

          - name: QUIZ_ID
            description: "This uniquely identifies each quiz in the system."
            quote: true

          - name: QUESTION1_ID
            description: "Identifies the question associated with the first question slot."
            quote: true

          - name: USER_ANSWER1
            description: "Stores the guest user's response for the first question."
            quote: true

          - name: QUESTION2_ID
            description: "Identifies the question associated with the second question slot."
            quote: true

          - name: USER_ANSWER2
            description: "Stores the guest user's response for the second question."
            quote: true

          - name: QUESTION3_ID
            description: "Identifies the question associated with the third question slot."
            quote: true

          - name: USER_ANSWER3
            description: "Stores the guest user's response for the third question."
            quote: true

          - name: QUESTION4_ID
            description: "Identifies the question associated with the fourth question slot."
            quote: true

          - name: USER_ANSWER4
            description: "Stores the guest user's response for the fourth question."
            quote: true

          - name: QUESTION5_ID
            description: "Identifies the question associated with the fifth question slot."
            quote: true

          - name: USER_ANSWER5
            description: "Stores the guest user's response for the fifth question."
            quote: true

          - name: QUESTION6_ID
            description: "Identifies the question associated with the sixth question slot."
            quote: true

          - name: USER_ANSWER6
            description: "Stores the guest user's response for the sixth question."
            quote: true

          - name: COUNTRY
            description: "Represents the country associated with the guest user."
            quote: true

      - name: CURATED__6TOWIN__QUIZ_DETAILS
        columns:
          - name: QUIZ_ID
            description: "This uniquely identifies each quiz in the system."
            quote: true

          - name: QUIZ_NAME
            description: "The name/title of the quiz."
            quote: true

          - name: QUIZ_DESCRIPTION
            description: "A brief description or overview of the quiz."
            quote: true

          - name: QUIZ_START_DATE
            description: "The date and time when the quiz begins."
            quote: true

          - name: QUIZ_END_DATE
            description: "The date and time when the quiz ends."
            quote: true

          - name: COUNTRY
            description: "Represents the country associated with the quiz."
            quote: true

      - name: CURATED__6TOWIN__QUIZ_MATCH_RESULT
        columns:
          - name: QUIZ_ID
            description: "Identifies the quiz to which the match result belongs."
            quote: true

          - name: QUESTION1_ID
            description: "Identifies the question associated with the first question slot in the quiz."
            quote: true

          - name: QUESTION1_ANSWER
            description: "Stores the answer provided for the first question in the match result."
            quote: true

          - name: Q1_ANSWER_TIMESTAMP
            description: "Records the timestamp when the answer for the first question was submitted."
            quote: true

          - name: QUESTION2_ID
            description: "Identifies the question associated with the second question slot in the quiz."
            quote: true

          - name: QUESTION2_ANSWER
            description: "Stores the answer provided for the second question in the match result."
            quote: true

          - name: Q2_ANSWER_TIMESTAMP
            description: "Records the timestamp when the answer for the second question was submitted."
            quote: true

          - name: QUESTION3_ID
            description: "Identifies the question associated with the third question slot in the quiz."
            quote: true

          - name: QUESTION3_ANSWER
            description: "Stores the answer provided for the third question in the match result."
            quote: true

          - name: Q3_ANSWER_TIMESTAMP
            description: "Records the timestamp when the answer for the third question was submitted."
            quote: true

          - name: QUESTION4_ID
            description: "Identifies the question associated with the fourth question slot in the quiz."
            quote: true

          - name: QUESTION4_ANSWER
            description: "Stores the answer provided for the fourth question in the match result."
            quote: true

          - name: Q4_ANSWER_TIMESTAMP
            description: "Records the timestamp when the answer for the fourth question was submitted."
            quote: true

          - name: QUESTION5_ID
            description: "Identifies the question associated with the fifth question slot in the quiz."
            quote: true

          - name: QUESTION5_ANSWER
            description: "Stores the answer provided for the fifth question in the match result."
            quote: true

          - name: Q5_ANSWER_TIMESTAMP
            description: "Records the timestamp when the answer for the fifth question was submitted."
            quote: true

          - name: QUESTION6_ID
            description: "Identifies the question associated with the sixth question slot in the quiz."
            quote: true

          - name: QUESTION6_ANSWER
            description: "Stores the answer provided for the sixth question in the match result."
            quote: true

          - name: Q6_ANSWER_TIMESTAMP
            description: "Records the timestamp when the answer for the sixth question was submitted."
            quote: true

          - name: COUNTRY
            description: "Represents the country associated with the match result."
            quote: true
