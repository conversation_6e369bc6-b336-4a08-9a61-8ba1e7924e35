{{
    config(
        materialized='table',
        schema='PRESENTATION',
        tags=['presentation-fanatics']
    )
}}

WITH orderheader AS (
    SELECT * FROM {{ ref('staging__fanatics__order_headers_current') }}
)

,final AS (
    SELECT
        CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
        ,"order_id"
        ,"dazn_user_id"
        ,"order_date"
        ,"despatched_date"
        ,"sales_channel"
        ,"goods_net_gbp"
        ,"goods_vat_gbp"
        ,"shipping_net_gbp"
        ,"shipping_vat_gbp"
        ,"currency"
        ,"shipping_country"
        ,"conversion_rate"
        ,"payment_type"
        ,"promo_code"
        ,"transaction_type"
        ,"reason"
    FROM orderheader
)

SELECT * FROM final