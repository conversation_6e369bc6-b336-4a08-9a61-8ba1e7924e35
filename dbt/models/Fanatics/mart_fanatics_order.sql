{{
    config(
        materialized='view',
        database='B2C__MART__' + snowflake_env(),
        schema='INTERMEDIATE',
        alias='fanatics_order',
        tags=['presentation-fanatics']
    )
}}

WITH source AS (
    SELECT * FROM {{ ref('fanatics_order') }}
)

,renamed AS (
    SELECT
        CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
        ,"order_id"
        ,"dazn_user_id" AS "customer_id"
        ,"order_date"
        ,"despatched_date"
        ,"sales_channel"
        ,"goods_net_gbp"
        ,"goods_vat_gbp"
        ,"shipping_net_gbp"
        ,"shipping_vat_gbp"
        ,"currency"
        ,"shipping_country"
        ,"conversion_rate"
        ,"payment_type"
        ,"promo_code"
        ,"transaction_type"
        ,"reason"
    FROM source
)

SELECT * FROM renamed
