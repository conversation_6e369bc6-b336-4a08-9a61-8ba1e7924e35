{{
    config(
        materialized='table',
        schema='PRESENTATION',
        tags=['presentation-fanatics']
    )
}}

WITH customers AS (
    SELECT * FROM {{ ref('staging__fanatics__customers_current') }}
)

,final AS (
    SELECT
        CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
        ,"customer_id"
        ,"country"
        ,"language"
        ,"currency"
        ,"registration_timestamp"
        ,"club_username"
        ,"archived"
        ,"newsletter_account"
        ,"is_guest"
        ,"retail_phone"
        ,"retail_email"
        ,"retail_sms"
        ,"retail_post"
        ,"3rd_party_phone"
        ,"3rd_party_email"
        ,"3rd_party_sms"
        ,"3rd_party_post"
        ,"club_phone"
        ,"club_email"
        ,"club_sms"
        ,"club_post"
        ,"change_date"
    FROM customers
)

SELECT * FROM final