{{
    config(
        materialized='table',
        schema='PRESENTATION',
        tags=['presentation-fanatics']
    )
}}

WITH productfeed AS (
    SELECT * from {{ ref('staging__fanatics__product_feed_current') }}
)

,final AS (
    SELECT
        CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
        ,"product_id"
        ,"item_id"
        ,"product_name"
        ,"product_size"
        ,"product_brand"
        ,"merchclass_level_1"
        ,"merchclass_level_2"
        ,"merchclass_level_3"
        ,"merchclass_level_4"
        ,"merchclass_level_5"
        ,"merchclass_level_6"
        ,"color_style"
        ,"team"
        ,"team_list"
        ,"color"
        ,"gender_age_group"
        ,"vendor"
        ,"product_url"
        ,"retail_price"
        ,"sale_price"
        ,"player_name"
        ,"vendor_product_code"
        ,"vendor_item_code"
        ,"upc"
        ,"gender"
        ,"qty_on_hand"
    FROM productfeed
)

SELECT * FROM final