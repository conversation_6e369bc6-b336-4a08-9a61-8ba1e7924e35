{{
    config(
        materialized='table',
        transient=false,
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_L_WH',
        tags=['presentation-customer-identity']
    )
}}

-- Getting the sources
WITH zuora_account AS (
    SELECT
        "billing_account_id"
        ,"crm_account_id"
        ,"dazn_user_id"
        ,"billing_account_created_timestamp"
    FROM {{ ref_env('staging__zuora__account_current') }}
)

{# , salesforce_account AS (
    SELECT
        "viewer_id"
        ,"dazn_user_id"
        ,"crm_account_id"
        ,"partner_id"
        ,"crm_account_created_timestamp"
        ,"crm_last_modified_timestamp"
        ,"crm_account_status"
    FROM {{ ref_env('staging__salesforce__account_current') }}
)

-- separating the null dazn_user_id before the de-duplication step
, salesforce_account_user_id_null AS (
    SELECT
        "viewer_id"
        ,"dazn_user_id"
        ,"crm_account_id"
        ,"partner_id"
        ,"crm_account_created_timestamp"
        ,"crm_last_modified_timestamp"
        ,"crm_account_status"
    FROM salesforce_account
    WHERE "dazn_user_id" IS NULL
)

-- de-duplicating on viewer_id in sf to remove partial account_status duplicates
, salesforce_account_user_id_dedup AS (
    SELECT
        "viewer_id"
        ,"dazn_user_id"
        ,"crm_account_id"
        ,"partner_id"
        ,"crm_account_created_timestamp"
        ,"crm_last_modified_timestamp"
        ,"crm_account_status"
    FROM salesforce_account
    WHERE "dazn_user_id" IS NOT NULL
    QUALIFY ROW_NUMBER() OVER(PARTITION BY "dazn_user_id" ORDER BY "crm_last_modified_timestamp" DESC) = 1
)

, salesforce_account_combined AS (
    SELECT * FROM salesforce_account_user_id_null
    UNION
    SELECT * FROM salesforce_account_user_id_dedup
)

-- Joining zuora and sf using the "crm_account_id"
, crm_id_join AS (
    SELECT
        zuora_account."billing_account_id"
        ,salesforce_account_combined."crm_account_id"
        ,COALESCE(salesforce_account_combined."dazn_user_id", zuora_account."dazn_user_id") AS "dazn_user_id"
        ,salesforce_account_combined."viewer_id"
        ,salesforce_account_combined."partner_id"
        ,zuora_account."billing_account_created_timestamp"
        ,salesforce_account_combined."crm_account_created_timestamp"
        ,salesforce_account_combined."crm_account_status"
    FROM salesforce_account_combined
    INNER JOIN zuora_account
        ON salesforce_account_combined."crm_account_id" = zuora_account."crm_account_id"

)

-- Joining zuora and sf using the "dazn_user_id", removing the zuora records that were matched from the above query
, dazn_user_id_join AS (
    SELECT
        zuora_account."billing_account_id"
        ,salesforce_account_combined."crm_account_id"
        ,zuora_account."dazn_user_id"
        ,salesforce_account_combined."viewer_id"
        ,salesforce_account_combined."partner_id"
        ,zuora_account."billing_account_created_timestamp"
        ,salesforce_account_combined."crm_account_created_timestamp"
        ,salesforce_account_combined."crm_account_status"
    FROM zuora_account
    INNER JOIN salesforce_account_combined
        ON salesforce_account_combined."dazn_user_id" = zuora_account."dazn_user_id"
    LEFT JOIN crm_id_join
        ON crm_id_join."billing_account_id" = zuora_account."billing_account_id"
    WHERE crm_id_join."billing_account_id" IS NULL
)

-- Getting the remaining zuora records that we couldn't join using any columns above
, zuora_unjoinable AS (
    SELECT
        zuora_account."billing_account_id"
        ,zuora_account."crm_account_id"
        ,zuora_account."dazn_user_id"
        ,NULL AS "viewer_id"
        ,NULL AS "partner_id"
        ,zuora_account."billing_account_created_timestamp"
        ,NULL AS "crm_account_created_timestamp"
        ,NULL AS "crm_account_status"
    FROM zuora_account
    LEFT JOIN crm_id_join
        ON crm_id_join."billing_account_id" = zuora_account."billing_account_id"
    LEFT JOIN dazn_user_id_join
        ON dazn_user_id_join."billing_account_id" = zuora_account."billing_account_id"
    WHERE crm_id_join."billing_account_id" IS NULL
        AND dazn_user_id_join."billing_account_id" IS NULL
)

-- Getting the remaining sf records that we couldn't join using any columns above
, sf_unjoinable AS (
    SELECT
        NULL AS "billing_account_id"
        ,salesforce_account_combined."crm_account_id"
        ,salesforce_account_combined."dazn_user_id"
        ,salesforce_account_combined."viewer_id"
        ,salesforce_account_combined."partner_id"
        ,NULL AS "billing_account_created_timestamp"
        ,salesforce_account_combined."crm_account_created_timestamp"
        ,salesforce_account_combined."crm_account_status"
    FROM salesforce_account_combined
    LEFT JOIN crm_id_join
        ON crm_id_join."crm_account_id" = salesforce_account_combined."crm_account_id"
    LEFT JOIN dazn_user_id_join
        ON dazn_user_id_join."crm_account_id" = salesforce_account_combined."crm_account_id"
    WHERE crm_id_join."crm_account_id" IS NULL
        AND dazn_user_id_join."crm_account_id" IS NULL

)

, final_dim AS (
    SELECT * FROM crm_id_join
    UNION
    SELECT * FROM dazn_user_id_join
    UNION
    SELECT * FROM zuora_unjoinable
    UNION
    SELECT * FROM sf_unjoinable
) #}

, user_event_source AS (
    SELECT * FROM {{ ref_env('staging__segment__user_events_identifies_current') }}
)

,mapping_source AS (
    SELECT * FROM {{ ref_env('staging__identity__mapping')}}
)

,mapping_viewer_id AS (
    SELECT * FROM {{ ref_env('staging__segment__user_events_users') }}
)

{# , account_create AS
(
    SELECT "dazn_user_id", MIN("timestamp") AS "crm_account_created_timestamp" FROM user_event_source
    GROUP BY 1
) #}

-- Added this below block Since User Account Created getting loaded as part of Subscription Tag
,user_events_scd AS (
    SELECT * FROM  {{ ref_env('staging__segment__user_events_identifies_scd') }}
)

,salesforce_created_patch AS (
    SELECT
        "dazn_user_id"
        ,"crm_account_created_timestamp"
    FROM {{ ref_env('staging__salesforce__account_current') }}
)

,account_create AS (
    --SELECT * FROM {{ ref_env('user_account_created')}}
    SELECT
    user_events_scd."dazn_user_id"
    ,CASE
        WHEN MIN(salesforce_created_patch."crm_account_created_timestamp") < MIN(user_events_scd."timestamp") THEN MIN(salesforce_created_patch."crm_account_created_timestamp")
        ELSE MIN(user_events_scd."timestamp")
    END AS "user_account_created_timestamp"
    FROM user_events_scd
    LEFT JOIN salesforce_created_patch USING ("dazn_user_id")
    GROUP BY 1
)

, user_event_account AS (
    SELECT
        --user_event_source."viewer_id"
         COALESCE(user_event_source."viewer_id",mapping_viewer_id."viewer_id") AS "viewer_id"
        ,user_event_source."dazn_user_id"
        ,COALESCE(mapping_source."crm_account_id", user_event_source."salesforce_id", user_event_source."provider_customer_id") AS "crm_account_id"
        ,COALESCE(user_event_source."partner_id",mapping_viewer_id."partner_id") AS "partner_id"
        ,account_create."user_account_created_timestamp" AS "crm_account_created_timestamp"
        ,user_event_source."timestamp" AS "crm_last_modified_timestamp"
        ,COALESCE(user_event_source."subscription_status", mapping_viewer_id."subscription_status") AS "crm_account_status"
    FROM user_event_source
    LEFT JOIN mapping_source USING ("dazn_user_id")
    LEFT JOIN account_create USING ("dazn_user_id")
    LEFT JOIN mapping_viewer_id USING ("dazn_user_id")
    QUALIFY ROW_NUMBER() OVER(PARTITION BY "dazn_user_id" ORDER BY "crm_last_modified_timestamp" DESC) = 1
)


-- Joining zuora and identifiers using the "crm_account_id"
, crm_id_join AS (
    SELECT
        zuora_account."billing_account_id"
        ,user_event_account."crm_account_id"
        ,COALESCE(user_event_account."dazn_user_id", zuora_account."dazn_user_id") AS "dazn_user_id"
        ,user_event_account."viewer_id"
        ,user_event_account."partner_id"
        ,zuora_account."billing_account_created_timestamp"
        ,user_event_account."crm_account_created_timestamp"
        ,user_event_account."crm_account_status"
    FROM user_event_account
    INNER JOIN zuora_account
        ON user_event_account."crm_account_id" = zuora_account."crm_account_id"

)

-- Joining zuora and identifiers using the "dazn_user_id", removing the zuora records that were matched from the above query
, dazn_user_id_join AS (
    SELECT
        zuora_account."billing_account_id"
        ,user_event_account."crm_account_id"
        ,zuora_account."dazn_user_id"
        ,user_event_account."viewer_id"
        ,user_event_account."partner_id"
        ,zuora_account."billing_account_created_timestamp"
        ,user_event_account."crm_account_created_timestamp"
        ,user_event_account."crm_account_status"
    FROM zuora_account
    INNER JOIN user_event_account
        ON user_event_account."dazn_user_id" = zuora_account."dazn_user_id"
    LEFT JOIN crm_id_join
        ON crm_id_join."billing_account_id" = zuora_account."billing_account_id"
    WHERE crm_id_join."billing_account_id" IS NULL
)

-- Getting the remaining zuora records that we couldn't join using any columns above
, zuora_unjoinable AS (
    SELECT
        zuora_account."billing_account_id"
        ,zuora_account."crm_account_id"
        ,zuora_account."dazn_user_id"
        ,NULL AS "viewer_id"
        ,NULL AS "partner_id"
        ,zuora_account."billing_account_created_timestamp"
        ,NULL AS "crm_account_created_timestamp"
        ,NULL AS "crm_account_status"
    FROM zuora_account
    LEFT JOIN crm_id_join
        ON crm_id_join."billing_account_id" = zuora_account."billing_account_id"
    LEFT JOIN dazn_user_id_join
        ON dazn_user_id_join."billing_account_id" = zuora_account."billing_account_id"
    WHERE crm_id_join."billing_account_id" IS NULL
        AND dazn_user_id_join."billing_account_id" IS NULL
)

-- Getting the remaining identifiers records that we couldn't join using any columns above
, event_unjoinable AS (
    SELECT
        user_event_account."dazn_user_id" AS "billing_account_id"
        ,user_event_account."crm_account_id"
        ,user_event_account."dazn_user_id"
        ,user_event_account."viewer_id"
        ,user_event_account."partner_id"
        ,NULL AS "billing_account_created_timestamp"
        ,user_event_account."crm_account_created_timestamp"
        ,user_event_account."crm_account_status"
    FROM user_event_account
    LEFT JOIN crm_id_join
        ON crm_id_join."crm_account_id" = user_event_account."crm_account_id"
    LEFT JOIN dazn_user_id_join
        ON dazn_user_id_join."crm_account_id" = user_event_account."crm_account_id"
    WHERE crm_id_join."crm_account_id" IS NULL
        AND dazn_user_id_join."crm_account_id" IS NULL

)

, final_dim AS (
    SELECT * FROM crm_id_join
    UNION
    SELECT * FROM dazn_user_id_join
    UNION
    SELECT * FROM zuora_unjoinable
    UNION
    SELECT * FROM event_unjoinable
)

SELECT
    *
    ,COALESCE("billing_account_created_timestamp", "crm_account_created_timestamp") AS "effective_from"
    --,LEAD("effective_from", 1, '9999-12-31') OVER (PARTITION BY "crm_account_id" ORDER BY "effective_from") AS "effective_until"
    ,LEAD("effective_from", 1, '9999-12-31') OVER (PARTITION BY "dazn_user_id" ORDER BY "effective_from") AS "effective_until"
FROM final_dim
