version: 2

models:
  - name: customer_identity_dim
    description: "This is a dimension table that contains info about different customer IDs from Zuora and Salesforce datasource"
    columns:
      - name: billing_account_id
        description: "Zuora Billing Account ID"
        quote: true

      - name: crm_account_id
        description: "Unique ID of the record (also known as Salesforce Account ID)"
        quote: true

      - name: dazn_user_id
        description: "Unique ID of the DAZN User"
        quote: true

      - name: viewer_id
        description: "Viewer ID value copied from User table (combines EXT and non EXT values, whichever is actually used)"
        quote: true

      - name: partner_id
        description: "This is the partner ID. Used for Docomo"
        quote: true

      - name: billing_account_created_timestamp
        description: "Timestamp the billing account was created in the billing system, <PERSON>uo<PERSON>"
        quote: true

      - name: crm_account_created_timestamp
        description: "Timestamp the account was created in SF (no logic ot counter gut subscribe)"
        quote: true

      - name: crm_account_status
        description: "Account status also known as user status (e.g. Free Trial, Active Paid)"
        quote: true

      - name: effective_from
        description: "Zuora account entry created date"
        quote: true

      - name: effective_until
        description: "Zuora account entry valid until date"
        quote: true
