{{
    config(
        materialized='table',
        transient=false,
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['presentation-customer-identity'],
        post_hook=[ "{{ grant_select_access('customer_identity_dim_current' ,'PRESENTATION_CUSTOMER_IDENTITY_DIM_CURRENT_TABLE_RO') }}" ]
    )
}}

WITH customer_identity_dim AS (
    SELECT * FROM {{ ref('customer_identity_dim') }}
)


SELECT
    "billing_account_id"
    ,"crm_account_id"
    ,"dazn_user_id"
    ,"viewer_id"
    ,"partner_id"
    ,"effective_from"
FROM customer_identity_dim
WHERE "effective_until" > CURRENT_DATE
QUALIFY ROW_NUMBER() OVER(PARTITION BY COALESCE("billing_account_id","dazn_user_id") ORDER BY "effective_until" DESC) = 1
