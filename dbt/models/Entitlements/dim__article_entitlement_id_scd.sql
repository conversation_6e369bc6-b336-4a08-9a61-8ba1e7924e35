{{
    config(
        materialized='table',
        transient=false,
        schema='PRESENTATION',
        tags=['presentation-article-entitlement-id-scd']
    )
}}

WITH source AS (
    SELECT * FROM {{ ref('staging__dots__gmr_articles') }}
    WHERE "gmr_articles_entitlement_ids" IS NOT NULL
)

--Cleans up the entitlement ids and removes adjacent rows where the entitlement id does not change, remove instances where entitlements are only ever null
,clean_table AS (
    SELECT
        "gmr_articles_article_id" AS "article_id"
        ,"gmr_articles_entitlement_ids" AS "entitlement_ids_temp"
        ,CASE
            WHEN "entitlement_ids_temp" IS NULL THEN NULL
            WHEN "entitlement_ids_temp" = '[]' THEN NULL
            ELSE "entitlement_ids_temp"
        END AS "entitlement_ids"
        ,"record_last_updated_timestamp" AS "effective_from"
        ,COALESCE("entitlement_ids", 'NULL') AS "non_null_entitlement_ids"
        ,"entitlement_ids" IS NULL AND COUNT(DISTINCT "non_null_entitlement_ids") OVER(PARTITION BY "article_id") = 1 AS "has_only_null_entitlements"
    FROM source
    WHERE "is_test" = 'FALSE'
    QUALIFY (LAG("non_null_entitlement_ids") OVER(PARTITION BY "article_id" ORDER BY "effective_from" ASC, "gmr_articles_article_updated_timestamp" DESC) != "non_null_entitlement_ids"
        OR LAG("non_null_entitlement_ids") OVER(PARTITION BY "article_id" ORDER BY "effective_from" ASC, "gmr_articles_article_updated_timestamp" DESC) IS NULL)
        AND NOT "has_only_null_entitlements"
)

SELECT
    "article_id"
    ,"entitlement_ids"
    ,"effective_from"
    ,COALESCE(LEAD("effective_from") OVER (PARTITION BY "article_id" ORDER BY "effective_from"),'9999-12-31') AS "effective_until"
    ,HASH(
        "article_id"
        ,"effective_from"
    ) AS "article_entitlement_skey"
FROM clean_table
QUALIFY ROW_NUMBER() OVER(PARTITION BY "article_entitlement_skey" ORDER BY "entitlement_ids") = 1
