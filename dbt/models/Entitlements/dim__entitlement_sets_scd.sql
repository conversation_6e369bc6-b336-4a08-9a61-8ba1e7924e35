{{
    config(
        materialized='incremental',
        incremental_strategy='merge',
        unique_key='"entitlement_sets_scd_primary_key"',
        schema='PRESENTATION',
        tags=['presentation-entitlements'],
        merge_update_columns=['"effective_until"']
    )
}}

WITH source AS (
    SELECT * FROM {{ ref('staging__entitlements__entitlement_sets') }}
)

-- The Entitlements sets service sends an update for all sets any time one of the sets change, therefore we need to dedup on the content of the messages.

-- First get all new messages since the last update to the scd, if there has been one
,openlayer AS (
    SELECT
        source."entitlement_sets_scd_primary_key"
        ,source."entitlement_set_id"
        ,source."updated_timestamp" AS "effective_from"
        ,source."message_id"
        ,source."message_timestamp"
        ,source."entitlement_ids"
        ,source."event_start_timestamp"
    FROM source
    {% if is_incremental() %}
        WHERE
            ({{ build_mode_trigger(var('build_mode') , 'source."message_timestamp"' , '"message_timestamp"') }})
    {% endif %}
)

-- If the SCD already exists, get the latest row. Otherwise just select the rows from the openlayer CTE
{% if is_incremental() %}
    -- Select the latest rows for all extant sets in the scd
    ,latestrows AS (
        SELECT
            "entitlement_sets_scd_primary_key"
            ,"entitlement_set_id"
            ,"effective_from"
            ,"message_id"
            ,"message_timestamp"
            ,"entitlement_ids"
            ,"event_start_timestamp"
        FROM {{ this }}
        QUALIFY ROW_NUMBER() OVER(PARTITION BY "entitlement_set_id" ORDER BY "effective_until" DESC) = 1
    )

    -- Join the latest rows and the new rows from the open layer
    ,combined AS (
        SELECT * FROM openlayer
        UNION
        SELECT * FROM latestrows
    )
{% else %}
    -- If the table does not aready exist then just take the openlayer rows
    ,combined AS (
        SELECT * FROM openlayer
    )
{% endif %}

-- Deduplicate on the key fields. Because all sets are updated when one changes, Other fields like message ID or timestamp can change, without these key fields changing.
,dedup AS (
    SELECT * FROM combined
    QUALIFY ROW_NUMBER() OVER(PARTITION BY "entitlement_set_id","entitlement_ids","event_start_timestamp" ORDER BY "effective_from" ASC) = 1
)

-- Calculate updated effective until as the new effective from
,final AS (
    SELECT
        "entitlement_sets_scd_primary_key"
        ,"entitlement_set_id"
        ,"effective_from"
        ,IFNULL(LEAD("effective_from", 1) OVER(PARTITION BY "entitlement_set_id" ORDER BY "effective_from" ASC),'9999-12-31') AS "effective_until"
        ,"message_id"
        ,"message_timestamp"
        ,"entitlement_ids"
        ,"event_start_timestamp"
    FROM dedup
)

SELECT * FROM final
