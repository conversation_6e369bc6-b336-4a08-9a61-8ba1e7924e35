{{
    config(
        materialized='incremental',
        incremental_strategy='merge',
        schema='PRESENTATION',
        unique_key='"user_entitlement_scd_pkey"',
        tags=['presentation-entitlements']
    )
}}

WITH ues_source AS (
    SELECT * FROM {{ ref('staging__entitlements__user_entitlement_service') }}
    WHERE "table_version" = 'v2'
)

{# ,sf_source AS (
    SELECT * FROM {{ ref('staging__salesforce__account') }}
) #}

,user_event_source AS (
    SELECT * FROM {{ ref('staging__segment__user_events_identifies') }}
)

,mapping_source AS (
    SELECT * FROM {{ ref('staging__identity__mapping')}}
)

,zuora_acc_source AS (
    SELECT * FROM {{ ref('staging__zuora__account_current') }}
)

,cleaned_tier_source AS (
    SELECT
        *
        ,COALESCE("entitlement_change_type" = 'DELETE'
            AND LAG("entitlement_change_type") OVER(PARTITION BY "dazn_user_id" ORDER BY "message_timestamp" ASC) IN ('UPDATE', 'MIGRATE')
            AND LAG("previous_entitlement_set_id") OVER(PARTITION BY "dazn_user_id" ORDER BY "message_timestamp") = "entitlement_set_id"
            ,FALSE) AS "is_redundant_delete"
    FROM ues_source
    WHERE ("product_type" = 'tier' OR "entitlement_set_id" LIKE 'tier%' OR "entitlement_set_id" LIKE '%tier')
        AND "entitlement_set_id" NOT IN ('tier_brozne_es')
    QUALIFY "is_redundant_delete" = FALSE
)

,cleaned_ppv_source AS (
    SELECT
        *
        ,COALESCE(LAG("entitlement_change_type") OVER(PARTITION BY "dazn_user_id", "entitlement_set_id", "subscription_name" ORDER BY "message_timestamp") = 'CREATE'
            AND "entitlement_change_type" = 'CREATE'
            ,FALSE) AS "is_redundant_create"
    FROM ues_source
    WHERE "product_type" != 'tier' OR "product_type" IS NULL
    QUALIFY "is_redundant_create" = FALSE
)

,ues_tiers AS (
    SELECT
        "dazn_user_id"
        ,"entitlement_set_id"
        ,"message_timestamp" AS "entitlement_set_effective_from"
        ,TO_TIMESTAMP("expiration_timestamp") AS "initial_expiration_timestamp"
        ,INITCAP("entitlement_change_type") AS "entitlement_change_type"
        ,CASE
            WHEN UPPER(LEAD("entitlement_change_type") OVER(PARTITION BY "dazn_user_id", "subscription_name" ORDER BY "message_timestamp")) IN ('MIGRATE', 'CREATE', 'DELETE', 'UPDATE') THEN 'Expired - ' || INITCAP(LEAD("entitlement_change_type") OVER(PARTITION BY "dazn_user_id", "subscription_name" ORDER BY "message_timestamp"))
            WHEN LEAD("entitlement_change_type") OVER(PARTITION BY "dazn_user_id" ORDER BY "message_timestamp") ILIKE 'Update' AND LEAD("previous_entitlement_set_id") OVER(PARTITION BY "dazn_user_id" ORDER BY "message_timestamp") = "entitlement_set_id" THEN 'Expired - Update'
            WHEN LEAD("entitlement_change_type") OVER(PARTITION BY "dazn_user_id" ORDER BY "message_timestamp") ILIKE 'Migrate' AND LEAD("previous_entitlement_set_id") OVER(PARTITION BY "dazn_user_id" ORDER BY "message_timestamp") = "entitlement_set_id" THEN 'Expired - Update'
            WHEN UPPER(LEAD("entitlement_change_type") OVER(PARTITION BY "dazn_user_id", "entitlement_set_id" ORDER BY "message_timestamp")) IN ('MIGRATE', 'CREATE', 'DELETE') THEN 'Expired - ' || INITCAP(LEAD("entitlement_change_type") OVER(PARTITION BY "dazn_user_id", "entitlement_set_id" ORDER BY "message_timestamp"))
            WHEN "initial_expiration_timestamp" IS NOT NULL AND "initial_expiration_timestamp" < CURRENT_DATE() THEN 'Expired - Expiration'
            ELSE 'Active'
        END AS "set_expired_type"
        ,CASE
            WHEN UPPER(LEAD("entitlement_change_type") OVER(PARTITION BY "dazn_user_id", "subscription_name" ORDER BY "message_timestamp")) IN ('MIGRATE', 'CREATE', 'DELETE', 'UPDATE') THEN LEAD("entitlement_set_effective_from") OVER(PARTITION BY "dazn_user_id", "subscription_name" ORDER BY "message_timestamp")
            WHEN "set_expired_type" = 'Expired - Update' THEN LEAD("entitlement_set_effective_from") OVER(PARTITION BY "dazn_user_id" ORDER BY "message_timestamp")
            WHEN UPPER(LEAD("entitlement_change_type") OVER(PARTITION BY "dazn_user_id", "entitlement_set_id" ORDER BY "message_timestamp")) IN ('MIGRATE', 'CREATE', 'DELETE') THEN LEAD("entitlement_set_effective_from") OVER(PARTITION BY "dazn_user_id", "entitlement_set_id" ORDER BY "message_timestamp")
            WHEN "set_expired_type" = 'Expired - Expiration' THEN "initial_expiration_timestamp"
            ELSE '9999-12-31'
        END AS "set_expired_timestamp"
        ,COALESCE("set_expired_type" = 'Active', FALSE) AS "is_active"
        ,INITCAP(LEAD("entitlement_change_type") OVER(PARTITION BY "dazn_user_id" ORDER BY "message_timestamp")) AS "next_entitlement_change_type"
        ,INITCAP("product_type") AS "product_type"
        ,"previous_entitlement_set_id"
        ,"source"
        ,"subscription_name"
    FROM cleaned_tier_source
)

,ues_ppv AS (
    SELECT
        "dazn_user_id"
        ,"entitlement_set_id"
        ,"message_timestamp" AS "entitlement_set_effective_from"
        ,TO_TIMESTAMP("expiration_timestamp") AS "initial_expiration_timestamp"
        ,INITCAP("entitlement_change_type") AS "entitlement_change_type"
        ,CASE
            WHEN LEAD("entitlement_change_type") OVER(PARTITION BY "dazn_user_id", "entitlement_set_id" ORDER BY "message_timestamp") ILIKE 'Delete' THEN 'Expired - Delete'
            WHEN "initial_expiration_timestamp" IS NOT NULL AND "initial_expiration_timestamp" < CURRENT_DATE() THEN 'Expired - Expiration'
            ELSE 'Active'
        END AS "set_expired_type"
        ,CASE
            WHEN "set_expired_type" = 'Expired - Delete' THEN LEAD("entitlement_set_effective_from") OVER(PARTITION BY "dazn_user_id", "entitlement_set_id" ORDER BY "message_timestamp" )
            WHEN "set_expired_type" = 'Expired - Expiration' THEN "initial_expiration_timestamp"
            ELSE '9999-12-31'
        END AS "set_expired_timestamp"
        ,COALESCE("set_expired_type" = 'Active', FALSE) AS "is_active"
        ,INITCAP(LEAD("entitlement_change_type") OVER(PARTITION BY "dazn_user_id" ORDER BY "message_timestamp")) AS "next_entitlement_change_type"
        ,COALESCE(UPPER("product_type"),'PPV') AS "product_type"
        ,"previous_entitlement_set_id"
        ,"source"
        ,"subscription_name"
    FROM cleaned_ppv_source
)

{# ,sf AS (
    SELECT
        "dazn_user_id"
        ,"crm_account_id"
    FROM sf_source
    QUALIFY ROW_NUMBER() OVER(PARTITION BY "dazn_user_id", "crm_account_id" ORDER BY "crm_last_modified_timestamp" DESC) = 1
) #}

,user_event AS (
    SELECT
        user_event_source."dazn_user_id"
        ,COALESCE(mapping_source."crm_account_id", user_event_source."salesforce_id", user_event_source."provider_customer_id") AS "crm_account_id"
    FROM user_event_source
    LEFT JOIN mapping_source USING ("dazn_user_id")
    QUALIFY ROW_NUMBER() OVER(PARTITION BY "dazn_user_id", "crm_account_id" ORDER BY "timestamp" DESC NULLS LAST) = 1
)

,zuora_acc AS (
    SELECT
        "dazn_user_id"
        ,"billing_account_id"
    FROM zuora_acc_source
)

,ues_combined AS (
    SELECT * FROM ues_ppv
    WHERE "entitlement_change_type" != 'Delete'
    UNION
    SELECT * FROM ues_tiers
    WHERE "entitlement_change_type" != 'Delete'
)

,final AS (
    SELECT
        ues_combined."dazn_user_id"
        ,ues_combined."entitlement_set_id"
        ,ues_combined."entitlement_set_effective_from"
        ,ues_combined."set_expired_timestamp" AS "entitlement_set_effective_until"
        ,ues_combined."set_expired_type"
        ,ues_combined."product_type"
        ,ues_combined."initial_expiration_timestamp"
        ,ues_combined."entitlement_change_type"
        {# ,sf."crm_account_id" #}
        ,user_event."crm_account_id"
        ,zuora_acc."billing_account_id"
        ,ues_combined."subscription_name"
        ,ues_combined."previous_entitlement_set_id"
        ,ues_combined."source"
        ,ues_combined."is_active"
        ,ues_combined."next_entitlement_change_type"
        ,HASH(ues_combined."dazn_user_id", ues_combined."entitlement_set_id", ues_combined."entitlement_set_effective_from", ues_combined."entitlement_change_type", ues_combined."subscription_name") AS "user_entitlement_scd_pkey"
    FROM ues_combined
    {# LEFT JOIN sf ON ues_combined."dazn_user_id" = sf."dazn_user_id" #}
    LEFT JOIN user_event USING ("dazn_user_id")
    LEFT JOIN zuora_acc ON ues_combined."dazn_user_id" = zuora_acc."dazn_user_id"
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ues_combined."dazn_user_id", ues_combined."entitlement_set_id", ues_combined."subscription_name", ues_combined."entitlement_set_effective_from", ues_combined."entitlement_change_type" ORDER BY ues_combined."entitlement_set_effective_from" DESC) = 1
)

SELECT * FROM final
