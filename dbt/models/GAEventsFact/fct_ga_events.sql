{{
    config(
        materialized='incremental',
        incremental_strategy='delete+insert',
        unique_key='"full_date"',
        on_schema_change='append_new_columns',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XL_WH',
        tags=['presentation-ga-events-fact']
    )
}}

WITH source AS (
    SELECT * FROM {{ source('CURATED', 'CURATED_GA__FLAT_SESSION') }}
)

,renamed AS (
    SELECT
        CURRENT_TIMESTAMP AS "dbt_inserted_at"
        ,"ga_unique_session_id"
        ,"ga_primary_key"
        ,"customer_id"
        ,"edm_inserted_at"
        ,"visit_number"
        ,"session_id"
        ,"visit_start_timestamp"
        ,"full_date"
        ,"full_visitor_id"
        ,"client_id"
        ,"channel_grouping"
        ,"total_events"
        ,"device_category"
        ,"geo_country"
        ,"application_id"
        ,"app_version"
        ,"hit_number"
        ,"hit_time_ms"
        ,"event_action"
        ,"event_category"
        ,"event_label"
        ,"hostname"
        ,"page_path"
        ,"page_title"
        ,"experiment_id"
        ,"user_agent"
        ,"application_type"
        ,"application_version"
        ,"vwo_test_variant_name"
        ,"application_environment"
        ,"client_id_scd"
        ,"click_ref"
        ,"launch_origin"
        ,"vwo_test_campaign_name"
        ,"vwo_test_variant_id"
        ,"publisher_id"
        ,"publisher_name"
        ,"vwo_test_campaign_id"
        ,"open_browse"
        ,"test_variant"
        ,"test_variant_open_browse"
        ,"test_variant_rail_management"
        ,"competition_id"
        ,"competition_name"
        ,"content_title_sport_name"
        ,"navigation_tile_article_id"
        ,"navigation_tile_navigate_to"
        ,"navigation_tile_title"
        ,"rail_current_position"
        ,"rail_length"
        ,"rail_name"
        ,"rail_starting_position"
        ,"rail_number_in_view"
        ,"search_number_of_results"
        ,"search_result_category"
        ,"search_selected_result_position"
        ,"search_term"
        ,"rail_number_of_loaded"
        ,"article_type"
        ,"player_action"
        ,"play_origin"
        ,"payment_billing_country"
        ,"payment_billing_period"
        ,"payment_state"
        ,"payment_subscription_type"
        ,"password_reset_state"
        ,"content_tile_position_in_view"
        ,"content_tile_position_of_loaded"
        ,"navigation_tile_position_in_view"
        ,"navigation_tile_position_of_loaded"
        ,"content_tile_article_name"
        ,"content_tile_coming_up_label"
        ,"perform_id_ansii"
        ,"perform_id"
        ,"ga_page_category"
        ,"payment_method"
        ,"page_load_delta"
        ,"competitor_id"
        ,"fixture_id"
        ,"gtm_container_version"
        ,"rail_title"
        ,"content_tile_label_id"
        ,"adblock_status"
        ,"content_country"
        ,"fixture_name"
        ,"competitor_name"
        ,"giftcode_action"
        ,"giftcode_status"
        ,"giftcode_type"
        ,"giftcode_id"
        ,"giftcode_extra_free_months"
        ,"giftcode_promo_id"
        ,"giftcode_campaign_id"
        ,"giftcode_extra_payment_method"
        ,"giftcode_eu_content_portability"
        ,"payment_action"
        ,"payment_additional_payment_method"
        ,"payment_auth_country"
        ,"payment_auth_provider"
        ,"payment_auth_response"
        ,"subscription_action"
        ,"subscription_action_status"
        ,"subscription_billing_period"
        ,"subscription_subscription_type"
        ,"subscription_restart_date"
        ,"edit_item"
        ,"edit_action_status"
        ,"edit_value"
        ,"edit_previous_value"
        ,"edit_field"
        ,"itm_source"
        ,"itm_medium"
        ,"itm_campaign"
        ,"live_edge"
        ,"pulse_alert_position"
        ,"player_playback_initiation"
        ,"player_playback_source"
        ,"dazn_device_id"
        ,"hit_time"
        ,"hit_id"
        ,"device_key"
        ,"page_url"
        ,"full_date_ts"
        ,"session_start_ts"
        ,"article_id"
        ,CASE
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/home.*') THEN 'Home'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/help.*') THEN 'Help'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/(myaccount|paymenthistory|cancel|changepaymentmethod)$') THEN 'My Account'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/account\/signin$') THEN 'Sign In'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/account\/signup$') THEN 'Create Account'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/account\/forgot-password.*') THEN 'Forgot Password'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/account\/remember-email.*') THEN 'Forgot Email'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/account\/(free-trial|subscription)-payment$') THEN 'Payment'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*payment\/(fail|success)$') THEN 'Payment Outcome'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/sport\/.*') THEN 'Sport'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/competitor\/.*') THEN 'Competitor'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/tournament\/.*') THEN 'Tournament'
            WHEN REGEXP_LIKE(LOWER("page_path"), '^\/(app\/(.)\/(\d*.\/))*[a-zA-z]{2}-[a-zA-z]{2}$') THEN 'Generic Landing'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*/c/.*') THEN 'Campaign Landing'
            WHEN REGEXP_LIKE(LOWER("page_url"), 'watch.dazn.com\/.*\/sports') THEN 'Generic Landing'
            WHEN REGEXP_LIKE(LOWER("page_url"), 'watch.dazn.com\/.*\/') AND NOT REGEXP_LIKE(LOWER("page_url"), '.*sports.*') THEN 'Campaign Landing'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/account\/new-user-payment.*') AND REGEXP_LIKE(LOWER("page_path"), '.*(fail|success).*') THEN 'Payment Outcome'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/account\/new-user-payment.*') THEN 'Payment'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/schedule.*') THEN 'Schedule'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/signin-selector.*') THEN 'Dual Sign In'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/competition\/.*') THEN 'Tournament'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/pause-or-cancel\/.*') THEN 'Pause or Cancel'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/setpause\/.*') THEN 'Pause'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/pause.*upsell\/.*') THEN 'Pause'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/app\/.*') THEN 'Living Room Specific'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/doubleoptin\/.*') THEN 'Generic Marketing'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/emailpreferences\/.*') THEN 'Generic Marketing'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/search.*') THEN 'Search'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/pin-and-pair.*') THEN 'Pin and Pair'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/account/confirmation.*') THEN 'Confirmation'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/account/optin*') THEN 'Marketing Opt-In'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/account/paused-user*') THEN 'Paused Landing'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/account/paypal-success*') THEN 'Payment Outcome'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/redeem*') THEN 'Redeem'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/account/no-redeem.*') THEN 'Redeem'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/account/last-step.*') THEN 'Docomo'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/account/registration-details.*') THEN 'Docomo'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/account/docomo-error/50-10052-36.*') THEN 'Docomo'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/docomo.*') THEN 'Docomo'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/welcome-back.*') THEN 'Welcome Back'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/tv.*') THEN 'Pin and Pair'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/show/*') THEN 'TV Show'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/account/paused-user.*') THEN 'Pause'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/payment-plan.*') THEN 'Select Plan'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/account/paypal-*') THEN 'Payment Outcome'
            WHEN LOWER("page_path") = '/' THEN 'Re-direct'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/getn.dazn.com/.*') THEN 'Beta Landing Page'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/my.dazn.com/contactus*') THEN 'Contact Us'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/my.dazn.com/keepintouch/initiate.*') THEN 'Pause'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/my.dazn.com/keepintouch/amend.*') THEN 'My Account - Marketing Preferences'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/my.dazn.com/ChangePassword.*') THEN 'My Account - Change Password'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/my.dazn.com/mylogin/contactus/livechat.*') THEN 'Live Chat'
            WHEN REGEXP_LIKE(LOWER("page_path"), '.*\/my.dazn.com/*') THEN 'My Account'
            WHEN LOWER("page_path_level_4") = '\/standings' THEN 'Standings'
            WHEN LOWER("page_path_level_4") = '\/matches' THEN 'Matches'
            WHEN LOWER("page_path_level_4") = '\/squad' THEN 'Squads'
            ELSE 'Undefined'
        END AS "page_category"
    FROM source
    WHERE
        ({{ unixtime_build_trigger(var('build_mode'), var('rebuild_days'), 'visit_start_timestamp') }})
        AND
        "ga_primary_key" IS NOT NULL
        AND
        "hit_type" = 'EVENT'
-- QUALIFY ROW_NUMBER() OVER (PARTITION BY "ga_primary_key" ORDER BY "hit_time_ms" DESC) = 1
)

SELECT * FROM renamed
