version: 2

models:
  - name: fct_ga_events
    description: "Google Analytics Events Fact"
    columns:
      - name: dbt_inserted_at
        description: "the timestamp of when the row was processed by dbt"
        quote: true

      - name: ga_unique_session_id
        description: "Unique identifier for the session. Obtained by concatenating fullVisitorId and visitId"
        quote: true

      - name: ga_primary_key
        description: "MD5 of concatenated ga_unique_session_id and hitTime"
        quote: true

      - name: customer_id
        description: "CRM ID"
        quote: true

      - name: edm_inserted_at
        description: "Date/Timestamp the record was inserted (the records for a given insert operation)"
        quote: true

      - name: visit_number
        description: "The session number for this user. If this is the first session, then this is set to 1."
        quote: true

      - name: session_id
        description: "An identifier for this session. This is part of the value usually stored as the _utmb cookie. This is only unique to the user. For a completely unique ID, you should use a combination of fullVisitorId and visitId."
        quote: true

      - name: visit_start_timestamp
        description: "the POSIX timestamp of the visit start time"
        quote: true

      - name: full_date
        description: "The date of the session"
        quote: true

      - name: full_visitor_id
        description: "The unique visitor ID (also known as client ID)."
        quote: true

      - name: client_id
        description: "Unhashed version of the Client ID for a given user associated with any given visit/session recorded on a hit level"
        quote: true

      - name: channel_grouping
        description: ""
        quote: true

      - name: total_events
        description: "The number of sessions (for convenience). This value is 1 for sessions with interaction events. The value is null if there are no interaction events in the session."
        quote: true

      - name: device_category
        description: "The type of device (Mobile, Tablet, Desktop)according to GA"
        quote: true

      - name: geo_country
        description: "The country from which sessions originated, based on IP address."
        quote: true

      - name: application_id
        description: "The id of the application"
        quote: true

      - name: app_version
        description: "The version of the application on a hit level"
        quote: true

      - name: hit_number
        description: "The sequenced hit number. For the first hit of each session, this is set to 1."
        quote: true

      - name: hit_time_ms
        description: "The number of milliseconds after the visitStartTime when this hit was registered. The first hit has a hits.time of 0"
        quote: true

      - name: event_action
        description: "Action Mid-level identifier"
        quote: true

      - name: event_category
        description: "Action High-level identifier"
        quote: true

      - name: event_label
        description: "Most specific Action identifier"
        quote: true

      - name: hostname
        description: "the hostname of the URL"
        quote: true

      - name: page_path
        description: "The URL path of the page."
        quote: true

      - name: page_title
        description: "The title of the page"
        quote: true

      - name: experiment_id
        description: "Experiment ID"
        quote: true

      - name: user_agent
        description: "The user agent associated with the device"
        quote: true

      - name: application_type
        description: "The type of the application"
        quote: true

      - name: application_version
        description: "The version of the application on a session level"
        quote: true

      - name: vwo_test_variant_name
        description: "VWO Test Variant Name"
        quote: true

      - name: application_environment
        description: "The Environment of the application"
        quote: true

      - name: clientid_scd
        description: "Unhashed version of the Client ID for a given user associated with any given visit/session recorded on a session level"
        quote: true

      - name: click_ref
        description: "Legacy field to capture the clickref UTM parameter - This was before the actual UTM parameter was working to pass the value into various traffic source fields"
        quote: true

      - name: launch_origin
        description: "For a given session, the origin of the launch - deeplinked location or through the front door"
        quote: true

      - name: vwo_test_campaign_name
        description: ""
        quote: true

      - name: vwo_test_variant_id
        description: ""
        quote: true

      - name: publisher_id
        description: "Legacy field to capture the publisher_id UTM parameter - This was before the actual UTM parameter was working to pass the value into various traffic source fields - initially used for Partnerise"
        quote: true

      - name: publisher_name
        description: "Legacy field to capture the publisher_name UTM parameter - This was before the actual UTM parameter was working to pass the value into various traffic source fields - Initially used for Partnerise"
        quote: true

      - name: vwo_test_campaign_id
        description: ""
        quote: true

      - name: open_browse
        description: "yes or no field to denote if the session had openBrowse experience or not. Note only available and session-scoped, so if a customer transitions from OB → catalogue in a session all events will be tagged as ‘no’"
        quote: true

      - name: test_variant
        description: ""
        quote: true

      - name: test_variant_open_browse
        description: ""
        quote: true

      - name: test_variant_rail_management
        description: ""
        quote: true

      - name: competition_id
        description: "The Id of the competition recorded by GA"
        quote: true

      - name: competition_name
        description: "The name of the competition recorded by GA"
        quote: true

      - name: content_title_sport_name
        description: "The sport name recorded by GA"
        quote: true

      - name: navigation_tile_article_id
        description: "Article ID of a navigation tile"
        quote: true

      - name: navigation_tile_navigate_to
        description: ""
        quote: true

      - name: navigation_tile_title
        description: "Legacy field used by Massive to capture the title of the navigation tile that's being clicked on - the value captured is in localised language"
        quote: true

      - name: rail_current_position
        description: "Legacy Massive tracking parameter for when a rail is displayed to the user where there are tiles outside of view on the left hand side"
        quote: true

      - name: rail_length
        description: "For a given interaction with a rail, this parameter shows how many tiles were in that that rail"
        quote: true

      - name: rail_name
        description: "Name of the rail (from the catalog API) in which the tile was clicked"
        quote: true

      - name: rail_starting_position
        description: "Legacy Massive tracking parameter for when a rail is displayed to the user where there are tiles outside of view on the left hand side. I.e. if there are two tiles to the left of the starting view port, this value would be 3 because the first tile that the user sees at the beginning of the rail is actually the third tile."
        quote: true

      - name: rail_number_in_view
        description: ""
        quote: true

      - name: search_number_of_results
        description: "Number of search results returned"
        quote: true

      - name: search_result_category
        description: "When the user interacts with the search results that are categorised in different tabs - this field denotes the tab label the select result was under"
        quote: true

      - name: search_selected_result_position
        description: "Position of the search result that was clicked on in the returned list"
        quote: true

      - name: search_term
        description: "Term entered in search bar"
        quote: true

      - name: rail_number_of_loaded
        description: ""
        quote: true

      - name: article_type
        description: "Type of the article. Eg: Live, Live to VOD, VOD, etc"
        quote: true

      - name: player_action
        description: "Captures which button/interaction the user initiated within the player"
        quote: true

      - name: play_origin
        description: "Passes the origin of the play back session - deeplinked location or within the app"
        quote: true

      - name: payment_billing_country
        description: "Country of payment, derived from geolocation in URL, i.e. CA, DE, CH, US etc"
        quote: true

      - name: payment_billing_period
        description: "Determines if the subscription is annual or monthly"
        quote: true

      - name: payment_state
        description: "Success or fail of a payment"
        quote: true

      - name: payment_subscription_type
        description: "Determines if the sign up was for a free trial or a hard offer"
        quote: true

      - name: password_reset_state
        description: "Denotes if the password reset was successful or failed"
        quote: true

      - name: content_tile_position_in_view
        description: "Used for a any rail, schedule, calendar click to determine which tile or calendar date was clicked"
        quote: true

      - name: content_tile_position_of_loaded
        description: "Used for a any rail, schedule, calendar click to determine which tile or calendar date was clicked"
        quote: true

      - name: navigation_tile_position_in_view
        description: "Used on navigation tiles (i.e. Sports rail tiles), the value here shows the position of the tile in view when it was clicked"
        quote: true

      - name: navigation_tile_position_of_loaded
        description: "Used on navigation tiles (i.e. Sports rail tiles), the value here shows the position of the tile when it was initially loaded in the rail"
        quote: true

      - name: content_tile_article_name
        description: "Name of the article in the localised language"
        quote: true

      - name: content_tile_coming_up_label
        description: "yes or no field - to denote if the tile interacted with is a coming up tile or not"
        quote: true

      - name: perform_id_ansii
        description: ""
        quote: true

      - name: perform_id
        description: ""
        quote: true

      - name: ga_page_category
        description: "The category of the page recorded by GA"
        quote: true

      - name: payment_method
        description: "debit/credit card, bank transfer, giftcode"
        quote: true

      - name: page_load_delta
        description: "Used to capture the number of milliseconds it took for any page load to happen"
        quote: true

      - name: competitor_id
        description: "The id of the competitor"
        quote: true

      - name: fixture_id
        description: "The id of the fixture"
        quote: true

      - name: gtm_container_version
        description: ""
        quote: true

      - name: rail_title
        description: "The Title of the Rail"
        quote: true

      - name: content_tile_label_id
        description: ""
        quote: true

      - name: adblock_status
        description: ""
        quote: true

      - name: content_country
        description: ""
        quote: true

      - name: fixture_name
        description: ""
        quote: true

      - name: competitor_name
        description: ""
        quote: true

      - name: giftcode_action
        description: ""
        quote: true

      - name: giftcode_status
        description: ""
        quote: true

      - name: giftcode_type
        description: ""
        quote: true

      - name: giftcode_id
        description: ""
        quote: true

      - name: giftcode_extra_free_months
        description: ""
        quote: true

      - name: giftcode_promo_id
        description: ""
        quote: true

      - name: giftcode_campaign_id
        description: ""
        quote: true

      - name: giftcode_extra_payment_method
        description: ""
        quote: true

      - name: giftcode_eu_content_portability
        description: ""
        quote: true

      - name: payment_action
        description: ""
        quote: true

      - name: payment_additional_payment_method
        description: ""
        quote: true

      - name: payment_auth_country
        description: ""
        quote: true

      - name: payment_auth_provider
        description: ""
        quote: true

      - name: payment_auth_response
        description: ""
        quote: true

      - name: subscription_action
        description: ""
        quote: true

      - name: subscription_action_status
        description: ""
        quote: true

      - name: subscription_billing_period
        description: ""
        quote: true

      - name: subscription_subscription_type
        description: ""
        quote: true

      - name: subscription_restart_date
        description: ""
        quote: true

      - name: edit_item
        description: ""
        quote: true

      - name: edit_action_status
        description: ""
        quote: true

      - name: edit_value
        description: ""
        quote: true

      - name: edit_previous_value
        description: ""
        quote: true

      - name: edit_field
        description: ""
        quote: true

      - name: itm_source
        description: ""
        quote: true

      - name: itm_medium
        description: ""
        quote: true

      - name: itm_campaign
        description: ""
        quote: true

      - name: live_edge
        description: ""
        quote: true

      - name: pulse_alert_position
        description: ""
        quote: true

      - name: player_playback_initiation
        description: ""
        quote: true

      - name: player_playback_source
        description: ""
        quote: true

      - name: dazn_device_id
        description: ""
        quote: true

      - name: hit_time
        description: "The number of seconds after the visitStartTime when this hit was registered. The first hit has a hits.time of 0"
        quote: true

      - name: hit_id
        description: "Unique identifier of the hit. Obtained by concatenating fullVisitorId, hit_time and hit_number"
        quote: true

      - name: device_key
        description: "Encrypted version of the User Agent"
        quote: true

      - name: page_url
        description: "The cleaned URL of the page"
        quote: true

      - name: full_date_ts
        description: "Timestamp of the event"
        quote: true

      - name: session_start_ts
        description: "Timestamp of the session start"
        quote: true

      - name: article_id
        description: "The id of the article"
        quote: true

      - name: page_category
        description: "Business logic derived page category from page path, page path level 4 and page url"
        quote: true
