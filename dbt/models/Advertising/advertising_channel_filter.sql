{{
config(
    materialized='table',
    schema='TRANSIENT',
    database='ADVERTISING__B2C__MART__' + snowflake_env(),
    snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
    tags=['presentation-advertising-logs']
)
}}

WITH logs AS (
    SELECT * FROM  {{ ref('advertising_window_filter') }}
)

,channel AS (
    SELECT * FROM {{ ref('advertising_channel_selection') }}
)

SELECT
     logs."fixture_id"
    ,logs."territory"
    ,logs."ad_date"
    ,logs."ad_start_timestamp"
    ,logs."ad_end_timestamp"
    ,logs."duration"
    ,logs."creative"
    ,logs."line_item_name"
    ,logs."line_item_id"
    ,logs."delivery_type"
    ,logs."event_type"
    ,logs."break_type"
    ,logs."ad_type"
    ,logs."pl"
    ,logs."advertiser"
    ,logs."channel"
    ,logs."source"
    ,logs."primary_key"
FROM logs
LEFT JOIN channel
    ON logs."fixture_id" = channel."fixture_id"
    AND logs."territory" = channel."territory"
    AND logs."ad_start_timestamp" BETWEEN channel."channel_effective_from" AND channel."channel_effective_until"
WHERE logs."channel" = channel."channel"
