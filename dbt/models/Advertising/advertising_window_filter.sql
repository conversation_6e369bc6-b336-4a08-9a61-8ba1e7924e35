{{
    config(
        materialized='table',
        schema='TRANSIENT',
        database='ADVERTISING__B2C__MART__' + snowflake_env(),
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['presentation-advertising-logs']
    )
}}

WITH logs AS (
    SELECT * FROM {{ ref('advertising_stack_sources') }}
)

, time_windows AS (
    SELECT * FROM {{ ref('advertising_time_windows') }}
)

SELECT
    logs."fixture_id"
    ,logs."territory"
    ,logs."ad_date"
    ,logs."ad_start_timestamp"
    ,logs."ad_end_timestamp"
    ,logs."duration"
    ,logs."creative"
    ,logs."line_item_name"
    ,logs."line_item_id"
    ,logs."delivery_type"
    ,logs."event_type"
    ,logs."break_type"
    ,logs."ad_type"
    ,logs."pl"
    ,logs."advertiser"
    ,logs."channel"
    ,logs."source"
    ,logs."primary_key"
FROM logs
LEFT JOIN time_windows
    ON logs."fixture_id" = time_windows."fixture_id"
WHERE 1=1
    AND logs."ad_start_timestamp" >= time_windows."window_start_timestamp"
    AND logs."ad_start_timestamp" <= time_windows."window_end_timestamp"
