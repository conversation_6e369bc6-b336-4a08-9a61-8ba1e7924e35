{{
    config(
        materialized='table',
        schema='TRANSIENT',
        database='ADVERTISING__B2C__MART__' + snowflake_env(),
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['presentation-advertising-logs']
    )
}}

/*
This step does two things:
 First, it applies business logic rules to some fields.
   - break_type: adds lbanners and 2-boxes to the possible break types.
   - as_run_type: defines linear or ott
   - ad_type: determines if an ad is a commercial, promo, sponsor etc..
   - file_name: processes specific files we know cause issues downstream
   - territory: processes the territory to make it comply with our standard territory values.
 Then it filters out unwanted ads (see where clause)
    - Filter for valid territory and fixture_id formats:
      We just want a valid territory and a fixture_id that is all alphanumeric and 20-27 characters long
      (all instances currently are actually 22-25 long so a bit of leeway is provided)
    - For the filter on file name:
      Filter to only file names which contain -AD-, -SP- or -APPLE- after the first hyphen,
      OR which contain a promo substring (2 upper case letters followed by 7 numbers, e.g. SP1234567),
      OR which contain -AD- after the second hyphen.
      This removes poorly formed file names
*/

-- This macro is defined in dazn-dp20-presentation/dbt/macros/Advertising/advertisement_variables.sql
{% set advertising_variables = advertising_variables() %}

WITH curated_grass_valley AS (SELECT * FROM {{ source_env('CURATED', 'CURATED__GRASS_VALLEY__CURRENT') }})

,processing AS (
    SELECT
        "ad_date"
        ,DATE_TRUNC(SECOND,"ad_start_timestamp") as "ad_start_timestamp"
        ,DATE_TRUNC(SECOND,"ad_end_timestamp") as "ad_end_timestamp"
        ,"fixture_id"
        ,"territory"
        ,REPLACE(REPLACE(REPLACE(REPLACE ("file_name", 'AvMedia', ''), 'E:', ''), '.mxf', ''), '\\', '') AS "file_name_f"
        ,"ad_name_title"
        ,"duration"
        ,CASE
            WHEN "as_run_type" = 'vod' THEN 'OTT'
            WHEN LOWER("as_run_type") LIKE '%linear%' THEN 'LINEAR'
            ELSE UPPER("as_run_type")
            END AS "as_run_type_f"
        ,"as_run_file_name"
        ,"channel"
        ,"event_type"
        ,CASE
            WHEN "event_type" ILIKE '%LBAND%' THEN 'lbanner'
            WHEN "event_type" ILIKE '%TWOBOX%' THEN '2box'
            ELSE "break_type"
            END AS "break_type_f"
        ,"primary_key"
    FROM curated_grass_valley
    WHERE "territory" != 'TERRITORY DATA NOT FOUND'
    AND "fixture_id" RLIKE '[A-Za-z0-9]{20,27}'
    -- Why we filter out NULLS and ICEPLAY records: https://livesport.atlassian.net/wiki/spaces/DATA/pages/6325633414/BUG+LIST#ICEPLAY-duplicates
    AND "ad_name_title" IS NOT NULL
    AND "event_type" != 'ICE RT Param1 Play Clip'
    AND (
        SPLIT_PART("file_name_f", '-', 2) IN ('AD', 'SP', 'APPLE')
        OR REGEXP_SUBSTR("file_name_f", '[A-Za-z]{2}[0-9]{7}') IS NOT NULL
        OR SPLIT_PART("file_name_f", '-', 3) IN ('AD')
        )
    AND "ad_date" >= '{{ advertising_variables.start_date }}'
)

SELECT
     "fixture_id"
    ,{{ advertising_map_territory("territory") }} AS "territory"
    ,"ad_date"
    ,"ad_start_timestamp"
    ,"ad_end_timestamp"
    ,"duration"
    ,"file_name_f" AS "creative"
    ,"ad_name_title" AS "line_item_name"
    ,"as_run_type_f" AS "delivery_type"
    ,"break_type_f" AS "break_type"
    ,"event_type"
    ,"channel"
    ,"as_run_file_name"
    ,"primary_key"
FROM processing
