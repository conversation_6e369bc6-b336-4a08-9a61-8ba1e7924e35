{{
    config(
        materialized='table',
        schema='TRANSIENT',
        database='ADVERTISING__B2C__MART__' + snowflake_env(),
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['presentation-advertising-logs']
    )
}}

WITH logs AS (SELECT DISTINCT "fixture_id" FROM {{ ref('advertising_stack_sources') }})

,km AS (
    SELECT
        "fixture_id"
        ,TO_TIMESTAMP("feed_start_epoch", 3) AS "feed_start_timestamp"
        ,TO_TIMESTAMP("feed_end_epoch", 3) AS "feed_end_timestamp"
    FROM {{ source('ANALYTICS','key_moments_fact') }}
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "fixture_id" ORDER BY "feed_start_timestamp", "outlet") = 1
)

,won AS (
    SELECT
        "fixture_id"
        ,"fixture_date" AS "fixture_date"
        ,"won_tile_start_plan"
    FROM {{ source('ANALYTICS','content_dimension') }}
    WHERE "competition_name" NOT LIKE '%Test%'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "fixture_id" ORDER BY "fixture_date" DESC NULLS LAST, "won_tile_start_plan" ASC NULLS LAST) = 1
)

--TODO: Streamlit CTE here. Include streamlit as first case in coalesce below (bias)

,fixture_window AS (
    SELECT
        logs."fixture_id"
        ,COALESCE(DATEADD(minute, -15, km."feed_start_timestamp"), won."won_tile_start_plan", DATEADD(minute, -30, won."fixture_date"), TO_TIMESTAMP('1970-01-01')) as "window_start_timestamp" --testing in keymoments for the datatypes timestamps
        ,COALESCE(DATEADD(minute, 60, km."feed_end_timestamp"), TO_TIMESTAMP('9999-01-01')) as "window_end_timestamp"
        ,km."feed_start_timestamp" as "key_moments_start_timestamp"
        ,km."feed_end_timestamp" AS "key_moments_end_timestamp"
        ,won."won_tile_start_plan" AS "won_start_timestamp"
        ,won."fixture_date"
    FROM logs
    LEFT JOIN km
        ON logs."fixture_id" = km."fixture_id"
    LEFT JOIN won
        ON logs."fixture_id" =  won."fixture_id"
)

SELECT * FROM fixture_window
