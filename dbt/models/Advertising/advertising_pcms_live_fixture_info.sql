{{
config(
    materialized='table',
    schema='TRANSIENT',
    database='ADVERTISING__B2C__MART__' + snowflake_env(),
    snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
    tags=['presentation-advertising-logs']
)
}}

WITH curated_pcms AS (
    SELECT 
        * 
    FROM 
        {{ source('CURATED', 'CURATED__DOTS__PCMS_LIVESTREAM') }}
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "fixture_id", "region" ORDER BY "lastModTime" DESC, "EDM_INSERT_DTTS"::TIMESTAMP DESC) = 1
)

SELECT 
    "payloadId"
    ,"fixture_id"
    ,{{ advertising_map_territory("region") }} AS "territory"
    ,"daznChannelId"
    ,"feedStartTime"
    ,"feedEndTime"
    ,"streamStartTime"
    ,"streamEndTime"
FROM curated_pcms
