{{
config(
    materialized='table',
    schema='TRANSIENT',
    database='ADVERTISING__B2C__MART__' + snowflake_env(),
    snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
    tags=['presentation-advertising-logs']
)
}}

/*
    This model provides PCMS live fixture information including daznChannelId.

    PCMS (Program Content Management System) contains information about content
    being transmitted during playback, including channel configuration data.

    This model serves as the source for channel extraction logic used in
    advertising channel selection.

    Note: This model currently uses a placeholder structure. You will need to:
    1. Update the source reference to point to your actual PCMS data source
    2. Adjust column mappings based on your actual PCMS schema
    3. Add any necessary filtering or transformation logic
*/

-- TODO: Replace this with your actual PCMS data source
-- Example sources might be:
-- - {{ source('CURATED', 'CURATED__PCMS__LIVE_FIXTURE_INFO') }}
-- - {{ ref('staging__pcms__live_fixture_info') }}
-- - {{ source('PCMS', 'LIVE_FIXTURE_INFO') }}

WITH pcms_source AS (
    -- TEMPORARY: Using content dimension as a proxy until actual PCMS source is configured
    -- This allows the model to compile and provides a structure for testing
    SELECT
        content."fixture_id"
        ,'12345' || content."fixture_id" AS "daznChannelId"  -- Placeholder channel ID
        ,content."fixture_id" AS "content_id"
        ,content."fixture_start_timestamp" AS "transmission_start"
        ,DATEADD('hour', 3, content."fixture_start_timestamp") AS "transmission_end"
        ,'Global' AS "territory"
        ,'LINEAR' AS "delivery_type"
    FROM {{ ref('mfl_fixture__dim') }} content
    WHERE content."fixture_id" IS NOT NULL
        AND content."fixture_start_timestamp" >= DATEADD('day', -7, CURRENT_DATE())  -- Last 7 days only
    LIMIT 0  -- This ensures no data is returned until actual PCMS source is configured

    -- TODO: Uncomment and modify one of these examples based on your actual source:

    -- Example 1: Direct source reference
    -- SELECT
    --     "fixture_id"
    --     ,"daznChannelId"
    --     ,"content_id"
    --     ,"transmission_start_timestamp" AS "transmission_start"
    --     ,"transmission_end_timestamp" AS "transmission_end"
    --     ,"territory"
    --     ,"delivery_type"
    -- FROM {{ source('CURATED', 'CURATED__PCMS__LIVE_FIXTURE_INFO') }}

    -- Example 2: Staging table reference
    -- SELECT
    --     "fixture_id"
    --     ,"dazn_channel_id" AS "daznChannelId"
    --     ,"content_id"
    --     ,"start_time" AS "transmission_start"
    --     ,"end_time" AS "transmission_end"
    --     ,"territory"
    --     ,"delivery_type"
    -- FROM {{ ref('staging__pcms__live_fixture_info') }}

    -- Example 3: Content dimension with PCMS data
    -- SELECT
    --     content."fixture_id"
    --     ,content."dazn_channel_id" AS "daznChannelId"
    --     ,content."article_id" AS "content_id"
    --     ,content."transmission_start_timestamp" AS "transmission_start"
    --     ,content."transmission_end_timestamp" AS "transmission_end"
    --     ,content."territory"
    --     ,content."delivery_type"
    -- FROM {{ ref('mart__content_item__dim') }} content
    -- WHERE content."content_item_origin" = 'PCMS'
)

SELECT
    "fixture_id"
    ,"daznChannelId"
    ,"content_id"
    ,"transmission_start"
    ,"transmission_end"
    ,"territory"
    ,"delivery_type"
    ,CURRENT_TIMESTAMP() AS "record_loaded_timestamp"
FROM pcms_source
WHERE "fixture_id" IS NOT NULL
    AND "daznChannelId" IS NOT NULL
