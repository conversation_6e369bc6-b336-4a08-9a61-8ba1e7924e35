{{
config(
    materialized='table',
    schema='FACT',
    database='ADVERTISING__B2C__MART__' + snowflake_env(),
    snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
    tags=['presentation-advertising-logs']
)
}}

WITH logs AS (
    SELECT * FROM {{ ref('advertising_channel_filter') }}
)

, pl_mapping AS (
    SELECT * FROM {{ ref('advertising_pl_mapping') }}
)

SELECT
    logs."fixture_id"
    ,logs."territory"
    ,logs."ad_date"
    ,logs."ad_start_timestamp"
    ,logs."ad_end_timestamp"
    ,logs."duration"
    ,logs."creative"
    ,logs."line_item_name"
    ,logs."line_item_id"
    ,logs."delivery_type"
    ,logs."event_type"
    ,logs."break_type"
    ,logs."ad_type"
    ,{{ advertising_compute_pl() }} AS "pl"
    -- macros defined in dbt/macros/Advertising/
    ,{{ advertising_compute_advertiser() }} AS "advertiser"
    ,{{ advertising_compute_campaign() }} AS "campaign"
    ,CASE WHEN logs."ad_type" IN ('House Ad', 'Legal Sting', 'Promo') THEN 'DAZN' ELSE pl_mapping."brand_industry" END AS "brand_industry"
    ,CASE WHEN logs."ad_type" IN ('House Ad', 'Legal Sting', 'Promo') THEN 'DAZN' ELSE pl_mapping."brand_category" END AS "brand_category"
    ,CASE WHEN logs."ad_type" IN ('House Ad', 'Legal Sting', 'Promo') THEN 'DAZN' ELSE pl_mapping."io" END AS "io"
    ,logs."channel"
    ,logs."source"
    ,logs."primary_key"
FROM logs
LEFT JOIN pl_mapping
    ON pl_mapping."pl" = logs."pl"
