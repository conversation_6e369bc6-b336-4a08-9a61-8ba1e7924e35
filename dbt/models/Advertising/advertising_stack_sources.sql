{{
    config(
        materialized='table',
        schema='TRANSIENT',
        database='ADVERTISING__B2C__MART__' + snowflake_env(),
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['presentation-advertising-logs']
    )
}}

WITH grass_valley AS (
    SELECT * FROM {{ ref('advertising_filter_grass_valley') }}
)

, cloud_tx AS (
    SELECT * FROM {{ ref('advertising_filter_cloud_tx') }}
)

, m2a AS (
    SELECT * FROM {{ ref('advertising_filter_m2a') }}
)

, stack_sources AS (
    SELECT
         "fixture_id"
        ,"territory"
        ,"ad_date"
        ,"ad_start_timestamp"
        ,"ad_end_timestamp"
        ,"duration"
        ,"creative"
        ,"line_item_name"
        ,NULL AS "line_item_id"
        ,"delivery_type"
        ,"break_type"
        ,NULL AS "event_type"
        ,"channel"
        ,"pl"
        ,NULL AS "advertiser"
        ,"primary_key"
        ,'cloud_tx' AS "source"
    FROM cloud_tx

    UNION

    SELECT
        "fixture_id"
        ,"territory"
        ,"ad_date"
        ,"ad_start_timestamp"
        ,"ad_end_timestamp"
        ,"duration"
        ,"creative"
        ,"line_item_name"
        ,NULL AS "line_item_id"
        ,"delivery_type"
        ,"break_type"
        ,"event_type"
        ,"channel"
        ,NULL as "pl"
        ,NULL AS "advertiser"
        ,"primary_key"
        ,'grass_valley' AS "source"
    FROM grass_valley

    UNION

    SELECT
        "fixture_id"
        ,"territory"
        ,"ad_date"
        ,"ad_start_timestamp"
        ,"ad_end_timestamp"
        ,"duration"
        ,"creative"
        ,"line_item_name"
        ,"gam_id" as "line_item_id"
        ,NULL AS "delivery_type"
        ,"break_type"
        ,NULL AS "event_type"
        ,-2 AS "channel" -- we assign a fake channel to M2A to avoid issues with types.
        ,NULL AS "pl"
        ,"advertiser"
        ,"primary_key"
        ,'m2a' AS "source"
    FROM m2a
)

SELECT
    "fixture_id"
    ,"territory"
    ,"ad_date"
    ,"ad_start_timestamp"
    ,"ad_end_timestamp"
    ,"duration"
    ,"creative"
    ,"line_item_name"
    ,"line_item_id"
    ,"delivery_type"
    ,"event_type"
    ,"break_type"
    -- macros defined in dbt/macros/Advertising/
    ,{{ advertising_compute_ad_type() }} AS "ad_type"
    ,"pl"
    ,"advertiser"
    ,"channel"
    ,"source"
    ,"primary_key"
FROM stack_sources

