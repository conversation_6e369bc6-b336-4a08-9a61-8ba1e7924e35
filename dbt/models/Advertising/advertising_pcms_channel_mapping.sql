{{
config(
    materialized='table',
    schema='TRANSIENT',
    database='ADVERTISING__B2C__MART__' + snowflake_env(),
    snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
    tags=['presentation-advertising-logs']
)
}}

WITH pcms_live_fixture_info AS (
    SELECT * FROM {{ ref('advertising_pcms_live_fixture_info') }}
)

SELECT
    "fixture_id"
    ,"territory"
    ,"daznChannelId"
    ,"feedStartTime"
    ,"feedEndTime"
    ,"streamStartTime"
    ,"streamEndTime"
    ,CASE
        WHEN RIGHT("daznChannelId", 2)::INT BETWEEN 1 AND 10 THEN LPAD(RIGHT("daznChannelId", 2)::STRING, 2, '0')
        WHEN RIGHT("daznChannelId", 2)::INT BETWEEN 11 AND 26 THEN LPAD((RIGHT("daznChannelId", 2)::INT - 10)::STRING, 2, '0')
        WHEN RIGHT("daznChannelId", 2)::INT BETWEEN 81 AND 90 THEN LPAD((RIGHT("daznChannelId", 2)::INT - 78)::STRING, 2, '0')
        ELSE RIGHT("daznChannelId", 2)
    END AS "extracted_channel"
FROM pcms_live_fixture_info
WHERE "daznChannelId" IS NOT NULL
