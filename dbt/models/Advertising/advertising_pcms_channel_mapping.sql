{{
config(
    materialized='table',
    schema='TRANSIENT',
    database='ADVERTISING__B2C__MART__' + snowflake_env(),
    snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
    tags=['presentation-advertising-logs']
)
}}

/*
    This model extracts channel information from PCMS data based on daznChannelId.
    
    The channel extraction logic transforms the last 2 digits of daznChannelId:
    - Values 1-10: Use as-is, left-padded with zeros to 2 digits
    - Values 11-26: Subtract 10, then left-pad with zeros to 2 digits  
    - Values 81-90: Subtract 78, then left-pad with zeros to 2 digits
    - All other values: Use the last 2 digits as-is
    
    This extracted channel value is used to filter advertising logs to only include
    channels that match the PCMS channel configuration.
*/

WITH gmr_articles AS (
    SELECT * FROM {{ ref('gmr_article__dim') }}
    WHERE "live_media_dazn_channel_id" IS NOT NULL
)

, mfl_fixtures AS (
    SELECT * FROM {{ ref('mfl_fixture__dim') }}
)

, pcms_channel_extraction AS (
    SELECT
        gmr."fixture_id"
        ,gmr."article_id"
        ,gmr."live_media_dazn_channel_id" AS "daznChannelId"
        ,gmr."article_territory"
        ,gmr."live_media_live_stream_start_timestamp"
        ,gmr."live_media_live_stream_end_timestamp"
        -- Apply the channel extraction logic
        ,CASE
            WHEN RIGHT(gmr."live_media_dazn_channel_id", 2)::INT BETWEEN 1 AND 10 
                THEN LPAD(RIGHT(gmr."live_media_dazn_channel_id", 2)::STRING, 2, '0')
            WHEN RIGHT(gmr."live_media_dazn_channel_id", 2)::INT BETWEEN 11 AND 26 
                THEN LPAD((RIGHT(gmr."live_media_dazn_channel_id", 2)::INT - 10)::STRING, 2, '0')
            WHEN RIGHT(gmr."live_media_dazn_channel_id", 2)::INT BETWEEN 81 AND 90 
                THEN LPAD((RIGHT(gmr."live_media_dazn_channel_id", 2)::INT - 78)::STRING, 2, '0')
            ELSE RIGHT(gmr."live_media_dazn_channel_id", 2)
        END AS "extracted_channel"
        -- Add metadata for debugging and auditing
        ,RIGHT(gmr."live_media_dazn_channel_id", 2)::INT AS "channel_id_last_two_digits"
        ,CURRENT_TIMESTAMP() AS "record_created_timestamp"
    FROM gmr_articles gmr
    LEFT JOIN mfl_fixtures mfl
        ON gmr."fixture_id" = mfl."fixture_id"
    WHERE gmr."fixture_id" IS NOT NULL
        AND gmr."live_media_dazn_channel_id" IS NOT NULL
        AND LENGTH(gmr."live_media_dazn_channel_id") >= 2  -- Ensure we have at least 2 characters
)

SELECT
    "fixture_id"
    ,"article_id"
    ,"daznChannelId"
    ,"article_territory"
    ,"live_media_live_stream_start_timestamp"
    ,"live_media_live_stream_end_timestamp"
    ,"extracted_channel"
    ,"channel_id_last_two_digits"
    ,"record_created_timestamp"
FROM pcms_channel_extraction
-- Ensure we only have one record per fixture_id (in case of multiple articles per fixture)
QUALIFY ROW_NUMBER() OVER (
    PARTITION BY "fixture_id" 
    ORDER BY "live_media_live_stream_start_timestamp" DESC, "record_created_timestamp" DESC
) = 1
