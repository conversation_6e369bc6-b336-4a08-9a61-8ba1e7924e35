{{
config(
    materialized='table',
    schema='TRANSIENT',
    database='ADVERTISING__B2C__MART__' + snowflake_env(),
    snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
    tags=['presentation-advertising-logs']
)
}}

/*
    This model extracts channel information from PCMS data based on daznChannelId.
    
    The channel extraction logic transforms the last 2 digits of daznChannelId:
    - Values 1-10: Use as-is, left-padded with zeros to 2 digits
    - Values 11-26: Subtract 10, then left-pad with zeros to 2 digits  
    - Values 81-90: Subtract 78, then left-pad with zeros to 2 digits
    - All other values: Use the last 2 digits as-is
    
    This extracted channel value is used to filter advertising logs to only include
    channels that match the PCMS channel configuration.
*/

WITH pcms_live_fixture_info AS (
    SELECT * FROM {{ ref('advertising_pcms_live_fixture_info') }}
)

, pcms_channel_extraction AS (
    SELECT
        "fixture_id"
        ,"daznChannelId"
        ,CASE
            WHEN RIGHT("daznChannelId", 2)::INT BETWEEN 1 AND 10 
                THEN LPAD(RIGHT("daznChannelId", 2)::STRING, 2, '0')
            WHEN RIGHT("daznChannelId", 2)::INT BETWEEN 11 AND 26 
                THEN LPAD((RIGHT("daznChannelId", 2)::INT - 10)::STRING, 2, '0')
            WHEN RIGHT("daznChannelId", 2)::INT BETWEEN 81 AND 90 
                THEN LPAD((RIGHT("daznChannelId", 2)::INT - 78)::STRING, 2, '0')
            ELSE RIGHT("daznChannelId", 2)
        END AS "extracted_channel"
        -- Add metadata for debugging and auditing
        ,RIGHT("daznChannelId", 2)::INT AS "channel_id_last_two_digits"
        ,CURRENT_TIMESTAMP() AS "record_created_timestamp"
    FROM pcms_live_fixture_info
    WHERE "daznChannelId" IS NOT NULL
        AND "fixture_id" IS NOT NULL
)

SELECT
    "fixture_id"
    ,"daznChannelId"
    ,"extracted_channel"
    ,"channel_id_last_two_digits"
    ,"record_created_timestamp"
FROM pcms_channel_extraction
-- Ensure we only have one record per fixture_id
QUALIFY ROW_NUMBER() OVER (PARTITION BY "fixture_id" ORDER BY "record_created_timestamp" DESC) = 1
