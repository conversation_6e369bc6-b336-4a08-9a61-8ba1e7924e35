{{
config(
    materialized='table',
    schema='TRANSIENT',
    database='ADVERTISING__B2C__MART__' + snowflake_env(),
    snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
    tags=['presentation-advertising-logs']
)
}}

WITH curated_pcms AS (
    SELECT
        *
    FROM
        {{ source('CURATED', 'CURATED__DOTS__PCMS_LIVESTREAM') }}
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "fixture_id", "region" ORDER BY "lastModTime" DESC, "EDM_INSERT_DTTS"::TIMESTAMP DESC) = 1
)

SELECT
    "payloadId"
    ,"fixture_id"
    ,{{ advertising_map_territory("region") }} AS "territory"
    ,"daznChannelId"
    ,"feedStartTime"
    ,"feedEndTime"
    ,"streamStartTime"
    ,"streamEndTime"
    ,CASE
        WHEN RIGHT("daznChannelId", 2)::INT BETWEEN 1 AND 10 THEN LPAD(RIGHT("daznChannelId", 2)::STRING, 2, '0')
        WHEN RIGHT("daznChannelId", 2)::INT BETWEEN 11 AND 26 THEN LPAD((RIGHT("daznChannelId", 2)::INT - 10)::STRING, 2, '0')
        WHEN RIGHT("daznChannelId", 2)::INT BETWEEN 81 AND 90 THEN LPAD((RIGHT("daznChannelId", 2)::INT - 78)::STRING, 2, '0')
        ELSE RIGHT("daznChannelId", 2)
    END AS "extracted_channel"
FROM curated_pcms
WHERE "daznChannelId" IS NOT NULL
