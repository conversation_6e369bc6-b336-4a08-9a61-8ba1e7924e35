version: 2

models:
  - name: advertising_won_encodings
    description: "This model will collect the breakId from the won encodings time allocations. These are then used to enrich the M2A source model."
    columns:
      - name: time_allocations_alloc_id
        description: "allocation id from the dots_won_timeallocations table (this comes from liveencoding)"
        quote: true

      - name: live_encoding_id
        description: "The live encoding id of this record"
        quote: true

      - name: live_event_id
        description: "The live event id of this record"
        quote: true

      - name: region_uuid
        description: "The region id of this record"
        quote: true

      - name: splice_event_id
        description: "The splice event from the time allocations, this is used for the join to the m2a records"
        quote: true

      - name: time_allocations_name
        description: "The name of the time allocation, this enriches the break id in the m2a table"
        quote: true

      - name: fixture_id
        description: "The fixture id of this event, it's a combination of the dummy fixture id from live_events and the fixture-id from live_encoding"
        quote: true

      - name: primary_key
        description: "The primary key of this table."
        quote: true
        tests:
          - unique

  - name: advertising_filter_grass_valley
    description: "This step reads from the source as runs table and filters out non ads related logs. We also apply some cleaning to the raw logs"
    columns:
      - &fixture_id
        name: fixture_id
        description: "The fixture id that this log played for"
        quote: true

      - &territory
        name: territory
        description: "The territory that this log played for"
        quote: true

      - &ad_date
        name: ad_date
        description: "The date that the ad log played on"
        quote: true

      - &ad_start_timestamp
        name: ad_start_timestamp
        description: "The time the ad started playing at"
        quote: true

      - &ad_end_timestamp
        name: ad_end_timestamp
        description: "The time the ad started playing at"
        quote: true

      - &duration
        name: duration
        description: "The duration, in seconds, of the ad"
        quote: true

      - &creative
        name: file_name
        description: "The file name of this log, or the creative in some datasets. This is one of the two fields that describe the ad"
        quote: true

      - &line_item_name
        name: line_item_name
        description: "The ad name. This is one of the two fields that describe the ad"
        quote: true

      - &delivery_type
        name: delivery_type
        description: "The type of the log, for example OTT, LINEAR, ITALY_LINEAR_1080P..."
        quote: true

      - &event_type
        name: event_type
        description: "The event type of this log."
        quote: true

      - &break_type
        name: break_type
        description: "The break type of this log."
        quote: true

      - &channel
        name: channel
        description: "The channel on which the ad played on"
        quote: true

      - &as_run_file_name
        name: as_run_file_name
        description: "The as run file name that contains this log, usually logs for the same fixture will be contained by the same as run file name."
        quote: true

      - &primary_key
        name: primary_key
        description: "The primary key of this log source, defined in the curated later"
        quote: true
        tests:
          - unique
          - not_null

  - name: advertising_filter_m2a
    description: "This step will use the timestamps derived in the grass_valley_window_ms model to cut ads that happen before/after the start/end of the calculated window."
    columns:
      - <<: *fixture_id
        description: "renamed from client content id, the fixture id of this log."
      - *territory
      - *ad_date
      - *ad_start_timestamp
      - *ad_end_timestamp
      - *duration
      - <<: *line_item_name
        description: "The line item name of this log. This originates in gam, if no record is found in gam, we use the fixture id instead"
      - <<: *creative
        description: "The creative of this log. Renamed from content_id in M2A"
      - <<: *break_type
        description: "The break-type of the log. Derived from the break-id field and a counter attached to it relative to the order in which it was played out."

      - name: gam_id
        description: "The gam ID of this log. Used to join to the GAM table and derive the line_item_name"
        quote: true

      - &advertiser
        name: advertiser
        description: "The advertiser of this log. Only populated for M2A logs until the pl_mapping join step."
        quote: true

      - name: streaming_protocol
        description: "The streaming protocol of this log"
        quote: true

      - name: origin_data_centre
        description: "The dataserver of this log, should be unique per fixture ID."
        quote: true

      - name: origin_server
        description: "Derived from the data centre. It's a shorthand for that field"
        quote: true

      - *primary_key

  - name: advertising_filter_cloud_tx
    description: "This step will use the timestamps derived in the grass_valley_window_ms model to cut ads that happen before/after the start/end of the calculated window."
    columns:
      - *fixture_id
      - *territory
      - *ad_date
      - *ad_start_timestamp
      - *ad_end_timestamp
      - *duration
      - <<: *creative
        description: "The creative of this log, renamed from asset_id in cloud tx"
      - <<: *line_item_name
        description: "The line item name of this log. Renamed from title in cloud tx"
      - *delivery_type
      - *break_type
      - *channel

      - name: pl
        description: "The pl code for this log"
        quote: true

      - *primary_key

  - name: advertising_stack_sources
    description: "This step produces a time window to filter for ads which are actually played out and viewable on the platfrom. It uses Key Moments, WON as parameters"
    columns:
      - *fixture_id
      - *territory
      - *ad_date
      - *ad_start_timestamp
      - *ad_end_timestamp
      - *duration
      - *creative
      - *line_item_name

      - &line_item_id
        name: line_item_id
        description: "Only available from M2a logs. This is equivalent to the gam ID."
        quote: true

      - *delivery_type
      - *event_type
      - *break_type

      - name: promo_substring
        description: "A field used to derive ad_types and pl codes"
        quote: true

      - &ad_type
        name: ad_type
        description: "Defined in the stack_sources model. This can be a commercial, promo, sponsor, bumper or gap media. Used downstream to select relevant ads."
        quote: true

      - &pl
        name: pl
        description: "Defined in the stack_sources model. The PL number of this log. Enriched with pl_mapping model."
        quote: true

      - *advertiser
      - *channel

      - &source
        name: source
        description: "Defined in the stack_sources model. Identifies which source this logs comes from. Can be m2a, grass_valley or cloud_tx"
        quote: true

      - *primary_key

  - name: advertising_time_windows
    description: "This step produces a time window to filter for ads which are actually played out and viewable on the platfrom. It uses Key Moments, WON as parameters"
    columns:
      - *fixture_id

      - name: window_start_timestamp
        description: "Timestamp derived from a combination of key moments and won. This is used to filter out any logs happening before it."
        quote: true

      - name: window_end_timestamp
        description: "Timestamp derived from a combination of key moments and won. This is used to filter out any logs happening after it."
        quote: true

      - name: key_moments_start_timestamp
        description: "Fixture start timestamp looked up from key moments"
        quote: true

      - name: key_moments_end_timestamp
        description: "Fixture end timestamp looked up from key moments"
        quote: true

      - name: won_start_timestamp
        description: "Fixture start timestamp looked up from WON"
        quote: true

      - name: fixture_date
        description: "Fixture start timestamp looked up from WON"
        quote: true

  - name: advertising_window_filter
    description: "This step will use the timestamps derived in the time_windows model to cut ads that happen before/after the start/end of the calculated window."
    columns:
      - *fixture_id
      - *territory
      - *ad_date
      - *ad_start_timestamp
      - *ad_end_timestamp
      - *duration
      - *creative
      - *line_item_name
      - *line_item_id
      - *delivery_type
      - *event_type
      - *break_type
      - *ad_type
      - *pl
      - *advertiser
      - *channel
      - *source
      - *primary_key

  - name: advertising_channel_selection
    description: "This model selects one channel and one source for each combination of fixture and territory. This is done via heuristics. see comments in sql for more information."
    columns:
      - *fixture_id
      - *territory
      - *source
      - *channel
      - *delivery_type

      - name: delivery_type_ranking
        description: "Metric used to establish a priority based on delivery type. See docs in the code."
        quote: true

      - name: n_ads
        description: ""
        quote: true

      - name: n_ads_gv
        description: ""
        quote: true

      - name: n_ads_ctx
        description: ""
        quote: true

      - name: n_ads_m2a
        description: "Number of M2A commercial ads for this fixture/territory/channel combination"
        quote: true

      - name: pcms_channel_match
        description: "Indicator (1/0) whether the channel matches the PCMS extracted channel value"
        quote: true

      - name: channel_effective_from
        description: "Timestamp from which this channel selection is effective"
        quote: true

      - name: channel_effective_until
        description: "Timestamp until which this channel selection is effective"
        quote: true

  - name: advertising_channel_filter
    description: "This model selects one channel and one source for each combination of fixture and territory. This is done via heuristics. see comments in sql for more information."
    columns:
      - *fixture_id
      - *territory
      - *ad_date
      - *ad_start_timestamp
      - *ad_end_timestamp
      - *duration
      - *creative
      - *line_item_name
      - *line_item_id
      - *delivery_type
      - *event_type
      - *break_type
      - *ad_type
      - *pl
      - *advertiser
      - *channel
      - *source
      - *primary_key

  - name: advertising_logs
    description: "This model selects one channel and one source for each combination of fixture and territory. This is done via heuristics. see comments in sql for more information."
    columns:
      - *fixture_id
      - *territory
      - *ad_date
      - *ad_start_timestamp
      - *ad_end_timestamp
      - *duration
      - *creative
      - *line_item_name
      - *line_item_id
      - *delivery_type
      - *event_type
      - *break_type
      - *ad_type
      - *pl
      - <<: *advertiser
        description: "Advertiser of this log. Combination of the advertiser in the pl_mapping step and the advertiser field in the M2A logs from GAM in filter_m2a."

      - name: campaign
        description: "Defined in the stack_sources model. The campaign name of this log. Enriched with pl_mapping model."
        quote: true

      - name: io
        description: "Defined in the stack_sources model. The IO of this log. Originates from the pl_mapping model."
        quote: true

      - name: brand_industry
        description: "Defined in the stack_sources model. The brand industry of this log. Originates from the pl_mapping model."
        quote: true

      - name: brand_category
        description: "Defined in the stack_sources model. The brand category of this log. Originates from the pl_mapping model."
        quote: true

      - *channel
      - *source
      - *primary_key

  - name: advertising_pl_mapping
    description: "This model selects one channel and one source for each combination of fixture and territory. This is done via heuristics. see comments in sql for more information."
    columns:
      - name: io
        description: ""
        quote: true

      - name: pl
        description: ""
        quote: true

      - name: campaign_name
        description: ""
        quote: true

      - name: advertiser
        description: ""
        quote: true

      - name: brand_industry
        description: ""
        quote: true

      - name: brand_category
        description: ""
        quote: true

  - name: advertising_pcms_channel_mapping
    description: "Combined model that sources PCMS live fixture information from CURATED__DOTS__PCMS_LIVESTREAM and extracts standardized channel values using transformation logic. Used to filter advertising logs to matching channels."
    columns:
      - name: payloadId
        description: "Unique identifier for the payload"
        quote: true

      - name: fixture_id
        description: "MFL fixture identifier"
        quote: true

      - name: territory
        description: "Territory/region mapped from PCMS region field using advertising_map_territory macro"
        quote: true

      - name: daznChannelId
        description: "Original DAZN channel identifier from PCMS"
        quote: true

      - name: feedStartTime
        description: "Feed start timestamp"
        quote: true

      - name: feedEndTime
        description: "Feed end timestamp"
        quote: true

      - name: streamStartTime
        description: "Stream start timestamp"
        quote: true

      - name: streamEndTime
        description: "Stream end timestamp"
        quote: true

      - name: extracted_channel
        description: "Standardized channel value extracted from daznChannelId using transformation logic (1-10: pad, 11-26: subtract 10 then pad, 81-90: subtract 78 then pad)"
        quote: true
