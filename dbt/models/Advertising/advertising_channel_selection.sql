{{
config(
    materialized='table',
    schema='TRANSIENT',
    database='ADVERTISING__B2C__MART__' + snowflake_env(),
    snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
    tags=['presentation-advertising-logs']
)
}}

/*
    The purpose of this model is to select the correct channel for a given fixture_id, territory combination.

    It will use a set of heuristics to determine which channel to select:
        - Choose M2A logs if any are available. (m2a has no channel)
        - For italy serie A we prioritise the delivery type = LINEAR_ITALY_1080P
        - Anything else we prioritise OTT
        - Choose the channel with the greater number of commercials

    The output will be used to filter out unwanted channels/sources from the ad logs.
*/

WITH logs AS (SELECT * FROM  {{ ref('advertising_window_filter') }} )

, content AS (
    SELECT * FROM {{ ref('mfl_fixture__dim') }}
)

-- derive metrics that we can use later to establish a ranking.
,channel_count AS (
    SELECT
        logs."fixture_id"
        ,logs."territory"
        ,logs."source"
        ,logs."channel"
        ,logs."delivery_type"
        ,CASE
            -- For italy & Serie A we want to prioritise the linear_italy_1080p first
            -- Italy, Soccer, Serie A -> 1r097lpxe0xn03ihb7wi98kao
            WHEN "territory" = 'Italy' AND content."competition_id" = '1r097lpxe0xn03ihb7wi98kao' AND "delivery_type" = 'LINEAR' THEN 0
            -- DACH, Soccer, Bundesliga -> 6by3h89i2eykc341oz7lv1ddd
            WHEN "territory" = 'DACH' AND content."competition_id" = '6by3h89i2eykc341oz7lv1ddd' AND "delivery_type" = 'LINEAR' THEN 0
            -- DACH, Soccer, UEFA Champions League -> 4oogyu6o156iphvdvphwpck10
            WHEN "territory" = 'DACH' AND content."competition_id" = '4oogyu6o156iphvdvphwpck10' AND "delivery_type" = 'LINEAR' THEN 0
            -- For everything else (italy non-serieA included) we prioritise OTT
            WHEN "delivery_type" = 'OTT' THEN 0
            ELSE 1
         END AS "delivery_type_ranking"
        ,count_if(logs."ad_type" = 'Commercial') AS "n_ads"
        ,count_if(logs."ad_type" = 'Commercial' AND logs."source" = 'grass_valley') AS "n_ads_gv"
        ,count_if(logs."ad_type" = 'Commercial' AND logs."source" = 'cloud_tx') AS "n_ads_ctx"
        ,count_if(logs."ad_type" = 'Commercial' AND logs."source" = 'm2a') AS "n_ads_m2a"
    FROM logs
    LEFT JOIN content
        ON content."fixture_id" = logs."fixture_id"
    GROUP BY ALL
)

-- TODO put streamlit CTE here

-- TODO, this is a template for the final query once the streamlit app is developed.
-- SELECT
--     "fixture_id"
--     ,"territory"
--     ,COALESCE("streamlit_channel", "channel") AS "channel"
--     ,COALESCE("streamlit_effective_from", TO_TIMESTAMP('1970-01-01')) AS "channel_effective_from"
--     ,COALESCE("streamlit_effective_until", TO_TIMESTAMP('9999-12-31')) AS "channel_effective_until"
-- FROM union_all
-- LEFT JOIN streamlit
--     ON "fixture_id" = "streamlit_fixture_id"
--     AND "territory" = "streamlit_territory"

-- define the ranking and qualify by it to ensure only one row per fixture, territory
SELECT
    "fixture_id"
    ,"territory"
    ,"source"
    ,"channel"
    ,"delivery_type"
    ,"delivery_type_ranking"
    ,TO_TIMESTAMP('1970-01-01') AS "channel_effective_from"
    ,TO_TIMESTAMP('9999-12-31') AS "channel_effective_until"
    ,"n_ads"
    ,"n_ads_gv"
    ,"n_ads_ctx"
    ,"n_ads_m2a"
FROM channel_count
QUALIFY ROW_NUMBER() OVER (PARTITION BY "fixture_id", "territory" ORDER BY  "n_ads_m2a" DESC, "delivery_type_ranking", "n_ads" DESC, "n_ads_gv" DESC, "n_ads_ctx" DESC) = 1
ORDER BY "fixture_id"
