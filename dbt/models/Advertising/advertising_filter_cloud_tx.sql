{{
    config(
        materialized='table',
        schema='TRANSIENT',
        database='ADVERTISING__B2C__MART__' + snowflake_env(),
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['presentation-advertising-logs']
    )
}}

-- This macro is defined in dazn-dp20-presentation/dbt/macros/Advertising/advertisement_variables.sql
{% set advertising_variables = advertising_variables() %}

WITH curated_cloud_tx AS (SELECT * FROM {{ source_env('CURATED', 'CURATED__CLOUD_TX__CURRENT') }})


SELECT
     "fixture_id"
    ,{{ advertising_map_territory("territory") }} AS "territory"
    ,"ad_date"
    ,DATE_TRUNC(SECOND,"ad_start_timestamp") as "ad_start_timestamp"
    ,DATE_TRUNC(SECOND,"ad_end_timestamp") as "ad_end_timestamp"
    ,"duration"
    ,"asset_id" AS "creative"
    ,"title" as "line_item_name"
    ,"log_type" as "delivery_type"
    ,"break_type"
    ,"channel"
    ,"plnumber" as "pl"
    ,"primary_key"
FROM curated_cloud_tx
WHERE "ad_date" >= '{{ advertising_variables.start_date }}'
