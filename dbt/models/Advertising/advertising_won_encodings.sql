{{
    config(
        materialized='table',
        schema='TRANSIENT',
        database='ADVERTISING__B2C__MART__' + snowflake_env(),
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['presentation-advertising-logs']
    )
}}

/*

This step uses tables from WON (liveevent, liveencoding, region)
It will join the three tables together and provide an output that can enrich the breakId coming from M2A.

The fields used in the downstream step are:
- splice_event_id: used for the join condition
- fixture_id: used for the join condition
- region_m2a_region_key: used for the join condition
- time_allocations_name: used to enrich the breakId from M2a
*/

with won_live_event AS (
    SELECT * FROM {{ ref('staging__dots__won_liveevent_current') }}
    WHERE "payload_state" = 'ACTIVE'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "live_encoding_id", "region_id" ORDER BY "live_event_commentary_languages_code" DESC NULLS LAST) = 1
)

, won_region AS (
    SELECT * FROM {{ ref('staging__dots__won_region_current') }}
)

, won_time_allocations AS (
    SELECT * FROM {{ ref('staging__dots__won_timeallocations_current') }}
    WHERE "payload_state" = 'ACTIVE'
)

, won_live_encoding AS (
    SELECT * FROM {{ ref('staging__dots__won_timeallocations_current') }}
    WHERE "payload_state" = 'ACTIVE'
)

, processing AS (
    SELECT
        won_time_allocations."time_allocations_allocation_id"
        ,won_time_allocations."live_encoding_id"
        ,won_live_event."live_event_id"
        ,won_live_event."region_id"
        ,COALESCE(
            won_time_allocations."time_allocations_secondary_events_splice_event_id_0"
            ,won_time_allocations."time_allocations_secondary_events_splice_event_id_1"
            ) AS "splice_event_id"
        ,won_time_allocations."time_allocations_name"
        ,won_region."region_m2a_key"
        ,IFNULL(won_live_event."dummy_fixture_id", won_time_allocations."fixture_id") AS "fixture_id_f"
        ,HASH(won_time_allocations."time_allocations_allocation_id",won_live_event."region_id") as "primary_key"
        ,HASH(won_region."region_m2a_key", "fixture_id_f", "splice_event_id") as "join_key"
    from won_time_allocations
    left join won_live_event
        on won_time_allocations."live_encoding_id" = won_live_event."live_encoding_id"
    left join won_region
        on won_region."region_id" = won_live_event."region_id"
    -- if the join fails on live_event_id then we can't use those records in the join in the next step
    where won_live_event."live_event_id" is not null
    -- same for the region uuid
    and won_region."region_id" is not null
    and won_time_allocations."time_allocations_type" LIKE '%AD%'
    and won_region."region_m2a_key" is not null
    and "splice_event_id" is not null
    and "fixture_id_f" is not null
    -- we need the join key for the next step to be unique, otherwise we causing duplicates in the m2a logs
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "join_key" ORDER BY "primary_key") = 1
)

SELECT
    "time_allocations_allocation_id"
    ,"live_encoding_id"
    ,"live_event_id"
    ,"region_id"
    ,"splice_event_id"
    ,"time_allocations_name"
    ,"region_m2a_key"
    ,"fixture_id_f" AS "fixture_id"
    ,"primary_key"
    ,"join_key"
FROM processing
