{{
    config(
        materialized='table',
        schema='TRANSIENT',
        database='ADVERTISING__B2C__MART__' + snowflake_env(),
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['presentation-advertising-logs']
    )
}}

-- This macro is defined in dazn-dp20-presentation/dbt/macros/Advertising/advertisement_variables.sql
{% set advertising_variables = advertising_variables() %}

WITH m2a_source AS ( SELECT * FROM {{ source_env('CURATED','CURATED__M2A__CURRENT') }} )

, won_encodings AS ( SELECT * FROM {{ ref('advertising_won_encodings') }} )

, gam_line_item_names AS ( SELECT * FROM {{ source_env('CURATED','CURATED__GAM_MATCH_TABLE_LINE_ITEM__CURRENT') }} )

, gam_orders AS ( SELECT * FROM {{ source_env('CURATED','CURATED__GAM_MATCH_TABLE_ORDER__CURRENT') }} )

, gam_companies AS ( SELECT * FROM {{ source_env('CURATED','CURATED__GAM_MATCH_TABLE_COMPANY__CURRENT') }} )


, processing AS (
    SELECT
         m2a_source."client_content_id" as "fixture_id"
        ,m2a_source."territory"
        ,m2a_source."ad_date"
        ,DATE_TRUNC(SECOND,m2a_source."ad_start_timestamp") as "ad_start_timestamp"
        ,DATE_TRUNC(SECOND,m2a_source."ad_end_timestamp") as "ad_end_timestamp"
        ,m2a_source."duration"
        ,IFNULL(won_encodings."time_allocations_name", m2a_source."break_id") AS "break_id_f"
        ,DENSE_RANK() OVER (PARTITION BY "origin_data_centre", "fixture_id", "outlet", "streaming_protocol" ORDER BY REPLACE("break_id", '-1', '')::INT) AS "break_number"
        ,CONCAT("break_id_f", ' - ', "break_number") as "break_type"
        ,REPLACE(IFNULL(gam_line_item_names."line_item_name", m2a_source."client_content_id"), '|', '_')  AS "line_item_name"
        ,m2a_source."content_id" as "creative"
        ,m2a_source."gam_id"
        ,REPLACE(gam_companies."company_name", '[Advertiser]', '') as "advertiser"
        ,m2a_source."streaming_protocol"
        ,m2a_source."origin_server"
        ,m2a_source."primary_key"
    FROM m2a_source
    LEFT JOIN gam_line_item_names
        ON m2a_source."gam_id" = gam_line_item_names."line_item_id"
    LEFT JOIN gam_orders
        ON gam_orders."order_id" = gam_line_item_names."order_id"
    LEFT JOIN gam_companies
        ON gam_companies."company_id" = gam_orders."advertiser_id"
    LEFT JOIN won_encodings
        ON m2a_source."client_content_id" = won_encodings."fixture_id"
        AND m2a_source."outlet" = won_encodings."region_m2a_key"
        --todo is this the same as the code below?
        AND m2a_source."break_id" = CONCAT(TRY_TO_NUMBER(won_encodings."splice_event_id"), '-1')
    --        AND (
    --            m2a_source."break_id" = CONCAT(won_encodings."len_timeAllocations_secondaryEvents_spliceEventId_0"::int, '-1')
    --            OR
    --            m2a_source."break_id" = CONCAT(won_encodings."len_timeAllocations_secondaryEvents_spliceEventId_1"::int, '-1')
    --            )
    WHERE
        m2a_source."gam_id" IS NOT NULL
        OR m2a_source."content_id" LIKE 'SP%' --include sponsorship for Serie B as they have null gam_id
        AND m2a_source."ad_date" >= '{{ advertising_variables.start_date }}'
)

SELECT
    "fixture_id"
    ,{{ advertising_map_territory("territory") }} AS "territory"
    ,"ad_date"
    ,"ad_start_timestamp"
    ,"ad_end_timestamp"
    ,"duration"
    ,"creative"
    ,"line_item_name"
    ,"break_type"
    ,"gam_id"
    ,"advertiser"
    ,"streaming_protocol"
    ,"origin_server"
    ,"primary_key"
FROM processing
