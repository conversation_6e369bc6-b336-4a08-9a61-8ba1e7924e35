{{
    config(
        materialized='table',
        schema='TRANSIENT',
        database='ADVERTISING__B2C__MART__' + snowflake_env(),
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['presentation-advertising-logs']
    )
}}

/*
    This query produces a PL mapping with which we can enrich the data with advertiser and brand information.

    The data comes from D65 and is currently produced in DSS. This will be migrated soon.

    The logic will first ensure that we priorise DAZN-produced IOs and PLs (see first qualify) and then also
    ensures that we only have one record per PL number as we're joining on that field later.

    A full EDA of this logic and where it comes from can be found here:
    https://livesport.atlassian.net/wiki/spaces/DATA/pages/**********/Advertiser+Brand+PL+Enrichment

*/

WITH ref_staging__d365__snowflake_data AS (
    SELECT * FROM {{ ref('staging__d365__snowflake_data') }}
)

, prioritise_dazn_as_data_source AS (
    SELECT
        "io"
        ,"pl"
        ,"campaign_name"
        ,IFNULL("brand_account_name", "brand") AS "advertiser"
        ,"brand_industry"
        ,"brand_category"
    FROM ref_staging__d365__snowflake_data
        -- this qualify ensures we get the DAZN records first by setting those to 1 and the rest to 2 in the order by.
    QUALIFY RANK() OVER (PARTITION BY "io", "pl" ORDER BY IFF("data_source" = 'DAZN',1,2)) = 1
)

SELECT * FROM prioritise_dazn_as_data_source
QUALIFY ROW_NUMBER() OVER (PARTITION BY "pl" ORDER BY "advertiser" NULLS LAST) = 1