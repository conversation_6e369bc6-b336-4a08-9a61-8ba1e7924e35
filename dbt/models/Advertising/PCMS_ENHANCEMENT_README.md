# PCMS Channel Selection Enhancement

## Overview

This enhancement integrates PCMS (Program Content Management System) data into the advertising channel selection model to improve channel filtering accuracy. The implementation adds channel extraction logic based on `daznChannelId` and filters advertising logs to only include channels that match the extracted PCMS channel values.

## Implementation Details

### New Models Created

1. **`advertising_pcms_live_fixture_info.sql`**
   - Source model for PCMS live fixture information
   - Currently uses a placeholder structure that needs to be configured with actual PCMS data source
   - Provides `daznChannelId` and other fixture-related information

2. **`advertising_pcms_channel_mapping.sql`**
   - Implements the channel extraction logic from `daznChannelId`
   - Transforms the last 2 digits of `daznChannelId` using specific business rules:
     - Values 1-10: Use as-is, left-padded with zeros to 2 digits
     - Values 11-26: Subtract 10, then left-pad with zeros to 2 digits
     - Values 81-90: Subtract 78, then left-pad with zeros to 2 digits
     - All other values: Use the last 2 digits as-is

### Enhanced Models

1. **`advertising_channel_selection.sql`**
   - Added PCMS channel mapping integration
   - New `pcms_channel_match` field indicates whether log channel matches PCMS extracted channel
   - Updated ranking logic to prioritize PCMS channel matches
   - Filters logs to only include channels that match PCMS extracted values (when PCMS data exists)

2. **`advertising.yml`**
   - Added comprehensive documentation for new models and fields
   - Updated existing field descriptions

## Channel Extraction Logic

The channel extraction logic transforms `daznChannelId` values as follows:

```sql
CASE
    WHEN RIGHT("daznChannelId", 2)::INT BETWEEN 1 AND 10 
        THEN LPAD(RIGHT("daznChannelId", 2)::STRING, 2, '0')
    WHEN RIGHT("daznChannelId", 2)::INT BETWEEN 11 AND 26 
        THEN LPAD((RIGHT("daznChannelId", 2)::INT - 10)::STRING, 2, '0')
    WHEN RIGHT("daznChannelId", 2)::INT BETWEEN 81 AND 90 
        THEN LPAD((RIGHT("daznChannelId", 2)::INT - 78)::STRING, 2, '0')
    ELSE RIGHT("daznChannelId", 2)
END
```

### Examples:
- `daznChannelId` ending in `05` → extracted channel `05`
- `daznChannelId` ending in `15` → extracted channel `05` (15-10)
- `daznChannelId` ending in `85` → extracted channel `07` (85-78)

## Updated Channel Selection Priority

The new ranking logic prioritizes channels in this order:
1. **M2A logs** (highest priority - unchanged)
2. **PCMS channel match** (new priority level)
3. **Delivery type ranking** (existing logic)
4. **Total ads count** (existing logic)
5. **Grass Valley ads count** (existing logic)
6. **Cloud TX ads count** (existing logic)

## Next Steps Required

### 1. Configure PCMS Data Source

Update `advertising_pcms_live_fixture_info.sql` to use your actual PCMS data source. Replace the placeholder with one of these patterns:

```sql
-- Option A: Direct source reference
SELECT 
    "fixture_id"
    ,"daznChannelId" 
    ,"content_id"
    ,"transmission_start_timestamp" AS "transmission_start"
    ,"transmission_end_timestamp" AS "transmission_end"
    ,"territory"
    ,"delivery_type"
FROM {{ source('CURATED', 'CURATED__PCMS__LIVE_FIXTURE_INFO') }}

-- Option B: Staging table reference  
SELECT 
    "fixture_id"
    ,"dazn_channel_id" AS "daznChannelId"
    ,"content_id" 
    ,"start_time" AS "transmission_start"
    ,"end_time" AS "transmission_end"
    ,"territory"
    ,"delivery_type"
FROM {{ ref('staging__pcms__live_fixture_info') }}

-- Option C: Content dimension with PCMS data
SELECT 
    content."fixture_id"
    ,content."dazn_channel_id" AS "daznChannelId"
    ,content."article_id" AS "content_id"
    ,content."transmission_start_timestamp" AS "transmission_start"
    ,content."transmission_end_timestamp" AS "transmission_end"
    ,content."territory"
    ,content."delivery_type"
FROM {{ ref('mart__content_item__dim') }} content
WHERE content."content_item_origin" = 'PCMS'
```

### 2. Remove Placeholder Limitations

Once the actual PCMS source is configured:
1. Remove the `LIMIT 0` clause from `advertising_pcms_live_fixture_info.sql`
2. Update column mappings to match your actual PCMS schema
3. Add appropriate filtering logic for data quality

### 3. Testing and Validation

1. **Test the channel extraction logic** with sample `daznChannelId` values
2. **Validate channel matching** between logs and PCMS extracted channels
3. **Monitor impact** on channel selection results
4. **Compare before/after** channel selection outcomes

### 4. Create Source Configuration

If using a new source, add it to your source configuration:

```yaml
# In sources.yml
sources:
  - name: PCMS
    database: YOUR_PCMS_DATABASE
    tables:
      - name: LIVE_FIXTURE_INFO
        description: "PCMS live fixture information including channel data"
        columns:
          - name: fixture_id
          - name: daznChannelId
          - name: content_id
          # ... other columns
```

## Benefits

1. **Improved Channel Accuracy**: Filters logs to only include channels that match PCMS configuration
2. **Enhanced Selection Logic**: Prioritizes channels that align with PCMS data
3. **Better Data Quality**: Reduces noise from irrelevant channel data
4. **Audit Trail**: Provides `pcms_channel_match` field for monitoring and debugging

## Monitoring

Monitor these metrics after implementation:
- Number of fixtures with PCMS channel data
- Percentage of logs matching PCMS channels
- Changes in channel selection distribution
- Impact on downstream advertising analytics
