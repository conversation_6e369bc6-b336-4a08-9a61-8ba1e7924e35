# PCMS Channel Selection Enhancement

## Overview

This enhancement integrates PCMS (Program Content Management System) data into the advertising channel selection model to improve channel filtering accuracy. The implementation uses the `live_media_dazn_channel_id` field from GMR articles and applies your specified channel extraction logic to filter advertising logs to only include channels that match the extracted PCMS channel values.

## Implementation Details

### New Model Created

**`advertising_pcms_channel_mapping.sql`**
- Extracts channel information from GMR articles using `live_media_dazn_channel_id`
- Implements the exact channel extraction logic you specified
- Links fixture_id to extracted channel values for filtering

### Enhanced Model

**`advertising_channel_selection.sql`**
- Added PCMS channel mapping integration
- New `pcms_channel_match` field indicates whether log channel matches PCMS extracted channel
- Updated ranking logic to prioritize PCMS channel matches
- Filters logs to only include channels that match PCMS extracted values (when PCMS data exists)

## Channel Extraction Logic

The implementation uses exactly the logic you provided:

```sql
CASE
    WHEN RIGHT(gmr."live_media_dazn_channel_id", 2)::INT BETWEEN 1 AND 10 
        THEN LPAD(RIGHT(gmr."live_media_dazn_channel_id", 2)::STRING, 2, '0')
    WHEN RIGHT(gmr."live_media_dazn_channel_id", 2)::INT BETWEEN 11 AND 26 
        THEN LPAD((RIGHT(gmr."live_media_dazn_channel_id", 2)::INT - 10)::STRING, 2, '0')
    WHEN RIGHT(gmr."live_media_dazn_channel_id", 2)::INT BETWEEN 81 AND 90 
        THEN LPAD((RIGHT(gmr."live_media_dazn_channel_id", 2)::INT - 78)::STRING, 2, '0')
    ELSE RIGHT(gmr."live_media_dazn_channel_id", 2)
END AS "extracted_channel"
```

### Examples:
- `daznChannelId` ending in `05` → extracted channel `05`
- `daznChannelId` ending in `15` → extracted channel `05` (15-10)
- `daznChannelId` ending in `85` → extracted channel `07` (85-78)

## Data Source

The implementation uses the existing GMR article dimension (`gmr_article__dim`) which contains:
- `live_media_dazn_channel_id` - The PCMS daznChannelId field
- `fixture_id` - Links to advertising logs
- `article_territory` - Territory information
- Live stream timestamps for additional context

## Updated Channel Selection Priority

The new ranking logic prioritizes channels in this order:
1. **M2A logs** (highest priority - unchanged)
2. **PCMS channel match** (new priority level)
3. **Delivery type ranking** (existing logic)
4. **Total ads count** (existing logic)
5. **Grass Valley ads count** (existing logic)
6. **Cloud TX ads count** (existing logic)

## Filtering Logic

The enhancement adds a WHERE clause that:
- Includes all logs when no PCMS data exists for a fixture
- Only includes logs where the channel matches the PCMS extracted channel when PCMS data exists
- Uses left-padded channel comparison to ensure consistent formatting

```sql
WHERE (pcms_channel_mapping."extracted_channel" IS NULL 
       OR LPAD(logs."channel"::STRING, 2, '0') = pcms_channel_mapping."extracted_channel")
```

## Benefits

1. **Improved Channel Accuracy**: Filters logs to only include channels that match PCMS configuration
2. **Enhanced Selection Logic**: Prioritizes channels that align with PCMS data
3. **Better Data Quality**: Reduces noise from irrelevant channel data
4. **Audit Trail**: Provides `pcms_channel_match` field for monitoring and debugging
5. **Backward Compatibility**: Maintains existing behavior when PCMS data is not available

## Testing and Validation

To validate the implementation:

1. **Test channel extraction logic**:
   ```sql
   SELECT 
       "daznChannelId",
       "extracted_channel",
       "channel_id_last_two_digits"
   FROM {{ ref('advertising_pcms_channel_mapping') }}
   LIMIT 10;
   ```

2. **Check PCMS channel matches**:
   ```sql
   SELECT 
       "fixture_id",
       "territory",
       "channel",
       "pcms_channel_match",
       COUNT(*) as log_count
   FROM {{ ref('advertising_channel_selection') }}
   GROUP BY ALL
   ORDER BY "pcms_channel_match" DESC;
   ```

3. **Monitor impact on channel selection**:
   ```sql
   SELECT 
       COUNT(*) as total_fixtures,
       COUNT_IF("pcms_channel_match" = 1) as pcms_matched_fixtures,
       COUNT_IF("pcms_channel_match" = 1) / COUNT(*) * 100 as match_percentage
   FROM {{ ref('advertising_channel_selection') }};
   ```

## Monitoring Metrics

After implementation, monitor:
- Number of fixtures with PCMS channel data
- Percentage of logs matching PCMS channels
- Changes in channel selection distribution
- Impact on downstream advertising analytics
- Data quality improvements

## Next Steps

1. **Deploy and Test**: Run the models and validate the channel extraction logic
2. **Monitor Performance**: Check query performance and data volumes
3. **Validate Results**: Compare before/after channel selection outcomes
4. **Optimize if Needed**: Adjust filtering logic based on business requirements
