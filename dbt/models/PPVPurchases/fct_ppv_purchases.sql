{{
    config(
        materialized='table',
        schema='PRESENTATION',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_M_WH',
        tags=['presentation-ppv-purchases']
    )
}}

WITH rateplan_source AS (
    SELECT * FROM {{ ref('staging__zuora__rateplan') }}
)

,rateplancharge_source AS (
    SELECT * FROM {{ ref('staging__zuora__rateplancharge_current') }}
)

,subscription_source AS (
    SELECT * FROM {{ ref('staging__zuora__subscription_id_current') }}
)

,account_source AS (
    SELECT * FROM {{ ref('staging__zuora__account_current') }}
)

,sub_created_date AS (
    SELECT
        "subscription_name"
        ,"subscription_version"
        ,MIN("subscription_id_created_timestamp__unpatched") AS "CreatedDate"
    FROM subscription_source
    WHERE "subscription_version" = 1
    GROUP BY 1,2
)

,orderlineitem_source AS (
    SELECT * FROM {{ ref('staging__zuora__orderlineitem') }}
    WHERE "order_line_item_item_category"= 'Sales'

)

,productrateplancharge_source AS (
    SELECT * FROM {{ ref('staging__zuora__productrateplancharge_current') }}
)

,subscription_name__scd AS (
    SELECT * FROM {{ ref('subscription_name__scd') }}
)

,country_source AS (
    SELECT * FROM {{ ref('region_dimension') }}
)

,contact_source AS (
    SELECT 
        "billing_account_id"
        ,"contact_country"
    FROM {{ ref('staging__zuora__contact_current') }}
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "billing_account_id" ORDER BY "contact_updated_timestamp" DESC)=1
)

,subscription_extended AS (
    -- SELECT * FROM {{ ref('subscription_extended') }}
    SELECT * FROM TRANSFORMATION_PROD.PRESENTATION.SUBSCRIPTION_EXTENDED
)

---EV CODE
,dmp_ppv_stage AS (
    SELECT * FROM {{ ref('staging__dmp_billing_ppv') }}
) 

,account_mapping AS (
    SELECT  * FROM {{ ref('staging__account_current') }}
) 
-----

,rateplan_subs AS (
    SELECT
        CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
        ,rateplan_source."rateplan_created_timestamp" AS "purchase_date"
        ,rateplan_source."rateplan_id" AS "rateplan_id"
        ,rateplan_source."rateplan_name" AS "rateplan_name"
        ,rateplan_source."rateplan_tracking_id" AS "tracking_id"
        ,rateplan_source."entitlement_set_id" AS "entitlement_set_id"
        ,rateplan_source."subscription_id" AS "subscription_id"
        ,subscription_source."subscription_name" AS "subscription_name"
        ,subscription_source."billing_account_id" AS "billing_account__id"
        ,account_source."crm_account_id" AS "crm_account__id"
        ,rateplancharge_source."rateplan_charge_charge_type" AS "charge_type"
        --,COALESCE(rateplan_source."SourceSystem__c",IFF(rateplan_source."Name" ILIKE '% PPV %', SPLIT_PART(rateplan_source."Name", ' ', 1), IFF(rateplan_source."Name" ILIKE '%PPV%', 'Direct','Unknown'))) AS "ppv_source"
        ,Trim(CASE WHEN rateplan_source."rateplan_source_system_name" IS NOT NULL THEN rateplan_source."rateplan_source_system_name"
                WHEN rateplan_source."rateplan_name" ILIKE '%DIRECT%' THEN 'Direct'
                WHEN rateplan_source."rateplan_name" ILIKE '% PPV %' THEN SPLIT_PART(rateplan_source."rateplan_name", 'PPV' , 1)
                WHEN rateplan_source."rateplan_name" ILIKE '%PPV%' THEN 'Direct'
                ELSE 'Unknown' END) AS "ppv_source"
        ,CASE
            WHEN DATEDIFF('hours', sub_created_date."CreatedDate", "purchase_date") < 0 THEN 'Error'
            WHEN DATEDIFF('hours', sub_created_date."CreatedDate", "purchase_date") < 48 THEN 'New Subscription'
            ELSE 'Existing Subscription'
        END AS "purchase_type"
        ,CASE
            WHEN DATEDIFF('hours', sub_created_date."CreatedDate", "purchase_date") < 0 THEN NULL
            WHEN DATEDIFF('hours', sub_created_date."CreatedDate", "purchase_date") < 48 THEN TRUE
            ELSE FALSE
        END AS "is_new_subscription"
        ,subscription_source."subscription_country" AS "subscription_country"
        ,account_source."dazn_user_id" AS "dazn_user_id"
        ,rateplan_source."is_3pp_exit_flow" AS "is_3pp_exit_flow"
        ,rateplan_source."rateplan_platform" AS "platform"
        ,'rateplan' AS "data_source"
    FROM rateplan_source
    LEFT JOIN rateplancharge_source ON rateplan_source."rateplan_id" = rateplancharge_source."rateplan_id"
    LEFT JOIN subscription_source ON rateplan_source."subscription_id" = subscription_source."subscription_id"
    LEFT JOIN account_source ON subscription_source."billing_account_id" = account_source."billing_account_id"
    LEFT JOIN sub_created_date ON subscription_source."subscription_name" = sub_created_date."subscription_name"
    WHERE TRUE AND
        -- We want to limit how much is run as we don't need to back date before ppv was launched
        rateplan_source."rateplan_created_timestamp" >= '2022-03-01'
        AND
        rateplan_source."rateplan_name" ILIKE '%PPV%'
        AND
        rateplancharge_source."rateplan_charge_charge_type" = 'OneTime'
        AND
        (rateplan_source."rateplan_product_type" != 'addon' OR rateplan_source."rateplan_product_type" IS NULL)
    -- QUALIFY ROW_NUMBER() OVER (PARTITION BY "subscription_name", "rateplan_name" ORDER BY "purchase_date" ASC) = 1
)


,prp_oli AS (
    SELECT
        CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
        ,orderlineitem_source."order_line_item_created_timestamp" AS "purchase_date"
        ,orderlineitem_source."order_line_item_id" AS "rateplan_id"
        ,orderlineitem_source."order_line_item_item_name" AS "rateplan_name"
        ,orderlineitem_source."order_line_item_tracking_id" AS "tracking_id"
        ,orderlineitem_source."entitlement_set_id" AS "entitlement_set_id"
        ,COALESCE(subscription_name__scd."subscription_id",subscription_extended."subscription_id") AS "subscription_id"
        ,COALESCE(orderlineitem_source."subscription_name",subscription_extended."subscription_name") AS "subscription_name"
        ,orderlineitem_source."billing_account_id" AS "billing_account__id"
        ,account_source."crm_account_id" AS "crm_account__id"
        ,productrateplancharge_source."product_rateplan_charge_charge_type" AS "charge_type"
        ,Trim(CASE WHEN orderlineitem_source."order_line_item_source_system" IS NOT NULL THEN orderlineitem_source."order_line_item_source_system" 
                WHEN orderlineitem_source."order_line_item_item_name" ILIKE '%DIRECT%' THEN 'Direct' 
                WHEN orderlineitem_source."order_line_item_item_name" ILIKE '% PPV %' THEN SPLIT_PART(orderlineitem_source."order_line_item_item_name", 'PPV' , 1) 
                WHEN orderlineitem_source."order_line_item_item_name" ILIKE '%PPV%' THEN 'Direct' 
                ELSE 'Unknown' END) AS "ppv_source"
        ,CASE
            --WHEN orderlineitem_source."subscription_name" IS NULL THEN 'New Subscription'
            WHEN DATEDIFF('hours', sub_created_date."CreatedDate", "purchase_date") < 0 THEN 'Error'
            WHEN DATEDIFF('hours', sub_created_date."CreatedDate", "purchase_date") < 48 THEN 'New Subscription'
            ELSE 'Existing Subscription'
        END AS "purchase_type"
        ,CASE
            --WHEN orderlineitem_source."subscription_name" IS NULL THEN TRUE
            WHEN DATEDIFF('hours', sub_created_date."CreatedDate", "purchase_date") < 0 THEN NULL
            WHEN DATEDIFF('hours', sub_created_date."CreatedDate", "purchase_date") < 48 THEN TRUE
            ELSE FALSE
        END AS "is_new_subscription"
        ,COALESCE(country_source."country",subscription_name__scd."subscription_country",subscription_extended."subscription_country",contact_source."contact_country")  AS "subscription_country"
        ,account_source."dazn_user_id" AS "dazn_user_id"
        ,orderlineitem_source."is_3pp_exit_flow" AS "is_3pp_exit_flow"
        ,orderlineitem_source."order_line_item_Platform" AS "platform"
        ,'orderlineitem' AS "data_source"
    FROM orderlineitem_source
    LEFT JOIN productrateplancharge_source ON orderlineitem_source."product_rateplan_charge_id" = productrateplancharge_source."product_rateplan_charge_id"
    LEFT JOIN account_source ON orderlineitem_source."billing_account_id" = account_source."billing_account_id"
--    LEFT JOIN subscription_source ON orderlineitem_source."subscription_name" = subscription_source."subscription_name"
    LEFT JOIN country_source ON orderlineitem_source."order_line_item_country" = country_source."country_code"
    LEFT JOIN contact_source ON orderlineitem_source."billing_account_id" = contact_source."billing_account_id"
    LEFT JOIN  subscription_name__scd
        ON subscription_name__scd."subscription_name" = orderlineitem_source."subscription_name"
        AND "purchase_date" >= subscription_name__scd."record_valid_from_timestamp" 
        AND "purchase_date" <  subscription_name__scd."record_valid_until_timestamp"
    LEFT JOIN subscription_extended  
        ON orderlineitem_source."order_line_item_order_id" = subscription_extended."subscription_order_id" AND orderlineitem_source."subscription_name" IS NULL
    LEFT JOIN sub_created_date ON COALESCE(orderlineitem_source."subscription_name",subscription_extended."subscription_name") = sub_created_date."subscription_name"  
    WHERE TRUE AND
        productrateplancharge_source."product_rateplan_charge_charge_type" = 'OneTime'
)
--ev code
,dmp_ppv AS (
    SELECT 
        CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
        ,dmp_ppv_stage."event_timestamp" AS "purchase_date"
        ,dmp_ppv_stage."billing_product_id" AS "rateplan_id"
        ,dmp_ppv_stage."catalog_product_id" AS "rateplan_name"  
        ,dmp_ppv_stage."tracking_id_derived" AS "tracking_id"
        ,dmp_ppv_stage."entitlement_set_id" AS "entitlement_set_id"
        ,subscription_name__scd."subscription_id" AS "subscription_id"
        ,subscription_name__scd."subscription_name" AS "subscription_name"
        ,IFNULL(account_mapping."billing_account_id",dmp_ppv_stage."dazn_id") AS "billing_account__id"
        ,account_mapping."crm_account_id" AS "crm_account__id"
        ,'OneTime' AS "charge_type"
        ,INITCAP(dmp_ppv_stage."source_system_derived") AS "ppv_source" 
        ,CASE 
            WHEN subscription_name__scd."subscription_name_original_created_timestamp" IS NULL THEN 'New Subscription'
            WHEN DATEDIFF('hours', subscription_name__scd."subscription_name_original_created_timestamp", "purchase_date") < 0 THEN 'Error'
            WHEN DATEDIFF('hours', subscription_name__scd."subscription_name_original_created_timestamp", "purchase_date") < 48 THEN 'New Subscription'
            ELSE 'Existing Subscription'
        END AS "purchase_type"
        ,CASE 
            WHEN subscription_name__scd."subscription_name_original_created_timestamp" IS NULL THEN TRUE
            WHEN DATEDIFF('hours', subscription_name__scd."subscription_name_original_created_timestamp", "purchase_date") < 0 THEN NULL
            WHEN DATEDIFF('hours', subscription_name__scd."subscription_name_original_created_timestamp", "purchase_date") < 48 THEN TRUE
            ELSE FALSE
        END AS "is_new_subscription"
        ,country_source."country" as "subscription_country"
        ,dmp_ppv_stage."dazn_id" AS "dazn_user_id"
        ,dmp_ppv_stage."is_3pp_exit" AS "is_3pp_exit_flow"
        ,dmp_ppv_stage."device_platform" AS "platform"
        ,'DMP' AS "data_source"
    FROM dmp_ppv_stage 
    LEFT JOIN country_source 
        ON  country_source."join_key"="billing_country"
    LEFT JOIN subscription_name__scd 
        ON subscription_name__scd."dazn_user_id"=dmp_ppv_stage."dazn_id" 
        AND subscription_name__scd."subscription_product_group"='DAZN'
        AND "purchase_date" >= DATEADD('second',-5,subscription_name__scd."record_valid_from_timestamp")
        AND "purchase_date" <  subscription_name__scd."record_valid_until_timestamp" 
    LEFT JOIN account_mapping 
        ON account_mapping."dazn_user_id" = dmp_ppv_stage."dazn_id"
QUALIFY ROW_NUMBER() OVER (PARTITION BY "rateplan_id" ORDER BY  subscription_name__scd."subscription_name_original_created_timestamp" DESC) = 1
)


,final AS (
    SELECT * FROM rateplan_subs
    UNION ALL
    SELECT * FROM prp_oli
    --ev code
    UNION ALL
    SELECT * FROM dmp_ppv
)

SELECT * FROM final
QUALIFY ROW_NUMBER() OVER (PARTITION BY "billing_account__id","subscription_name", "rateplan_name" ORDER BY "purchase_date" ASC) = 1
