{{
    config(
        materialized='table',
        schema='PRESENTATION',
        tags=['presentation-ppv-purchases']
    )
}}

WITH ppv_source AS (
    SELECT * FROM {{ ref('fct_ppv_purchases') }}
)

,invoice_source AS (
    SELECT * FROM {{ ref('fct_invoice_items') }}
)

,firstpurchase AS (
    SELECT
        ppv_source."subscription_name"
        ,ppv_source."purchase_date" AS "first_purchase_date"
        ,ppv_source."entitlement_set_id" AS "first_entitlement_set_id"
        ,ppv_source."purchase_type" AS "first_purchase_type"
        ,ppv_source."tracking_id" AS "first_tracking_id"
        ,ppv_source."ppv_source" AS "first_ppv_source"
        ,invoice_source."invoice_item_charge_amount" AS "first_charge_amount"
        ,invoice_source."billing_account_currency_code" AS "first_currency"
        ,invoice_source."usd_charge_amount" AS "first_usd_charge_amount"
        ,COUNT(DISTINCT ppv_source."rateplan_id") OVER(PARTITION BY ppv_source."subscription_name") AS "total_ppv_purchases"
        ,SUM(invoice_source."usd_charge_amount") OVER(PARTITION BY ppv_source."subscription_name") AS "total_usd_charge_amount"
    FROM ppv_source
    LEFT JOIN invoice_source ON ppv_source."rateplan_id" = invoice_source."rateplan_id"
    QUALIFY ROW_NUMBER() OVER(PARTITION BY ppv_source."subscription_name" ORDER BY ppv_source."purchase_date" ASC) = 1
)

,latestpurchase AS (
    SELECT
        ppv_source."subscription_name"
        ,ppv_source."billing_account__id"
        ,ppv_source."crm_account__id"
        ,ppv_source."purchase_date" AS "latest_purchase_date"
        ,ppv_source."entitlement_set_id" AS "latest_entitlement_set_id"
        ,ppv_source."tracking_id" AS "latest_tracking_id"
        ,ppv_source."ppv_source" AS "latest_ppv_source"
        ,invoice_source."invoice_item_charge_amount" AS "latest_charge_amount"
        ,invoice_source."billing_account_currency_code" AS "latest_currency"
        ,invoice_source."usd_charge_amount" AS "latest_usd_charge_amount"
    FROM ppv_source
    LEFT JOIN invoice_source ON ppv_source."rateplan_id" = invoice_source."rateplan_id"
    QUALIFY ROW_NUMBER() OVER(PARTITION BY ppv_source."subscription_name" ORDER BY ppv_source."purchase_date" DESC) = 1
)

,allpurchases AS (
    SELECT
        "subscription_name"
        ,ARRAY_AGG(OBJECT_CONSTRUCT(
            'entitlement_set_id', "entitlement_set_id"
            ,'purchase_date', "purchase_date"
            ,'ppv_source', "ppv_source"
            ,'tracking_id', "tracking_id"
        )) AS "all_purchases"
    FROM ppv_source
    GROUP BY "subscription_name"
)

,final AS (
    SELECT
        firstpurchase."subscription_name"
        ,latestpurchase."billing_account__id"
        ,latestpurchase."crm_account__id"
        ,firstpurchase."first_purchase_date"
        ,firstpurchase."first_entitlement_set_id"
        ,firstpurchase."first_purchase_type"
        ,firstpurchase."first_tracking_id"
        ,firstpurchase."first_ppv_source"
        ,firstpurchase."first_charge_amount"
        ,firstpurchase."first_currency"
        ,firstpurchase."first_usd_charge_amount"
        ,latestpurchase."latest_purchase_date"
        ,latestpurchase."latest_entitlement_set_id"
        ,latestpurchase."latest_tracking_id"
        ,latestpurchase."latest_ppv_source"
        ,latestpurchase."latest_charge_amount"
        ,latestpurchase."latest_currency"
        ,latestpurchase."latest_usd_charge_amount"
        ,firstpurchase."total_ppv_purchases"
        ,firstpurchase."total_usd_charge_amount"
        ,allpurchases."all_purchases"
    FROM firstpurchase
    LEFT JOIN latestpurchase ON firstpurchase."subscription_name" = latestpurchase."subscription_name"
    LEFT JOIN allpurchases ON firstpurchase."subscription_name" = allpurchases."subscription_name"
)

SELECT * FROM final
