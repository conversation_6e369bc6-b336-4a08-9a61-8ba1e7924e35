{{
    config(
        materialized='view',
        database='SUBSCRIPTION_DAILY_COUNT__B2C__MART__' + snowflake_env(),
        schema='INTERMEDIATE',
        tags=['presentation-ppv-purchases']
    )
}}

WITH ppv AS (
    SELECT * FROM {{ ref('fct_ppv_purchases') }}
)

,user AS (
    SELECT * FROM {{ ref('user_mapping') }}
    WHERE "nonbilled_user" = FALSE OR "nonbilled_user" IS NULL
)

,final AS (
    SELECT ppv.* FROM ppv
    INNER JOIN user 
    ON ppv."billing_account__id" = user."billing_account_id"
)

SELECT * FROM final