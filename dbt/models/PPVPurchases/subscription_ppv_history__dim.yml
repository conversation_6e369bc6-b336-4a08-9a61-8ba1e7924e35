version: 2

models:
  - name: subscription_ppv_history__dim
    description: "Subscription PPV Purchases History - Each record in this table representes a Subscription that has at least one PPV rateplan purchase associated with it, and summarises their PPV history."
    columns:
      - name: subscription_name
        description: "Zuora Subscription Name"
        quote: true

      - name: billing_account__id
        description: "Zuora Account ID"
        quote: true

      - name: crm_account__id
        description: "CRM/Customer ID"
        quote: true

      - name: first_purchase_date
        description: "Rateplan created datetime of the first PPV purchase for this subscription"
        quote: true

      - name: first_entitlement_set_id
        description: "Entitlement Set ID of the first PPV purchase for this subscription"
        quote: true

      - name: first_purchase_type
        description: "Bundle Type of the first PPV purchase for this subscription name - Whether the customer purchased the PPV with a DAZN subscription or as standalone"
        quote: true

      - name: first_tracking_id
        description: "Tracking ID of the first PPV purchase for this subscription"
        quote: true

      - name: first_ppv_source
        description: "PPV Source (e.g. Apple, Google, Direct) of the first PPV purchase for this subscription"
        quote: true

      - name: first_charge_amount
        description: "Local Currency Charge Amount of the first PPV purchase for this subscription, excluding tax"
        quote: true

      - name: first_currency
        description: "Local Currency of the first PPV purchase for this subscription"
        quote: true

      - name: first_usd_charge_amount
        description: "USD Charge Amount of the first PPV purchase for this subscription, excluding tax"
        quote: true

      - name: latest_purchase_date
        description: "Rateplan created datetime of the most recent PPV purchase for this subscription"
        quote: true

      - name: latest_entitlement_set_id
        description: "Entitlement Set ID of the most recent PPV purchase for this subscription"
        quote: true

      - name: latest_tracking_id
        description: "Tracking ID of the most recent PPV purchase for this subscription"
        quote: true

      - name: latest_ppv_source
        description: "PPV Source (e.g. Apple, Google, Direct)  of the most recent PPV purchase for this subscription"
        quote: true

      - name: latest_charge_amount
        description: "Local Currency Charge Amount of the most recent PPV purchase for this subscription, excluding tax"
        quote: true

      - name: latest_currency
        description: "Local Currency of the most recent PPV purchase for this subscription"
        quote: true

      - name: latest_usd_charge_amount
        description: "USD Charge Amount of the most recent PPV purchase for this subscription, excluding tax"
        quote: true

      - name: total_ppv_purchases
        description: "Count of all distinct PPV rateplans that have been purchased by this subscription"
        quote: true

      - name: total_usd_charge_amount
        description: "Sum of USD Charge Amounts for PPV rateplans that have been purchased by this subscription, excluding tax"
        quote: true

      - name: all_purchases
        description: "An Array of JSON Objects, with each JSON corresponding to a PPV purchase made by this sub name. Each JSON contains the purchase date, entitlement set id, tracking id, and ppv source for that PPV Purchase"
        quote: true
