version: 2

models:
  - name: subscription_extended
    description: "Subscription extended table surfacing for the Subscription details of ringside free trail for ppv purchase"
    columns:

      - name: subscription_order_id
        description: "The ID of the Order, Orders can have subscription,ppv,addon relating to order placed"
        quote: true

      - name: subscription_id
        description: "The ID of the Subscription, Subscription Names can have multiple IDs relating to different types of changes to the Subscription"
        quote: true
        tests:
          - unique

      - name: subscription_name
        description: "The Name of the Subscription E.g. A-S15283950"
        quote: true

      - name: subscription_id_created_timestamp
        description: "The timestamp the Subscription ID was created in Zuora"
        quote: true

      - name: subscription_id_updated_timestamp
        description: "The last timestamp the Subscription ID was updated in Zuora"
        quote: true

      - name: subscription_billing_account_id
        description: "The Zuora Account ID coming from the Zuora Account entity E.g. 2c92a0076b9d926f016bc1fc305051c9"
        quote: true

      - name: subscription_country
        description: "The Country of the Subscription, Japan for Docomo"
        quote: true

      - name: META__DBT_INSERT_DTTS
        description: "DBT Record insert timestamp"
        quote: true
