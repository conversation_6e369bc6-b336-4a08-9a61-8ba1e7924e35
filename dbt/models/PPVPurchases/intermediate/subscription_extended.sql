 {{
    config(
        materialized='incremental',
        incremental_strategy='append',
        unique_key='subscription_id',
        schema='TRANSIENT', 
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH'
    )
}}
 
WITH source AS (
    SELECT * FROM {{ source('ZUORA_6040', 'SUBSCRIPTION') }}
    WHERE VERSION=1
     AND ORDERID IS NOT NULL

    {% if is_incremental() %}

       AND  "CREATEDDATE" > (SELECT MAX("subscription_id_created_timestamp") FROM {{ this }})

    {% endif %} 
   
)

, final AS (
    SELECT
        CURRENT_TIMESTAMP AS "META__DBT_INSERT_DTTS"
        ,ORDERID AS "subscription_order_id"
        ,ID as "subscription_id"
        ,NAME as "subscription_name"
        ,CREATEDDATE AS "subscription_id_created_timestamp"
        ,UPDATEDDATE AS "subscription_id_updated_timestamp"
        ,ACCOUNTID AS "subscription_billing_account_id"
        ,COUNTRYOFSUBSCRIPTION__C AS "subscription_country"
        FROM source
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ID ORDER BY UPDATEDDATE DESC, CREATEDDATE DESC) = 1
)

SELECT * FROM final
