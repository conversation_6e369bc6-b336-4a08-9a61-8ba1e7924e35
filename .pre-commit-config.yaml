# See https://pre-commit.com for more information
---
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      - id: check-added-large-files
      - id: check-ast
      - id: check-merge-conflict
      - id: debug-statements
      - id: end-of-file-fixer
      - id: mixed-line-ending
        args:
          - --fix=lf
      - id: trailing-whitespace
  - repo: https://github.com/Lucas-C/pre-commit-hooks
    rev: v1.2.0
    hooks:
      - id: forbid-tabs
      - id: remove-tabs
        name: Replace tabs with 4 whitespaces
        args:
          - --whitespaces-count=4
        types_or:
          - python
          - sql
          - json
          - shell
      - id: remove-tabs
        name: Replace tabs with 2 whitespaces
        args:
          - --whitespaces-count=2
        types_or:
          - yaml
          - markdown
  - repo: https://github.com/PyCQA/isort
    rev: 5.10.1
    hooks:
      - id: isort
  - repo: https://github.com/adrienverge/yamllint.git
    rev: v1.26.3
    hooks:
      - id: yamllint
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.31.1
    hooks:
      - id: markdownlint
