# Presentation Layer

## Overview

This repo holds the [dbt](https://www.getdbt.com/docs/) models and [Airflow]((https://airflow.apache.org/docs/)) pipelines for the Data Warehouses Presentation Layer.

## Branching

### Branch Naming Convention

Branch name pattern

`<prefix>/JIRA-1234-short-but-descriptive-name`

Use all lower caps letters and hyphen (`-`) to separate words unless it is a specific item name that has an underscore (`_`).

#### Branch Prefix

- `feature`

    Any code changes for a new feature or extension of existing feature should be done on a feature branch

- `bugfix`

    Branch that fixes bug in existing production code

- `hotfix`

    Branch that contains a critical fix or configuration change that should be released immediately. It does not follow the scheduled integration of code and could be merged directly to the production branch, then on the development branch later.

- `docs`

    Branch that contains changes to the documentation only.

- `chore`

    Branch that contains general updating tasks, such as bumping package version. No production code changes

### Branching Strategy

We use an alternative branching strategy designed to allow features to progress though the environments at different speeds without any dependencies on other features. Overview of strategy below and [more detail is available in Confluence](https://livesport.atlassian.net/wiki/spaces/DATA/pages/1592852820/EDM+Branching+Strategy+-+Airflow+Repo). If you are not uncertain on anything it's best to ask.

The repo's default branch is *main*, so always create a new feature branch from *main*. Code should then be merged from you feature branch into each environment branch. If additional changes are required after you've merged into one of *develop* or *staging* then these should be made on the same feature branch and merged again into each environment branch.

``` shell
* main --> feature

* feature --> develop : Merge PR
* feature --> staging : Merge PR
* feature --> main    : Squash Merge PR
```

When merging a PR into *develop* & *staging* you should use the default **Merge** method. But when merging PR into *main* you should use **Squash and Merge** method. More details on [GitHub merge methods available here](https://docs.github.com/en/repositories/configuring-branches-and-merges-in-your-repository/configuring-pull-request-merges/about-merge-methods-on-github). Once a branch has been **Squash and Merged** in *main* branch it should no longer be worked on, instead a new branch should be created off *main*.

:exclamation: Never resolved conflicts via GitHub
UI

:exclamation: Never merge *develop* or *staging* into your
feature branch

:exclamation: Never rebase your feature branch on top
of *develop* or *staging*

### Pull Requests

We view mandatory approvals on PRs as a minimum threshold and not a target to be achieved. As a result only PRs into *main* require approvals to merge. The onus is on the engineer to decide at which stage and to what extent their change should be reviewed.

It's better to have changes reviewed early. There is no need to wait until it is ready to merge, a draft PR can be created as soon as you push a remote branch or just get someone on a call.

:exclamation: Pull Requests from developers not in Team :t-rex: will require an approval from someone in the team

## Testing & Linting

### Testing

We do not currently have any automated testing of our dbt project.

### Linting

We have comprehensive linting setup for the primary components of a dbt project - SQL, YAML & Markdown.

#### SQLFLuff

[SQLFluff](https://docs.sqlfluff.com/en/stable/) is used to enforce our [SQL Style Guile](docs/style-guides/sql_style_guide.md).For details on specific rules see the [SQLFluff Rules Guide](https://docs.sqlfluff.com/en/stable/rules.html).

##### Run SQLFluff locally

SQLFulff can be run locally from the root directory of the repository.

Before running there are two setup steps:

- The [*SQLFluff credentials* block of your .env file](docs/local-development-setup.md#dbt-snowflake-credentials) needs to be completed and the variables exported to your terminal.

- dbt Deps needs to be run - `dbt deps --project-dir dbt --profiles-dir dbt`

SQLFluff linting can be run with `sqlfluff lint --processes 4 .`

SQLFluff fixing can be run with `sqlfluff fix .`

#### yamllint

[yamllint](https://yamllint.readthedocs.io/en/stable/) is used to ensure consistent and valid YAML. For details on specific rules see the [yamllint Rules Guide](https://yamllint.readthedocs.io/en/stable/rules.html)

##### Run yamllint Locally

yamllint will be run by [pre-commit](docs/local-development-setup.md#pre-commit) on each commit.

pre-commit can be used to run yamllint manually with `pre-commit run yamllint --all-files`

#### markdownlint

[markdownlint](https://github.com/DavidAnson/markdownlint#markdownlint) is used to ensure consistent and valid Markdown. For detail on specific rules see the [markdownlint Rules Guide](https://github.com/DavidAnson/markdownlint/blob/main/doc/Rules.md#rules)

##### Run markdownlint Locally

markdownlint will be run by [pre-commit](docs/local-development-setup.md#pre-commit) on each commit.

pre-commit can be used to run markdownlint manually with `pre-commit run markdownlint --all-files`

## Deployments

There are no additional deployment steps required to release code to any environment beyond merging into the relevant environment branch - *develop*, *staging* or *main* for production.

The dbt project & DAGs synced to our Airflow cluster from the environment branches with [git-sync](https://github.com/kubernetes/git-sync#git-sync). Once the dbt project has been synced to Airflow it's compiled so the `manifest.json` file is avaliable for our [dbt task generator](https://github.com/getndazn/dazn-dp20-airflow-dbt#dazn-airflow-dbt-package).

:exclamation: Don't release changes to an Airflow DAG when it is Running

:exclamation: Code changes can take up to five minutes to show up in Airflow

:exclamation: New DAGs need to be manually set to active via the Airflow UI

:exclamation: As the code is released on merge any post merge GHAs will not prevent the code being released if they fail

## Local Development Setup

Please follow the [local development setup](docs/local-development-setup.md) guide which covers in detail:

- [EditorConfig](docs/local-development-setup.md#editorconfig)
- [Virtual Environment](docs/local-development-setup.md#virtual-environment)
- [pre-commit](docs/local-development-setup.md#pre-commit)
- [dbt Snowflake Connection](docs/local-development-setup.md#dbt-snowflake-credentials)
- [Local Airflow](docs/local-development-setup.md#run-airflow-locally)

----

:t-rex: Team
