-- Sample data validation for PCMS channel extraction logic

SELECT 
    "fixture_id",
    "territory",
    "daznChannelId",
    RIGHT("daznChannelId", 2) as last_two_digits,
    "extracted_channel",
    CASE 
        WHEN RIGHT("daznChannelId", 2)::INT BETWEEN 1 AND 10 THEN 'Rule 1: 1-10 → pad with zeros'
        WHEN RIGHT("daznChannelId", 2)::INT BETWEEN 11 AND 26 THEN 'Rule 2: 11-26 → subtract 10, pad'
        WHEN RIGHT("daznChannelId", 2)::INT BETWEEN 81 AND 90 THEN 'Rule 3: 81-90 → subtract 78, pad'
        ELSE 'Rule 4: Use last 2 digits as-is'
    END as extraction_rule_applied
FROM ADVERTISING__B2C__MART__DEV.TRANSIENT.advertising_pcms_channel_mapping
ORDER BY "daznChannelId"
LIMIT 20;
