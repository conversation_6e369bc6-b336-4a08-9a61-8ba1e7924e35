# EditorConfig is awesome: # EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
end_of_line = lf
indent_style = space
insert_final_newline = true
trim_trailing_whitespace = true
charset = utf-8

# 4 space indentation for Python files
[*.py]
indent_size = 4

# 4 space indentation for SQL files
[*.sql]
indent_size = 4

# 2 space indentation for yaml files
[*.[yml,yaml]]
indent_size = 2

# 4 space indentation for json files
[*.json]
indent_size = 4

# 2 space indentation for markdown and RST files
[*.{md,rst}]
indent_size = 2

# 4 space indentation for shell files
[*.sh]
indent_size = 4
