name: Dbt Compile
on:
  push

jobs:
  build:
    name: Dbt Compile
    timeout-minutes: 10
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    env:
      SECRETS_PROFILE_STAGE: "************"
      SECRETS_PROFILE_PROD: "************"

    steps:
      - name: checkout
        uses: actions/checkout@v2

      - name: Set Dev Env
        run: |
          echo "ENVIRONMENT=dev" >> "$GITHUB_ENV"
          echo "AIRFLOW_VAR_ENV=dev" >> "$GITHUB_ENV"
          echo "SNOWFLAKE_VAR_ENV=DEV" >> "$GITHUB_ENV"
          echo "PRD_SNOWFLAKE_DATABASE=PRD_DEV" >> "$GITHUB_ENV"
          echo "SECRET_ENV_SPACE=DEV" >> "$GITHUB_ENV"
          echo "SECRETS_ACCOUNT=${{ env.SECRETS_PROFILE_STAGE }}" >> "$GITHUB_ENV"
        if: "!contains(github.ref, 'staging') || !contains(github.ref, 'main')"

      - name: Set Stage Env
        run: |
          echo "ENVIRONMENT=stage" >> "$GITHUB_ENV"
          echo "AIRFLOW_VAR_ENV=stage" >> "$GITHUB_ENV"
          echo "SNOWFLAKE_VAR_ENV=UAT" >> "$GITHUB_ENV"
          echo "PRD_SNOWFLAKE_DATABASE=PRD_UAT" >> "$GITHUB_ENV"
          echo "SECRET_ENV_SPACE=STAGE" >> "$GITHUB_ENV"
          echo "SECRETS_ACCOUNT=${{ env.SECRETS_PROFILE_STAGE }}" >> "$GITHUB_ENV"
        if: contains(github.ref, 'staging')

      - name: Set Prod Env
        run: |
          echo "ENVIRONMENT=prod" >> "$GITHUB_ENV"
          echo "AIRFLOW_VAR_ENV=prod" >> "$GITHUB_ENV"
          echo "SNOWFLAKE_VAR_ENV=PROD" >> "$GITHUB_ENV"
          echo "PRD_SNOWFLAKE_DATABASE=PRD_PROD" >> "$GITHUB_ENV"
          echo "SECRET_ENV_SPACE=PROD" >> "$GITHUB_ENV"
          echo "SECRETS_ACCOUNT=${{ env.SECRETS_PROFILE_PROD }}" >> "$GITHUB_ENV"
        if: contains(github.ref, 'main')

      - name: Configure AWS secrets account credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-region: 'eu-central-1'
          role-to-assume: arn:aws:iam::${{ env.SECRETS_ACCOUNT }}:role/secrets-edm-dp20-admin

      - name: Get Secrets from aws secrets
        uses: aws-actions/aws-secretsmanager-get-secrets@v1
        id: aws_secrets
        with:
          secret-ids: |
            edm-dp20/secret/${{ env.ENVIRONMENT }}/cicd/dbt

      - name: Set snowflake env vars
        run: |
          SNOWFLAKE_HOST=$(echo $EDM_DP20_SECRET_${{ env.SECRET_ENV_SPACE }}_CICD_DBT | jq -r '."snowflake_host"')
          T_REX_SNOWFLAKE_LOGIN=$(echo $EDM_DP20_SECRET_${{ env.SECRET_ENV_SPACE }}_CICD_DBT | jq -r '."snowflake_user"')
          T_REX_SNOWFLAKE_PASSWORD=$(echo $EDM_DP20_SECRET_${{ env.SECRET_ENV_SPACE }}_CICD_DBT | jq -r '."snowflake_password"')
          T_REX_SNOWFLAKE_ROLE=$(echo $EDM_DP20_SECRET_${{ env.SECRET_ENV_SPACE }}_CICD_DBT | jq -r '."snowflake_role"')
          T_REX_SNOWFLAKE_DATABASE=$(echo $EDM_DP20_SECRET_${{ env.SECRET_ENV_SPACE }}_CICD_DBT | jq -r '."snowflake_database"')
          T_REX_SNOWFLAKE_DEFAULT_WAREHOUSE=$(echo $EDM_DP20_SECRET_${{ env.SECRET_ENV_SPACE }}_CICD_DBT | jq -r '."snowflake_warehouse"')
          for var in "SNOWFLAKE_HOST" "T_REX_SNOWFLAKE_LOGIN" "T_REX_SNOWFLAKE_PASSWORD" "T_REX_SNOWFLAKE_ROLE" "T_REX_SNOWFLAKE_DATABASE" "T_REX_SNOWFLAKE_DEFAULT_WAREHOUSE"; do
            if [ -z "${!var}" ]; then
                echo "Var $var is empty"
                exit 1
            fi
          done
          echo "SNOWFLAKE_HOST=${SNOWFLAKE_HOST}" >> "$GITHUB_ENV"
          echo "T_REX_SNOWFLAKE_LOGIN=${T_REX_SNOWFLAKE_LOGIN}" >> "$GITHUB_ENV"
          echo "T_REX_SNOWFLAKE_PASSWORD=${T_REX_SNOWFLAKE_PASSWORD}" >> "$GITHUB_ENV"
          echo "T_REX_SNOWFLAKE_ROLE=${T_REX_SNOWFLAKE_ROLE}" >> "$GITHUB_ENV"
          echo "T_REX_SNOWFLAKE_DATABASE=${T_REX_SNOWFLAKE_DATABASE}" >> "$GITHUB_ENV"
          echo "T_REX_SNOWFLAKE_DEFAULT_WAREHOUSE=${T_REX_SNOWFLAKE_DEFAULT_WAREHOUSE}" >> "$GITHUB_ENV"

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: 3.9
          cache: "pip"

      - name: Install dbt
        run: |
          pip install dbt-snowflake
          dbt deps --project-dir dbt --profiles-dir dbt

      - name: Dbt compile
        run: |
          dbt compile --project-dir dbt --profiles-dir dbt
