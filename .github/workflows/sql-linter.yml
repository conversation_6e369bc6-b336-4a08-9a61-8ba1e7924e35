name: <PERSON><PERSON>

on:
  push:
    paths:
      - '**.sql'

jobs:
  lint-sql:
    name: Lint SQL Files
    runs-on: ubuntu-latest
    timeout-minutes: 15
    permissions:
      id-token: write
      contents: read
    env:
      AIRFLOW_VAR_ENV: sqlfluff
      SNOWFLAKE_VAR_ENV: sqlfluff
      PRD_SNOWFLAKE_DATABASE: PRD_DEV
      T_REX_SNOWFLAKE_DATABASE: TRANSFORMATION_UAT
      SECRETS_ACCOUNT_PROD: "************"

    steps:
      - name: Checkout repo
        uses: actions/checkout@v3

      - name: Configure AWS secrets account credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-region: 'eu-central-1'
          role-to-assume: arn:aws:iam::${{ env.SECRETS_ACCOUNT_PROD }}:role/secrets-edm-dp20-admin

      - name: Get Secrets from aws secrets
        uses: aws-actions/aws-secretsmanager-get-secrets@v1
        id: aws_secrets
        with:
          secret-ids: |
            edm-dp20/secret/prod/cicd/sqlfluff

      - name: Set snowflake env vars
        run: |
          SQLFLUFF_SNOWFLAKE_USER=$(echo $EDM_DP20_SECRET_PROD_SQLFLUFF | jq -r '."snowflake_user"')
          SQLFLUFF_SNOWFLAKE_PASSWORD=$(echo $EDM_DP20_SECRET_PROD_SQLFLUFF | jq -r '."snowflake_password"')
          SQLFLUFF_SNOWFLAKE_ROLE=$(echo $EDM_DP20_SECRET_PROD_SQLFLUFF | jq -r '."snowflake_role"')
          for var in "SQLFLUFF_SNOWFLAKE_USER" "SQLFLUFF_SNOWFLAKE_PASSWORD" "SQLFLUFF_SNOWFLAKE_ROLE"; do
            if [ -z "${!var}" ]; then
                echo "Var $var is empty"
                exit 1
            fi
          done
          echo "SQLFLUFF_SNOWFLAKE_USER=${SQLFLUFF_SNOWFLAKE_USER}" >> "$GITHUB_ENV"
          echo "SQLFLUFF_SNOWFLAKE_PASSWORD=${SQLFLUFF_SNOWFLAKE_PASSWORD}" >> "$GITHUB_ENV"
          echo "SQLFLUFF_SNOWFLAKE_ROLE=${SQLFLUFF_SNOWFLAKE_ROLE}" >> "$GITHUB_ENV"

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: 3.9

      - name: Install SQLFluff
        run: |
          pip install \
            dbt-snowflake==1.1.0 \
            sqlfluff==1.1.0 \
            sqlfluff-templater-dbt==1.1.0

      - name: Run dbt deps
        run: dbt deps --project-dir dbt --profiles-dir dbt

      - name: Run SQL Fluff Lint
        run: sqlfluff lint --processes 4 .
