# SQL Fluff package config
[sqlfluff]
# verbose is an integer (0-2) indicating the level of log output
verbose = 0

# Color formatting of output
nocolor = False

# Run 'sqlfluff dialects' to find supported dialects
dialect = snowflake

# One of [raw|jinja|python|placeholder]
templater = dbt

# Comma separated list of rules to check, or None for all
rules = None

# Comma separated list of rules to exclude, or None
# L007 - Operators should follow a standard for being before/after newlines.
# L008 - Commas should be followed by a single whitespace unless followed by a comment
# L034 - Select wildcards then simple targets before calculations and aggregates
# L059 - Unnecessary quoted identifier
# L060 - Use COALESCE instead of IFNULL or NVL.
# L043 - Unnecessary CASE statement. Use COALESCE function instead.
# L003 - Indentation not consistent with previous lines.
exclude_rules = L007, L008, L034, L059, L060, L043, L003

# The depth to recursively parse to (0 for unlimited)
recurse = 0

# Below controls SQLFluff output, see max_line_length for SQL output
output_line_length = 999

# Number of passes to run before admitting defeat
runaway_limit = 10

# Ignore linting errors in templated sections
ignore_templated_areas = True

# Comma separated list of file extensions to lint.
# NB: This config will only apply in the root folder.
sql_file_exts = .sql,.sql.j2,.dml,.ddl

# DBT config
[sqlfluff:templater:dbt]
project_dir = dbt
profiles_dir = dbt
profile = presentation
target = sqlfluff

# Linting config
[sqlfluff:rules]
tab_space_size = 4
max_line_length = 999
indent_unit = space
comma_style = leading
# Column expression without alias. Use explicit AS clause.
allow_scalar = True
# References should be consistent in statements with a single table.
single_table_references = consistent
# Aliases are quoted or not
unquoted_identifiers_policy = all

# Rule Specific Config

# Inconsistent capitalisation of keywords
[sqlfluff:rules:L010]
capitalisation_policy = upper
ignore_words = day, days, minute, millisecond, microseconds, s

# Inconsistent capitalisation of unquoted identifiers
[sqlfluff:rules:L014]
unquoted_identifiers_policy = aliases
