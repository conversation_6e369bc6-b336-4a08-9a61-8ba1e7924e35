-- Validation queries for PCMS channel selection enhancement

-- 1. Check PCMS channel mapping data
SELECT 
    'PCMS Channel Mapping' as model_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT "fixture_id") as unique_fixtures,
    COUNT(DISTINCT "extracted_channel") as unique_extracted_channels
FROM ADVERTISING__B2C__MART__DEV.TRANSIENT.advertising_pcms_channel_mapping

UNION ALL

-- 2. Check channel selection with PCMS matches
SELECT 
    'Channel Selection with PCMS' as model_name,
    COUNT(*) as total_records,
    COUNT_IF("pcms_channel_match" = 1) as pcms_matched_records,
    ROUND(COUNT_IF("pcms_channel_match" = 1) / COUNT(*) * 100, 2) as pcms_match_percentage
FROM ADVERTISING__B2C__MART__DEV.TRANSIENT.advertising_channel_selection

UNION ALL

-- 3. Check final advertising logs
SELECT 
    'Final Advertising Logs' as model_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT "fixture_id") as unique_fixtures,
    COUNT(DISTINCT "territory") as unique_territories
FROM ADVERTISING__B2C__MART__DEV.FACT.advertising_logs;
