
# DAZN SQL Style Guide

In this document, we outline some best practices and guidelines to follow when writing SQL. Please make sure your code follows the DAZN standards

## Example

Here's a query to give you an idea of what this style guide looks like:

```sql
-- setting the dependences
WITH total_rekall AS (
  SELECT * FROM PRD_PROD.ANALYTICS."total_rekall_fact"
)

,content_dimension AS (
  SELECT * FROM PRD_PROD.ANALYTICS."content_dimension"
)

SELECT
  -- define the high bit_rate threshold
  CASE
    WHEN total_rekall."str_max_bitrate" > 7000000 THEN 1
    ELSE 0
  END AS "high_bit_rate"
  -- calculate the number of sessions
  ,COUNT(DISTINCT "str_dazn_session_id") AS "sessions"
  -- calculate the number of users
  ,COUNT(DISTINCT "str_viewer_id") AS "users"
FROM total_rekall
LEFT JOIN content_dimension ON
  total_rekall."str_asset_id" = content_dimension."article_id"
  AND total_rekall."str_start_time">= content_dimension."effective_from"
  AND total_rekall."str_start_time"< content_dimension."effective_until"
WHERE
  -- filter for japan
  total_rekall."str_country" = 'jp'
  AND
  -- filter for NPB
  content_dimension."competition_name" = 'NPB'
  AND
  -- filter for the past 3 days
  total_rekall."str_full_date" > DATEADD('day', -3, CURRENT_DATE)
GROUP BY 1

```

## Guidelines

### Use upper SQL

Uppercase statements are more readable and easier to debug

```sql
-- Good
SELECT * FROM users

-- Bad
select * from users

-- Bad
Select * From users
```

### Put each selected column on its own line

When selecting columns, always put each column name on its own line and never on the same line as `SELECT`. For multiple columns, it's easier to read when each column is on its own line. And for single columns, it's easier to add additional columns without any reformatting (which you would have to do if the single column name was on the same line as the `SELECT`).

```sql
-- Good
SELECT
  id
FROM users

-- Good
SELECT
  id
  ,email
FROM users

-- Bad
SELECT id
FROM users

-- Bad
SELECT id, email
FROM users
```

## SELECT *

When selecting `*` it's fine to include the `*` next to the `SELECT` and also fine to include the `FROM` on the same line, assuming no additional complexity like `WHERE` conditions:

```sql
-- Good
SELECT * FROM users

-- Good too
SELECT *
FROM users

-- Bad
SELECT * FROM users WHERE email = '<EMAIL>'
```

## Indenting where conditions

Similarly, conditions should always be spread across multiple lines to maximize readability and make them easier to add. When adding a where statement with multiple conditions, make sure logical operators such as `AND` and `OR` are on their own line

```sql
-- Good
SELECT *
FROM users
WHERE
  email = '<EMAIL>'

-- Good
SELECT *
FROM users
WHERE
  email LIKE '%@domain.com'
  AND
  created_at >= '2021-10-08'

-- Bad
SELECT *
FROM users
WHERE email = '<EMAIL>'

-- Bad
SELECT *
FROM users
WHERE
  email LIKE '%@domain.com' AND created_at >= '2021-10-08'

-- Bad
SELECT *
FROM users
WHERE
  email LIKE '%@domain.com'
  AND created_at >= '2021-10-08'
```

### Left align SQL keywords

```sql
-- Good
SELECT
  id
  ,email
FROM users
WHERE
  email LIKE '%@gmail.com'

-- Bad
SELECT
  id
  ,email
  FROM users
  WHERE email LIKE '%@gmail.com'
```

### Use `!=` over `<>`

Simply because `!=` reads like "not equal" which is closer to how we'd say it out loud.

```sql
-- Good
SELECT
  COUNT(*) AS paying_users_count
FROM users
WHERE
  plan_name != 'free'

-- Bad
SELECT
  COUNT(*) AS paying_users_count
FROM users
WHERE
  plan_name <> 'free'
```

### Commas should be at the start of lines

```sql
-- Good
SELECT
  id
  ,email
FROM users

-- Bad
SELECT
  id
  , email
FROM users

-- Bad
SELECT
  id,
  email
FROM users
```

The commas-first style makes it easier to spot missing commas and results in cleaner diffs. Make sure there is no space between the comma and the column name

### Avoid spaces inside the parenthesis

i.e. There should be a space before the open parenthesis and no space after it, and there should be a space after the closed parenthesis and no space before it.

```sql
-- Good
SELECT *
FROM users
WHERE
  id IN (1, 2)

-- Bad
SELECT *
FROM users
WHERE
  id IN ( 1, 2 )
```

### Break long lists of `in` values into multiple indented lines

```sql
-- Good
SELECT *
FROM users
WHERE
  email in (
    '<EMAIL>'
    ,'<EMAIL>'
    ,'<EMAIL>'
    ,'<EMAIL>'
  )
```

### When creating a CTE, table names should be a plural snake case of the noun

```sql
-- Good
SELECT *
FROM users

-- Good
SELECT *
FROM visit_logs

-- Bad
SELECT *
FROM user

-- Bad
SELECT *
FROM visitLog
```

### Column names should be snake_case

```sql
-- Good
SELECT
  id
  ,email
  ,DATE_TRUNC(day,created_at) AS signup_date
FROM users

-- Bad
SELECT
  id
  ,email
  ,DATE_TRUNC(day,created_at) AS SignupMonth
FROM users
```

### Column name conventions

* Boolean fields should be prefixed with `is_`, `has_`, or `does_`. For example, `is_customer`, `has_unsubscribed`, etc.
* Date-only fields should be suffixed with `_date`. For example, `report_date`.

### Column order conventions

Put the primary key first, followed by foreign keys, then by all other columns. If the table has any system columns (`created_at`, `updated_at`, `is_deleted`, etc.), add those last.

```sql
-- Good
SELECT
  id
  ,name
  ,created_at
FROM users

-- Bad
SELECT
  created_at
  ,name
  ,id
FROM users
```

### For join conditions, put the table that was referenced first immediately after the `ON`

By doing it this way, it makes it easier to determine if your join is going to cause the results to fan out. Also, write the join condition on the same line of the FROM statement

```sql
-- Good
SELECT
  ...
FROM users LEFT JOIN charges ON users.id = charges.user_id
-- primary_key = foreign_key --> one-to-many --> fanout

SELECT
  ...
FROM charges LEFT JOIN users ON charges.user_id = users.id
-- foreign_key = primary_key --> many-to-one --> no fanout

-- Bad
SELECT
  ...
FROM users LEFT JOIN charges ON charges.user_id = users.id
```

### Single join conditions should be on the same line as the join

```sql
-- Good
SELECT
  users.email
  ,SUM(charges.amount) AS total_revenue
FROM users INNER JOIN charges ON users.id = charges.user_id
GROUP BY email

-- Bad
SELECT
  users.email,
  sum(charges.amount) AS total_revenue
FROM users
INNER JOIN charges
ON users.id = charges.user_id
GROUP BY email
```

When you have multiple join conditions, place each one on their own indented line:

```sql
-- Good
SELECT
  users.email,
  sum(charges.amount) AS total_revenue
FROM users INNER JOIN charges ON
  users.id = charges.user_id AND
  refunded = false
GROUP BY email
```

### Avoid aliasing table names most of the time

It can be tempting to abbreviate table names like `users` to `u` and `charges` to `c`, but it winds up making the SQL less readable:

```sql
-- Good
SELECT
  users.email
  ,SUM(charges.amount) AS total_revenue
FROM users INNER JOIN charges ON users.id = charges.user_id
GROUP BY email

-- Bad
SELECT
  u.email
  ,SUM(c.amount) AS total_revenue
FROM users u INNER JOIN charges c ON u.id = c.user_id
GROUP BY email
```

Most of the time is best to type out the full table name.

There are two exceptions:

* If you need to join to a table more than once in the same query and need to distinguish each version of it, aliases are necessary.

* If you're working with long or ambiguous table names, it can be useful to alias them (but still use meaningful names):

```sql
-- Good: Meaningful table aliases
SELECT
  companies.com_name
  ,beacons.created_at
FROM stg_mysql_helpscout__helpscout_companies companies INNER JOIN stg_mysql_helpscout__helpscout_beacons_v2 beacons ON companies.com_id = beacons.com_id

-- OK: No table aliases
SELECT
  stg_mysql_helpscout__helpscout_companies.com_name
  ,stg_mysql_helpscout__helpscout_beacons_v2.created_at
FROM stg_mysql_helpscout__helpscout_companies INNER JOIN stg_mysql_helpscout__helpscout_beacons_v2 ON stg_mysql_helpscout__helpscout_companies.com_id = stg_mysql_helpscout__helpscout_beacons_v2.com_id

-- Bad: Unclear table aliases
SELECT
  c.com_name
  ,b.created_at
FROM stg_mysql_helpscout__helpscout_companies c INNER JOIN stg_mysql_helpscout__helpscout_beacons_v2 b ON c.com_id = b.com_id
```

### Include the table when there is a join, but omit it otherwise

When there are no joins involved, there's no ambiguity around which table the columns came from, so you can leave the table name out:

```sql
-- Good
SELECT
  id
  ,name
FROM companies

-- Bad
SELECT
  companies.id
  ,companies.name
FROM companies
```

But when there are joins involved, it's better to be explicit, so it's clear where the columns originated:

```sql
-- Good
SELECT
  users.email
  ,SUM(charges.amount) AS total_revenue
FROM users INNER JOIN charges ON users.id = charges.user_id

-- Bad
SELECT
  email
  ,SUM(amount) AS total_revenue
FROM users INNER JOIN charges ON users.id = charges.user_id

```

### Always rename aggregates and function-wrapped arguments

```sql
-- Good
SELECT
  COUNT(*) AS total_users
FROM users

-- Bad
SELECT
  COUNT(*)
FROM users

-- Good
SELECT
  timestamp_millis(property_beacon_interest) AS expressed_interest_at
FROM hubspot.contact
WHERE
  property_beacon_interest IS NOT NULL

-- Bad
SELECT
  timestamp_millis(property_beacon_interest)
FROM hubspot.contact
WHERE
  property_beacon_interest IS NOT NULL
```

### Always add a comment if introducing a new function

```sql
SELECT
  users.email
  -- calculate total revenue
  ,SUM(charges.amount) AS total_revenue
FROM users INNER JOIN charges ON users.id = charges.user_id
```

### Be explicit in boolean conditions

```sql
-- Good
SELECT *
FROM customers
WHERE
  is_cancelled = true

-- Good
SELECT *
FROM customers
WHERE
  is_cancelled = false

-- Bad
SELECT *
FROM customers
WHERE
  is_cancelled

-- Bad
SELECT *
FROM customers
WHERE
    not is_cancelled
```

### Use `AS` to alias column names

```sql
-- Good
SELECT
  id
  ,email
  ,DATE_TRUNC(month,created_at) AS signup_month
FROM users

-- Bad
SELECT
  id
  ,email
  ,DATE_TRUNC(month,created_at)  signup_month
FROM users
```

### Group using column names or numbers, but not both

```sql
-- Good
SELECT
  user_id
  ,COUNT(*) AS total_charges
FROM charges
GROUP BY user_id

-- Good
SELECT
  user_id
  ,COUNT(*) AS total_charges
FROM charges
GROUP BY  1

-- Bad
SELECT
  DATE_TRUNC(month,created_at) AS signup_month
  ,vertical
  ,COUNT(*) AS users_count
FROM users
GROUP BY 1, vertical
```

### Take advantage of lateral column aliasing when grouping by name

```sql
-- Good
SELECT
  DATE_TRUNC(month,created_at) AS signup_year
  ,COUNT(*) AS total_companies
FROM companies
GROUP BY signup_year

-- Bad
SELECT
  DATE_TRUNC(month,created_at) AS signup_year
  ,COUNT(*) AS total_companies
FROM companies
GROUP BY DATE_TRUNC(month,created_at)
```

### Grouping columns should go first

```sql
-- Good
SELECT
  DATE_TRUNC(month,created_at) AS signup_year
  ,COUNT(*) AS total_companies
FROM companies
GROUP BY signup_year

-- Bad
SELECT
  COUNT(*) AS total_companies
  ,DATE_TRUNC(month,created_at) AS signup_year
FROM companies
GROUP BY signup_year
```

### Aligning case/when statements

Each `when` should be on its own line (nothing on the `case` line) and should be indented one level deeper than the `case` line. The `then` should be on the same line.

```sql
-- Good
SELECT
  CASE
      WHEN event_name = 'viewed_homepage' THEN 'Homepage'
      WHEN event_name = 'viewed_editor' THEN 'Editor'
      ELSE 'Other'
  END AS page_name
from events


-- Bad
SELECT
  CASE WHEN event_name = 'viewed_homepage' THEN 'Homepage'
      WHEN event_name = 'viewed_editor' THEN 'Editor'
      ELSE 'Other'
  END AS page_name
FROM events
```

### Use CTEs, not subqueries

Avoid subqueries; CTEs will make your queries easier to read.

When using CTEs, pad the query with new lines.

If you use any CTEs, always `SELECT *` from the last CTE at the end. That way, you can quickly inspect the output of other CTEs used in the query to debug the results.

Closing CTE parentheses should use the same indentation level as `WITH` and the CTE names.

```sql
-- Good
WITH ordered_details AS (
  SELECT
    user_id
    ,name
    ,ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY date_updated DESC) AS details_rank
  FROM billing_stored_details
),

first_updates AS (
  SELECT
    user_id
    ,name
  FROM ordered_details
  WHERE
    details_rank = 1
)

SELECT * FROM first_updates

-- Bad
SELECT
  user_id
  ,name
FROM (
  SELECT
    user_id
    ,name
    ,ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY date_updated DESC) AS details_rank
  FROM billingdaddy.billing_stored_details
) ranked
WHERE
  details_rank = 1
```

### When writing SQL scripts for DBT always start by "importing the relevant tables"

It makes the code easier to read and it does [not affect the performance of the query](https://discourse.getdbt.com/t/ctes-are-passthroughs-some-research/155)

```sql
-- Good
WITH customers AS (
  SELECT * FROM {{ref('fct_customers')}}
),

orders AS (
  SELECT * FROM {{ref('fct_orders')}}
)
...
```

### Use meaningful CTE names

```sql
-- Good
WITH ordered_details AS (
  ...
)

-- Bad
WITH d1 AS (
  ...
)
```

### Window functions

Leave it all on its own line:

```sql
-- Good
SELECT
  user_id
  ,name
  ,ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY date_updated DESC) AS details_rank
FROM billingdaddy.billing_stored_details

-- Okay
SELECT
  user_id
  ,name
  ,ROW_NUMBER() OVER (
    PARTITION BY user_id
    ORDER BY date_updated DESC
  ) AS details_rank
FROM billingdaddy.billing_stored_details
```

## Credits

This style guide was inspired in part by:

* [dbt Labs' dbt Style Guide](https://github.com/dbt-labs/corp/blob/master/dbt_style_guide.md)
* [KickStarter's SQL Style Guide](https://gist.github.com/fredbenenson/7bb92718e19138c20591)
* [GitLab's SQL Style Guide](https://about.gitlab.com/handbook/business-ops/data-team/sql-style-guide/)

----

:t-rex: Team
