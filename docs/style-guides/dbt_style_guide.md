# DAZN DBT Style Guide

In this document, we outline some best practices and guidelines to follow when creating a new project in the DBT repo. Please make sure your code follows the DAZN standards

## Model Naming

Our models (typically) fit into 3 main categories: staging, core, reporting.

- All the models in staging should have the stg_ prefix
- All objects should be plural, such as `stg_stripe__invoices`
- Intermediate tables should end with a past tense verb indicating the action performed on the object, such as `customers__unioned`
- Core tables are categorized between fact (immutable, verbs) and dimensions (mutable, nouns) with a prefix that indicates either, such as: `fct_orders` or `dim_customers`

## Model configuration

- Model-specific attributes (like sort/dist keys) should be specified in the model.
- If a particular configuration applies to all models in a directory, it should be specified in the `dbt_project.yml` file.
- In-model configurations should be specified like this:

```python
{{
  config(
    materialized = 'table',
    sort = 'id',
    dist = 'id'
  )
}}
```

- In the `dbt_project.yml`, we assume that the materialization is always a view
- For well-tested intermediate steps we use Ephemeral
- For persistent tables, we use materialized = 'table'
- For high storage tables (eg: plays_fact; ga, firebase, etc...) we use materialized = 'incremental'

Here are some more details on [dbt model materialisations](https://docs.getdbt.com/docs/building-a-dbt-project/building-models/materializations)

## DBT conventions

- Only `stg_` models should select from `source`s.
- All other models should only select from other models.

## Testing

- Every subdirectory should contain a `.yml` file, in which each model in the subdirectory is tested. For staging folders, the naming structure should be `src_sourcename.yml`. For other folders, the structure should be `foldername.yml` (example `core.yml`).
- At a minimum, unique and not_null tests should be applied to the primary key of each model.

## Naming and field conventions

- Schema, table and column names should be in `snake_case`.
- Use names based on the _business_ terminology, rather than the source terminology.
- Each model should have a primary key.
- The primary key of a model should be named `<object>_id`, e.g. `account_id` – this makes it easier to know what `id` is being referenced in downstream joined models.
- For base/staging models, fields should be ordered in categories, where identifiers are first and timestamps are at the end.
- Timestamp columns should be named `<event>_at`, e.g. `created_at`, and should be in UTC.
- Booleans should be prefixed with `is_` or `has_`.
- Price/revenue fields should be in decimal currency (e.g. `19.99` for $19.99). If non-decimal currency is used, indicate this with suffix, e.g. `price_in_cents`.
- Avoid reserved words as column names
- Consistency is key! Use the same field names across models where possible, e.g. a key to the `customers` table should be named `customer_id` rather than `user_id`.

## CTEs

For more information about why we use so many CTEs, check out [this discourse post](https://discourse.getdbt.com/t/why-the-fishtown-sql-style-guide-uses-so-many-ctes/1091).

- All `{{ ref('...') }}` statements should be placed in CTEs at the top of the file
- Where performance permits, CTEs should perform a single, logical unit of work.
- CTE names should be as verbose as needed to convey what they do
- CTEs with confusing or noteable logic should be commented
- CTEs that are duplicated across models should be pulled out into their own models
- create a `final` or similar CTE that you select from as your last line of code. This makes it easier to debug code within a model (without having to comment out code!)
- CTEs should be formatted like this:

``` sql
WITH

events AS (
    ...
),

-- CTE comments go here
filtered_events AS (
    ...
)

SELECT * FROM filtered_events
```

## YAML style guide

- Indents should be two spaces
- List items should be indented
- Use a new line to separate list items that are dictionaries where appropriate
- Lines of YAML should be no longer than 80 characters.

### Example YAML

```yaml
version: 2

models:
  - name: events
    columns:
      - name: event_id
        description: This is a unique identifier for the event
        tests:
          - unique
          - not_null

      - name: event_time
        description: "When the event occurred in UTC (eg. 2018-01-01 12:00:00)"
        tests:
          - not_null

      - name: user_id
        description: The ID of the user who recorded the event
        tests:
          - not_null
          - relationships:
              to: ref('users')
              field: id
```

## Jinja style guide

- When using Jinja delimiters, use spaces on the inside of your delimiter, like `{{ this }}` instead of `{{this}}`
- Use newlines to visually indicate logical blocks of Jinja

## Macros

- Always be mindful of the DRY (don't repeat yourself) VS Readability trade-off.
- Do not create macros for functions only used once in the model.

## Seeds

Seeds are CSV files in your dbt project (typically in your data directory), that dbt can load into your data warehouse using the dbt seed command.

Seeds can be referenced in downstream models the same way as referencing models — by using the ref function.

Because these CSV files are located in your dbt repository, they are version controlled and code reviewable. Seeds are best suited to static data which changes infrequently.

The best usecases are:

- date dimension
- region dimension

## Credits

- [dbt Best Practices Guide](https://docs.getdbt.com/docs/guides/best-practices/)
- [dbt Labs' dbt Style Guide](https://github.com/dbt-labs/corp/blob/master/dbt_style_guide.md)
- [dbt Labs' How We Structure Our dbt Projects Guild](https://discourse.getdbt.com/t/how-we-structure-our-dbt-projects/355)
- [Photobox Tips To Get The Best Of dbt - part 1](https://medium.com/photobox-technology-product-and-design/practical-tips-to-get-the-best-out-of-data-building-tool-dbt-part-1-8cfa21ef97c5)
- [Photobox Tips To Get The Best Of dbt - part 2](https://medium.com/photobox-technology-product-and-design/practical-tips-to-get-the-best-out-of-data-build-tool-dbt-part-2-a3581c76723c)
- [Essential dbt Project Checklist](https://discourse.getdbt.com/t/your-essential-dbt-project-checklist/1377)

----

:t-rex: Team
