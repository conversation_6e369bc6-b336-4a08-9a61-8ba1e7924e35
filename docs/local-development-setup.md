# Local Development Setup

This guide will take you through setting up your machine so that you are able to develop and run dbt and Airflow on it. This deliberately doesn't refer to a specific repository as we have multiple dbt projects in separate repos.

:exclamation: This guide is premised on you having a Mac, some steps may be different on Windows or Linux machines. Where applicable please update for steps for other OSs

## Prerequisites

This guide has a number of prerequisites that need to be in place before continuing.

1. Zsh/Bash Terminal
  Recommended autocomplete for your shell setup, unless you really like typing!!

1. Have git cloned the dbt project repository you want to work with
  For this you need [git installed](https://docs.github.com/en/get-started/quickstart/set-up-git#setting-up-git) & [GitHub ssh setup](https://docs.github.com/en/authentication/connecting-to-github-with-ssh/checking-for-existing-ssh-keys)

1. IDE of you choice installed
  Good options include [VS Code](https://code.visualstudio.com/), [VS Code](https://code.visualstudio.com/), [VS Code](https://code.visualstudio.com/) & [PyCharm](https://www.jetbrains.com/pycharm/)

1. Python
  This guide works on the basis that [pyenv](https://github.com/pyenv/pyenv#simple-python-version-management-pyenv) is being used to manage python versions, with [pyenv-virtualenv](https://github.com/pyenv/pyenv-virtualenv#pyenv-virtualenv) plugin to manage virtual environments. To install see the install with Homebrew sections of the respective links and [Managing Multiple Python Versions With pyenv](https://realpython.com/intro-to-pyenv/) for a good introduction to pyenv (Note the *Installing pyenv* section is out of date).

## EditorConfig

We are using [EditorConfig](https://editorconfig.org/) to enable a consistent coding style for the project across multiple IDEs. Most major IDEs support EditorConfig, either [out of the box](https://editorconfig.org/#pre-installed) or [via a plugin](https://editorconfig.org/#download).

- VS Code needs [EditorConfig plugin](https://marketplace.visualstudio.com/items?itemName=EditorConfig.EditorConfig) to be installed

- PyCharm just requires [EditorConfig support to be enabled](https://www.jetbrains.com/help/pycharm/settings-code-style.html#EditorConfig) in Editor Preferences

## Virtual Environment

It's best to create a virtual environment for each repo as there is no guarantee that dbt repo will all be the same.

For the virtual env use the lastest version of Python 3.9 - while any Python version greater than 3.6 will work, 3.9 is the version used by Airflow.

### Install Latest Python 3.9

If you've not setup a dbt Virtual Environment before or it's been a while then follow these steps to install the latest version of Python 3.9. Skip to [Create a Virtual Environment](#create-a-virtual-environment) if you're happy with your Python version.

- Makes sure you have the latest version of pyenv installed as it ships with the latest Python versions. You can [upgrade pyenv](https://github.com/pyenv/pyenv#upgrading) with Homebrew.

- Check which Python 3.9 versions pyenv has avaliable - run `pyenv install --list | grep 3.9`

- Install the latest version of Python 3.9 from the list - run `pyenv install 3.9.<PATCH>`

  Replacing `<PATCH>` with the highest patch number in the list output in the previous command.

  If it is already installed pyenv will say and ask if you want to reinstall - say No. Installing a Python version can take a few minutes.

### Create a Virtual Environment

- Create a virtual environment - run `pyenv virtualenv <PYTHON_VERSION> <VIRTUAL_ENV_NAME>`

  Where `<PYTHON_VERSION>` is the 3.9 version you installed & `<VIRTUAL_ENV_NAME>` is the name you want to called the virtual env. For the name it's good to use the repo name, as it makes managing your virtual environments easier.

  If a virtual environment with the same name already exists pyenv will return that this is the case.

- Set the virtual env as the default for the repo, so when you enter the directory it is automatically activated. This must be run from the root directory of your repo - `echo <VIRTUAL_ENV_NAME> >> .python-version`

This is not the only way to create Python virtual environments and if you are happy using an alternative approch then that's fine.

### Install Requirements

Once your virtual environment is created and activated, use the `requirements.txt` file to install dbt, pre-commit as well other requirements listed in the file.

Run `pip install --requirement requirements.txt`.

To check dbt is installed - run `dbt --version`

## pre-commit

We are using [pre-commit](https://pre-commit.com/) to enable an automated check suite to be run locally on each commit. The checks will only run on the files that you change during your commit, so they are usually pretty fast and do not slow down your development speed. This provides quick feedback on issues and in some cases automatically resolves them.

### Setup pre-commit

pre-commit should be installed in your virtual environnement if you have followed the [Virtual Environment setup steps](#virtual-environment). To set up the git hook scripts run `pre-commit install`

### Run pre-commit

Once setup the pre-commit checks will run on files included in each commit. If any checks fail then the commit will be stopped. If possible the issues will be automatically fixed, so you can just inspect the changes and stage the files for committing.

Alternatively you can run the pre-commit checks against all staged files with `pre-commit run`

When adding or updating hooks it's best to run the pre-commits checks against all files with `pre-commit run --all-files`

## dbt Snowflake Credentials

All of Snowflake account details in the `profiles.yml` are being fetched from environment variables.

To run dbt locally we need to set the environment variables in the terminal that we are going to run dbt in. To make this easy, we'll store these values in a `.env` file.

Create a `.env` file in the root of the repo. Either via your IDE or in the repository's root directory run `touch .env`.

Add the following to your `.env` file and fill in the missing values. These valuse should be:

- `SNOWFLAKE_USER_EMAIL` - Your DAZN email address
- SQLFluff credentials can be found in aws secrets with service name `edm-dp20` at secret path `edm-dp20/secret/prod/cicd/sqlfluff`

``` shell
## Comand to export all variables from .env file
## Needs to be run from the repo's root directory
##   set -a && source .env && set +a

## dbt Profiles directory
DBT_PROFILES_DIR="$(pwd)/dbt"

AIRFLOW_VAR_ENV=local

## Snowflake credentials
SNOWFLAKE_USER_EMAIL=
T_REX_SNOWFLAKE_DATABASE=TRANSFORMATION_DEV
T_REX_SNOWFLAKE_ROLE=TRANSFORMATION_DEV_ROLE
T_REX_SNOWFLAKE_DEFAULT_WAREHOUSE=TRANSFORMATION_DEV_XS_WH
PRD_SNOWFLAKE_DATABASE=PRD_DEV

## SQLFluff credentials
SQLFLUFF_SNOWFLAKE_USER=
SQLFLUFF_SNOWFLAKE_PASSWORD=
SQLFLUFF_SNOWFLAKE_ROLE=SQLFLUFF_SERVICE_ROLE
```

Before running dbt in a terminal window you need to load these variables from the `.env` file. From the repository's root directory run `set -a && source .env && set +a`.

If you update any of the values in the `.env` file you will need to re-run the above command for the change to take effect.

:bulb: The above command is a little wordy so why not simplify things with a cheeky alias....

Bash Alias

``` shell
echo 'alias source-dotenv="set -a && source .env || set +a"' >> ~/.bashrc
```

Zsh Alias

``` shell
echo 'alias source-dotenv="set -a && source .env || set +a"' >> ~/.zshrc
```

## Run Airflow Locally

In the [dazn-dp20-airflow](https://github.com/getndazn/dazn-dp20-airflow) repo we've got an extendable Docker Compose setup for running Airflow locally. You will need to clone that repo and then [follow the instructions to get going](https://github.com/getndazn/dazn-dp20-airflow/blob/master/docs/run-airflow-locally.md). There are some [additional prerequisites](# COMPOSE_FILE='docker-compose.yml:docker-compose.b2c.yml:docker-compose.t-rex.yml'
) to those covered above that need to be met.

To just run the T-Rex Airflow worker you will add the following to the .env file in your *dazn-dp20-airflow* directory.

``` shell
COMPOSE_FILE='docker-compose.yml:docker-compose.b2c.yml:docker-compose.t-rex.yml'

# Build args
PYPI_PASSWORD=

# Snowflake Details
SNOWFLAKE_HOST="si44367.eu-central-1"

# T-Rex
T_REX_SNOWFLAKE_LOGIN=
T_REX_SNOWFLAKE_PASSWORD=
T_REX_SNOWFLAKE_ROLE=TRANSFORMATION_DEV_ROLE
T_REX_SNOWFLAKE_DATABASE=TRANSFORMATION_DEV
T_REX_SNOWFLAKE_DEFAULT_WAREHOUSE=TRANSFORMATION_DEV_XS_WH
PRD_SNOWFLAKE_DATABASE=PRD_DEV
```

The missing values can be found in the AWS SSM of the *dazn-data-dev* AWS account.

----

:t-rex: Team
