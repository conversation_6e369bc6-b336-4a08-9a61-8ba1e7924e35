# Presentation Layer - Technical User Guide

## Overview

The objective of the **Presentation** layer is to make it easy for data consumers to navigate, understand and analyse data models, with a bias towards usability over rigorous structures. 

Contents of this layer are produced from datasets that are readily available in the `Open` and `Curated` layers, which are modelled into business focussed entities via the `Transient` layer and are stored as Snowflake tables.


## High Level Design

This section outlines the target design of the Presentation layer, its logical & physical components. Readers should note that marts released as part of the MVP may not be fully aligned with the design principles listed below.

<img src="Presentation_Layer_Simplified_Design.jpg" alt="drawing" width="1000"/>

### A. Databases

#### A.1 General Characteristics

###### A.1.1
The main entities represented at database level in the Presentation layer are **marts** (aka. data products), **domains** and **reports**. So any database in this layer may represent either a mart, a domain or a report. 

:warning:  Report databases should exist as databases in this layer for tactical purposes only.

###### A.1.2
The type of entity that a database represents must be explicitly indicated in the name of the database object:
- Names of domain databases will contain the string `DOMAIN`
<img src="Domain_Database_Name_EXAMPLE.png" alt="drawing" width="350"/>
- Names of mart databases will contain the string `MART`
<img src="Mart_Database_Name_EXAMPLE.png" alt="drawing" width="350"/>
- Names of report databases will contain the string `REPORT`

See the complete list of [naming conventions for Presentation layer databases](https://livesport.atlassian.net/wiki/spaces/DATA/pages/5559419370/Object+Naming+Conventions#A.-Databases.3).

#### A.2 Domain Databases

Domain databases store conformed dimension tables which have a particular theme in common.

###### A.2.1
Domain databases each represent and store dimension tables belonging to one of the following 7 domains:

<img src="Domains.jpg" alt="drawing" width="1000"/>

**Global** - Dimensions belonging to this domain are not specific to DAZN e.g. date, currency, geographic location. Example database name: `GLOBAL__B2C__DOMAIN`

**Customer** - Dimensions in the customer domain describe relationships that DAZN has with product users e.g. customer, customer preferences. Example database name: `CUSTOMER__B2C__DOMAIN`

**Product** - The product domain groups dimensions that describe DAZN products & services e.g. tier, entitlement set. Example database name:  `PRODUCT__B2C__DOMAIN` 

**Purchase** - Dimensions describing what DAZN customers have bought e.g. giftcode, subscription, commercial partnership. Example database name: `PURCHASE__B2C__DOMAIN`

**Payments** - Dimensions relating to payments made for DAZN products & services e.g. invoice item, payment method. Example database name: `PAYMENTS__B2C__DOMAIN`

**Content** - Dimensions describing creative assets e.g. content item, content item right, advert. Example database name: `CONTENT__B2C__DOMAIN`

**Operations** - Dimensions related to processes, people or technology e.g. device, employee, CDN. Example database name: `OPERATIONS__B2C__DOMAIN`


#### A.3 Mart Databases

###### A.3.1

Mart databases provide users with easy-to-query, highly curated datasets that are tailored to the reporting and analysis requirements of specific subject areas. 

Tables within a mart are logically structured as a star dimensional model, consisting of one [fact table](#c33) that references multiple [dimension tables](#c21) which are otherwise unrelated. Storing the data in this configuration makes future developments easier to integrate within the overall structure, requires less maintenance, enables faster, more efficient queries and facilitates the automated surfacing of these datasets in the BI layer. It also gives consumers the option to select for data relevant to their analysis, by querying the fact table on its own or joining it to only a subset of dimensions.

The following marts are currently part of the Presentation layer development roadmap:
- Playback Stream - available in Snowflake in database `PLAYBACK_STREAM__B2C__MART__PROD`
- Subscription Movements
- Daily Subscriptions Counts
- Invoices
- Advertising Impressions
- Giftcodes Redemption
- User Messages

---

### B. Schemas

The structure of schemas within Presentation layer databases is closely tied to the type of entity that a database represents.

#### B.1 Domain Schemas

###### B.1.1
A domain database may only have **one schema** storing all dimension tables belonging to a particular domain. 

###### B.1.2
The single schema within a domain database must reflect the name of the domain, as specified in the database name. For example:`CONTENT__B2C__DOMAIN`.`CONTENT`, `GLOBAL__B2C__DOMAIN`.`GLOBAL`, `OPERATIONS__B2C__DOMAIN`.`OPERATIONS`


#### B.2 Mart Schemas

###### B.2.1
Tables within a mart database are grouped by category (or domain) under separate schemas i.e. a schema may store only fact tables or only reporting tables or only bridge tables, never a mix. Therefore:
- fact tables within a mart are stored under a `FACT` schema
- bridge tables within a mart are stored under a `BRIDGE` schema
- report tables within a mart are stored under a `REPORT` schema

###### B.2.2
Conformed dimension table views pointing to the same domain database are grouped under one schema, which inherits its name from the schema of the original tables i.e. the name of the domain.

For example, given 2 table views within a mart database, both of which point to tables stored in the `CUSTOMER__B2C__DOMAIN` database, both table views should be stored under a `CUSTOMER` schema.

###### B.2.3
Non-conformed dimension tables should share a common schema that inherits the name of the mart. For example: `PLAYBACK_STREAM__B2C__MART`.`PLAYBACK_STREAM` 

---

### C. Tables

#### C.1 General Characteristics

###### C.1.1

The entity that a table represents in the Presentation layer depends on the entity type of the parent database. Valid table entity types are **dimension**, **fact**, **bridge** and **report**.

###### C.1.2
**Facts** <span style="color:blue"> represent quantitative data or measurements related to a specific business process.

###### C.1.3
**Bridges** <span style="color:blue"> are used to resolve many-to-many relationships between either dimensions or fact tables that have different grains.

###### C.1.4
**Reports** <span style="color:blue"> are persistent datasets that are optimised for reporting, obtained by joining the fact and dimension tables and table views within a mart.

###### C.1.5
**Dimensions** <span style="color:blue"> are collections of attributes that describe - and may be used to categorise - the measurable events recorded by facts. Dimension entities are further categorised as:

- **Conformed dimensions**, <span style="color:blue">which preserve their meaning when used to describe multiple facts.
- **Non-conformed dimensions**, <span style="color:blue"> which are specific to a particular fact and cannot meaningfully describe or be referenced by more than one fact table.

<img src="Dimension_types.jpg" alt="drawing" width="800"/>


###### C.1.6

All tables in the Presentation layer must have a variant data type column that stores **metadata** information about a record as key-value pairs. 

This column should always be in the first position and be labeled as `META__DATA`. 

<img src="Metadata_column_EXAMPLE.png" alt="drawing" width="400"/>

###### C.1.7

All tables in the Presentation layer must have a **primary key** column, with values that uniquely identify every row of a table. 

Primary key values are also referred to as \'**surrogate keys**\' due to these being system generated values, with no business meaning.

The column storing the primary key of a table is always positioned as **the first non-metadata column** and is named after the row-level entity, suffixed with the string '__skey'. For example:
 - *The primary key column of the CONTENT_ITEM__DIM dimension is listed as 'content_item__skey'.*
 - *The primary key column of the PLAYBACK_STREAM__FACT table is listed as 'playback_stream__skey'.*
 
###### C.1.8
All columns in a table which store foreign keys must be positioned immediately after the primary key column.


#### C.2 Tables in Domain Databases

###### C.2.1

Generally, dimension tables store discrete data that describes - and may be used to categorise - the measurable events recorded in a fact table.

###### C.2.2
All tables within a domain database represent **conformed dimensions** belonging to a common domain.

###### C.2.3
There cannot be overlaps between dimensions belonging to distinct domains. What this means:
- Any dimension may be associated with **one and only one** domain,
- A dimension table may only be stored within a single domain database,
- No two domain databases may contain tables representing the same dimension.

###### C.2.4
Dimensions of this type are stored exclusively in domain databases and are surfaced in mart databases as **table views**, grouped under schemas named after the parent domain. Conformed dimension table views may be joined onto the fact table to obtain additional information about the fact or related entities.

<img src="Conformed_dimensions_in_marts.jpg" alt="drawing" width="800"/>


Example scenario:

<img src="Dimensions_surfaced_in_mart_EXAMPLE.png" alt="drawing" width="600"/>

###### C.2.5
Each record of a dimension table or table view represents **a single instance of the entity listed in the table name**. For example:
 - *A single record from the CONTENT_ITEM__DIM dimension captures information about 1 and only 1 piece of content, reflecting its latest state.* 
 - *A single record from the PLAYBACK_STREAM_DETAIL__DIM dimension is a unique combination of stream-specific attributes or details, which may be relate to one or more instances of a playback stream recorded in the fact table.*

###### C.2.6
Tables that capture change history of dimensional entities are also known as **slowly changing dimensions** and have the following characteristics:
- There can be multiple rows corresponding to any one entity, each row representing a distinct version of that entity, including the most recent or current version.
- Each row stores the following attributes, in columns positioned after the `META__DATA` column and before the primary key column of the table:
    
  :small_blue_diamond: in field `record_valid_from_timestamp` - a timestamp indicating when the entity record became valid.
  
    :small_blue_diamond: in field `record_valid_to_timestamp` - a timestamp indicating when the entity record expired. For records that have not expired and represent the latest entity versions, this value should be a date in the future.
  
    :small_blue_diamond: in field `record_is_current` - a flag indicating if the record represents the latest version of an entity.
- When an attribute of an entity changes its value, a new row is inserted storing the new values, and field `record_valid_to_timestamp` from the old record is updated with an expiry timestamp.

###### C.2.7
Dimension tables that store the latest version of each entity are also known as **current dimensions** and have the following characteristics:
- There is a single row per entity, wit values representing its most recent state.
- Each row has a timestamp indicating when the entity record became valid, in field `record_valid_from_timestamp` positioned after the `META__DATA` column and before the primary key column of the table.
- When an attribute of an entity changes its value, the old attribute value is overwritten and the `record_valid_from_timestamp` is updated.

###### C.2.8
A dimension that is slowly changing can also be materialized as a current dimension view, which is derived from and coexists alongside the original table that captures history. The current view consists of the subset of history table records that represent the latest version of each entity (all rows having the `is_current` flag).

---

#### C.3 Tables and Table Views in Mart Databases

###### C.3.1
Tables within a mart database are exclusively categorised as either **facts**, **bridges**, **non-conformed dimensions** or **reports**. 

###### C.3.2
Fact tables, dimension tables and dimension table views within a mart database must have the same grain.

##### FACT TABLES
 
###### C.3.3
Fact tables are expected to store values such as measures, business identifiers and foreign keys that reference linked dimensions.

###### C.3.4
Fact tables can represent data in different ways, at different levels of atomicity. 

**Transaction Fact Tables**
This type of fact table captures measurements as they occur i.e. only measurements that take place are stored. The table grain is 1 row per unique transaction or measurement event. Each record also records a timestamp that indicates when the transaction or measurement event took place.

Examples of facts that may be stored in a transaction fact table:
- *customer purchases*: 1 row per purchase
- *events occurring during playback streaming*: 1 row per event
- *user interactions with the platform*: 1 row per interaction

**Periodic Snapshot Fact Tables**
Periodic snapshot fact tables summarize multiple measurement events occurring over a standard period of time, such as a day, a week or a month. The table grain is always 1 row per time period.

Examples of facts that may be stored in a periodic snapshot table:
- *daily customer counts by market*: 1 row per day per market
- *monthly ad revenue*: 1 row per month per ad
- *weekly subscription cancellations*: 1 row per week

**Accumulating Snapshot Fact Tables**
This type of fact table summarizes measurements events occurring at predictable stages of a process. Each row of an accumulating snapshot table represents an entity that participates in that process, against which measures are stored.
As new measurments occur, the row for the corresponding entity is updated.

Examples of facts that may be stored in an accumulating snapshot table:
- *giftcode redemption status*
 
 
##### BRIDGE TABLES

###### C.3.5
Bridge tables may be used to implement dimension hierarchies (similar to lookup tables e.g. mapping of invoice items to their parent invoice ID) or to align grains between dimension and fact tables.

###### C.3.6
Bridge tables should only store foreign keys  and timestamps recording the beginning and end of their association.

##### NON-CONFORMED DIMENSION TABLES

###### C.3.7
Non-conformed dimensions are materialized directly as tables inside the same mart that stores the fact table to which they relate.

###### C.3.8
Non-conformed dimension tables may not be referenced by more than one fact table, stored within the same mart.

##### CONFORMED DIMENSION TABLE VIEWS

###### C.3.9
Conformed dimensions referenced by fact tables are made available **as views** in the mart databases storing the fact tables. 

###### C.3.10
A mart should only store views of conformed dimension tables that are directly linked to the fact table.

###### C.3.11
A mart may store multiple views pointing to the same conformed dimension table. These views are also known as role-playng dimensions and they are used to represent multiple valid relationships between a fact table and a confirmed dimension.

###### C.3.12
Given that the role of dimensions is to store reference data about facts, all dimension tables and dimension table views within a mart database link to the fact table via **foreign keys**. To enable these joins, the fact table stores the primary key values of each related dimension in a foreign key column that retains the name of primary key column from the source dimension. For example:
- *To retrieve the characteristics of devices that were used for streaming DAZN content, the `PLAYBACK_STREAM__FACT` table can be joined with the `DEVICE_INFO__DIM` table view on the `device_info__skey` column, which is present in both.*

###### C.3.13
After the initial build, new dimensions can be added to an existing fact table by creating new foreign key columns, provided that the new dimension does not alter / is aligned to the fact table's grain.



