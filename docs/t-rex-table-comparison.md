# T-Rex Table Comparison Stored Procedure

The T-Rex team’s table comparison stored procedure was created as a way to quickly compare the data present in two tables.

This was required as part of our DSS to DBT migration to ensure the models we created in DBT were not significantly changing any of the logic within our DSS projects.

The stored procedure itself lives in Snowflake, but we have also [kept it in source control](https://github.com/getndazn/dazn-dp20-presentation/blob/main/dbt/analysis/table_comparison_sproc.sql) so that it can more easily be reviewed and its history tracked.

## Where does the stored procedure live

Currently, the stored procedure has been duplicated three times (one for each of our environments) and the below table shows where each copy lives.

|       Database       |  Schema  |        Owner Role        |
| -------------------- | -------- | ------------------------ |
| TRANSFORMATION_DEV   |  PUBLIC  | TRANSFORMATION_DEV_ROLE  |
| TRANSFORMATION_UAT   |  PUBLIC  | TRANSFORMATION_UAT_ROLE  |
| TRANSFORMATION_PROD  |  PUBLIC  | TRANSFORMATION_PROD_ROLE |

## How to use the stored procedure

The stored procedure's signature currently looks like:

```sql
TREX_TABLE_COMPARISON(
    TABLE1 VARCHAR, TABLE1_PRIMARY_KEY VARCHAR, TABLE1_WHERE_CLAUSE VARCHAR,
    TABLE2 VARCHAR, TABLE2_PRIMARY_KEY VARCHAR, TABLE2_WHERE_CLAUSE VARCHAR
)
```

In order to call it you will need to use the CALL statement within Snowflake, e.g.:

``` sql
CALL TREX_TABLE_COMPARISON('"TRANSFORMATION_UAT"."PRESENTATION"."FCT_FIREBASE_EVENTS"', '"id_dedup"', 'WHERE "event_date" = \'2022-01-18\'',
                           '"PRD_UAT"."ANALYTICS"."firebase_events_fact"', '"user_pseudo_id" || "event_timestamp"', 'WHERE "event_date" = \'2022-01-18\'');
```

### Things to note

1. When calling the stored procedure all arguments are of type `VARCHAR` so should be surrounded by single quotes
1. Always use the fully qualified table names e.g. `"TRANSFORMATION_UAT"."PRESENTATION"."FCT_FIREBASE_EVENTS"`
1. Primary key argument should adhere to the following points.
   - use column name(s) which exists in the relevant table
   - surround the column name(s) in double quotes e.g. `'"id_dedup"'`
   - feel free to use valid SQL when necessary (you are not limited to only using a single column as the primary key) e.g. concatenating two columns e.g. `'"user_pseudo_id" || "event_timestamp"'`
1. The where clause argument should surround column names with double quotes and values with single quotes (which have been escaped with a `\` i.e. `\'` to prevent clashes with the single quotes which surround the argument e.g. `'WHERE "event_date" = \'2022-01-18\''`

## The tests

The following tests run as part of the stored procedure:

1. Row count are the same
1. Each row is distinct
1. Both tables contain all primary keys in the other

Note: The second and third tests are run twice. Test two is run once for each table whereas test three checks all the primary keys in table 1 are present in table 2 as well as checking all the primary keys in table 2 are present in table 1

For more info on the tests check out the [Table Comparison Toolkit Confluence page](https://livesport.atlassian.net/wiki/spaces/AMLDS/pages/5636555063/Table+Comparison+Toolkit)

----

:t-rex: Team
