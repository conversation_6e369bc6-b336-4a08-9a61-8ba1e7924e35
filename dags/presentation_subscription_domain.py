import json
from airflow.operators.python import ShortCircuitOperator
from airflow.sensors.external_task_sensor import <PERSON>TaskMark<PERSON>
from airflow.utils.state import State
from astronomer.providers.core.sensors.external_task import \
    ExternalTaskSensorAsync
from datetime import datetime, timedelta
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.sns_operator import DaznSnsPublishOperator
from helpers.edmdag import EDMDAG

# this dag is defined in a for loop to create a second identical 'full_refresh'
# dag which we can use to run a full_refresh for specific dbt models by clearing
# the relevant run task within this dag
for i in range(2):
    default_args = {
        'profiles_dir': '/usr/local/airflow/dbt/presentation',
        'project_dir': '/usr/local/airflow/dbt/presentation'
    }

    if i:
        # the trigger_rule for all tasks is set to all_done. This allows us to
        # clear any tasks in the full_refresh dag regardless of the state of
        # upstream tasks
        default_args.update(
            {
                'trigger_rule': 'all_done'
            }
        )

    # Airflow will not allow two dags with the same dag_id so we add
    # '-full_refresh' as a suffix to differentiate between the two dags
    dag_id_suffix = '-full_refresh' if i else ''
    with EDMDAG(
        dag_id=f'presentation-subscription-domain{dag_id_suffix}',
        partner='subscription-domain',
        default_args=default_args,
        start_date=datetime(2022, 6, 14),
        schedule_interval="50 0 * * *",
        owner='t-rex',
        tags=[
            'presentation',
            'subscription',
            'payments'
        ]
    ) as dag:

        # TODO: Need to add this functionality to dazn-airflow-dbt package
        with open('dbt/presentation/target/manifest.json') as json_file:
            manifest_data = json.load(json_file)

        dbt_task_generator = DbtTaskGenerator(
            manifest_data,
            # select_tag value below means that the DbtTaskGenerator will create
            # a dag for all dbt resources with the tag:
            # 'presentation-subscription-domain'
            select_tag='presentation-subscription-domain',
            full_refresh=True if i else False
        )
        dbt_task_generator.generate_dbt_tasks()

        check_curated_zuora_success = ExternalTaskSensorAsync(
                task_id='check-presentation-staging-success',
                external_dag_id='presentation-staging',
                external_task_id='presentation-staging-success-marker',
                failed_states=[
                    State.FAILED,
                    State.UPSTREAM_FAILED
                ],
                execution_delta=timedelta(minutes=0),
                mode='reschedule',
                poke_interval=60 * 10,
                timeout=60 * 60 * 8,
                retries=0
            )
        check_curated_zuora_success >> dbt_task_generator.roots

        if i:
            # the ShortCircuitOperator is added to start of the fll_refresh dag
            # with a  python_callable which always returns False. This ensures
            # that the downstream tasks in the DAG never run unless they are
            # manually cleared
            short_circuit = ShortCircuitOperator(
                task_id='short_circuit',
                python_callable=lambda: False,
            )
            short_circuit >> check_curated_zuora_success


        msg_details={
            'DagName' : dag.dag_id,
            #'BatchDate':f'{dag.year}-{dag.month}-{dag.day}',
            'DagCompletionTime':datetime.now().isoformat(sep=' ',timespec='seconds'),
            'State' : "Success" }

        send_validation_message = DaznSnsPublishOperator(
        task_id=f'send-{dag.dag_id}-success-notification',
        target_arn='{{ var.value.airflow_notifications_sns_arn }}',
        subject='Airflow {{dag.env.upper()}} - {{dag.dag_id}} - ' + msg_details["DagCompletionTime"] + ' - DAG Success',
        message= '\n'.join([f"{key}: {value}" for key, value in msg_details.items()]),
        message_attributes={
            "callback_event" : "dag-success"
            }
        )


        pipeline_success_marker = ExternalTaskMarker(
            task_id='presentation-subscription-domain-success-marker',
            external_dag_id='core-P1Daily-CustomerHistorySCD',
            external_task_id='check-presentation-subscription-domain-success',
            execution_date='{{ execution_date.add(hours=1).isoformat() }}',
            retries=3
        )

        pipeline_success_marker_sky_addon_reconcilation = ExternalTaskMarker(
            task_id='presentation-subscription-domain-success-marker-sky-addon-reconciliation',
            external_dag_id='presentation-sky-addon-reconciliation',
            external_task_id='check-presentation-subscription-domain-success',
            execution_date='{{ execution_date.add(hours=5, minutes=10).isoformat() }}',
            retries=3
        )

        pipeline_success_marker_revenue_share_sky_it = ExternalTaskMarker(
            task_id='presentation-subscription-domain-success-marker-sky-it-revenue-share',
            external_dag_id='presentation-sky-it-revenue-share',
            external_task_id='check-presentation-subscription-domain-success',
            execution_date='{{ execution_date.add(hours=4).isoformat() }}',
            retries=3
        )

        pipeline_success_marker_optimove = ExternalTaskMarker(
            task_id='presentation-subscription-domain-success-marker-optimove',
            external_dag_id='exchange-optimove',
            external_task_id='check-presentation-subscription-domain-success',
            execution_date='{{ execution_date.add(hours=1, minutes=55).isoformat() }}',
            retries=3
        )

        pipeline_success_marker_partner_access_codes_daily = ExternalTaskMarker(
            task_id='presentation-subscription-domain-success-marker-partner-access-codes-daily',
            external_dag_id='exchange-partner-access-codes-daily',
            external_task_id='check-presentation-subscription-domain-success',
            execution_date='{{ execution_date.add(hours=1, minutes=10).isoformat() }}',
            retries=3
        )

        pipeline_success_marker_plays_mart = ExternalTaskMarker(
            task_id='presentation-subscription-domain-success-marker-plays-mart',
            external_dag_id='presentation-playback-stream-mart',
            external_task_id='check-pipeline-subscription-domain-success',
            execution_date='{{ execution_date.add(hours=1, minutes=10).isoformat() }}',
            retries=3
        )

        pipeline_success_marker_nielsen = ExternalTaskMarker(
            task_id='presentation-subscription-domain-success-marker-nielsen',
            external_dag_id='exchange-nielsen',
            external_task_id='check-pipeline-subscription-domain-success',
            execution_date='{{ execution_date.add(hours=3, minutes=10).isoformat() }}',
            retries=3
        )

        pipeline_success_marker_exchange_bigquery = ExternalTaskMarker(
            task_id='presentation-subscription-domain-success-marker-exchange-bigquery',
            external_dag_id='exchange-bigquery-daily',
            external_task_id='check-presentation-subscription-domain-success',
            execution_date='{{ execution_date.add(hours=5, minutes=10).isoformat() }}',
            retries=3
        )

        pipeline_success_marker_nfl_gpi = ExternalTaskMarker(
            task_id='presentation-subscription-domain-success-marker-nfl-gpi',
            external_dag_id='exchange-nfl-gpi',
            external_task_id='check-presentation-subscription-domain-success',
            execution_date='{{ execution_date.add(hours=2, minutes=55).isoformat() }}',
            retries=3
        )

        pipeline_success_marker_freemium_playback_stream = ExternalTaskMarker(
            task_id='presentation-subscription-domain-success-marker-presentation-playback-stream-freemium',
            external_dag_id='presentation-playback-stream-freemium',
            external_task_id='check-presentation-subscription-domain-success',
            execution_date='{{ execution_date.add(hours=1, minutes=55).isoformat() }}',
            retries=3
        )

        pipeline_success_marker_pga = ExternalTaskMarker(
            task_id='presentation-subscription-domain-success-marker-pga',
            external_dag_id='exchange-pga',
            external_task_id='check-presentation-subscription-domain-success',
            execution_date='{{ execution_date.add(hours=1, minutes=55).isoformat() }}',
            retries=3
        )

        pipeline_success_marker_airship = ExternalTaskMarker(
            task_id='presentation-subscription-domain-success-marker-daily-airship',
            external_dag_id='core-P1Daily-Airship',
            external_task_id='check-presentation-subscription-domain-success',
            execution_date='{{ execution_date.add(hours=2).isoformat() }}',
            retries=3
        )

        pipeline_success_marker_rally_tv = ExternalTaskMarker(
            task_id='presentation-subscription-domain-success-marker-rally-tv',
            external_dag_id='exchange-rally-tv',
            external_task_id='check-presentation-subscription-domain-success',
            execution_date='{{ execution_date.add(hours=1, minutes=55).isoformat() }}',
            retries=3
        )

        pipeline_success_marker_atp = ExternalTaskMarker(
            task_id='presentation-subscription-domain-success-marker-atp',
            external_dag_id='exchange-atp',
            external_task_id='check-presentation-subscription-domain-success',
            execution_date='{{ execution_date.add(hours=1, minutes=55).isoformat() }}',
            retries=3
        )

        pipeline_success_marker_ppv_purchases = ExternalTaskMarker(
            task_id='presentation-success-marker-ppv-purchases',
            external_dag_id='ppv-purchases-fact',
            external_task_id='check-presentation-scd-success',
            execution_date='{{ execution_date.add(minutes=0).isoformat() }}',
            retries=3
        )

        pipeline_success_marker_inv_itm_join = ExternalTaskMarker(
            task_id='presentation-success-marker-inv-itm-join',
            external_dag_id='presentation-invoice-items',
            external_task_id='check-presentation-scd-success',
            execution_date='{{ execution_date.add(minutes=20).isoformat() }}',
            retries=3
        )

        pipeline_success_marker_elf = ExternalTaskMarker(
            task_id='presentation-subscription-domain-success-marker-elf',
            external_dag_id='exchange-elf',
            external_task_id='check-presentation-subscription-domain-success',
            execution_date='{{ execution_date.add(hours=6, minutes=40).isoformat() }}',
            retries=3
        )

        task = dbt_task_generator.get_task_by_id('run-subscription_name__scd')
        task >> pipeline_success_marker_ppv_purchases
        task >> pipeline_success_marker_inv_itm_join

        leaves = dbt_task_generator.leaves

        leaves >> pipeline_success_marker
        leaves >> pipeline_success_marker_sky_addon_reconcilation
        leaves >> pipeline_success_marker_revenue_share_sky_it
        leaves >> pipeline_success_marker_optimove
        leaves >> pipeline_success_marker_partner_access_codes_daily
        leaves >> pipeline_success_marker_plays_mart
        leaves >> pipeline_success_marker_nielsen
        leaves >> pipeline_success_marker_exchange_bigquery
        leaves >> pipeline_success_marker_nfl_gpi
        leaves >> pipeline_success_marker_freemium_playback_stream
        leaves >> pipeline_success_marker_pga
        leaves >> pipeline_success_marker_airship
        leaves >> pipeline_success_marker_rally_tv
        leaves >> pipeline_success_marker_atp
        leaves >> pipeline_success_marker_elf

        [pipeline_success_marker, pipeline_success_marker_sky_addon_reconcilation, pipeline_success_marker_revenue_share_sky_it,
         pipeline_success_marker_optimove, pipeline_success_marker_partner_access_codes_daily, pipeline_success_marker_plays_mart,
         pipeline_success_marker_nielsen, pipeline_success_marker_exchange_bigquery, pipeline_success_marker_nfl_gpi,
         pipeline_success_marker_freemium_playback_stream, pipeline_success_marker_pga, pipeline_success_marker_airship,
         pipeline_success_marker_rally_tv, pipeline_success_marker_atp, pipeline_success_marker_elf] >> send_validation_message
