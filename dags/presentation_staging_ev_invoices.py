import json
from datetime import datetime, timedelta

from airflow.sensors.external_task_sensor import <PERSON><PERSON>ask<PERSON>ark<PERSON>
from astronomer.providers.core.sensors.external_task import ExternalTaskSensorAsync
from airflow.utils.state import State
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.edmdag import EDMDAG

default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/presentation',
    'project_dir': '/usr/local/airflow/dbt/presentation'
}

with EDMDAG(
    dag_id='presentation-staging-ev-invoices',
    partner='staging',
    default_args=default_args,
    start_date=datetime(2025, 2, 23),
    schedule_interval="20 0 * * *",
    owner='t-rex',
    tags=[ 'presentation', 'ev', 'dmp', 'invoices', 'payments' ]
) as dag:

    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    roots = dbt_task_generator.roots

    check_curated_zuora_success = ExternalTaskSensorAsync(
            task_id='check-curated-ev-invoices-success',
            external_dag_id='curated-ev-invoices',
            external_task_id='curated-ev-invoices-success-marker-presentation-staging-ev-invoices',
            failed_states=[
                State.FAILED,
                State.UPSTREAM_FAILED
            ],
            execution_delta=timedelta(minutes=5),
            mode='reschedule',
            poke_interval=60 * 10,
            timeout=60 * 60 * 8,
            retries=0
        )
    check_curated_zuora_success >> roots

    pipeline_success_marker_ev_invoices = ExternalTaskMarker(
        task_id='presentation-staging-ev-invoices-success-marker-presentation-staging',
        external_dag_id='presentation-staging',
        external_task_id='check-presentation-staging-ev-invoices-success',
        execution_date='{{ execution_date.add(minutes=30).isoformat() }}',
        retries=0
    )
    leaves = dbt_task_generator.leaves
    leaves >> pipeline_success_marker_ev_invoices
