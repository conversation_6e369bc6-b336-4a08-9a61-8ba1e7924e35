import json
from datetime import datetime, timedelta

from airflow.sensors.external_task_sensor import <PERSON><PERSON>askMark<PERSON>
from airflow.utils.state import State
from astronomer.providers.core.sensors.external_task import \
    ExternalTaskSensorAsync
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.edmdag import EDMDAG

with EDMDAG(
    dag_id='presentation-linear-dimension',
    partner='broadcast',
    start_date=datetime(2022, 6, 14),
    schedule_interval="25 1 * * *",
    owner='t-rex',
    tags=[
        'broadcast'
    ]
) as dag:

    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    # TODO: Should use Airflow default_args to pass in these values
    dbt_operators_kwargs = {
        'profiles_dir': '/usr/local/airflow/dbt/presentation',
        'dir': '/usr/local/airflow/dbt/presentation'
    }
    dbt_task_generator = DbtTaskGenerator(manifest_data, dbt_operators_kwargs=dbt_operators_kwargs)
    dbt_task_generator.generate_dbt_tasks()

    check_curated_bc_success = ExternalTaskSensorAsync(
            task_id='check-curated-broadcast-success',
            external_dag_id='curated-broadcast',
            external_task_id='curated-broadcast-success-marker',
            failed_states=[
                State.FAILED,
                State.UPSTREAM_FAILED
            ],
            execution_delta=timedelta(minutes=20),
            mode='reschedule',
            poke_interval=60 * 10,
            timeout=60 * 60 * 8,
            retries=0
        )

    check_curated_bc_success >> dbt_task_generator.roots

    success_marker_linear_fact = ExternalTaskMarker(
        task_id='presentation-linear-dimension-success-marker',
        external_dag_id='presentation-playback-linear-stream-mart',
        external_task_id='check-presentation-linear-dimension-success',
        execution_date='{{ execution_date.add(hours=2, minutes=35).isoformat() }}',
        retries=0
    )

    leaves = dbt_task_generator.leaves
    leaves >> success_marker_linear_fact
