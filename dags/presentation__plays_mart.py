import json
from datetime import datetime, timedelta
from airflow.sensors.external_task_sensor import <PERSON><PERSON>askMarker
from airflow.utils.state import State
from astronomer.providers.core.sensors.external_task import \
    ExternalTaskSensorAsync
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.sns_operator import DaznSnsPublishOperator
from helpers.edmdag import EDMDAG

default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/presentation',
    'project_dir': '/usr/local/airflow/dbt/presentation'
}

with EDMDAG(
    dag_id='presentation-playback-stream-mart',
    partner='conviva',
    default_args=default_args,
    start_date=datetime(2022, 5, 4),
    schedule_interval="0 2 * * *",
    owner='t-rex',
    tags=[
        'mart',
        'content',
        'presentation'
    ]
) as dag:

    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    check_curated_plays_mart_success = ExternalTaskSensorAsync(
            task_id='check-curated-plays-mart-success',
            external_dag_id='curated-plays-mart',
            external_task_id='curated-plays-mart-success-marker',
            failed_states=[
                State.FAILED,
                State.UPSTREAM_FAILED
            ],
            execution_delta=timedelta(minutes=30),
            mode='reschedule',
            poke_interval=60 * 5,
            timeout=60 * 60 * 8,
            retries=0
        )

    check_content_dimension_success = ExternalTaskSensorAsync(
        task_id='check-presentation-content-dimension-success',
        external_dag_id='presentation-content-dimension',
        external_task_id='presentation-content-dimension-success-marker-plays-mart',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(minutes=50),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 8,
        retries=0
    )

    check_pipeline_subscription_success = ExternalTaskSensorAsync(
        task_id='check-pipeline-subscription-domain-success',
        external_dag_id='presentation-subscription-domain',
        external_task_id='presentation-subscription-domain-success-marker-plays-mart',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours=1, minutes=10),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 4,
        retries=0
    )

    check_core_p1_device_adjustments_success = ExternalTaskSensorAsync(
        task_id='check-core-P1Daily-DeviceAdjustments-success',
        external_dag_id='core-P1Daily-DeviceAdjustments',
        external_task_id='core-P1Daily-DeviceAdjustments-success-marker-plays-mart',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours=1),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 4,
        retries=0
    )

    roots = dbt_task_generator.roots

    check_curated_plays_mart_success >> roots
    check_content_dimension_success >> roots
    check_pipeline_subscription_success >> roots
    check_core_p1_device_adjustments_success >> roots


    msg_details={
            'DagName' : dag.dag_id,
            #'BatchDate':f'{dag.year}-{dag.month}-{dag.day}',
            'DagCompletionTime':datetime.now().isoformat(sep=' ',timespec='seconds'),
            'State' : "Success" }
    
    send_validation_message = DaznSnsPublishOperator(
        task_id=f'send-{dag.dag_id}-success-notification',
        target_arn='{{ var.value.airflow_notifications_sns_arn }}',
        subject='Airflow {{dag.env.upper()}} - {{dag.dag_id}} - ' + msg_details["DagCompletionTime"] + ' - DAG Success',
        message= '\n'.join([f"{key}: {value}" for key, value in msg_details.items()]),
        message_attributes={
            "callback_event" : "dag-success"
            } 
        )
    success_marker_exchange_auditel_viewership_sample = ExternalTaskMarker(
        task_id='success-marker-exchange-auditel-viewership-sample',
        external_dag_id='exchange-auditel-daily-viewership-sample',
        external_task_id='check-presentation-playback-stream-mart-success',
        execution_date='{{ execution_date.add(hours=2).isoformat() }}',
        retries=0
    )

    success_marker_revenue_share_sky_it = ExternalTaskMarker(
        task_id='presentation-plays-mart-success-marker-sky-it-revenue-share',
        external_dag_id='presentation-sky-it-revenue-share',
        external_task_id='check-presentation-plays-mart-success',
        execution_date='{{ execution_date.add(hours=2, minutes=50).isoformat() }}',
        retries=0
    )

    success_marker_ads = ExternalTaskMarker(
        task_id='presentation-plays-mart-success-marker-ads',
        external_dag_id='core-P1Daily-AdsPipelines',
        external_task_id='check-presentation-plays-mart-success',
        execution_date='{{ execution_date.add(hours=5, minutes=30).isoformat() }}',
        retries=0
    )

    success_marker_tippler = ExternalTaskMarker(
        task_id='presentation-plays-mart-success-marker-tippler',
        external_dag_id='core-P1Daily-TipplerMVP',
        external_task_id='check-presentation-plays-mart-success',
        execution_date='{{ execution_date.add(hours=7).isoformat() }}',
        retries=0
    )

    success_marker_watch_party = ExternalTaskMarker(
        task_id='presentation-plays-mart-success-marker-watch-party',
        external_dag_id='core-P1Daily-WatchPartySessions',
        external_task_id='check-presentation-plays-mart-success',
        execution_date='{{ execution_date.add(hours=3).isoformat() }}',
        retries=0
    )

    success_marker_docomo = ExternalTaskMarker(
        task_id='presentation-plays-mart-success-marker-docomo',
        external_dag_id='exchange-docomo',
        external_task_id='check-presentation-plays-mart-success',
        execution_date='{{ execution_date.add(hours=3, minutes=45).isoformat() }}',
        retries=0
    )

    success_marker_optimove = ExternalTaskMarker(
        task_id='presentation-plays-mart-success-marker-optimove',
        external_dag_id='exchange-optimove',
        external_task_id='check-presentation-playback-stream-mart-success',
        execution_date='{{ execution_date.add( minutes=45).isoformat() }}',
        retries=0
    )

    success_marker_nielsen = ExternalTaskMarker(
        task_id='presentation-plays-mart-success-marker-nielsen',
        external_dag_id='exchange-nielsen',
        external_task_id='check-presentation-plays-mart-success',
        execution_date='{{ execution_date.add(hours=2).isoformat() }}',
        retries=0
    )

    success_marker_nfl_gpi = ExternalTaskMarker(
        task_id='presentation-plays-mart-success-marker-nfl-gpi',
        external_dag_id='exchange-nfl-gpi',
        external_task_id='check-presentation-plays-mart-success',
        execution_date='{{ execution_date.add(hours=1, minutes=45).isoformat() }}',
        retries=0
    )

    success_marker_concurrency_predictions = ExternalTaskMarker(
        task_id='presentation-plays-mart-success-marker-concurrency-predictions',
        external_dag_id='presentation-concurrency-predictions',
        external_task_id='check-presentation-playback-stream-mart-success',
        execution_date='{{ execution_date.add(hours=1, minutes=45).isoformat() }}',
        retries=0
    )

    success_marker_freemium_playback_stream = ExternalTaskMarker(
            task_id='presentation-playback-stream-mart-success-marker-presentation-playback-stream-freemium',
            external_dag_id='presentation-playback-stream-freemium',
            external_task_id='check-presentation-playback-stream-mart-success',
            execution_date='{{ execution_date.add( minutes=45).isoformat() }}',
            retries=0
    )

    success_marker_pga = ExternalTaskMarker(
        task_id='presentation-playback-stream-mart-success-marker-pga',
        external_dag_id='exchange-pga',
        external_task_id='check-presentation-playback-stream-mart-success',
        execution_date='{{ execution_date.add( minutes=45).isoformat() }}',
        retries=0
    )

    success_marker_telefonica = ExternalTaskMarker(
        task_id='presentation-playback-stream-mart-success-marker-telefonica',
        external_dag_id='exchange-telefonica',
        external_task_id='check-presentation-playback-stream-mart-success',
        execution_date='{{ execution_date.add( minutes=45).isoformat() }}',
        retries=0
    )

    success_marker_rally_tv = ExternalTaskMarker(
        task_id='presentation-playback-stream-mart-success-marker-rally-tv',
        external_dag_id='exchange-rally-tv',
        external_task_id='check-presentation-playback-stream-mart-success',
        execution_date='{{ execution_date.add( minutes=45).isoformat() }}',
        retries=0
    )

    success_marker_atp = ExternalTaskMarker(
        task_id='presentation-playback-stream-mart-success-marker-atp',
        external_dag_id='exchange-atp',
        external_task_id='check-presentation-playback-stream-mart-success',
        execution_date='{{ execution_date.add( minutes=45).isoformat() }}',
        retries=0
    )

    success_marker_linear_playback_stream = ExternalTaskMarker(
        task_id='presentation-playback-stream-mart-success-marker-playback-linear',
        external_dag_id='presentation-playback-stream-freemium',
        external_task_id='check-presentation-playback-stream-mart-success',
        execution_date='{{ execution_date.add(hours=1,minutes=30).isoformat() }}',
        retries=0
    )

    success_marker_elf = ExternalTaskMarker(
        task_id='presentation-playback-stream-mart-success-marker-elf',
        external_dag_id='exchange-elf',
        external_task_id='check-presentation-playback-stream-mart-success',
        execution_date='{{ execution_date.add(hours=5, minutes=30).isoformat() }}',
        retries=0
    )

    mart_task_names = [
        'run-MART__APPLICATION__DIM',
        'run-MART__CUSTOMER_IDENTITY__DIM',
        'run-MART__DATE__DIM',
        'run-MART__DEVICE_INFO__DIM',
        'run-MART__ENTITLEMENTS__DIM',
        'run-MART__PLAYBACK_STREAM_DETAILS__DIM',
        'run-MART__PLAYBACK_STREAM_ISSUE__DIM',
        'run-MART__PLAYBACK_STREAM_LOCATION__DIM',
        'run-MART__PLAYBACK_STREAM__FACT',
        'run-plays_fact_dn',
    ]


    for task_name in mart_task_names:

        leaf = dbt_task_generator.get_task_by_id(f'{task_name}')

        leaf >> success_marker_exchange_auditel_viewership_sample
        leaf >> success_marker_revenue_share_sky_it
        leaf >> success_marker_ads
        leaf >> success_marker_tippler
        leaf >> success_marker_watch_party
        leaf >> success_marker_docomo
        leaf >> success_marker_nielsen
        leaf >> success_marker_optimove
        leaf >> success_marker_nfl_gpi
        leaf >> success_marker_concurrency_predictions
        leaf >> success_marker_freemium_playback_stream
        leaf >> success_marker_pga
        leaf >> success_marker_telefonica
        leaf >> success_marker_rally_tv
        leaf >> success_marker_atp
        leaf >> success_marker_linear_playback_stream
        leaf >> success_marker_elf


    [success_marker_exchange_auditel_viewership_sample, success_marker_revenue_share_sky_it, success_marker_ads, success_marker_tippler,
     success_marker_watch_party, success_marker_docomo, success_marker_nielsen, success_marker_optimove, success_marker_nfl_gpi,
     success_marker_concurrency_predictions, success_marker_freemium_playback_stream, success_marker_pga,
     success_marker_telefonica, success_marker_rally_tv, dbt_task_generator.get_task_by_id('run-CONCURRENCY__FACT'),
     success_marker_atp, success_marker_linear_playback_stream, success_marker_elf ] >> send_validation_message
