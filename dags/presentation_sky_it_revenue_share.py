import json
from datetime import datetime, timedelta

from astronomer.providers.core.sensors.external_task import ExternalTaskSensorAsync
from airflow.utils.state import State
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.edmdag import EDMDAG

default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/presentation',
    'project_dir': '/usr/local/airflow/dbt/presentation'
}

with EDMDAG(
    dag_id='presentation-sky-it-revenue-share',
    partner='sky-it-revenue-share',
    default_args=default_args,
    start_date=datetime(2022, 8, 1),
    max_active_runs=1,
    catchup=True,
    schedule_interval="50 4 * * *",
    owner='t-rex',
    tags=[
        'presentation',
        'subscription'
    ]
) as dag:
    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    check_presentation_subscription_domain_success = ExternalTaskSensorAsync(
        task_id='check-presentation-subscription-domain-success',
        external_dag_id='presentation-subscription-domain',
        external_task_id='presentation-subscription-domain-success-marker-sky-it-revenue-share',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours=4),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 8,
        retries=0
    )

    check_invoice_fact_success = ExternalTaskSensorAsync(
        task_id='check-presentation-invoice-fact-success',
        external_dag_id='presentation-invoice-fact',
        external_task_id='presentation-subscription-domain-success-marker-sky-it-revenue-share',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours= 2, minutes=35),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 8,
        retries=0
    )

    check_plays_mart_success = ExternalTaskSensorAsync(
        task_id='check-presentation-plays-mart-success',
        external_dag_id='presentation-playback-stream-mart',
        external_task_id='presentation-plays-mart-success-marker-sky-it-revenue-share',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours=2, minutes=50),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 8,
        retries=0
    )

    roots = dbt_task_generator.roots
    check_presentation_subscription_domain_success >> roots
    check_invoice_fact_success >> roots
    check_plays_mart_success >> roots
