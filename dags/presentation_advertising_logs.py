import json
from datetime import datetime, timedelta

from astronomer.providers.core.sensors.external_task import ExternalTaskSensorAsync
from airflow.sensors.external_task_sensor import ExternalTaskMarker
from airflow.utils.state import State
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.edmdag import EDMDAG

default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/presentation',
    'project_dir': '/usr/local/airflow/dbt/presentation'
}


with EDMDAG(
    dag_id='presentation-advertising-logs',
    partner='advertising', #TBC
    default_args=default_args,
    start_date=datetime(2022, 5, 4),
    schedule_interval="15 7 * * *",
    owner='t-rex',
    tags=[
        'presentation',
        'advertising'
    ]
) as dag:

    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    check_m2a_success = ExternalTaskSensorAsync(
        task_id='check-ingest-m2a-success',
        external_dag_id='ingest-m2a',
        external_task_id='eventlog-load_into_snowflake',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours=5, minutes=15),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 8,
        retries=0
    )

    check_GV_success = ExternalTaskSensorAsync(
        task_id='check-curated-GV-success',
        external_dag_id='curated-asruns',
        external_task_id='curated-GV-success-marker',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(minutes=10),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 8,
        retries=0
    )


    roots = dbt_task_generator.roots

    check_m2a_success >> roots
    check_GV_success >> roots

    marker_migration_nielsen_italy = ExternalTaskMarker(
        task_id='presentation-advertising-logs-success-nielsen-italy',
        external_dag_id='exchange-nielsen-italy',
        external_task_id='check-presentation-advertising-logs-success',
        execution_date='{{ execution_date.add(minutes=15).isoformat() }}',
        retries=0
    )

    leaves = dbt_task_generator.leaves

    leaves >> marker_migration_nielsen_italy
