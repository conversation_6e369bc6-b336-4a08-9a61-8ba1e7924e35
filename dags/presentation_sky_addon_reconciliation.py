import json
from datetime import datetime, timedelta

from astronomer.providers.core.sensors.external_task import ExternalTaskSensorAsync
from airflow.utils.state import State
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.edmdag import EDMDAG

default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/presentation',
    'project_dir': '/usr/local/airflow/dbt/presentation'
}

with EDMDAG(
    dag_id='presentation-sky-addon-reconciliation',
    partner='sky-addon-reconciliation',
    default_args=default_args,
    start_date=datetime(2022, 5, 4),
    schedule_interval="0 6 * * *",
    owner='t-rex',
    tags=[
        'presentation',
        'payments'
    ]
) as dag:
    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    check_presentation_subscription_domain_success = ExternalTaskSensorAsync(
        task_id='check-presentation-subscription-domain-success',
        external_dag_id='presentation-subscription-domain',
        external_task_id='presentation-subscription-domain-success-marker-sky-addon-reconciliation',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours=5, minutes=10),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 4,
        retries=0
    )

    check_presentation_subscription_domain_success >> dbt_task_generator.roots
