import json
from datetime import datetime, timedelta

from astronomer.providers.core.sensors.external_task import ExternalTaskSensorAsync
from airflow.utils.state import State
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.edmdag import EDMDAG

default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/presentation',
    'project_dir': '/usr/local/airflow/dbt/presentation'
}


with EDMDAG(
    dag_id='presentation-concurrency-predictions',
    partner='concurrency', #TBC
    default_args=default_args,
    start_date=datetime(2022, 5, 4),
    schedule_interval="45 3 * * *",
    owner='t-rex',
    tags=[
        'presentation',
        'concurrency'
    ]
) as dag:

    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    check_plays_mart_success = ExternalTaskSensorAsync(
        task_id='check-presentation-playback-stream-mart-success',
        external_dag_id='presentation-playback-stream-mart',
        external_task_id='presentation-plays-mart-success-marker-concurrency-predictions',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours=1, minutes=45),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 8,
        retries=0
    )

    check_content_dimension_success = ExternalTaskSensorAsync(
        task_id='check-presentation-content-dimension-success',
        external_dag_id='presentation-content-dimension',
        external_task_id='presentation-content-dimension-success-marker-concurrency-predictions',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours=2, minutes=35),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 8,
        retries=0
    )


    roots = dbt_task_generator.roots

    check_plays_mart_success >> roots
    check_content_dimension_success >> roots
