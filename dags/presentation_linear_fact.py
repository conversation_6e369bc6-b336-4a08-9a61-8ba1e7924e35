import json
from datetime import datetime, timedelta

from airflow.sensors.external_task_sensor import <PERSON><PERSON>askMark<PERSON>
from airflow.utils.state import State
from astronomer.providers.core.sensors.external_task import \
    ExternalTaskSensorAsync
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.edmdag import EDMDAG

with EDMDAG(
    dag_id='presentation-playback-linear-stream-mart',
    partner='conviva',
    start_date=datetime(2025, 1, 1),
    schedule_interval="30 3 * * *",
    owner='t-rex',
    tags=[
        'broadcast',
        'mart',
        'linear',
        'presentation'
    ]
) as dag:

    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    # TODO: Should use Airflow default_args to pass in these values
    dbt_operators_kwargs = {
        'profiles_dir': '/usr/local/airflow/dbt/presentation',
        'dir': '/usr/local/airflow/dbt/presentation'
    }
    dbt_task_generator = DbtTaskGenerator(manifest_data, dbt_operators_kwargs=dbt_operators_kwargs)
    dbt_task_generator.generate_dbt_tasks()

    check_playback_stream_mart_success = ExternalTaskSensorAsync(
            task_id='check-presentation-playback-stream-mart-success',
            external_dag_id='presentation-playback-stream-mart',
            external_task_id='presentation-playback-stream-mart-success-marker-playback-linear',
            failed_states=[
                State.FAILED,
                State.UPSTREAM_FAILED
            ],
            execution_delta=timedelta(hours=1,minutes=30),
            mode='reschedule',
            poke_interval=60 * 10,
            timeout=60 * 60 * 8,
            retries=0
        )

    check_presentation_linear_dimension_success = ExternalTaskSensorAsync(
            task_id='check_presentation_linear_dimension_success',
            external_dag_id='presentation-linear-dimension',
            external_task_id='presentation-linear-dimension-success-marker',
            failed_states=[
                State.FAILED,
                State.UPSTREAM_FAILED
            ],
            execution_delta=timedelta(hours=2,minutes=5),
            mode='reschedule',
            poke_interval=60 * 10,
            timeout=60 * 60 * 8,
            retries=0
        )
    
    check_playback_stream_mart_success >> dbt_task_generator.roots
    check_presentation_linear_dimension_success >> dbt_task_generator.roots
