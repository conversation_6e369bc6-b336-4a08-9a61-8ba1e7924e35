import json
from datetime import datetime, timedelta

from airflow.sensors.external_task_sensor import <PERSON><PERSON>askMark<PERSON>
from airflow.utils.state import State
from astronomer.providers.core.sensors.external_task import \
    ExternalTaskSensorAsync
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.edmdag import EDMDAG

with EDMDAG(
    dag_id='presentation-content-dimension',
    partner='content',
    start_date=datetime(2022, 6, 14),
    schedule_interval="10 1 * * *",
    owner='t-rex',
    tags=[
        'presentation',
        'content',
        'dots'
    ]
) as dag:

    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    # TODO: Should use Airflow default_args to pass in these values
    dbt_operators_kwargs = {
        'profiles_dir': '/usr/local/airflow/dbt/presentation',
        'dir': '/usr/local/airflow/dbt/presentation'
    }
    dbt_task_generator = DbtTaskGenerator(manifest_data, dbt_operators_kwargs=dbt_operators_kwargs)
    dbt_task_generator.generate_dbt_tasks()

    check_curated_dots_success = ExternalTaskSensorAsync(
            task_id='check-curated-dots-success',
            external_dag_id='curated-dots',
            external_task_id='curated-dots-success-marker-content-dimension',
            failed_states=[
                State.FAILED,
                State.UPSTREAM_FAILED
            ],
            execution_delta=timedelta(minutes=5),
            mode='reschedule',
            poke_interval=60 * 10,
            timeout=60 * 60 * 8,
            retries=0
        )

    check_curated_dots_success >> dbt_task_generator.roots

    success_marker_concurrency_predictions = ExternalTaskMarker(
        task_id='presentation-content-dimension-success-marker-concurrency-predictions',
        external_dag_id='presentation-concurrency-predictions',
        external_task_id='check-presentation-content-dimension-success',
        execution_date='{{ execution_date.add(hours=2, minutes=35).isoformat() }}',
        retries=0
    )

    success_marker_plays_mart = ExternalTaskMarker(
        task_id='presentation-content-dimension-success-marker-plays-mart',
        external_dag_id='presentation-playback-stream-mart',
        external_task_id='check-presentation-content-dimension-success',
        execution_date='{{ execution_date.add(minutes=50).isoformat() }}',
        retries=0
    )

    success_marker_key_moments = ExternalTaskMarker(
        task_id='presentation-content-dimension-success-marker-key-moments',
        external_dag_id='core-P1Daily-KeyMomentsDimensions',
        external_task_id='check-presentation-content-dimension-success',
        execution_date='{{ execution_date.add(hours=1, minutes=50).isoformat() }}',
        retries=0
    )

    success_marker_airship = ExternalTaskMarker(
        task_id='presentation-content-dimension-success-marker-airship',
        external_dag_id='core-P1Daily-Airship',
        external_task_id='check-presentation-content-dimension-success',
        execution_date='{{ execution_date.add(hours=1, minutes=40).isoformat() }}',
        retries=0
    )

    success_marker_ga_events = ExternalTaskMarker(
        task_id='presentation-content-dimension-success-marker-ga-events',
        external_dag_id='core-P1daily-GAEventsFactDN',
        external_task_id='check-presentation-content-dimension-success',
        execution_date='{{ execution_date.add(hours=7, minutes=20).isoformat() }}',
        retries=0
    )

    success_marker_firebase_events = ExternalTaskMarker(
        task_id='presentation-content-dimension-success-marker-firebase-events',
        external_dag_id='core-P1Daily-FirebaseEventsFactDN',
        external_task_id='check-presentation-content-dimension-success',
        execution_date='{{ execution_date.add(hours=7, minutes=20).isoformat() }}',
        retries=0
    )

    success_marker_tippler = ExternalTaskMarker(
        task_id='presentation-content-dimension-success-marker-tippler',
        external_dag_id='core-P1Daily-TipplerMVP',
        external_task_id='check-presentation-content-dimension-success',
        execution_date='{{ execution_date.add(hours=7, minutes=50).isoformat() }}',
        retries=0
    )

    success_marker_favourites_reminders = ExternalTaskMarker(
        task_id='presentation-content-dimension-success-marker-favourites-reminders',
        external_dag_id='look-P1daily-Favourites-Reminders',
        external_task_id='check-presentation-content-dimension-success',
        execution_date='{{ execution_date.add(hours=5, minutes=50).isoformat() }}',
        retries=0
    )

    leaves = dbt_task_generator.leaves
    leaves >> success_marker_concurrency_predictions
    leaves >> success_marker_plays_mart
    leaves >> success_marker_key_moments
    leaves >> success_marker_airship
    leaves >> success_marker_ga_events
    leaves >> success_marker_firebase_events
    leaves >> success_marker_tippler
    leaves >> success_marker_favourites_reminders
