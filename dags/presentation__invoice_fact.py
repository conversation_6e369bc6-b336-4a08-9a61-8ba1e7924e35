import json
from datetime import datetime, timedelta
from airflow.sensors.external_task_sensor import <PERSON><PERSON>askMark<PERSON>
from airflow.utils.state import State
from astronomer.providers.core.sensors.external_task import \
    ExternalTaskSensorAsync
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.sns_operator import DaznSnsPublishOperator
from helpers.edmdag import EDMDAG

default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/presentation',
    'project_dir': '/usr/local/airflow/dbt/presentation'
}

with EDMDAG(
    dag_id='presentation-invoice-fact',
    partner='invoice-fact',
    default_args=default_args,
    start_date=datetime(2022, 6, 27),
    schedule_interval="15 2 * * *",
    # schedule_interval="20 4 * * *",
    owner='t-rex',
    tags=[
        'presentation',
        'invoice'
    ]
) as dag:
    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    check_curated_zuora_success = ExternalTaskSensorAsync(
        task_id='check-curated-zuora-success',
        external_dag_id='curated-zuora',
        external_task_id='curated-zuora-success-marker',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours=1, minutes=30),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 8,
        retries=0
    )

    check_presentation_ii_success = ExternalTaskSensorAsync(
        task_id='check-presentation-invoice-items-success',
        external_dag_id='presentation-invoice-items',
        external_task_id='presentation-invoice-items-success-marker',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours=1, minutes=5),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 8,
        retries=0
    )

    check_presentation_subscription_domain_success = ExternalTaskSensorAsync(
        task_id='check-presentation-subscription-domain-success',
        external_dag_id='presentation-subscription-domain',
        external_task_id='presentation-subscription-domain-success-marker',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours=1, minutes=25),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 8,
        retries=0
    )

    pipeline_success_marker_revenue_share_sky_it = ExternalTaskMarker(
        task_id='presentation-subscription-domain-success-marker-sky-it-revenue-share',
        external_dag_id='presentation-sky-it-revenue-share',
        external_task_id='check-presentation-invoice-fact-success',
        execution_date='{{ execution_date.add(hours = 2, minutes=35).isoformat() }}',
        retries=0
    )

    pipeline_success_marker_optimove = ExternalTaskMarker(
        task_id='presentation-subscription-domain-success-marker-optimove',
        external_dag_id='exchange-optimove',
        external_task_id='check-presentation-invoice-fact-success',
        execution_date='{{ execution_date.add(minutes=30).isoformat() }}',
        retries=0
    )

    pipeline_success_marker_nfl_gpi = ExternalTaskMarker(
        task_id='presentation-invoice-fact-success-marker-nfl-gpi',
        external_dag_id='exchange-nfl-gpi',
        external_task_id='check-presentation-invoice-fact-success',
        execution_date='{{ execution_date.add(hours=1, minutes=30).isoformat() }}',
        retries=0
    )

    roots = dbt_task_generator.roots
    check_presentation_subscription_domain_success >> roots
    check_curated_zuora_success >> roots
    check_presentation_ii_success >> roots


    msg_details={
            'DagName' : dag.dag_id,
            #'BatchDate':f'{dag.year}-{dag.month}-{dag.day}',
            'DagCompletionTime':datetime.now().isoformat(sep=' ',timespec='seconds'),
            'State' : "Success" }
    
    send_validation_message = DaznSnsPublishOperator(
        task_id=f'send-{dag.dag_id}-success-notification',
        target_arn='{{ var.value.airflow_notifications_sns_arn }}',
        subject='Airflow {{dag.env.upper()}} - {{dag.dag_id}} - ' + msg_details["DagCompletionTime"] + ' - DAG Success',
        message= '\n'.join([f"{key}: {value}" for key, value in msg_details.items()]),
        message_attributes={
            "callback_event" : "dag-success"
            } 
        )


    leaves = dbt_task_generator.leaves
    leaves >> pipeline_success_marker_revenue_share_sky_it
    leaves >> pipeline_success_marker_optimove
    leaves >> pipeline_success_marker_nfl_gpi

    [pipeline_success_marker_revenue_share_sky_it, pipeline_success_marker_optimove, pipeline_success_marker_nfl_gpi] >> send_validation_message
