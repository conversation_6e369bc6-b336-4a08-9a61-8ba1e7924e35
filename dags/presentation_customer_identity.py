import json
from datetime import datetime, timedelta

from airflow.sensors.external_task_sensor import <PERSON><PERSON>ask<PERSON>ark<PERSON>
from airflow.utils.state import State
from astronomer.providers.core.sensors.external_task import \
    ExternalTaskSensorAsync
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.edmdag import EDMDAG

default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/presentation',
    'project_dir': '/usr/local/airflow/dbt/presentation'
}

with EDMDAG(
    dag_id='presentation-customer-identity',
    partner='customer-identity',  # TBC
    default_args=default_args,
    start_date=datetime(2022, 9, 28),
    schedule_interval="0 1 * * *",  # TBC
    owner='t-rex',
    tags=[
        'presentation',
    ]
) as dag:

    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    roots = dbt_task_generator.roots

    check_curated_zuora_success = ExternalTaskSensorAsync(
            task_id='check-curated-zuora-success',
            external_dag_id='curated-zuora',
            external_task_id='curated-zuora-cust-id-dim-success-marker',
            failed_states=[
                State.FAILED,
                State.UPSTREAM_FAILED
            ],
            execution_delta=timedelta(minutes=15),
            mode='reschedule',
            poke_interval=60 * 10,
            timeout=60 * 60 * 6,
            retries=0
        )
    check_curated_zuora_success >> roots

    # check_curated_salesforce_success = ExternalTaskSensorAsync(
    #         task_id='check-curated-salesforce-success',
    #         external_dag_id='curated-salesforce',
    #         external_task_id='curated-salesforce-cust-id-dim-success-marker',
    #         failed_states=[
    #             State.FAILED,
    #             State.UPSTREAM_FAILED
    #         ],
    #         execution_delta=timedelta(minutes=15),
    #         mode='reschedule',
    #         poke_interval=60 * 10,
    #         timeout=60 * 60 * 6,
    #         retries=0
    #     )
    # check_curated_salesforce_success >> roots

    check_curated_segment_success = ExternalTaskSensorAsync(
            task_id='check-curated-segment-customer-identity-success',
            external_dag_id='curated-segment',
            external_task_id='curated-segment-customer-identity-success-marker',
            failed_states=[
                State.FAILED,
                State.UPSTREAM_FAILED
            ],
            execution_delta=timedelta(minutes=30),
            mode='reschedule',
            poke_interval=60 * 10,
            timeout=60 * 60 * 6,
            retries=0
        )
    check_curated_segment_success >> roots

    customer_identity_dim_success_marker = ExternalTaskMarker(
        task_id='presentation-customer-id-dim-optimove-success-marker',
        external_dag_id='exchange-optimove',
        external_task_id='check-presentation-customer-id-dim-success',
        execution_date='{{ execution_date.add(hours=1, minutes=45).isoformat() }}',
        retries=0
    )

    pipeline_success_marker_nielsen = ExternalTaskMarker(
        task_id='presentation-customer-id-dim-nielsen-success-marker',
        external_dag_id='exchange-nielsen',
        external_task_id='check-presentation-customer-id-dim-success',
        execution_date='{{ execution_date.add(hours=3).isoformat() }}',
        retries=0
    )

    pipeline_success_marker_nfl_gpi = ExternalTaskMarker(
        task_id='presentation-customer-id-dim-nfl-gpi-success-marker',
        external_dag_id='exchange-nfl-gpi',
        external_task_id='check-presentation-customer-id-dim-success',
        execution_date='{{ execution_date.add(hours=2, minutes=45).isoformat() }}',
        retries=0
    )

    dbt_task_generator.get_task_by_id('run-customer_identity_dim_current') >> customer_identity_dim_success_marker
    dbt_task_generator.get_task_by_id('run-customer_identity_dim_current') >> pipeline_success_marker_nielsen
    dbt_task_generator.get_task_by_id('run-customer_identity_dim_current') >> pipeline_success_marker_nfl_gpi
