import json
from datetime import datetime, timedelta

from astronomer.providers.core.sensors.external_task import ExternalTaskSensorAsync
from airflow.utils.state import State
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.edmdag import EDMDAG

default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/presentation',
    'project_dir': '/usr/local/airflow/dbt/presentation'
}

with EDMDAG(
    dag_id="presentation-entitlements",
    partner='entitlements',
    default_args=default_args,
    start_date=datetime(2022, 5, 4),
    schedule_interval="50 1 * * *",
    owner='t-rex',
    tags=[
        'presentation',
        'entitlements'
    ]
) as dag:

    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    check_curated_entitlement_sets_success = ExternalTaskSensorAsync(
            task_id='check-curated-entitlements-success',
            external_dag_id='curated-entitlements',
            external_task_id='curated-entitlements-success-marker',
            failed_states=[
                State.FAILED,
                State.UPSTREAM_FAILED
            ],
            execution_delta=timedelta(minutes=5),
            mode='poke',
            poke_interval=60,
            timeout=60 * 25,
            retries=0
        )

    check_curated_entitlement_sets_success >> dbt_task_generator.roots
