from helpers.edmdag import EDMDAG
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from astronomer.providers.core.sensors.external_task import ExternalTaskSensorAsync
from airflow.sensors.external_task_sensor import ExternalTaskMarker
from airflow.utils.state import State
import json
from datetime import datetime, timedelta

default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/presentation',
    'project_dir': '/usr/local/airflow/dbt/presentation'
}

with EDMDAG(
    dag_id="staging-hyper-personalisation",
    partner='hyper-personalisation',
    default_args=default_args,
    start_date=datetime(2024, 5, 13),
    schedule_interval="05 00 * * 2",
    owner='t-rex',
    tags=['staging', 'hyper-personalisation']
) as dag:

    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    check_curated_hyper_personalisation_success = ExternalTaskSensorAsync(
            task_id=f'check-curated-hyper-personalisation-success',
            external_dag_id='curated-hyper-personalisation',
            external_task_id='curated-hyper-personalisation-success-marker-staging-hyper-personalisation',
            failed_states=[
                State.FAILED,
                State.UPSTREAM_FAILED
            ],
            execution_delta=timedelta(minutes=5),
            mode='reschedule',
            poke_interval=60 * 10,
            timeout=60 * 60 * 8,
            retries=0
        )
    

    pipeline_staging_hyper_personalisation_success_marker = ExternalTaskMarker(
        task_id='staging-hyper-personalisation-success-marker-exchange-hyper-personalisation',
        external_dag_id='exchange-hyper-personalisation',
        external_task_id='check-staging-hyper-personalisation-success',
        execution_date='{{ execution_date.add(minutes=5).isoformat() }}',
        retries=0
    )
        
    check_curated_hyper_personalisation_success >> dbt_task_generator.roots
    dbt_task_generator.roots >> pipeline_staging_hyper_personalisation_success_marker
