import json
from datetime import datetime, timedelta

from airflow.sensors.external_task_sensor import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from astronomer.providers.core.sensors.external_task import ExternalTaskSensorAsync
from airflow.utils.state import State
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.edmdag import EDMDAG

default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/presentation',
    'project_dir': '/usr/local/airflow/dbt/presentation'
}

with EDMDAG(
    dag_id='presentation-staging',
    partner='staging',
    default_args=default_args,
    start_date=datetime(2022, 6, 14),
    schedule_interval="50 0 * * *",
    owner='t-rex',
    tags=[ 'presentation' ]
) as dag:

    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    roots = dbt_task_generator.roots

    check_curated_zuora_success = ExternalTaskSensorAsync(
            task_id='check-curated-zuora-success',
            external_dag_id='curated-zuora',
            external_task_id='curated-zuora-success-marker',
            failed_states=[
                State.FAILED,
                State.UPSTREAM_FAILED
            ],
            execution_delta=timedelta(minutes=5),
            mode='reschedule',
            poke_interval=60 * 10,
            timeout=60 * 60 * 8,
            retries=0
        )
    check_curated_zuora_success >> roots

    check_curated_segment_success = ExternalTaskSensorAsync(
            task_id='check-curated-segment-presentation-staging-success',
            external_dag_id='curated-segment',
            external_task_id='curated-segment-presentation-staging-success-marker',
            failed_states=[
                State.FAILED,
                State.UPSTREAM_FAILED
            ],
            execution_delta=timedelta(minutes=20),
            mode='reschedule',
            poke_interval=60 * 10,
            timeout=60 * 60 * 8,
            retries=0
        )
    check_curated_segment_success >> roots

    check_presentation_staging_ev_invoices_success = ExternalTaskSensorAsync(
            task_id='check-presentation-staging-ev-invoices-success',
            external_dag_id='presentation-staging-ev-invoices',
            external_task_id='presentation-staging-ev-invoices-success-marker-presentation-staging',
            failed_states=[
                State.FAILED,
                State.UPSTREAM_FAILED
            ],
            execution_delta=timedelta(minutes=30),
            mode='reschedule',
            poke_interval=60 * 10,
            timeout=60 * 60 * 8,
            retries=0
        )
    check_presentation_staging_ev_invoices_success >> roots

    pipeline_success_marker = ExternalTaskMarker(
        task_id='presentation-staging-success-marker',
        external_dag_id='presentation-subscription-domain',
        external_task_id='check-presentation-staging-success',
        execution_date='{{ execution_date.add(minutes=0).isoformat() }}',
        retries=0
    )

    pipeline_success_marker_pga = ExternalTaskMarker(
        task_id='presentation-staging-success-marker-pga',
        external_dag_id='exchange-pga',
        external_task_id='check-presentation-staging-success',
        execution_date='{{ execution_date.add(hours=1, minutes=55).isoformat() }}',
        retries=0
    )

    pipeline_success_marker_ppv = ExternalTaskMarker(
        task_id='presentation-staging-success-marker-ppv',
        external_dag_id='presentation-ppv-purchases',
        external_task_id='check-presentation-staging-success',
        execution_date='{{ execution_date.add(minutes=0).isoformat() }}',
        retries=0
    )

    pipeline_success_marker_inv_itm = ExternalTaskMarker(
        task_id='presentation-staging-success-marker-inv-itm',
        external_dag_id='presentation-invoice-items',
        external_task_id='check-presentation-staging-success',
        execution_date='{{ execution_date.add(minutes=20).isoformat() }}',
        retries=0
    )

    pipeline_success_marker_rally_tv = ExternalTaskMarker(
        task_id='presentation-staging-success-marker-rally-tv',
        external_dag_id='exchange-rally-tv',
        external_task_id='check-presentation-staging-success',
        execution_date='{{ execution_date.add(hours=1, minutes=55).isoformat() }}',
        retries=0
    )

    pipeline_success_marker_atp = ExternalTaskMarker(
        task_id='presentation-staging-success-marker-atp',
        external_dag_id='exchange-atp',
        external_task_id='check-presentation-staging-success',
        execution_date='{{ execution_date.add(hours=1, minutes=55).isoformat() }}',
        retries=0
    )

    pipeline_success_marker_elf = ExternalTaskMarker(
        task_id='presentation-staging-success-marker-elf',
        external_dag_id='exchange-elf',
        external_task_id='check-presentation-staging-success',
        execution_date='{{ execution_date.add(hours=6, minutes=40).isoformat() }}',
        retries=0
    )

    # dbt_task_generator.leaves >> pipeline_success_marker
    # dbt_task_generator.leaves >> pipeline_success_marker_pga
    # dbt_task_generator.leaves >> pipeline_success_marker_ppv
    # dbt_task_generator.leaves >> pipeline_success_marker_inv_itm

    leaves = dbt_task_generator.leaves
    leaves >> pipeline_success_marker
    leaves >> pipeline_success_marker_pga
    leaves >> pipeline_success_marker_ppv
    leaves >> pipeline_success_marker_inv_itm
    leaves >> pipeline_success_marker_rally_tv
    leaves >> pipeline_success_marker_atp
    leaves >> pipeline_success_marker_elf
