from astronomer.providers.core.sensors.external_task import ExternalTaskSensorAsync
from airflow.utils.state import State
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.sns_operator import DaznSnsPublishOperator
from helpers.edmdag import EDMDAG
from datetime import datetime, timedelta
import json

default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/presentation',
    'project_dir': '/usr/local/airflow/dbt/presentation'
}

with EDMDAG(
    dag_id="presentation-playback-stream-freemium",
    partner='freemium',
    default_args=default_args,
    start_date=datetime(2022, 5, 4),
    schedule_interval="45 2 * * *",
    owner='t-rex',
    tags=[
        'freemium'
    ]
) as dag:

    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    check_pipeline_subscription_success = ExternalTaskSensorAsync(
        task_id='check-presentation-subscription-domain-success',
        external_dag_id='presentation-subscription-domain',
        external_task_id='presentation-subscription-domain-success-marker-presentation-playback-stream-freemium',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours=1, minutes=55),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 4,
        retries=0
    )

    check_pipeline_playsmart_success = ExternalTaskSensorAsync(
        task_id='check-presentation-playback-stream-mart-success',
        external_dag_id='presentation-playback-stream-mart',
        external_task_id='presentation-playback-stream-mart-success-marker-presentation-playback-stream-freemium',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta( minutes=45),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 4,
        retries=0
    )


    msg_details={
            'DagName' : dag.dag_id,
            #'BatchDate':f'{dag.year}-{dag.month}-{dag.day}',
            'DagCompletionTime':datetime.now().isoformat(sep=' ',timespec='seconds'),
            'State' : "Success" }
    
    send_validation_message = DaznSnsPublishOperator(
        task_id=f'send-{dag.dag_id}-success-notification',
        target_arn='{{ var.value.airflow_notifications_sns_arn }}',
        subject='Airflow {{dag.env.upper()}} - {{dag.dag_id}} - ' + msg_details["DagCompletionTime"] + ' - DAG Success',
        message= '\n'.join([f"{key}: {value}" for key, value in msg_details.items()]),
        message_attributes={
            "callback_event" : "dag-success"
            } 
        )


    roots = dbt_task_generator.roots

    check_pipeline_subscription_success >> roots
    check_pipeline_playsmart_success >> roots


    leaves = dbt_task_generator.leaves
    leaves >> send_validation_message
    