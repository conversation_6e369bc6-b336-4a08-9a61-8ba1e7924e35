from helpers.edmdag import EDMDAG
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from astronomer.providers.core.sensors.external_task import ExternalTaskSensorAsync
from airflow.sensors.external_task_sensor import ExternalTaskMarker
from airflow.utils.state import State
import json
from datetime import datetime, timedelta

default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/presentation',
    'project_dir': '/usr/local/airflow/dbt/presentation'
}

with EDMDAG(
    dag_id="staging-fanatics",
    partner='fanatics',
    default_args=default_args,
    start_date=datetime(2024, 3, 7),
    schedule_interval="11 13 * * *",
    owner='t-rex',
    tags=['staging', 'fanatics']
) as dag:

    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    check_curated_fanatics_success = ExternalTaskSensorAsync(
            task_id=f'check-curated-fanatics-success',
            external_dag_id='curated-fanatics',
            external_task_id='curated-fanatics-success-marker-staging-fanatics',
            failed_states=[
                State.FAILED,
                State.UPSTREAM_FAILED
            ],
            execution_delta=timedelta(minutes=1),
            mode='reschedule',
            poke_interval=60 * 10,
            timeout=60 * 60 * 8,
            retries=0
        )
    check_curated_fanatics_success >> dbt_task_generator.roots

    pipeline_staging_fanatics_success_marker = ExternalTaskMarker(
        task_id='staging-fanatics-success-marker-presentation-fanatics',
        external_dag_id='presentation-fanatics',
        external_task_id='check-staging-fanatics-success',
        execution_date='{{ execution_date.add(minutes=1).isoformat() }}',
        retries=0
    )
    dbt_task_generator.leaves >> pipeline_staging_fanatics_success_marker
