from helpers.edmdag import EDMDAG
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from astronomer.providers.core.sensors.external_task import ExternalTaskSensorAsync
from airflow.sensors.external_task_sensor import ExternalTaskMarker
from airflow.utils.state import State
import json
from datetime import datetime, timedelta

default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/presentation',
    'project_dir': '/usr/local/airflow/dbt/presentation'
}

with EDMDAG(
    dag_id="staging-amazon-personalize",
    partner='amazon',
    default_args=default_args,
    start_date=datetime(2024, 2, 19),
    schedule_interval="35 0 * * *",
    owner='t-rex',
    tags=['staging', 'amazon', 'personalize']
) as dag:

    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    check_curated_amazon_personalize_success = ExternalTaskSensorAsync(
            task_id=f'check-curated-amazon-personalize-success',
            external_dag_id='curated-amazon-personalize',
            external_task_id='curated-amazon-personalize-success-marker-staging-amazon-personalize',
            failed_states=[
                State.FAILED,
                State.UPSTREAM_FAILED
            ],
            execution_delta=timedelta(minutes=5),
            mode='reschedule',
            poke_interval=60 * 10,
            timeout=60 * 60 * 8,
            retries=0
        )
    check_curated_amazon_personalize_success >> dbt_task_generator.roots

    pipeline_staging_amazon_personalize_success_marker = ExternalTaskMarker(
        task_id='staging-amazon-personalize-success-marker-exchange-optimove',
        external_dag_id='exchange-optimove',
        external_task_id='check-staging-amazon-personalize-success',
        execution_date='{{ execution_date.add(hours=2, minutes=10).isoformat() }}',
        retries=0
    )
    dbt_task_generator.leaves >> pipeline_staging_amazon_personalize_success_marker
