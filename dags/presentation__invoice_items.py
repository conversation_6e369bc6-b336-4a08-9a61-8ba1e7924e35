import json
from datetime import datetime, timedelta
from airflow.sensors.external_task_sensor import <PERSON><PERSON>ask<PERSON>arker
from astronomer.providers.core.sensors.external_task import ExternalTaskSensorAsync
from airflow.utils.state import State
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.sns_operator import DaznSnsPublishOperator
from helpers.edmdag import EDMDAG

# todo: consider bringing all invoice related models under a single dag
# Currently upstream to multiple flows.

default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/presentation',
    'project_dir': '/usr/local/airflow/dbt/presentation'
}

with EDMDAG(
    dag_id="presentation-invoice-items",
    partner='invoice-items',
    default_args=default_args,
    start_date=datetime(2022, 5, 4),
    schedule_interval="10 1 * * *",
    owner='t-rex',
    tags=[
        'presentation',
        'payments'
    ]
) as dag:
    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    check_curated_fxloader_success = ExternalTaskSensorAsync(
        task_id='check-curated-fxloader-success',
        external_dag_id='curated-fxloader',
        external_task_id='curated-fxloader-success-marker',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(minutes=5),
        mode='reschedule',
        poke_interval=60 * 5,
        timeout=60 * 25,
        retries=0
    )

    check_staging_zuora_success = ExternalTaskSensorAsync(
        task_id='check-presentation-staging-success',
        external_dag_id='presentation-staging',
        external_task_id='presentation-staging-success-marker-inv-itm',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(minutes=20),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 5,
        retries=0
    )

    check_presentation_scd_success = ExternalTaskSensorAsync(
            task_id='check-presentation-scd-success',
            external_dag_id='presentation-subscription-domain',
            external_task_id='presentation-success-marker-inv-itm-join',
            failed_states=[
                State.FAILED,
                State.UPSTREAM_FAILED
            ],
            execution_delta=timedelta(minutes=20),
            mode='reschedule',
            poke_interval=60 * 10,
            timeout=60 * 60 * 8,
            retries=0
    )

    roots = dbt_task_generator.roots
    check_staging_zuora_success >> roots
    check_curated_fxloader_success >> roots
    check_presentation_scd_success >> roots


    msg_details={
            'DagName' : dag.dag_id,
            #'BatchDate':f'{dag.year}-{dag.month}-{dag.day}',
            'DagCompletionTime':datetime.now().isoformat(sep=' ',timespec='seconds'),
            'State' : "Success" }

    send_validation_message = DaznSnsPublishOperator(
        task_id=f'send-{dag.dag_id}-success-notification',
        target_arn='{{ var.value.airflow_notifications_sns_arn }}',
        subject='Airflow {{dag.env.upper()}} - {{dag.dag_id}} - ' + msg_details["DagCompletionTime"] + ' - DAG Success',
        message= '\n'.join([f"{key}: {value}" for key, value in msg_details.items()]),
        message_attributes={
            "callback_event" : "dag-success"
            }
        )

    pipeline_success_marker = ExternalTaskMarker(
        task_id='presentation-invoice-items-success-marker',
        external_dag_id='presentation-invoice-fact',
        external_task_id='check-presentation-invoice-items-success',
        execution_date='{{ execution_date.add(hours=1, minutes=5).isoformat() }}',
        retries=0
    )

    dbt_task_generator.leaves >> pipeline_success_marker >> send_validation_message
