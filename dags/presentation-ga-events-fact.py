import json
from datetime import datetime, timedelta

from astronomer.providers.core.sensors.external_task import ExternalTaskSensorAsync
from airflow.utils.state import State
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.edmdag import EDMDAG

default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/presentation',
    'project_dir': '/usr/local/airflow/dbt/presentation'
}

with EDMDAG(
    dag_id="presentation-ga-events-fact",
    partner='ga-events-fact',
    default_args=default_args,
    start_date=datetime(2022, 5, 4),
    # Start DAG five minutes after curated_ga DAG, which is will run once pipeline_ga has finished
    schedule_interval="5 8 * * *",
    owner='t-rex',
    tags=[
        'presentation',
        'user-behaviour-and-experimentation'
    ]
) as dag:

    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/presentation/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    check_curated_ga_success = ExternalTaskSensorAsync(
            task_id='check-curated-ga-success',
            external_dag_id='curated-ga',
            external_task_id='curated-ga-success-marker',
            failed_states=[
                State.FAILED,
                State.UPSTREAM_FAILED
            ],
            execution_delta=timedelta(minutes=5),
            mode='reschedule',
            poke_interval=60 * 10,
            timeout=60 * 60 * 48,
            retries=0
        )
    check_curated_ga_success >> dbt_task_generator.roots
